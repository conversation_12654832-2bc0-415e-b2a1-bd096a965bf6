using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Service;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
namespace JieNor.AMS.YDJ.Store.AppService.Service
{
    [InjectService]
    public class RepairService : IRepairService
    {
        /// <summary>
        /// 修正选单时保存后，对应的表头源单号无有效对应明细行的情况数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="billHeadSourceNumField"></param>
        /// <param name="billHeadSourceIdField"></param>
        /// <param name="activeEntityKey"></param>
        /// <param name="activeSourceNumField"></param>
        /// <param name="activeSourceIdField"></param>
        public void RepairBillHeadSourceNum(UserContext userCtx,
            DynamicObject[] dataEntitys,
            string billHeadSourceNumField = "fsourcenumber",
            string billHeadSourceIdField = "fsourceinterid",
            string activeEntityKey = "fentity",
            string activeSourceNumField = "fsourcebillno",
            string activeSourceIdField = "fsourceinterid")
        {
            if (dataEntitys == null || !dataEntitys.Any()) return;

            bool hasEntity = !(dataEntitys.First() as DynamicObject)[activeEntityKey].IsNullOrEmpty();

            if (!hasEntity|| billHeadSourceNumField.IsNullOrEmptyOrWhiteSpace() || activeSourceNumField.IsNullOrEmptyOrWhiteSpace()) return;

            foreach (var item in dataEntitys)
            {
                var entry = item[activeEntityKey] as DynamicObjectCollection;
                var resultEntry = entry?.Where(x => !x[activeSourceNumField].IsNullOrEmptyOrWhiteSpace());
                var sumSourceNo = resultEntry?.Select(x => x[activeSourceNumField])?.Distinct().ToList();
                if (!sumSourceNo.Contains(item[billHeadSourceNumField]))
                {
                    item[billHeadSourceNumField] = sumSourceNo?.FirstOrDefault();
                }
                if (!billHeadSourceIdField.IsNullOrEmptyOrWhiteSpace() &&
                        !item[billHeadSourceIdField].IsNullOrEmpty() &&
                        resultEntry != null &&
                        !activeSourceIdField.IsNullOrEmptyOrWhiteSpace() &&
                        resultEntry.Any(x => !x[activeSourceIdField].IsNullOrEmpty()))
                {
                    item[billHeadSourceIdField] = resultEntry.FirstOrDefault(x => Convert.ToString(x[activeSourceNumField]) == Convert.ToString(item[billHeadSourceNumField])&&!x[activeSourceIdField].IsNullOrEmptyOrWhiteSpace())?[activeSourceIdField];
                }
            }
        }
    }
}
