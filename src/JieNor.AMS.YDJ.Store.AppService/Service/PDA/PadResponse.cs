using JieNor.Framework.DataTransferObject.IoC;
using JieNor.Framework.DataTransferObject.Poco;
using ServiceStack;
using System;

namespace JieNor.AMS.YDJ.Store.AppService.Service.PDA
{
    /// <summary>
    /// 接口响应结果对象
    /// </summary>
    [Serializable]
    [AutoWired]
    public class PadResponse : IHasResponseStatus
    {
        /// <summary>
        /// 请求回传对象
        /// </summary>
        public ResponseStatus ResponseStatus { get; set; }

        /// <summary>
        /// 操作返回结果
        /// </summary>
        [AutoWired]
        public IOperationResult OperationResult { get; set; } = new OperationResult();
    }
}