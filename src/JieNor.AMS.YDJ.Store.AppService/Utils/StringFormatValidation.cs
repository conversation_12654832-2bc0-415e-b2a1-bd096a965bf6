using JieNor.Framework;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Utils
{
    /// <summary>
    /// 字符串格式验证
    /// 作者：zpf
    /// 日期：2022/01/11
    /// </summary>
    public class StringFormatValidation
    {
        /// <summary>
        /// 字符串正则解析
        /// 作者：zpf
        /// 日期：2021/01/11
        /// </summary>
        /// <param name="userContext">上下文</param>
        /// <param name="targetStr">目标字符串</param>
        /// <param name="isValidationPasses">是否验证通过</param>
        /// <returns>符合规则的属性名与属性值的键值对集合</returns>
        public static Dictionary<string, string> StringRegularParsing(UserContext userContext, string targetStr, out bool isValidationPasses)
        {
            //var targetStr = "[床架长]=190and[床架宽]=180and[床架高度]=10or[床架其他定制]='床邦加高10cm'";
            isValidationPasses = true;
            Dictionary<string, string> dic = new Dictionary<string, string>();
            return dic;
            var paramSer = userContext.Container.GetService<ISystemProfile>();
            var stringseparators = paramSer.GetSystemParameter<string>(userContext, "ydj_goodscorrelatedparms", "fstringseparators"); //字符串分割符号
            var regularexpressionCiphertext = paramSer.GetSystemParameter<string>(userContext, "ydj_goodscorrelatedparms", "fregularexpression"); //正则表达式
            var stringcontentseparators = paramSer.GetSystemParameter<string>(userContext, "ydj_goodscorrelatedparms", "fstringcontentseparators"); //字符串内容分割符号
            var regularexpression = DecodeBase64(regularexpressionCiphertext);
            Regex regexExp = new Regex(regularexpression);
            Regex regexSplit = new Regex(stringcontentseparators, RegexOptions.IgnoreCase);
            isValidationPasses = true;
            //字符串分割符号数组
            string[] stringSeparators = stringseparators.Split(',');
            string[] result = targetStr.Split(stringSeparators, StringSplitOptions.None);
            foreach (string s in result)
            {
                var match = regexExp.Match(s);
                if (match.Success)
                {
                    string[] sArray = regexSplit.Split(match.Value);
                    if (sArray.Length == 2)
                    {
                        if (!dic.ContainsKey(sArray.FirstOrDefault()))
                            dic.Add(sArray.FirstOrDefault(), sArray.LastOrDefault());
                    }
                }
                else
                {
                    isValidationPasses = false;
                }
            }
            return dic;
        }

        /// <summary>
        /// Base64解密
        /// </summary>
        /// <param name="codeName">解密采用的编码方式，注意和加密时采用的方式一致</param>
        /// <param name="result">待解密的密文</param>
        /// <returns>解密后的字符串</returns>
        private static string DecodeBase64(Encoding encode, string result)
        {
            string decode = "";
            byte[] bytes = Convert.FromBase64String(result);
            try
            {
                decode = encode.GetString(bytes);
            }
            catch
            {
                decode = result;
            }
            return decode;
        }

        /// <summary>
        /// Base64解密，采用utf8编码方式解密
        /// </summary>
        /// <param name="result">待解密的密文</param>
        /// <returns>解密后的字符串</returns>
        private static string DecodeBase64(string result)
        {
            return DecodeBase64(Encoding.UTF8, result);
        }

        /// <summary>
        /// 判断是否汉字或数字或英文单词
        /// </summary>
        public static bool IsChineseOrNumberOrWord(string value)
        {
            var data = value.ToCharArray();
            Regex rg = new Regex("^[\u4e00-\u9fa5a-zA-Z0-9]$");
            foreach (var item in data)
            {
                if (!rg.IsMatch(item.ToString()))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 字符串是否中文，字母或者数字
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static bool IsChineseOrLetterOrDigit(string input)
        {
            for (int i = 0; i < input.Length; i++)
            {
                if (!IsChineseLetter(input[i].ToString()) && !Char.IsLetterOrDigit(input[i]))
                    return false;
            }
            return true;
        }

        /// <summary>
        /// 校验字符是否是中文
        /// </summary>
        /// <param name="input">字符</param>
        /// <param name="index">开始位置</param>
        /// <returns></returns>
        private static bool IsChineseLetter(string input, int index = 0)
        {
            int code = 0;
            int chfrom = Convert.ToInt32("4e00", 16);    //范围（0x4e00～0x9fff）转换成int（chfrom～chend）
            int chend = Convert.ToInt32("9fff", 16);
            if (input != "")
            {
                code = Char.ConvertToUtf32(input, index);    //获得字符串input中指定索引index处字符unicode编码
                if (code >= chfrom && code <= chend)
                {
                    return true;     //当code在中文范围内返回true
                }
                else
                {
                    return false;
                }
            }
            return true;
        }

    }
}
