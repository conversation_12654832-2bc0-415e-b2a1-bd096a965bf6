using JieNor.AMS.YDJ.Store.AppService;
using JieNor.AMS.YDJ.Store.AppService.Service;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.PriceSynthesize
{
    /// <summary>
    /// 库龄分析报表：手动更新
    /// </summary>
    [InjectService]
    [FormId("rpt_stockageanalysis")]
    [OperationNo("updateData")]
    public class UpdateStockAgeData : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            // 通过临时表生成数据
            var tmpTableName = string.Empty;
            try
            {
                var stockdatestr = DateTime.Now.AddDays(1).Date.ToString("yyyy-MM-dd");
                var stockAgeAnalysisService = this.Context.Container.GetService<StockAgeAnalysisService>();
                var result = stockAgeAnalysisService?.StockAgeCalculate(this.Context, stockdatestr);

                //同步商品体积数据
                var syncProductVolumeService = this.Context.Container.GetService<SyncProductVolumeService>();
                var syncResult = syncProductVolumeService.SyncProductVolume(this.Context, this.HtmlForm.Id);
            }
            finally
            {
                this.DBService.DeleteTempTableByName(this.Context, tmpTableName, true);
                //刷新页面
                this.AddRefreshPageAction();
            }
        }
    }
}
