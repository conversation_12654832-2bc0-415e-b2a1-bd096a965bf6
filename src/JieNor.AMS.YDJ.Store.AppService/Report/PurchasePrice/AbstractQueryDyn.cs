using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Report.PurchasePrice
{
    public abstract class AbstractQueryDyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "onAfterParseFilterString":
                    this.OnAfterParseFilterString(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理基础资料字段过滤条件解析后事件逻辑
        /// </summary>
        /// <param name="e"></param>
        private void OnAfterParseFilterString(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, string>;
            var fieldKey = eventData.Item1?.ToLowerInvariant(); //基础资料字段标识
            var fieldFilter = eventData.Item2; //基础资料字段过滤条件
            var formid = this.HtmlForm.Id;
            switch (fieldKey)
            {
                case "fproductnumber_h":
                    {
                        // 仅能搜索总部商品
                        var filter = $" fmainorgid='{this.Context.TopCompanyId}' ";

                        e.Result = filter;
                        e.Cancel = true;
                        break;
                    }
            }
        }
    }
}
