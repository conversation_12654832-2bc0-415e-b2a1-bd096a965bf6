using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Report.OrdertoBy
{
    /// <summary>
    /// 订单转采购基本过滤：新增
    /// </summary>
    [InjectService]
    [FormId("rpt_ordertobuy_filter")]
    [OperationNo("new")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;
            var data = e.DataEntitys.FirstOrDefault();
            data["fproductid"] = this.ParentPageSession.fproductIds;
            data["fproductid_txt"] = this.ParentPageSession.fproductIds_name;
            data["fdatefrom"] = this.ParentPageSession.fdatefrom;
            data["fdateto"] = this.ParentPageSession.fdateto;
            data["fwarehousetype"] = this.ParentPageSession.fwarehousetype;
            data["forderstatus"] = this.ParentPageSession.forderstatus;
            data["fisbz"] = this.ParentPageSession.fisbz;
            data["fisdz"] = this.ParentPageSession.fisdz;
            data["fsupplierid"] = this.ParentPageSession.fsupplierid;
            data["fsupplierid_txt"] = this.ParentPageSession.fsupplierid_txt;
            data["fcategoryid"] = this.ParentPageSession.fcategoryid;
            data["fcategoryid_txt"] = this.ParentPageSession.fcategoryid_txt;
            data["fisshow"] = this.ParentPageSession.fisshow;
        }

        
    }
}