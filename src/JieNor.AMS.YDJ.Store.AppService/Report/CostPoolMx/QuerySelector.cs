using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Report.CostPoolMx
{

    /// <summary>
    /// 总部费用池查询：动态列基础资料字段弹窗查询操作
    /// </summary>
    [InjectService]
    [FormId("rpt_costpool_filter")]
    [OperationNo("QuerySelector")]
    public class QuerySelector : AbstractQueryDyn
    {
        //该插件功能逻辑在基类实现
    }
}
