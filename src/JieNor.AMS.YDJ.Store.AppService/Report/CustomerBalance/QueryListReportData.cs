using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report.CustomerBalance
{
    /// <summary>
    /// 客户余额报表
    /// </summary>
    [InjectService]
    [FormId("rpt_customerbalance")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 业务日期从
        /// </summary>
        private DateTime dateFrom { get; set; }
        /// <summary>
        /// 业务日期至
        /// </summary>
        private DateTime dateTo { get; set; }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.CheckDataEnvironment();
            base.OnExecuteLogic();
            this.GetCustomerBalanceData();
        }

        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {

            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
            if (!dtDateFrom.HasValue && !dtDateTo.HasValue)
            {
                //如果都为空，则说明为报表打开第一次查询，此时需跟前端初始化保持一致
                var nowTime = DateTime.Now;
                //dtDateFrom = new DateTime(nowTime.Year, nowTime.Month, 1);//当月第一天
                dtDateFrom = new DateTime(2004, 1, 1);

                var nextTime = nowTime.AddMonths(1);
                dtDateTo = new DateTime(nextTime.Year, nextTime.Month, 1).AddDays(-1);//当月最后一天
            }

            if (!dtDateFrom.HasValue)
            {
                throw new BusinessException("过滤条件【业务日期从】不能为空！");
            }
            if (!dtDateTo.HasValue)
            {
                throw new BusinessException("过滤条件【业务日期至】不能为空！");
            }

            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【截止日期不能早于开始日期】！");
            }

            dateFrom = dtDateFrom.Value.DayBegin();
            dateTo = dtDateTo.Value.DayEnd();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        private void GetCustomerBalanceData()
        {
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            //查询当前经销商下所有的客户
            StringBuilder sbCustomerSql = new StringBuilder();
            sbCustomerSql.AppendLine($@"/*dialect*/INSERT INTO {this.DataSourceTableName}(fid,fjnidentityid,FFormId,fcustomerid,factualbalance) ");
            sbCustomerSql.AppendLine($@"SELECT row_number() over(order by A.fnumber DESC) as fid,row_number() over(order by A.fnumber DESC) as fjnidentityid,'{this.HtmlForm.Id}' AS FFormId,A.fid AS fcustomerid,B.fbalance_e AS factualbalance  
                                        FROM T_YDJ_CUSTOMER AS A WITH(NOLOCK)
                                        INNER JOIN T_YDJ_CUSTOMERACCOUNT AS B WITH(NOLOCK) ON A.fid = B.fid
                                        WHERE A.fmainorgid = @fmainorgid AND A.fforbidstatus = @fforbidstatus 
                                        ORDER BY A.fnumber DESC");
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
                new SqlParam("@fforbidstatus", DbType.String, "0")
            };
            dbServiceExt.Execute(this.Context, sbCustomerSql.ToString(), sqlParams);

            //查询需要用到的收支记录支付方式
            var waySql = "SELECT fid,fenumitem FROM v_bd_enum WHERE fcategory='收支记录支付方式' AND fenumitem IN ('账户支付','商场代金券')";
            var wayObjs = this.DBService.ExecuteDynamicObject(this.Context, waySql);

            //本期收款：按【支付日期】在已选时间范围，汇总【数据状态】=“已审核”的《收款单》中的【结算明细.金额】，并需剔除【支付方式】=“账户支付”且【账户方向】=“减”（账户支付无需包含，但订单付款需包含）的记录
            var recExcludeWayIds = wayObjs.Where(t => Convert.ToString(t["fenumitem"]).EqualsIgnoreCase("账户支付"))
                                       .Select(e => Convert.ToString(e["fid"])).ToList();
            //本期退款：按【支付日期】在已选时间范围，汇总【数据状态】=“已审核”的《收款退款单》中的【结算明细.金额】，并需剔除【支付方式】=“账户支付”且【账户方向】=“减”（退到账户无需包含，但订单退款需包含）的记录
            var refExcludeWayIds = wayObjs.Where(t => Convert.ToString(t["fenumitem"]).EqualsIgnoreCase("账户支付"))
                                       .Select(e => Convert.ToString(e["fid"])).ToList();

            //查询需要用到的订单金额方向
            var drctSql = "SELECT fid,fenumitem FROM v_bd_enum WHERE fcategory='订单金额方向'";
            var drctObjs = this.DBService.ExecuteDynamicObject(this.Context, drctSql);
            //增
            var addDrctId = drctObjs.Where(t => Convert.ToString(t["fenumitem"]).EqualsIgnoreCase("增")).Select(e => Convert.ToString(e["fid"])).FirstOrDefault();
            //减
            var reduceDrctId = drctObjs.Where(t => Convert.ToString(t["fenumitem"]).EqualsIgnoreCase("减")).Select(e => Convert.ToString(e["fid"])).FirstOrDefault();

            var sqlUpdateParams = new List<SqlParam>()
            {
                new SqlParam("@fdatefrom",DbType.DateTime,dateFrom.DayBegin()),
                new SqlParam("@fdateto",DbType.DateTime,dateTo.DayEnd()),
                new SqlParam("@fdirection_rec",DbType.String,reduceDrctId),
                new SqlParam("@fdirection_ref",DbType.String,addDrctId),
                new SqlParam("@fway_ref",DbType.String,refExcludeWayIds.First())
            };

            //更新收款、退款数据
            StringBuilder sbUpdateSql = new StringBuilder();
            sbUpdateSql.Append(@"/*dialect*/UPDATE t0 SET t0.finitialreceipt = t1.finitialreceipt,t0.fcurrentreceipt = t1.fcurrentreceipt,t0.finitialrefund = t1.finitialrefund,t0.fcurrentrefund = t1.fcurrentrefund 
                                            FROM {0} AS t0 
                                            INNER JOIN (
                                            	SELECT A.fid,A.fcustomerid
                                            	,ISNULL((
                                            		SELECT SUM(ISNULL(iniRec.famount,0)) FROM T_COO_INCOMEDISBURSE AS iniRec WITH(NOLOCK) 
                                            		WHERE iniRec.fcustomerid = A.fcustomerid AND iniRec.fstatus='E' AND iniRec.fdate < @fdatefrom 
                                                    AND iniRec.fsourceformid IN ('ydj_saleintention','ydj_order','ydj_customer','ydj_collectreceipt','stk_sostockreturn') AND iniRec.fbizdirection = 'bizdirection_01'  and iniRec.fcancelstatus=0 --主控菜单【收款单】预置条件
	                                                AND NOT EXISTS (
	                                                	SELECT 1 FROM T_COO_INCOMEDISBURSE AS ICDB WITH(NOLOCK) 
	                                                	WHERE ICDB.fid = iniRec.fid AND ICDB.fway IN ('{1}') AND ICDB.fdirection = @fdirection_rec
	                                                )
                                            		GROUP BY iniRec.fcustomerid
                                            	),0) AS finitialreceipt 
                                            	,ISNULL((
                                            		SELECT SUM(ISNULL(curRec.famount,0)) FROM T_COO_INCOMEDISBURSE AS curRec WITH(NOLOCK) 
                                            		WHERE curRec.fcustomerid = A.fcustomerid AND curRec.fstatus='E' AND curRec.fdate >= @fdatefrom AND curRec.fdate <= @fdateto  and curRec.fcancelstatus=0
                                                    AND curRec.fsourceformid IN ('ydj_saleintention','ydj_order','ydj_customer','ydj_collectreceipt','stk_sostockreturn') AND curRec.fbizdirection = 'bizdirection_01' --主控菜单【收款单】预置条件
	                                                AND NOT EXISTS (
	                                                	SELECT 1 FROM T_COO_INCOMEDISBURSE AS ICDB WITH(NOLOCK) 
	                                                	WHERE ICDB.fid = curRec.fid AND ICDB.fway IN ('{1}') AND ICDB.fdirection = @fdirection_rec
	                                                )
                                            		GROUP BY curRec.fcustomerid
                                            	),0) AS fcurrentreceipt 
                                            	,ISNULL((
                                            		SELECT SUM(ISNULL(iniRef.famount,0)) FROM T_COO_INCOMEDISBURSE AS iniRef WITH(NOLOCK) 
                                            		WHERE iniRef.fcustomerid = A.fcustomerid AND iniRef.fstatus='E' AND iniRef.fdate < @fdatefrom 
                                                    AND iniRef.fsourceformid IN ('ydj_saleintention','ydj_order','ydj_customer','ydj_collectreceipt','stk_sostockreturn') AND iniRef.fbizdirection = 'bizdirection_02'   and iniRef.fcancelstatus=0 --主控菜单【收款退款单】预置条件
	                                                AND NOT EXISTS (
	                                                	SELECT 1 FROM T_COO_INCOMEDISBURSE AS ICDB WITH(NOLOCK) 
	                                                	WHERE ICDB.fid = iniRef.fid AND ICDB.fway = @fway_ref AND ICDB.fdirection = @fdirection_ref
	                                                )
                                            		GROUP BY iniRef.fcustomerid
                                            	),0) AS finitialrefund 
                                            	,ISNULL((
                                            		SELECT SUM(ISNULL(curRef.famount,0)) FROM T_COO_INCOMEDISBURSE AS curRef WITH(NOLOCK) 
                                            		WHERE curRef.fcustomerid = A.fcustomerid AND curRef.fstatus='E' AND curRef.fdate >= @fdatefrom AND curRef.fdate <= @fdateto  and curRef.fcancelstatus=0
                                                    AND curRef.fsourceformid IN ('ydj_saleintention','ydj_order','ydj_customer','ydj_collectreceipt','stk_sostockreturn') AND curRef.fbizdirection = 'bizdirection_02' --主控菜单【收款退款单】预置条件
	                                                AND NOT EXISTS (
	                                                	SELECT 1 FROM T_COO_INCOMEDISBURSE AS ICDB WITH(NOLOCK) 
	                                                	WHERE ICDB.fid = curRef.fid AND ICDB.fway = @fway_ref AND ICDB.fdirection = @fdirection_ref
	                                                )
                                            		GROUP BY curRef.fcustomerid
                                            	),0) AS fcurrentrefund 
                                            	FROM {0} AS A WITH(NOLOCK) 
                                            ) AS t1 ON t0.fid=t1.fid ".Fmt(this.DataSourceTableName, string.Join("','", recExcludeWayIds)));
            dbServiceExt.Execute(this.Context, sbUpdateSql.ToString(), sqlUpdateParams);

            //更新出库数据
            sbUpdateSql.Clear();
            sbUpdateSql.Append(@"/*dialect*/UPDATE T SET T.finitialstockout = ISNULL(t0.finitialstockout,0),T.fcurrentstockout = ISNULL(t1.fcurrentstockout,0) 
                                            FROM {0} AS T 
                                            LEFT JOIN (
                                            	SELECT C.fid,C.fcustomerid,SUM(B.famount) finitialstockout   
                                            	FROM T_STK_SOSTOCKOUT AS A WITH(NOLOCK) 
                                            	INNER JOIN T_STK_SOSTOCKOUTENTRY AS B WITH(NOLOCK) ON A.fid = B.fid 
                                            	INNER JOIN {0} AS C ON A.fcustomerid = C.fcustomerid
                                            	WHERE A.fstatus = 'E' AND A.fdate < @fdatefrom  and A.fcancelstatus=0
                                            	GROUP BY C.fid,C.fcustomerid 
                                            ) AS t0 ON T.fid = t0.fid 
                                            LEFT JOIN (
                                            	SELECT C.fid,C.fcustomerid,SUM(B.famount) fcurrentstockout   
                                            	FROM T_STK_SOSTOCKOUT AS A WITH(NOLOCK) 
                                            	INNER JOIN T_STK_SOSTOCKOUTENTRY AS B WITH(NOLOCK) ON A.fid = B.fid 
                                            	INNER JOIN {0} AS C ON A.fcustomerid = C.fcustomerid
                                            	WHERE A.fstatus = 'E' AND A.fdate >= @fdatefrom AND A.fdate <= @fdateto  and A.fcancelstatus=0
                                            	GROUP BY C.fid,C.fcustomerid 
                                            ) AS t1 ON T.fid = t1.fid ".Fmt(this.DataSourceTableName));
            dbServiceExt.Execute(this.Context, sbUpdateSql.ToString(), sqlUpdateParams);

            //更新退货数据
            sbUpdateSql.Clear();
            sbUpdateSql.Append(@"/*dialect*/UPDATE T SET T.finitialreturn = ISNULL(t0.finitialreturn,0),T.fcurrentreturn = ISNULL(t1.fcurrentreturn,0) 
                                            FROM {0} AS T 
                                            LEFT JOIN (
                                            	SELECT C.fid,C.fcustomerid,SUM(B.fbizqty * B.fprice) finitialreturn   
                                            	FROM T_STK_SOSTOCKRETURN AS A WITH(NOLOCK) 
                                            	INNER JOIN T_STK_SOSTOCKRETURNENTRY AS B WITH(NOLOCK) ON A.fid = B.fid 
                                            	INNER JOIN {0} AS C ON A.fcustomerid = C.fcustomerid
                                            	WHERE A.fstatus = 'E' AND A.fdate < @fdatefrom and A.fcancelstatus=0
                                            	GROUP BY C.fid,C.fcustomerid 
                                            ) AS t0 ON T.fid = t0.fid 
                                            LEFT JOIN (
                                            select T.fid,T.fcustomerid,SUM(fcurrentreturn) fcurrentreturn from (
                                            	SELECT C.fid,C.fcustomerid,SUM(A.factualreturnamount) fcurrentreturn   
                                            	FROM T_STK_SOSTOCKRETURN AS A WITH(NOLOCK) 
                                            	INNER JOIN {0} AS C ON A.fcustomerid = C.fcustomerid
                                            	WHERE A.fstatus = 'E' AND A.fdate >= @fdatefrom AND A.fdate <= @fdateto 
												and A.freturntype='sostockreturn_biztype_02' and A.fcancelstatus=0
                                            	GROUP BY C.fid,C.fcustomerid
                                                union all
                                            	SELECT C.fid,C.fcustomerid,SUM(B.fbizqty * B.fprice) fcurrentreturn   
                                            	FROM T_STK_SOSTOCKRETURN AS A WITH(NOLOCK) 
                                            	INNER JOIN T_STK_SOSTOCKRETURNENTRY AS B WITH(NOLOCK) ON A.fid = B.fid 
                                            	INNER JOIN {0} AS C ON A.fcustomerid = C.fcustomerid
                                            	WHERE A.fstatus = 'E' AND A.fdate >= @fdatefrom AND A.fdate <= @fdateto 
												and A.freturntype='sostockreturn_biztype_01' and A.fcancelstatus=0
                                            	GROUP BY C.fid,C.fcustomerid
                                                ) t
                                                GROUP BY fid,fcustomerid
                                            ) AS t1 ON T.fid = t1.fid ".Fmt(this.DataSourceTableName));
            dbServiceExt.Execute(this.Context, sbUpdateSql.ToString(), sqlUpdateParams);

            //更新其他应收单数据
            sbUpdateSql.Clear();
            sbUpdateSql.Append(@"/*dialect*/UPDATE T SET T.finitialotherrec = ISNULL(t0.finitialotherrec,0),T.fotherreceipt = ISNULL(t1.fotherreceipt,0) 
                                            FROM {0} AS T 
                                            LEFT JOIN (
                                            	SELECT C.fid,C.fcustomerid,SUM(A.fsumtaxamount) finitialotherrec   
                                            	FROM T_YDJ_COLLECTRECEIPT AS A WITH(NOLOCK) 
                                            	INNER JOIN {0} AS C ON A.frelatecusid = C.fcustomerid
                                            	WHERE A.fstatus = 'E' AND A.frelatetype='ydj_customer' AND A.fregistdate < @fdatefrom  and A.fcancelstatus=0
                                            	GROUP BY C.fid,C.fcustomerid 
                                            ) AS t0 ON T.fid = t0.fid 
                                            LEFT JOIN (
                                            	SELECT C.fid,C.fcustomerid,SUM(A.fsumtaxamount) fotherreceipt   
                                            	FROM T_YDJ_COLLECTRECEIPT AS A WITH(NOLOCK) 
                                            	INNER JOIN {0} AS C ON A.frelatecusid = C.fcustomerid
                                            	WHERE A.fstatus = 'E' AND A.frelatetype='ydj_customer' AND A.fregistdate >= @fdatefrom AND A.fregistdate <= @fdateto  and A.fcancelstatus=0
                                            	GROUP BY C.fid,C.fcustomerid 
                                            ) AS t1 ON T.fid = t1.fid ".Fmt(this.DataSourceTableName));
            dbServiceExt.Execute(this.Context, sbUpdateSql.ToString(), sqlUpdateParams);

            //更新期初余额
            sbUpdateSql.Clear();
            sbUpdateSql.Append(@"UPDATE {0} SET finitialbalance = finitialreceipt - finitialrefund - finitialstockout + finitialreturn - finitialotherrec WHERE 1 = 1".Fmt(this.DataSourceTableName));
            dbServiceExt.Execute(this.Context, sbUpdateSql.ToString());

            //更新本期余额
            sbUpdateSql.Clear();
            sbUpdateSql.Append(@"UPDATE {0} SET fcurrentbalance = finitialbalance + fcurrentreceipt - fcurrentrefund - fcurrentstockout + fcurrentreturn - fotherreceipt WHERE 1 = 1".Fmt(this.DataSourceTableName));
            dbServiceExt.Execute(this.Context, sbUpdateSql.ToString());
        }
    }
}
