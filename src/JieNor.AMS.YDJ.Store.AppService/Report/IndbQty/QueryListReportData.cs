using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.AMS.YDJ.Store.AppService.Report.IndbQty
{
    /// <summary>
    /// 分布式调拨在途明细
    /// </summary>
    [InjectService]
    [FormId("rpt_indbqty")]
    [OperationNo("QueryListReportData")]
	public class QueryListReportData : AbstractReportServicePlugIn
	{
		/// <summary>
		/// 数据库服务
		/// </summary>
		[InjectProperty]
		protected IDBServiceEx DBServiceEx { get; set; }


		/// <summary>
		/// 执行报表逻辑
		/// </summary>
		protected override void OnExecuteLogic()
		{

            var stockId = this.ParentPageSession.stockId as string;
            if (stockId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"参数 stockId 为空，请检查！");
            }
            var fmaterialid = this.ParentPageSession.fmaterialid as string;
            if (fmaterialid.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"参数 fmaterialid 为空，请检查！");
            }
            var fattrinfo = this.ParentPageSession.fattrinfo as string??"";
            var fattrinfo_e = this.ParentPageSession.fattrinfo_e as string ?? "";
            var fcustomdesc = this.ParentPageSession.fcustomdesc as string??"";

            var fstorehouseid = this.ParentPageSession.fstorehouseid as string??"";
            var fstorelocationid = this.ParentPageSession.fstorelocationid as string??"";


            //查询数据后往报表对应的临时表中插入数据

            //查找物料，定制说明，辅助属性，调入仓库，调入仓位,非审核，非作废，已分布式的库存调拨单
            var insertSql = $@"/*dialect*/
            insert into {this.DataSourceTableName}
            (
                fid,fbillno,fbizobject,fqty,fcreatorid,fcreatedate,fstatus
            )
           select row_number() over(order by fbillno) fid, fbillno,FFormId as fbizobject ,fqty,fcreatorid,fcreatedate,fstatus from  (
           select t.fbillno,t.FFormId,t.fcreatorid,t.fcreatedate,t.fstatus,te.fstorehouseidto,te.fstorelocationidto,sum(te.fqty) fqty
           from t_stk_invtransfer t with(nolock) 
           inner join t_stk_invtransferentry te with(nolock) on t.fid=te.fid
           where t.fmainorgid='{this.Context.Company}' and t.fstatus != 'E'  and t.fcancelstatus='0' and fisstockout='1'
           and te.fmaterialid='{fmaterialid}'and te.fattrinfoto='{fattrinfo}'and te.fcallupcustomdescto='{fcustomdesc}'
           and te.fstorehouseidto='{fstorehouseid}'
           and te.fstorelocationidto='{fstorelocationid}'
           group by fbillno,FFormId,fcreatorid,fcreatedate,fstatus,te.fstorehouseidto,te.fstorelocationidto
           )t
            
            ";

            this.DBServiceEx.Execute(this.Context, insertSql);

        }

        protected override void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
		{
			base.OnPrepareReportQueryParameter(listQueryPara);

			listQueryPara.OrderByString = "fjnidentityid asc";
		}
	}
}
