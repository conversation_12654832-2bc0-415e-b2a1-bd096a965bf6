//using JieNor.AMS.YDJ.Core.Interface;
//using JieNor.Framework;
//using JieNor.Framework.CustomException;
//using JieNor.Framework.DataTransferObject.Poco;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.MetaCore.Validator;
//using JieNor.Framework.SuperOrm.DataEntity;
//using System;
//using System.Collections.Generic;
//using System.Linq;

//namespace JieNor.AMS.YDJ.Store.AppService.Report.PurchasePlan
//{
//    /// <summary>
//    /// 采购计划表：立即采购
//    /// </summary>
//    [InjectService]
//    [FormId("rpt_purchaseplan")]
//    [OperationNo("buynow")]
//    public class BuyNow : AbstractOperationServicePlugIn
//    {
//        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
//        {
//            base.PrepareValidationRules(e);
//        }

//        public override void EndOperationTransaction(EndOperationTransactionArgs e)
//        {
//            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;
//            var allCooDatas = this.OperationContext.UserContext.LoadBizDataById("coo_incomedisburse", e.DataEntitys.Select(x => Convert.ToString(x["fcooinid"])));
//            var datas = e.DataEntitys.GroupBy(x => x["fagentid"]).Select(x => new { orgid = Convert.ToString(x.Key), ids = x.ToList().Select(y => Convert.ToString(y["fcooinid"])) });
//            foreach (var item in datas)
//            {
//                var cooids = item.ids;
//                if (cooids == null || !cooids.Any()) continue;
//                var orgContext = this.Context.CreateAgentDBContext(item.orgid);
//                var invokeSubmit = this.Gateway.InvokeBillOperation(orgContext,
//                                "coo_incomedisburse",
//                                allCooDatas.Where(x => Convert.ToString(x["fmainorgid"]).EqualsIgnoreCase(item.orgid)),
//                                "audit",
//                                new Dictionary<string, object>());
//            }
//            this.AddRefreshPageAction();
//        }
//    }
//}
