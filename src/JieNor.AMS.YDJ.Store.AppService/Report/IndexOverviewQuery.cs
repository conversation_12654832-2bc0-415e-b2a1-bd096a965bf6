using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Report
{
    /// <summary>
    /// （首页）仪表盘：目标
    /// </summary>
    [InjectService]
    [FormId("ydj_dashboard")]
    [OperationNo("goalinfo")]
    public class GetGoalInfo : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            #region 查询条件

            List<Dictionary<string, object>> goalDataList = new List<Dictionary<string, object>>();
            string evalType = this.GetQueryOrSimpleParam<string>("evalType", "fal_001");// 考核维度：fal_001:开单额,fal_002:意向额,fal_003:商机数
            string dtType = this.GetQueryOrSimpleParam<string>("dtType", "本月"); // 本月，本季度，本年
            string[] dtTimes = ObjectUtils.GetDatetimesByName(dtType);// 统计销售额时间范围
            string evalTypeSql = string.Empty;
            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            var rptFormId = this.GetQueryOrSimpleParam<string>("rptFormId");
            var dataPermId = this.GetQueryOrSimpleParam<string>("dataPermId");
            var deptId = string.Empty;
            var staffId = string.Empty;
            var subDeptIds = new List<string>();

            if (string.IsNullOrWhiteSpace(rptFormId))
            {
                rptFormId = this.OperationContext.HtmlForm.Id;
            }

            //// 默认值使用第一个权限
            //if (dataPermId.IsNullOrEmptyOrWhiteSpace())
            //{
            //    var permissionService = this.Container.GetService<IPermissionService>();

            //    foreach (var item in this.HtmlForm.FormPermItems)
            //    {
            //        if (permissionService.HasPermission(this.Context, new PermAuth(this.Context) { FormId = rptFormId, PermId = item.Id }))
            //        {
            //            dataPermId = item.Id;
            //            break;
            //        }
            //    }
            //}

            baseFormProvider.CheckMyDataPerms(Context, rptFormId, dataPermId, subDeptIds, out staffId, out deptId);

            if (subDeptIds != null && subDeptIds.Count > 0)
            {
                deptId = subDeptIds.Count == 1 ? subDeptIds[0] : string.Join(",", subDeptIds.Select(x => $"'{x}'"));
            }

            switch (evalType)
            {
                case "fal_001":
                    // 开单额增加条件：需审核通过
                    evalTypeSql = $@"
select sum(ou.famount) as 'finished' 
from t_ydj_order od with(nolock) 
left join t_ydj_orderduty ou with(nolock) on od.fid=ou.fid 
where od.fmainorgid='{this.Context.Company}' and od.fcancelstatus='0' and od.forderdate>={{ts '{dtTimes[0]}'}} and od.forderdate<{{ts '{dtTimes[1]}'}} and od.fstatus='E'
";
                    break;
                case "fal_002":
                    evalTypeSql = $@"
select sum(ou.famount) as 'finished' 
from t_ydj_saleintention od with(nolock) 
left join t_ydj_orderduty ou with(nolock) on od.fid=ou.fid 
where od.fmainorgid='{this.Context.Company}' and od.fcancelstatus='0' and od.fdate>={{ts '{dtTimes[0]}'}} and od.fdate<{{ts '{dtTimes[1]}'}}
";
                    break;
                case "fal_003":
                    evalTypeSql = $@"
select count(distinct od.fid) as 'finished' 
from t_ydj_customerrecord od with(nolock) 
left join t_ydj_customerrecordduty ou with(nolock) on od.fid=ou.fid 
where od.fmainorgid='{this.Context.Company}' and od.fcancelstatus='0'  and (fchancestatus='chance_status_02' or fchancestatus='chance_status_03') and od.fgoshopdate>={{ts '{dtTimes[0]}'}} 
and od.fgoshopdate<{{ts '{dtTimes[1]}'}}
";
                    break;
                default:
                    throw new BusinessException($"系统不存在{evalType}的考核维度");
            }

            var goalWhere = string.Empty;
            var evalTypeWhere = string.Empty;
            var assObject = "fao_002";

            if (false == string.IsNullOrWhiteSpace(staffId))
            {
                goalWhere = $" and g.fastaffid ='{staffId}'";
                evalTypeWhere = $" and ou.fdutyid ='{staffId}' ";
                assObject = "fao_001";
            }

            if (false == string.IsNullOrWhiteSpace(deptId))
            {
                var isMulti = deptId.IndexOf(',') > -1;
                goalWhere = isMulti ? $" and g.fdeptid in ({deptId}) " : $" and g.fdeptid ='{deptId}'";
                evalTypeWhere = isMulti ? $" and od.fdeptid in({deptId}) " : $" and od.fdeptid ='{deptId}' ";
                assObject = "fao_002";
            }

            string sqlSelect = $@"
select isnull(sum(a.goal),0) as '目标',isnull(sum(b.finished),0) as '实际完成',0 as '回款' from 
(select sum(g.{GetTargetField(dtType)}) as 'goal' 
from t_ste_goal g with(nolock) 
where g.fmainorgid='{this.Context.Company}' and fcancelstatus='0' and g.fassobject='{assObject}' and fasslatitude='{evalType}' and g.fyear='fy_{DateTime.Now.Year}' {goalWhere}) a,
({evalTypeSql} {evalTypeWhere}) b ";

            #endregion

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlSelect))
            {
                if (reader.Read())
                {
                    Dictionary<string, object> goalData = new Dictionary<string, object>();
                    for (int iCol = 0; iCol < reader.FieldCount; iCol++)
                    {
                        var objValue = reader[iCol];
                        var colName = reader.GetName(iCol);
                        goalData[colName] = objValue.IsNullOrEmptyOrWhiteSpace() ? "" : objValue;
                    }
                    goalDataList.Add(goalData);
                }
            }
            this.Result.SrvData = goalDataList;
        }

        private string GetTargetField(string dtType)
        {
            string fieldName = string.Empty;
            DateTime dtNow = DateTime.Now;
            string[] monthGoalNames = new string[] { "frelateman", "faqjm", "faqfm", "faqmm", "ftqapr", "ftqmay", "ftqjune", "fthjuly", "fthaug", "fthsep", "ffoct", "ffnov", "ffdec" };
            string[] seasonGoalNames = new string[] { "faquarter", "ftquarter", "fthrqarter", "ffqarter" };
            switch (dtType)
            {
                case "本月":
                    fieldName = monthGoalNames[dtNow.Month];
                    break;
                case "本季度":
                    fieldName = seasonGoalNames[(int)((dtNow.Month - 1) / 3)];
                    break;
                case "本年":
                    fieldName = monthGoalNames[0];
                    break;
                default:
                    fieldName = "frelateman";
                    break;
            }
            return fieldName;
        }
    }

    /// <summary>
    /// （首页）仪表盘：任务列表
    /// </summary>
    [InjectService]
    [FormId("ydj_dashboard")]
    [OperationNo("taskinfo")]
    public class GetTaskInfo : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作执行结束处理单元
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            #region 查询条件
            string curUserId = this.Context.UserId;
            /*
             1.我的任务
             任务状态=执行中，且任务执行人=当前登录用户
             【显示字段】：任务名称，执行人，截止日期，创建人
             2.我分配的
             任务状态=执行中，且任务创建人=当前登录用户
             【显示字段】：任务名称，执行人，任务状态
             3.我参与的
             任务状态=执行中，且任务的参与人包含当前登录用户
             【显示字段】：任务名称，执行人，截止日期，创建人
             */
            List<Dictionary<string, object>> taskDataList = new List<Dictionary<string, object>>();
            string sqlSelect = string.Format(@" 
select * from 
(
    select tk.fid,tk.FFormId, tk.ftaskname  as '任务名称',tk.fcreatorid,u.fname as '创建人' ,tk.fexcuter,su.fname as '执行人',ee.fenumitem,tk.fenddate as '截止日期',tk.fcreatedate,
	(case when tk.fcreatorid='{0}' then '我分配的' when tk.fexcuter='{0}' then '我的任务' when charindex('{0}', tk.fjoiner)>0 then '我参与的' else '未知' end) as '任务类别'
	from t_bf_task tk with(nolock) left join t_sec_user u with(nolock) on tk.fcreatorid=u.fid
	left join t_sec_user su with(nolock) on tk.fexcuter=su.fid 
    left join T_BD_ENUMDATAENTRY ee with(nolock) on tk.ftaskstatus=fentryid
	where tk.fcreatorid='{0}' and tk.fmainorgid='{1}' and tk.ftaskstatus in 
    (
        select top 1 fentryid from T_BD_ENUMDATAENTRY with(nolock) 
        where fid in(select fid from T_BD_ENUMDATA with(nolock) where fname='任务状态') and CHARINDEX('执行中',fenumitem)>0
    )

    union all

    select tk.fid,tk.FFormId, tk.ftaskname  as '任务名称',tk.fcreatorid,u.fname as '创建人' ,tk.fexcuter,su.fname as '执行人',ee.fenumitem,tk.fenddate as '截止日期',tk.fcreatedate,
	(case when tk.fexcuter='{0}' then '我的任务' when tk.fcreatorid='{0}' then '我分配的' when charindex('{0}', tk.fjoiner)>0 then '我参与的' else '未知' end) as '任务类别'
	from t_bf_task tk with(nolock) 
    left join t_sec_user u with(nolock) on tk.fcreatorid=u.fid
	left join t_sec_user su with(nolock) on tk.fexcuter=su.fid 
    left join T_BD_ENUMDATAENTRY ee with(nolock) on tk.ftaskstatus=fentryid
	where tk.fexcuter='{0}' and tk.fmainorgid='{1}' and tk.ftaskstatus in
    (
        select top 1 fentryid 
        from T_BD_ENUMDATAENTRY with(nolock) 
        where fid in(select fid from T_BD_ENUMDATA with(nolock) where fname='任务状态') and CHARINDEX('执行中',fenumitem)>0
    )

	union all

    select tk.fid,tk.FFormId, tk.ftaskname  as '任务名称',tk.fcreatorid,u.fname as '创建人' ,tk.fexcuter,su.fname as '执行人',ee.fenumitem,tk.fenddate as '截止日期',tk.fcreatedate,
	(case when charindex('{0}', tk.fjoiner)>0 then '我参与的' when tk.fcreatorid='{0}' then '我分配的' when tk.fexcuter='{0}' then '我的任务' else '未知' end) as '任务类别'
	from t_bf_task tk with(nolock) 
    left join t_sec_user u with(nolock) on tk.fcreatorid=u.fid
	left join t_sec_user su with(nolock) on tk.fexcuter=su.fid 
    left join T_BD_ENUMDATAENTRY ee with(nolock) on tk.ftaskstatus=fentryid
	where tk.fmainorgid='{1}' and charindex('{0}', fjoiner)>0 and tk.ftaskstatus in
    (
        select top 1 fentryid 
        from T_BD_ENUMDATAENTRY with(nolock) 
        where fid in(select fid from T_BD_ENUMDATA with(nolock) where fname='任务状态') and CHARINDEX('执行中',fenumitem)>0
    )
) x 
order by x.fcreatedate desc ", curUserId, this.Context.Company);
            #endregion
            var dbSvc = this.Context.Container.GetService<IDBService>();
            using (var reader = dbSvc.ExecuteReader(this.Context, sqlSelect))
            {
                while (reader.Read())
                {
                    Dictionary<string, object> taskData = new Dictionary<string, object>();
                    for (int iCol = 0; iCol < reader.FieldCount; iCol++)
                    {
                        var objValue = reader[iCol];
                        var colName = reader.GetName(iCol);
                        taskData[colName] = objValue.IsNullOrEmptyOrWhiteSpace() ? "" : objValue;
                    }
                    taskDataList.Add(taskData);
                }
            }
            this.Result.SrvData = taskDataList;
        }
    }


    /// <summary>
    /// （首页）仪表盘：销售业绩
    /// </summary>
    [InjectService]
    [FormId("ydj_dashboard")]
    [OperationNo("sysoverview")]
    public class GetOverviewInfo : AbstractOperationServicePlugIn
    {
        public string MyDeptId { get; set; }

        public string MyStaffId { get; set; }
        /// <summary>
        /// 操作执行结束处理单元
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            //string curUserId = this.Context.UserId;
            string dtType = this.GetQueryOrSimpleParam<string>("dtType");
            if (string.IsNullOrEmpty(dtType)) dtType = "本周";

            // 支持多时间维度
            var dtTypes = dtType.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
            if (dtTypes.Length == 0)
            {
                return;
            }

            // 兼容麦浩后台首页
            if (dtTypes.Length == 1)
            {
                this.Result.SrvData = GetStatDataByDTType(dtTypes.First());
            }
            else
            {
                var results = new Dictionary<string, object>();

                foreach (var item in dtTypes)
                {
                    results.Add(item, GetStatDataByDTType(item));
                }

                this.Result.SrvData = results;
            }
        }

        private List<object> GetStatDataByDTType(string dtType)
        {

            // 当前员工和部门
            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            MyDeptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
            MyStaffId = baseFormProvider.GetMyStaff(this.Context)?.Id;

            string[] dtTimes = ObjectUtils.GetDatetimesByName(dtType);
            Dictionary<string, string> saleRptFields = new Dictionary<string, string>();
            //获取销售参数 <销售额统计排除零收款合同>配置项
            var sysProfile = this.Container.GetService<ISystemProfile>();
            var excludezeroorder = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fexcludezeroorder", false);

            // 线索 商机 客户 量尺 意向 订金 合同 货款
            saleRptFields.Add("newleads", "线索数");
            saleRptFields.Add("newcusr", "商机数");
            saleRptFields.Add("newcust", "客户数");
            saleRptFields.Add("newsrecord", "量尺数");
            saleRptFields.Add("newintention", "意向数");
            saleRptFields.Add("cashamount", "订金额");
            saleRptFields.Add("odamount", "合同");
            saleRptFields.Add("recvamount", "货款额");
            saleRptFields.Add("intamount", "意向额");
            saleRptFields.Add("amountreceived", "货款");

            var rptFormId = this.GetQueryOrSimpleParam<string>("rptFormId");
            var dataPermId = this.GetQueryOrSimpleParam<string>("dataPermId");
            var deptId = string.Empty;
            var staffId = string.Empty;
            var subDeptIds = new List<string>();
            var customerRecordJoin = string.Empty;
            var customerRecordWhere = dataPermId.EqualsIgnoreCase("mycompany") ? string.Empty : " and 1=0";
            var customerWhere = "";
            var orderAmount = "t.fdealamount";
            var orderJoin = string.Empty;
            var orderWhere = customerRecordWhere;
            var saleAmount = "t.ffbillamount";
            var saleIntentionJoin = string.Empty;
            var saleIntentionWhere = customerRecordWhere;
            var scaleRecordWhere = customerRecordWhere;
            var leadsWhere = customerRecordWhere;
            //收款额查询条件
            var coowhere = " and t.fbizstatus='bizstatus_02' ";
            // var baseFormProvider = this.Container.GetService<IBaseFormProvider>();

            if (string.IsNullOrWhiteSpace(rptFormId))
            {
                rptFormId = this.OperationContext.HtmlForm.Id;
            }

            //// 默认值使用第一个权限
            //if (dataPermId.IsNullOrEmptyOrWhiteSpace())
            //{
            //    var permissionService = this.Container.GetService<IPermissionService>();

            //    foreach (var item in this.HtmlForm.FormPermItems)
            //    {
            //        if (permissionService.HasPermission(this.Context, new PermAuth(this.Context) { FormId = rptFormId, PermId = item.Id }))
            //        {
            //            dataPermId = item.Id;
            //            break;
            //        }
            //    }
            //}

            //baseFormProvider.CheckMyDataPerms(this.Context, rptFormId, dataPermId, subDeptIds, out staffId, out deptId);
            CheckMyDataPerms2(this.Context, dataPermId, out subDeptIds, out staffId, out deptId);

            if (subDeptIds.Count > 0)
            {
                deptId = subDeptIds.Count == 1 ? subDeptIds.First() : string.Join(",", subDeptIds.Select(x => $"'{x}'"));
            }


            if (false == string.IsNullOrWhiteSpace(deptId))
            {
                //orderAmount = "t.fdealamount";
                //saleAmount = "t.ffbillamount";
                if (deptId.Contains(","))
                {
                    customerRecordWhere = $" and fdeptid in ({deptId}) ";
                    // 客户取数逻辑改成多负责人（2021-08-31）
                    //customerWhere = $" and fdeptid in ({deptId}) ";
                    customerWhere =
                        $" and fid in (select fid from t_ydj_customerdutyentry cd with(nolock) where cd.fdeptid in ({deptId}) ) ";
                    orderWhere = $" and e.fdeptid in ({deptId}) ";
                    saleIntentionWhere = $" and e.fdeptid in ({deptId}) ";
                    scaleRecordWhere = $" and fdeptid in ({deptId}) ";
                    leadsWhere = $" and fdeptid in ({deptId}) ";
                    coowhere += $" and t.fdeptid in ({deptId}) ";
                }
                else
                {
                    customerRecordWhere = $" and fdeptid='{deptId}' ";
                    // 客户取数逻辑改成多负责人（2021-08-31）
                    //customerWhere = $" and fdeptid='{deptId}'";
                    customerWhere =
                        $" and fid in (select fid from t_ydj_customerdutyentry cd with(nolock) where cd.fdeptid='{deptId}' ) ";
                    orderWhere = $" and e.fdeptid='{deptId}'";
                    saleIntentionWhere = $" and e.fdeptid='{deptId}'";
                    scaleRecordWhere = $" and fdeptid='{deptId}'";
                    leadsWhere = $" and fdeptid='{deptId}'";
                    coowhere += $" and fdeptid='{deptId}'";
                }
            }


            if (false == string.IsNullOrWhiteSpace(staffId))
            {
                customerRecordWhere = $" and fdutyid='{staffId}'";
                //customerRecordJoin = $"inner join t_ydj_customerrecordduty e on t.fid=e.fid and e.fdutyid='{staffId}'";
                // 客户取数逻辑改成多负责人（2021-08-31）
                //customerWhere = $" and fdutyid='{staffId}'";
                customerWhere =
                    $" and fid in (select fid from t_ydj_customerdutyentry cd with(nolock) where cd.fdutyid='{staffId}') ";
                orderJoin = $"inner join t_ydj_orderduty e with(nolock) on t.fid=e.fid and e.fdutyid='{staffId}'";
                //orderAmount = "t.fdealamount*e.fratio/100";
                orderWhere = string.Empty;
                //saleAmount = "e.famount*e.fratio/100";
                saleIntentionJoin = orderJoin;
                saleIntentionWhere = string.Empty;
                scaleRecordWhere = $" and fdesignerid='{staffId}'";
                leadsWhere = $" and fdutyid='{staffId}'";
                coowhere += $@" and t.fcreatorid in (select top 1 flinkuserid from T_BD_STAFF where fid='{staffId}')";
            }

            //if (false == string.IsNullOrWhiteSpace(deptId))
            //{
            //    orderAmount = "t.fdealamount*e.fratio/100";
            //    saleAmount = "e.famount*e.fratio/100";
            //    orderJoin = $"inner join t_ydj_orderduty e on t.fid=e.fid ";
            //    saleIntentionJoin = orderJoin;
            //}

            orderAmount = "t.fdealamount*e.fratio/100";
            saleAmount = "e.famount*e.fratio/100";
            if (string.IsNullOrWhiteSpace(orderJoin))
            {
                orderJoin = $"inner join t_ydj_orderduty e with(nolock) on t.fid=e.fid ";
            }
            saleIntentionJoin = orderJoin;

            // 客户取数逻辑改成取【客户性质】=成交，初次成交时间在时间范围内（2021-08-31）
            //customerWhere+=$@" and fid in (Select fcustomerid from T_YDJ_ORDER where fstatus='E' and fmainorgid='{this.Context.Company}'
            //       and fcreatedate>={{ts '{dtTimes[0]}'}} and fcreatedate<{{ts '{dtTimes[1]}'}})";//取成交客户数
            customerWhere += $" and fcusnature='cusnature_02' and ffirstordertime>={{ts '{dtTimes[0]}'}} and ffirstordertime<{{ts '{dtTimes[1]}'}} ";

            var orderWhere1 = "";
            var orderWhere2 = "";
            if (excludezeroorder)//如果勾选了 <仪表盘合同金额统计包含零收款金额>配置项
            {
                orderWhere1 = orderWhere + "  and t.fstatus='E' ";
            }
            else
            {
                orderWhere1 = orderWhere + "  and t.fstatus='E' and t.fsumreceivable!=0 ";//剔除掉销售合同的“已收款=0”的数据(不包含零收款金额)
            }

            orderWhere1 += orderWhere;
            // 开单额增加条件：需审核通过
            orderWhere2 = orderWhere + "  and t.fstatus='E' "+orderWhere;



            // orderWhere += "  and t.fstatus='E' ";

            var sqlSelect = $@"
select a.cr_count as newcusr,a.cr_formId as newcusr_formid, (case when a.cr_count=1 then a.cr_fid else '' end) as newcusr_pkid,
b.cm_count as newcust,b.cm_formId as newcust_formid,(case when b.cm_count=1 then b.cm_fid else '' end) as newcust_pkid,
isnull(c.order_amount,0) as odamount,c.order_formId as odamount_formid, (case when c.order_count=1 then c.order_fid else '' end) as odamount_pkid,
isnull(d1.recv_cash,0) as cashamount,d1.recv_formId as cashamount_formid, (case when d1.recv_count=1 then d1.recv_fid else '' end) as cashamount_pkid,
isnull(d2.recv_amount,0) as recvamount,d2.recv_formId as recvamount_formid, (case when d2.recv_count=1 then d2.recv_fid else '' end) as recvamount_pkid,
isnull(d2.recv_amount,0) as amountreceived,isnull(d3.amrec_formId,'COO_INCOMEDISBURSE') as amountreceived_formid,(case when d3.amrec_count>1 then d3.amrec_fid else '' end) as amountreceived_pkid,
e.cc_count as newsrecord,e.cc_formId as newsrecord_formid, (case when e.cc_count=1 then e.cc_fid else '' end) as newsrecord_pkid,
f.fc_count as newleads,f.fc_formId as newleads_formid,(case when f.fc_count=1 then f.fc_fid else '' end) as newleads_pkid,  
g.intention_count as newintention,g.intention_formId as newintention_formid,(case when g.intention_count=1 then g.intention_fid else '' end) as newintention_pkid,
isnull(g.intention_amount,0) as intamount,g.intention_formId as intamount_formid,(case when g.intention_count=1 then g.intention_fid else '' end) as intamount_pkid from 

(select count(1) as cr_count,max(t.fid) as cr_fid,max(t.FFormId) as cr_formId from t_ydj_customerrecord t with(nolock) 
{customerRecordJoin}
where t.fgoshopdate>={{ts '{dtTimes[0]}'}} and t.fgoshopdate<{{ts '{dtTimes[1]}'}} and t.fmainorgid='{this.Context.Company}' and t.fcancelstatus='0' {customerRecordWhere}) as a,

(select count(1) as cm_count,max(fid) as cm_fid,max(FFormId) as cm_formId  from t_ydj_customer with(nolock) 
where fmainorgid='{this.Context.Company}' and fforbidstatus='0' {customerWhere}) as b,

(select count(1) as order_count, sum({orderAmount}) as order_amount,max(t.fid) as order_fid,max(t.FFormId) as order_formId from t_ydj_order t with(nolock) 
{orderJoin} 
where t.forderdate>={{ts '{dtTimes[0]}'}} and t.forderdate<{{ts '{dtTimes[1]}'}} and t.fmainorgid='{this.Context.Company}' and t.fcancelstatus='0' {orderWhere1}) c,

(select sum(isnull(t.fcollectedamount,0)) as recv_cash, count(1) as recv_count,max(t.fid) as recv_fid,max(t.FFormId) as recv_formId from t_ydj_saleintention t with(nolock) 
{saleIntentionJoin}
where t.fdate>={{ts '{dtTimes[0]}'}} and t.fdate<{{ts '{dtTimes[1]}'}} and t.fmainorgid='{this.Context.Company}' and t.fcancelstatus='0' {saleIntentionWhere}) d1,

(select sum(isnull(t.freceivable,0)-isnull(t.fcollectedamount,0)) as recv_amount, count(1) as recv_count,max(t.fid) as recv_fid,max(t.FFormId) as recv_formId from t_ydj_order t with(nolock) 
{orderJoin}
where t.forderdate>={{ts '{dtTimes[0]}'}} and t.forderdate<{{ts '{dtTimes[1]}'}} and t.fmainorgid='{this.Context.Company}' and t.fcancelstatus='0' {orderWhere2}) d2,

(select SUM(case when t.fdirection='direction_02' then t.famount else 0 end ) famount_add,SUM(case when t.fdirection='direction_01' then t.famount else 0 end ) famount_reduce,max(t.fid) as amrec_fid,max(t.FFormId) as amrec_formId , count(1) as  amrec_count  from t_coo_incomedisburse t with(nolock)
where t.fconfirmdate>={{ts '{dtTimes[0]}'}} and t.fconfirmdate<{{ts '{dtTimes[1]}'}} and t.fmainorgid='{this.Context.Company}' and t.fcancelstatus='0' {coowhere})d3,

(select count(1) as cc_count,max(fid) as cc_fid,max(FFormId) as cc_formId from t_ydj_scalerecord  with(nolock) 
where fscaledate>={{ts '{dtTimes[0]}'}} and fscaledate<{{ts '{dtTimes[1]}'}} and fmainorgid='{this.Context.Company}' and fcancelstatus='0' {scaleRecordWhere}) e,

(select count(1) as fc_count,max(fid) as fc_fid,max(FFormId) as fc_formId from t_ydj_leads  with(nolock) 
where fcreatedate>={{ts '{dtTimes[0]}'}} and fcreatedate<{{ts '{dtTimes[1]}'}} and fmainorgid='{this.Context.Company}' and fcancelstatus='0' {leadsWhere}) f,

(select count(1) as intention_count,sum({saleAmount}) as intention_amount,max(t.fid) as intention_fid,max(t.FFormId) as intention_formId from t_ydj_saleintention t  with(nolock) 
{saleIntentionJoin}
where t.fdate>={{ts '{dtTimes[0]}'}} and t.fdate<{{ts '{dtTimes[1]}'}} and t.fmainorgid='{this.Context.Company}' and t.fcancelstatus='0' {saleIntentionWhere}) g 
";

            List<object> results = new List<object>();
            var isCreateStaff = true;
            if (MyStaffId.IsNullOrEmptyOrWhiteSpace())//如果请求的是本人且本用户没有关联员工，则全部数据返回0
            {
                isCreateStaff = false;
            }
            using (var reader = this.DBService.ExecuteReader(this.Context, sqlSelect))
            {
                if (reader.Read())
                {
                    foreach (var kv in saleRptFields)
                    {
                        results.Add(new
                        {
                            formId = reader[kv.Key + "_formid"].ToString(),
                            caption = kv.Value,
                            itemVal = isCreateStaff ? reader[kv.Key] : 0,
                            pkId = reader[kv.Key + "_pkid"],
                            id = kv.Key
                        });
                    }
                }
            }

            return results;
        }

        /// <summary>
        /// 校验权限范围（注意：忽略麦浩后台数据授权权限范围）
        /// </summary>
        /// <param name="context"></param>
        /// <param name="rptFormId"></param>
        /// <param name="dataPermId"></param>
        /// <param name="subDeptIds"></param>
        /// <param name="staffId"></param>
        /// <param name="deptId"></param>
        private void CheckMyDataPerms2(UserContext context, string dataPermId, out List<string> subDeptIds, out string staffId, out string deptId)
        {
            subDeptIds = new List<string>();

            switch (dataPermId)
            {
                case "myself"://本人
                    deptId = "";
                    staffId = MyStaffId;
                    break;
                case "mydepartment"://本部门
                    staffId = "";
                    deptId = MyDeptId;
                    break;
                case "mysubordinates"://本部门及下属部门
                    staffId = "";
                    deptId = MyDeptId;
                    subDeptIds = GetDeptIds(context, MyDeptId);
                    break;
                default:
                    staffId = "";
                    deptId = "";
                    break;
            }
        }

        /// <summary>
        /// 获取本部门及下属部门
        /// </summary>
        /// <param name="mydeptId"></param>
        /// <returns></returns>
        private List<string> GetDeptIds(UserContext context, string mydeptId)
        {
            List<string> deptlist = new List<string>();
            var sql = @"select fpath,fid from t_bd_department with(nolock) where fpath like @fpath and fmainorgid=@fmainorgid";
            List<SqlParam> pars = new List<SqlParam>();
            pars = new List<SqlParam>();
            pars.Add(new SqlParam("@fpath", System.Data.DbType.String, $@"%{mydeptId}%"));
            pars.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, context.Company));
            var res = DBService.ExecuteDynamicObject(context, sql, pars);
            if (res != null && res.Any())
            {
                foreach (var item in res)
                {
                    deptlist.Add(Convert.ToString(item["fid"]));
                }
            }
            return deptlist;
        }
    }
}
