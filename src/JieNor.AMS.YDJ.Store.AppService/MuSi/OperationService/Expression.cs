using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.MuSi.MetaCore;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.OperationService
{
    /// <summary>
    /// 慕思同步
    /// </summary>
    [InjectService("expression")]
    public class Expression : AbstractOperationService
    {
        /// <summary>
        /// 表单模型
        /// </summary>
        protected HtmlForm HtmlForm => this.OperationContext.HtmlForm;

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var expressionText = this.GetQueryOrSimpleParam("expressionText", "");
            var rawData = this.GetQueryOrSimpleParam("rawData", "");

            var evaluator = this.Container.GetService<IBizExpressionEvaluator>();
            var bizExpCtx = this.Container.GetService<IBizExpressionContext>();
            bizExpCtx.HtmlForm = this.HtmlForm;
            bizExpCtx.Context = this.UserCtx;

            JsonDynamicDataRow dcRow = new JsonDynamicDataRow();
            bizExpCtx.BindSetField(new TrySetValueHandler(dcRow.TrySetMember));
            bizExpCtx.BindGetField(new TryGetValueHandler(dcRow.TryGetMember));

            //var exprObj = this.Container.GetService<IExpressionFactory>();
            //if (exprObj != null)
            //{
            //    IBizExpressionContext exprCtx = this.Container.GetService<IBizExpressionContext>();
            //    exprCtx.Context = this.UserCtx;

            //    IBizExpression exprItem = this.Container.GetService<IBizExpression>();
            //    exprItem.ExpressionText = expressionText;


            //    this.OperationContext.Result.SrvData = exprObj.Eval(exprItem, exprCtx);
            //}

            var item = JObject.Parse(rawData);

            //IBizExpression exprItem = this.Container.GetService<IBizExpression>();
            //exprItem.ExpressionText = expressionText;

            dcRow.ActiveDataObject = item;
            //this.OperationContext.Result.SrvData = evaluator.Eval(exprItem, bizExpCtx);
            this.OperationContext.Result.SrvData = evaluator.CheckCondition(expressionText, bizExpCtx);
        }
    }
}
