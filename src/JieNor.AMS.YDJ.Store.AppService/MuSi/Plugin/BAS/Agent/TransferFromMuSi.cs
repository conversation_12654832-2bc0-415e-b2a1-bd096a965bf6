using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.DataTransferObject.Const;
using System.Threading.Tasks;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Agent
{
    /// <summary>
    /// 经销商：慕思同步
    /// </summary>
    [InjectService]
    [FormId("bas_agent")]
    [OperationNo("syncfrommusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataFromMuSiPlugIn
    {
        [InjectProperty]
        protected IAgentService AgentService { get; set; }

        public override void BeforeSave(BeforeSaveEventArgs e)
        {
            base.BeforeSave(e);

            var dataEntities = e.DataEntitys;
            if (dataEntities.IsNullOrEmpty()) return;
            dataEntities.ForEach(a => a["freminddelistling"] = 1);
            // 更新【实控人ID】
            this.AgentService.UpdateBoss(this.Context, dataEntities);
            // 更新【销售组织】
            this.AgentService.UpdateSaleOrg(this.Context, dataEntities);

            // 更新【招商经销商】
            this.AgentService.UpdateCrmDistributor(this.Context, e.DataEntitys);

            var lstValidDataEntities = new List<DynamicObject>();

            var phoneFld = this.HtmlForm.GetField("fcontacterphone");
            var actualownernumberFld = this.HtmlForm.GetField("actualownernumber");

            foreach (var dataEntity in dataEntities)
            {
                if (dataEntity["fcontacterphone"].IsNullOrEmptyOrWhiteSpace())
                {
                    string msg =
                        $"{this.HtmlForm.Caption}{dataEntity["fnumber"]}的【{phoneFld.Caption}】字段为空，无法保存，请检查是否未同步实控人或实控人的联系方式为空！";

                    this.WriteOpLog(msg);
                    e.Result.ComplexMessage.ErrorMessages.Add(msg);
                    continue;
                }

                if (dataEntity["actualownernumber"].IsNullOrEmptyOrWhiteSpace())
                {
                    string msg =
                        $"{this.HtmlForm.Caption}{dataEntity["fnumber"]}的【{actualownernumberFld.Caption}】字段为空，无法保存，请检查是否未同步实控人！";

                    this.WriteOpLog(msg);
                    e.Result.ComplexMessage.ErrorMessages.Add(msg);
                    continue;
                }

                lstValidDataEntities.Add(dataEntity);
            }

            e.DataEntitys = lstValidDataEntities;
        }

        public override void AfterSave(AfterSaveEventArgs e)
        {
            base.AfterSave(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            //查询已审核的经销商被下发时应该要更新其组织信息
            var dataEntities = e.DataEntitys.Where(s =>
            Convert.ToString(s["fstatus"]).EqualsIgnoreCase("E")
            && !Convert.ToBoolean(s["fforbidstatus"])
            && Convert.ToString(s["fagentstatus"]).EqualsIgnoreCase("1")).ToList();

            var srcPKId = from p in dataEntities
                          select p["Id"].ToString();
            //var exists = this.Context.LoadBizDataById("bas_organization", srcPKId);
            var exists = this.Context.LoadBizBillHeadDataById("bas_organization", srcPKId.ToList());

            foreach (var agentInfo in dataEntities)
            {
                var srcId = agentInfo["Id"].ToString();
                if (exists.Any(f => srcId.EqualsIgnoreCase(f["Id"]?.ToString())))
                {
                    //组织已存在时做更新操作。
                    AlterOrgAndCompany(srcId);
                    continue;
                }
            }

            var pubSubService = this.Container.GetService<IPubSubService>();
            PublishSubDBCenterMsgInfo msg = new PublishSubDBCenterMsgInfo();
            var svc = this.Container.GetService<IDataCenterService>();
            SQLDBServerInfo sqlInfo = svc.GetDataCenterServer();

            //拉取完经销商 更新 对应企业信息 
            Task task = new Task(() =>
            {
                foreach (var data in e.DataEntitys)
                {
                    //var orgItem = this.Context.LoadBizDataById("bas_organization", Convert.ToString(data["id"]));
                    //if (orgItem != null)
                    //{
                    //    orgItem["fnumber"] = data["fnumber"];
                    //    orgItem["fname"] = data["fname"];
                    //}
                    var comps = DataCenterExtentions.GetAllCompanys(this); //this.GetAllCompanys();
                    var compItem = new Framework.DataTransferObject.DB.CompanyDCInfo();
                    comps.TryGetValue(Convert.ToString(data["id"]), out compItem);
                    if (compItem != null)
                    {
                        compItem.CompanyNumber = Convert.ToString(data["fnumber"]);
                        compItem.CompanyName = Convert.ToString(data["fname"]);

                        msg = new PublishSubDBCenterMsgInfo() { companyInfo = compItem, dbServerInfo = sqlInfo, Add = false };
                        pubSubService.PublishMessage(PubSubChannel.DatacenterConfig, msg);

                        msg = new PublishSubDBCenterMsgInfo() { companyInfo = compItem, dbServerInfo = sqlInfo, Add = true };
                        pubSubService.PublishMessage(PubSubChannel.DatacenterConfig, msg);

                        AgentService.UpdateCompany(this.Context, new DynamicObject[] { data });
                    }
                }

                //var result1 = this.Gateway.InvokeBillOperation(this.Context, "bas_organization", new DynamicObject[] { orgItem }, "save",
                //    new Dictionary<string, object>());
            });

            ThreadWorker.QuequeTask(task, result => { });
        }

        public override void AfterExecute(AfterExecuteEventArgs e)
        {
            base.AfterExecute(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            IOperationResult result = null;

            // 查出非【已审核】且【非禁用】且【状态】=启用 的状态
            var dataEntities = e.DataEntitys.Where(s =>
                !Convert.ToString(s["fstatus"]).EqualsIgnoreCase("E")
                && !Convert.ToBoolean(s["fforbidstatus"])
                && Convert.ToString(s["fagentstatus"]).EqualsIgnoreCase("1")).ToList();
            if (!dataEntities.IsNullOrEmpty())
            {
                foreach (var dataEntity in dataEntities)
                {
                    // 改为【提交】状态，便于后续全部执行审核
                    //dataEntity["fstatus"] = "D";

                    result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { dataEntity }, "submit",
                        this.Option.ToDictionary());
                    this.Result.MergeResult(result);
                }
            }
            //迁移到定时任务处理
            //// 更新《主经销商配置表》的【企业微信主体经销商】 
            //this.AgentService.UpdateQYWXMainAgent(this.Context, e.DataEntitys);

            //// 更新《门店》（解决拉取顺序先后有问题时，互相反写）
            //var agentIds = e.DataEntitys.Select(s => Convert.ToString(s["id"])).ToList();
            //var stores = this.Context.LoadBizDataByNo("bas_store", "fagentid", agentIds);
            //var storeService = this.Container.GetService<IStoreService>();
            //storeService.UpdateAgentAndDeliver(this.Context, stores);

            //if (stores.Any())
            //{
            //    this.Context.SaveBizData("bas_store", stores);
            //}

            //this.AgentService.RewriteCrmDistributor(this.Context, e.DataEntitys);

            //this.AgentService.UpdateRelationName(this.Context, e.DataEntitys);
        }

        private void AlterOrgAndCompany(string agentId)
        {
            //新经销商
            var newagent = this.Context.LoadBizDataById("bas_agent", agentId);
            var newOrg = this.Context.LoadBizDataById("bas_organization", Convert.ToString(newagent["id"]));

            if (newOrg != null && newagent != null)
            {
                newOrg["fnumber"] = newagent["fnumber"];
                newOrg["fname"] = newagent["fname"];
                //组织的联系人从之前的取经销商负责人改为取经销商实控人。
                //newOrg["fcontacter"] = newagent["fcontacter"];
                newOrg["fcontacter"] = newagent["actualownername"];
                newOrg["fcontacterphone"] = newagent["fcontacterphone"];
                newOrg["fcorporatename"] = newagent["fcorporatename"];

                newOrg["fdetailaddress"] = newagent["faddress"];

                var tmpresult = this.Gateway.InvokeBillOperation(this.Context, "bas_organization", new DynamicObject[] { newOrg }, "save", new Dictionary<string, object>());
                this.Result.MergeResult(tmpresult);
            }
        }
    }
}
