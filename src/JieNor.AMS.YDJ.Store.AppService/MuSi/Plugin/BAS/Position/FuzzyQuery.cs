using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Position
{
    /// <summary>
    /// 岗位：动态列营销助手APP职位弹窗查询操作
    /// </summary>
    [InjectService]
    [FormId("ydj_position")]
    [OperationNo("FuzzyQuery")]
    public class FuzzyQuery : AbstractQueryDyn
    {
        //该插件功能逻辑在基类实现
    }
}
