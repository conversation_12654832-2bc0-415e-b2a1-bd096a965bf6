using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.MuSi.OperationService;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Price
{
    /// <summary>
    /// 销售价目：慕思通用保存同步操作
    /// </summary>
    [InjectService]
    [FormId("ydj_price|ydj_selfprice")]
    [OperationNo("MSSaveSync")]
    public class MSSaveSync : AbstractOperationServicePlugIn
    {
        protected HtmlField NumberField { get; set; }

        #region 同步日志参数

        /// <summary>
        /// 操作日志
        /// </summary>

        private DynamicObject OpLogObj = null;

        /// <summary>
        /// 操作日志表单模型
        /// </summary>
        private HtmlForm OpLogForm = null;

        /// <summary>
        /// 系统集成服务
        /// </summary> 
        protected ISystemIntegrationService SystemIntegrationService { get; set; }

        #endregion

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            this.NumberField = this.HtmlForm.GetNumberField();
            this.SystemIntegrationService = this.Container.GetService<ISystemIntegrationService>();
            this.OpLogForm = this.MetaModelService.LoadFormModel(this.Context, "si_operationlog");

            // 取消默认操作
            e.CancelOperation = true;

            var dataEntities = e.DataEntitys?.Where(s =>
                !Convert.ToString(s[this.NumberField.PropertyName]).IsNullOrEmptyOrWhiteSpace());

            if (dataEntities == null || dataEntities.Count() < 1)
            {
                var errorMsg = "空数据或者数据结构错误！";
                this.Result.SimpleMessage = errorMsg;
                this.Result.ComplexMessage.ErrorMessages.Add(errorMsg);
                HandleException(new List<DynamicObject>());
                return;
            }

            this.OpLogObj = this.SystemIntegrationService.CreateOperationLog(this.Context, this.OpLogForm, this.GetCurrentRequest().RawUrl, this.OperationName, this.HtmlForm.Id, "2", "从慕思系统同步到当前系统");

            //数据同步状态设置 
            this.WriteOpLog($"正在初始化慕思数据，请稍等...");

            // 保存初始数据
            var requestData = this.Context.CurrentRequestObject?.ToJson();
            this.WriteOpLog($"请求参数：{requestData}");

            try
            {
                DoExecute(dataEntities);
            }
            catch (Exception ex)
            {
                this.OpLogObj["ffailnumbers"] = string.Join(",", dataEntities.Select(s => s[NumberField.PropertyName]));
                this.OpLogObj["fdescription"] = $"{this.OpLogObj["fdescription"]}，错误原因：{ex.Message}";
                //数据同步状态设置
                this.OpLogObj["fopstatus"] = "3";    // 出错
                this.OpLogObj["ferrorsource"] = "1";  
                this.WriteOpLog(ex.Message + "\r\n" + ex.StackTrace, true);

                if (ex is InvokeOperationException)
                {
                    // 内嵌异常非业务异常时，当作未处理异常
                    if (ex.InnerException == null || ex.InnerException is BusinessException)
                    {
                        // 当作错误返回
                        this.Result.SimpleMessage = $"【{this.HtmlForm.Caption}】保存操作失败！";
                        this.Result.ComplexMessage.ErrorMessages.Add(ex.Message);
                        HandleException(dataEntities);
                        return;
                    }
                }

                if (ex is BusinessException)
                {
                    if (ex.InnerException == null || ex.InnerException is BusinessException)
                    {
                        // 当作错误返回
                        this.Result.SimpleMessage = $"【{this.HtmlForm.Caption}】保存操作失败！";
                        this.Result.ComplexMessage.ErrorMessages.Add(ex.Message);
                        HandleException(dataEntities);
                        return;
                    }
                }

                HandleException(dataEntities);

                this.Container.GetService<ILogServiceEx>().Error($"【{this.HtmlForm.Caption}】保存操作异常", ex);
                // 返回异常错误 
                this.Result.SimpleMessage = $"【{this.HtmlForm.Caption}】保存操作异常，请联系厂家！";
            }
            finally
            {
                //日志保存至数据库
                this.SaveOpLog();
            }
        }

        private void DoExecute(IEnumerable<DynamicObject> dataEntities)
        {
            var dic = GetNumberToIdDic(dataEntities);

            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, this.HtmlForm, dataEntities.ToArray(), this.Option);

            ProcNewDataEntities(dataEntities, dic);

            List<string> failedNumbers, succeedNumbers;
            ProcField(dataEntities, dic, out failedNumbers, out succeedNumbers);

            //IPriceService priceService = this.Container.GetService<IPriceService>();
            //priceService.InitEntryId(this.Context, dataEntities, this.HtmlForm.Id);

            Save(dataEntities);

            HandleResult(failedNumbers, succeedNumbers);

            ClearPriceCache(dataEntities);
        }

        private void ClearPriceCache(IEnumerable<DynamicObject> dataEntities)
        {
            Task.Run(() =>
            {
                var productIds = new List<string>();

                foreach (var dataEntity in dataEntities)
                {
                    productIds.AddRange(
                        ((DynamicObjectCollection)dataEntity["fentry"]).Select(entry =>
                           Convert.ToString(entry["fproductid"])));
                }

                productIds = productIds.Distinct().ToList();

                var priceService = this.Container.GetService<IPriceService>();
                priceService.ClearPriceCache(this.Context, productIds); //更新缓存方便取价服务 
            });
        }

        private void ProcField(IEnumerable<DynamicObject> dataEntities, Dictionary<string, string> dic, out List<string> failedNumbers, out List<string> succeedNumbers)
        {
            // 处理字段映射
            var productDic = GetProductNumberToProductDic(dataEntities);

            failedNumbers = new List<string>();
            succeedNumbers = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                string number = Convert.ToString(dataEntity[this.NumberField.PropertyName]);
                string id = dic[number];
                dataEntity["id"] = id;

                var entrys = dataEntity["fentry"] as DynamicObjectCollection;

                var vaildedEntrys = new List<DynamicObject>();

                foreach (var entry in entrys)
                {
                    var productNumber = Convert.ToString(entry["fproductid"]);
                    if (productNumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        string message = $"价目明细行【ftranid={entry["ftranid"]}】的商品为空！";
                        this.Result.ComplexMessage.ErrorMessages.Add(message);
                        this.WriteOpLog(message);
                        continue;
                    }
                    if (!productDic.ContainsKey(productNumber))
                    {
                        string message = $"价目明细行【ftranid={entry["ftranid"]}】的商品【{productNumber}】不存在！";
                        this.Result.ComplexMessage.ErrorMessages.Add(message);
                        this.WriteOpLog(message);
                        continue;
                    }

                    var items = productDic[productNumber];

                    entry["fproductid"] = items.Item1;
                    entry["funitid"] = items.Item2;     // 从商品带出销售单位
                    entry["fqty"] = 1;                  // 销售数量=1
                    entry["fconfirmstatus"] = 2;        // 自动确认

                    vaildedEntrys.Add(entry);
                }

                if (entrys.Count != vaildedEntrys.Count)
                {
                    failedNumbers.Add(number);
                }
                else
                {
                    succeedNumbers.Add(number);
                }

                entrys.Clear();
                foreach (var item in vaildedEntrys)
                {
                    entrys.Add(item);
                }
            }
        }

        private void ProcNewDataEntities(IEnumerable<DynamicObject> dataEntities, Dictionary<string, string> dic)
        {
            List<DynamicObject> newDataEntities = new List<DynamicObject>();
            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
            // 处理新的实体
            foreach (var dataEntity in dataEntities)
            {
                string number = Convert.ToString(dataEntity[this.NumberField.PropertyName]);
                // 判断销售价目是否存在
                if (!dic.ContainsKey(number))
                {
                    var newDataEntity = dt.CreateInstance() as DynamicObject;
                    newDataEntity["id"] = dataEntity["id"];
                    newDataEntity["fnumber"] = dataEntity["fnumber"];
                    newDataEntity["fname"] = dataEntity["fname"];

                    newDataEntities.Add(newDataEntity);
                }
            }

            // 处理新的实体
            if (newDataEntities.Any())
            {
                var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(this.Context, this.HtmlForm, newDataEntities.ToArray(), this.Option);

                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                dm.Save(newDataEntities);

                foreach (var dataEntity in newDataEntities)
                {
                    dic[Convert.ToString(dataEntity[this.NumberField.PropertyName])] = Convert.ToString(dataEntity["id"]);
                }
            }
        }

        private void Save(IEnumerable<DynamicObject> dataEntities)
        {
            List<string> sqls = new List<string>();
            IDBServiceEx dBServiceEx = this.Container.GetService<IDBServiceEx>();
            int batch = 200; // 批量处理数量 
            // 保存
            var productidLst = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                // 删除商品下现有的销售价目
                var productIds = entrys.Select(entry => entry["fproductid"]?.ToString()).Where(s => !s.IsNullOrEmptyOrWhiteSpace());
                productidLst.AddRange(productIds);
                if (!productIds.IsNullOrEmpty())
                {
                    string sql = $@"delete from t_ydj_priceentry where fid = '{dataEntity["id"]}' and fproductid in ('{string.Join("','", productIds)}');";

                    dBServiceEx.Execute(this.Context, sql);
                }

                // 插入新的销售价目
                foreach (var entry in entrys)
                {
                    string sql = $@"
    insert into t_ydj_priceentry(fentryid, fproductid, funitid, fsalprice, fqty, fstartdate, fexpiredate, fconfirmstatus, fid, ftranid,fsyncdate) values (
'{entry["id"]}', 
'{entry["fproductid"]}', 
'{entry["funitid"]}', 
{entry["fsalprice"]}, 
{entry["fqty"]}, 
'{Convert.ToDateTime(entry["fstartdate"]).ToString("yyyy-MM-dd HH:mm:ss.fff")}', 
'{Convert.ToDateTime(entry["fexpiredate"] == null ? "9999-09-09 00:00:00.000" : entry["fexpiredate"]).ToString("yyyy-MM-dd HH:mm:ss.fff")}', 
{entry["fconfirmstatus"]}, 
'{dataEntity["id"]}', 
'{entry["ftranid"]}',
'{DateTime.Now.ToString("yyyy-MM-dd HH:mm")}');
";

                    sqls.Add(sql);

                    if (sqls.Count == batch)
                    {
                        string batchSql = sqls.JoinEx(" ", false);
                        dBServiceEx.Execute(this.Context, batchSql);
                        sqls.Clear();
                    }
                }
            }

            if (sqls.Count > 0)
            {
                string batchSql = sqls.JoinEx(" ", false);
                dBServiceEx.Execute(this.Context, batchSql);
                sqls.Clear();
            }
            //因为价目表没有走通用保存插件 所以这里单独处理。
            if (productidLst.Count() > 0) 
            {
                if (!this.HtmlForm.Id.EqualsIgnoreCase("ydj_price")) return;

                var priceService = this.Container.GetService<IPriceService>();
                var PriceFactorObj = this.Context.LoadBizDataByFilter("ydj_factorprice", $"fmainorgid = '{this.Context.TopCompanyId}' and fcreatorid = 'sysadmin' ")?.FirstOrDefault();
                if (!PriceFactorObj.IsNullOrEmptyOrWhiteSpace())
                {
                    priceService.CreateOrAlterPriceFactor(this.Context, PriceFactorObj, productidLst.Distinct().ToList());
                    var geteway = this.Context.Container.GetService<IHttpServiceInvoker>();
                    geteway.InvokeBillOperation(this.Context, "ydj_factorprice", new[] { PriceFactorObj }, "draft", new Dictionary<string, object> { { "IgnoreCheckPermssion", "true" } });
                }
            }
        }

        private void HandleResult(List<string> failedNumbers, List<string> succeedNumbers)
        {
            MuSiData resp = new MuSiData();

            if (this.Result.ComplexMessage.HasMessage)
            {
                this.Result.IsSuccess = false;
                resp.ErrorMsgs = this.Result.ComplexMessage.ErrorMessages;
            }
            else
            {
                this.Result.IsSuccess = true;
            }

            resp.FailedNumbers.AddRange(failedNumbers);
            resp.SucceedNumbers.AddRange(succeedNumbers);

            // 全部成功
            if (resp.SucceedNumbers.Any() && !resp.FailedNumbers.Any()) resp.Flag = MuSiFlag.SUCCESS.ToString();
            // 全部失败
            if (!resp.SucceedNumbers.Any() && resp.FailedNumbers.Any()) resp.Flag = MuSiFlag.FAIL.ToString();
            // 部分成功
            if (resp.SucceedNumbers.Any() && resp.FailedNumbers.Any()) resp.Flag = MuSiFlag.PARTSUCCESS.ToString();

            this.Result.SrvData = resp;

            //数据同步状态设置
            string msg = string.Empty;
            switch (resp.Flag)
            {
                case "SUCCESS":
                    msg = "数据同步完成！";
                    this.OpLogObj["fopstatus"] = "2";
                    break;
                case "PARTSUCCESS":
                    msg = "数据同步部分完成！";
                    this.OpLogObj["fopstatus"] = "2";
                    break;
                case "FAIL":
                    msg = "数据同步失败！";
                    this.OpLogObj["fopstatus"] = "3";
                    break;
                case "ERROR":
                    msg = "数据同步出错！";
                    this.OpLogObj["fopstatus"] = "3";
                    break;
            }


            this.OpLogObj["fsuccessnumbers"] = string.Join(",", resp.SucceedNumbers);
            this.OpLogObj["ffailnumbers"] = string.Join(",", resp.FailedNumbers);
            if (Convert.ToString(this.OpLogObj["fopstatus"]).EqualsIgnoreCase("3"))
            {
                this.OpLogObj["ferrorsource"] = "1";
                this.OpLogObj["fdescription"] += $"，错误原因：{string.Join(" ", resp.ErrorMsgs)}";
            }

            this.WriteOpLog("响应结果：" + resp.ToJson());
            this.WriteOpLog(msg);
        }

        private Dictionary<string, Tuple<string, string>> GetProductNumberToProductDic(IEnumerable<DynamicObject> dataEntities)
        {
            var dic = new Dictionary<string, Tuple<string, string>>();

            var numbers = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                var entry = dataEntity["fentry"] as DynamicObjectCollection;
                numbers.AddRange(entry.Select(s => Convert.ToString(s["fproductid"])).Where(s => !s.IsNullOrEmptyOrWhiteSpace()));
            }

            if (!numbers.Any()) return dic;

            var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var productNumberField = productForm.GetNumberField();
            //只能改总部商品的价目信息
            string sql =
                $"select {productForm.BillPKFldName}, {productNumberField.FieldName}, fsalunitid from {productForm.BillHeadTableName} where {productNumberField.FieldName} in ({string.Join(",", numbers.Select(s => $"'{s}'"))}) and fmainorgid='{this.Context.TopCompanyId}'";

            using (var reader = this.DBService.ExecuteReader(this.Context, sql))
            {
                while (reader.Read())
                {
                    string number = reader.GetValueToString(productNumberField.FieldName);
                    string id = reader.GetValueToString(productForm.BillPKFldName);
                    string salUnitId = reader.GetValueToString("fsalunitid");
                    dic[number] = Tuple.Create(id, salUnitId);
                }
            }

            return dic;
        }

        private Dictionary<string, string> GetNumberToIdDic(IEnumerable<DynamicObject> dataEntities)
        {
            var numbers = dataEntities.Select(s => Convert.ToString(s[this.NumberField.PropertyName]));

            var dic = new Dictionary<string, string>();

            string sql =
                $"select {this.HtmlForm.BillPKFldName}, {this.NumberField.FieldName} from {this.HtmlForm.BillHeadTableName} where {this.NumberField.FieldName} in ({string.Join(",", numbers.Select(s => $"'{s}'"))})";

            using (var reader = this.DBService.ExecuteReader(this.Context, sql))
            {
                while (reader.Read())
                {
                    dic[reader.GetValueToString(this.NumberField.FieldName)] =
                        reader.GetValueToString(this.HtmlForm.BillPKFldName);
                }
            }

            return dic;
        }

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="dataEntities"></param>
        private void HandleException(IEnumerable<DynamicObject> dataEntities)
        {
            this.Result.IsSuccess = false;

            MuSiData resp = new MuSiData();
            resp.FailedNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s[this.NumberField.PropertyName])));
            resp.ErrorMsgs = this.Result.ComplexMessage.ErrorMessages;
            // 异常
            resp.Flag = MuSiFlag.ERROR.ToString();

            this.Result.SrvData = resp;
        }

        /// <summary>
        /// 写操作日志
        /// </summary>
        /// <param name="logContent"></param>
        /// <param name="isError">是否出错</param>
        private void WriteOpLog(string logContent, bool isError = false)
        {
            if (OpLogObj == null) return;

            this.SystemIntegrationService.WriteOperationLog(this.Context, this.OpLogObj, logContent, isError);
        }

        /// <summary>
        /// 保存操作日志
        /// </summary>
        private void SaveOpLog()
        {
            if (this.OpLogObj == null) return;

            SystemIntegrationService.SaveOperationLog(this.Context, this.OpLogForm, new[] { this.OpLogObj },
              this.OperationContext?.Option);
        }
    }
}
