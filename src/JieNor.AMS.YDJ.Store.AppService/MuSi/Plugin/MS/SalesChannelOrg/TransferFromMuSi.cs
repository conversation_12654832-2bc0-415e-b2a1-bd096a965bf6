using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.MS.SalesChannelOrg
{
    /// <summary>
    /// 客户销售组织与渠道关系：慕思同步
    /// </summary>
    [InjectService]
    [FormId("ms_saleschannel_org")]
    [OperationNo("syncfrommusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataFromMuSiPlugIn
    {
        public override void BeforeSave(BeforeSaveEventArgs e)
        {
            base.BeforeSave(e);

            var dataEntities = e.DataEntitys;
            if (dataEntities.IsNullOrEmpty()) return;
        }

        /// <summary>
        /// 调用保存后触发
        /// </summary>
        /// <param name="e"></param>
        public override void AfterSave(AfterSaveEventArgs e)
        {
            base.AfterSave(e);

            var dataEntities = e.DataEntitys;
            if (dataEntities.IsNullOrEmpty()) return;
            //fcustomernumber 客户编码 可能是送达方编码也可能是经销商编码。
            var numbers = dataEntities.Select(s => Convert.ToString(s["fcustomernumber"]));

            var agents = this.Context.LoadBizDataByNo("bas_agent", "fnumber", numbers);
            var delivers = this.Context.LoadBizDataByNo("bas_deliver", "fnumber", numbers);
            if (delivers.IsNullOrEmpty() && agents.IsNullOrEmpty()) return;
            ////如果下发的【状态】=“启用中”，则【送达方.销售组织编码】直接取最新的【客户销售组织与渠道关系.销售组织编码】，【送达方.销售组织名称】取对应的《组织》进行关联赋值。
            //var delivers_Unforbid = delivers.Where(o => Convert.ToString(o["fforbidstatus"]).EqualsIgnoreCase("0"));
            ////如果下发的【状态】=“已停用”，则【送达方.销售组织编码】【送达方.销售组织名称】需查找【状态】=“启用中”且【更新时间】最新的那一条数据的销售组织进行更新。
            //var delivers_forbid = delivers.Except(delivers_Unforbid);
            //更新经销商关联销售组织
            var agentService = this.Container.GetService<IAgentService>();
            agentService.UpdateSaleOrg(this.Context, agents);
            this.Context.SaveBizData("bas_agent", agents);

            // 更新送达方 相关信息。
            var deliverService = this.Container.GetService<IDeliverService>(); 
            deliverService.UpdateSalOrgBySales(this.Context, delivers); 
            this.Context.SaveBizData("bas_deliver", delivers); 
        }
    }
}
