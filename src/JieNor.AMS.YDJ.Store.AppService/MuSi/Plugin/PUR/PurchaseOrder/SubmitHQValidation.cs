using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Service;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Pur;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface.QueryBuilder;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.PUR.PurchaseOrder
{
    /// <summary>
    /// 采购订单提交总部校验器
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class SubmitHQValidation : AbstractBaseValidation
    {
        private Enu_DomainType domainType;

        private bool isTopOper = false;

        public SubmitHQValidation(Enu_DomainType domainType)
        {
            this.domainType = domainType;
        }

        public SubmitHQValidation(Enu_DomainType domainType, bool isTopOper = false)
        {
            this.domainType = domainType;
            this.isTopOper = isTopOper;
        }

        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, formInfo.GetDynamicObjectType(userCtx), dataEntities, false);
            CheckCloseStatus(dataEntities, result);

            CheckQCBillType(dataEntities, result);
            CheckPurMul(dataEntities, result);
            CheckChannel(dataEntities, result);
            CheckTopProduct(userCtx, dataEntities, result);
            //CheckUnAllOwPur(userCtx, dataEntities, result);
            CheckSaleOrg(userCtx, dataEntities, result);
            CheckResultBrand(dataEntities, result);
            CheckTopSupplier(dataEntities, result);
            CheckDeliverCanPur(dataEntities, result);
            CheckSelection(dataEntities, result);
            CheckOrderChange(dataEntities, result);
            CheckChangeCountOrClose(dataEntities, result);
            //判断 商品【选配套件】=“是”且没有子件商品 提交时给校验
            CheckSubmitSuitProduct(userCtx, dataEntities, result);

            // 如果对应《采购订单》商品明细行的商品  对应《商品》基础资料 勾选上"配件标记" 且  《采购订单》商品明细对应找不到对应配件时, 不允许提交总部
            CheckHaveTGC(dataEntities, result, domainType, true);

            // 如果对应《采购订单》商品明细行的商品  对应《商品》基础资料 勾选上"配件标记" 且 在铁架床配件映射中是配件商品但没配件号时
            CheckHaveTGC(dataEntities, result, domainType, false);

            CheckProduct(dataEntities, result);


            //判断商品业绩品牌是否都存在于 送达方品牌配置中
            CheckDeliverBrand(dataEntities, result);

            CheckAllowPur(userCtx, dataEntities, result);

            //List<string> combinemessage = new List<string>();
            //foreach (var datatentity in dataEntities)
            //{
            //    UpdatePurOrderProd(datatentity["fentity"] as DynamicObjectCollection, combinemessage);
            //    if (combinemessage.Count > 0)
            //    {

            //        var errormessage = "";
            //        foreach (var item in combinemessage)
            //        {
            //            errormessage += "“" + item + "组合套餐”、";
            //        }
            //        result.Errors.Add(new ValidationResultEntry()
            //        {
            //            ErrorMessage = $"由于{errormessage.Substring(0, errormessage.Length - 1)}活动时间已过期，所以组合套餐中的商品已没有活动折扣优惠信息",
            //            DataEntity = datatentity
            //        });
            //    }
            //}

            return result;
        }

        /// <summary>
        /// 判断 商品【选配套件】=“是”且没有子件商品 提交时给校验
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="formInfo"></param>
        /// <param name="result"></param>
        /// <param name="noOrders"></param>
        private void CheckSubmitSuitProduct(UserContext userCtx, DynamicObject[] dataEntities, ValidationResult result)
        {
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return;
            }
            // 所有的商品ID
            var productIds = dataEntities?.SelectMany(o =>
            {
                var entrys = o["fentity"] as DynamicObjectCollection;
                var _productIds = entrys
                .Select(entry => Convert.ToString(entry["fmaterialid"]))
                .Where(productId => !productId.IsNullOrEmptyOrWhiteSpace());
                return _productIds;
            })
            ?.Distinct()
            ?.ToList();
            // 批量加载商品信息
            DynamicObjectCollection productObjs = null;
            if (productIds != null && productIds.Any())
            {
                productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fnumber,fname,fsuiteflag,fmainorgid");
            }
            foreach (var dataEntity in dataEntities)
            {
                if (productObjs == null || !productObjs.Any()) return;

                var entrys = dataEntity["fentity"] as DynamicObjectCollection;

                var orderService = this.Context.Container.GetService<IOrderService>();
                //套件组合号
                var suitcombnumberLst = entrys
                    .Where(f => Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1"))
                    .Select(o => Convert.ToString(o["fsuitcombnumber"])).Distinct().ToList<string>();

                foreach (var suitcombnumber in suitcombnumberLst)
                {

                    //是否存在提交时 只有套件头 没子件的情况
                    var Isexist = entrys.Any(f => suitcombnumber == Convert.ToString(f["fsuitcombnumber"]) &&
                    Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("0")
                    );
                    if (!Isexist)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"对不起，当前订单存在套件商品未选择子件，禁止提交总部！",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        }

        private void CheckSaleOrg(UserContext userCtx, DynamicObject[] dataEntities, ValidationResult result)
        {
            // 增加产品销售组织校验
            var topOrgId = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;

            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);

            foreach (var dataEntity in dataEntities)
            {
                bool purchaseOrderIsChg = IsPurchaseOrderChg(getChgPurDys, dataEntity);

                //1.1) 采购订单 如果有 送达方时, 根据该送达方基础资料里的"销售组织"进行匹配(如果没有到送达方就不用该校验)
                //1.2) 匹配商品明细的商品 基础资料的 单据体 - 产品销售组织 里的 销售组织, 匹配上后或取对应行的"禁用状态"
                //1.3) 如果"禁用状态" = 已禁用, 则不允许转采购, 这一行就不会转到采购订单上, 其它数据可以继续转采购, 但是如果没有任何数据可以转采购时, 要提示错误信息"商品XXXX 在 XXXXX (某某销售组织) 已停产, 不允许采购 !"
                //1.4) 如果"禁用状态" = 已启用, 则就可以正常转采购
                //1.5) 如果没有匹配到销售组织 , 则不允许采购(只针对总部的商品, 非总部的不需要该校验), 要提示错误信息"商品XXXX 已停产不允许采购, 请重新删除该商品重新提交!"
                //http://dmp.jienor.com:81/zentao/task-view-28399.html

                string fchangestatus = Convert.ToString(dataEntity["fchangestatus"]);

                var fdeliverid = Convert.ToString(dataEntity["fdeliverid"]);
                if (fdeliverid.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var deliver = dataEntity["fdeliverid_ref"] as DynamicObject;
                if (deliver == null)
                {
                    continue;
                }

                // 送达方的销售组织
                var fsaleorgid = Convert.ToString(deliver["fsaleorgid"]);
                if (fsaleorgid.IsNullOrEmptyOrWhiteSpace())
                {
                    //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //判断送达方销售组织；-调整不校验
                    if (purchaseOrderIsChg)
                    {
                        continue;
                    }
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"送达方{deliver["fname"]}没有关联销售组织!",
                        DataEntity = dataEntity
                    });
                    continue;
                }

                var saleOrg = this.Context.LoadBizDataById("bas_organization", fsaleorgid);
                if (saleOrg == null)
                {
                    //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //判断送达方销售组织；-调整不校验
                    if (purchaseOrderIsChg)
                    {
                        continue;
                    }
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"送达方{deliver["fname"]}关联的销售组织已删除!",
                        DataEntity = dataEntity
                    });
                    continue;
                }

                var entrys = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var product = entry["fmaterialid_ref"] as DynamicObject;
                    // 非总部的不需要该校验
                    if (Convert.ToString(product["fmainorgid"]) != topOrgId) continue;

                    // 匹配销售组织
                    var saleOrgEntrys = product["fsaleorgentry"] as DynamicObjectCollection;
                    var salOrgEntry =
                        saleOrgEntrys?.FirstOrDefault(s => Convert.ToString(s["fsaleorgid"]).EqualsIgnoreCase(fsaleorgid));

                    // 如果没有匹配到销售组织 , 则不允许采购(只针对总部的商品, 非总部的不需要该校验), 要提示错误信息"商品XXXX 已停产不允许采购, 请重新删除该商品重新提交!"
                    if (salOrgEntry == null && !fchangestatus.EqualsIgnoreCase("1"))
                    {
                        //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                        //商品已停产不允许采购, 请重新删除该商品重新提交；-调整为不校验
                        if (purchaseOrderIsChg)
                        {
                            continue;
                        }
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"商品{product["fname"]} 已停产不允许采购, 请重新删除该商品重新提交!",
                            DataEntity = dataEntity
                        });
                        continue;
                    }

                    //http://dmp.jienor.com:81/zentao/task-view-37717.html 采购订单变更单提交总部不需要校验是否停产

                    // 如果"禁用状态" = 已禁用, 则不允许转采购, 这一行就不会转到采购订单上, 其它数据可以继续转采购, 但是如果没有任何数据可以转采购时, 要提示错误信息"商品XXXX 在 XXXXX (某某销售组织) 已停产, 不允许采购 !"
                    if (Convert.ToString(salOrgEntry?["fdisablestatus"]).EqualsIgnoreCase("2") &&
                        !fchangestatus.EqualsIgnoreCase("1"))
                    {

                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"商品{product["fname"]} 在 {saleOrg["fname"]} 已停产, 不允许采购!",
                            DataEntity = dataEntity
                        });
                        continue;
                    }
                }
            }
        }

        /// <summary>
        /// 检查总部商品
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        /// <param name="result"></param>
        private void CheckTopProduct(UserContext userCtx, DynamicObject[] dataEntities, ValidationResult result)
        {
            //1. 在 采购订单 列表 与表单  点击 <提交总部>, 如果存在 非总部商品时, 要报错提示"采购订单存在非总部商品, 不允许提交至总部!"
            var topOrgId = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;
            foreach (var dataEntity in dataEntities)
            {
                var entrys = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var product = entry["fmaterialid_ref"] as DynamicObject;
                    if (Convert.ToString(product?["fmainorgid"]) != topOrgId)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"采购订单存在非总部商品【{product?["fname"]}】 不允许提交至总部",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 检查当前组织对应商品授权清单 是否存在例外商品被提交总部，需要校验：“不允许采购的商品无法提交总部”
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        /// <param name="result"></param>
        private void CheckUnAllOwPur(UserContext userCtx, DynamicObject[] dataEntities, ValidationResult result)
        {
            string sql = $@"select atmx.fproductid_o from  t_ydj_productauthbs_prd as atmx with(nolock) inner join  t_ydj_productauth as at with (nolock) on at.fid = atmx.fid
                            where forgid ='{userCtx.Company}' and forgtype='4' and fforbidstatus =0";
            var UnAllowProductIds = this.DBService.ExecuteDynamicObject(this.Context, sql).Select(o => Convert.ToString(o["fproductid_o"])).ToList<string>().Distinct();

            var topOrgId = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;

            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);

            foreach (var dataEntity in dataEntities)
            {
                bool purchaseOrderIsChg = IsPurchaseOrderChg(getChgPurDys, dataEntity);

                var entrys = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230   优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //不允许采购的商品无法提交总部；-调整为不校验
                    if (purchaseOrderIsChg)
                    {
                        continue;
                    }
                    var product = entry["fmaterialid_ref"] as DynamicObject;
                    //如果当前商品属于例外商品则不允许提交总部
                    if (UnAllowProductIds.Contains(Convert.ToString(product?["id"])))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"不允许采购的商品【{product?["fname"]}】 无法提交总部",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 校验是否允许下单
        /// 查送达方对应的商品授权清单，是否在例外商品或不允许采购列表中
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        /// <param name="result"></param>
        private void CheckAllowPur(UserContext userCtx, DynamicObject[] dataEntities, ValidationResult result)
        {
            var agents = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(this.Context);
            if (agents == null || agents.Count == 0)
            {
                return;
            }
            var rulePara = new DataQueryRuleParaInfo()
            {
                SrcFldId = "ydj_purchaseorder",
            };
            var prdAuths = new ProductDataIsolateHelper().GetProductAuthInfo(Context, rulePara, agents);

            //string sql = $@"select atmx.fproductid_o from  t_ydj_productauthbs_prd as atmx with(nolock) inner join  t_ydj_productauth as at with (nolock) on at.fid = atmx.fid
            //                where forgid ='{userCtx.Company}' and forgtype='4' and fforbidstatus =0";
            //var UnAllowProductIds = this.DBService.ExecuteDynamicObject(this.Context, sql).Select(o => Convert.ToString(o["fproductid_o"])).ToList<string>().Distinct();

            //var topOrgId = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;

            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);

            foreach (var dataEntity in dataEntities)
            {

                bool purchaseOrderIsChg = IsPurchaseOrderChg(getChgPurDys, dataEntity);

                var city = Convert.ToString((dataEntity["fdeliverid_ref"] as DynamicObject)?["fcity"]);
                var agentid = Convert.ToString((dataEntity["fdeliverid_ref"] as DynamicObject)?["fagentid"]);
                var _prdAuths = prdAuths.Where(a => a.forgid.Equals(agentid)).ToList();
                _prdAuths = _prdAuths.Where(a => a.fcityid.Equals(city)).ToList();
                var prdAuthLst = _prdAuths.SelectMany(f => f.fproductauthlist).ToList();//授权商品
                var prdAuthLstExclude = _prdAuths.SelectMany(f => f.fproductauthexclude).ToList();//例外商品

                var noPoProductIds = prdAuthLst.Where(f => f != null && f.fnopurchase)?.ToList()?.Select(f => f.fproductid)?.ToList();//不允许采购
                var excludeProductIds = prdAuthLstExclude.Select(a => a.fproductid_o).ToList();//例外商品

                var entrys = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var product = entry["fmaterialid_ref"] as DynamicObject;
                    // http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //不允许采购的商品无法提交总部 -调整为不校验
                    //采购的例外商品无法提交总部；-调整为不校验
                    if (purchaseOrderIsChg)
                    {
                        continue;
                    }
                    //如果当前商品属于例外商品则不允许提交总部
                    if (noPoProductIds.Contains(Convert.ToString(product?["id"])))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"不允许采购的商品【{product?["fname"]}】 无法提交总部",
                            DataEntity = dataEntity
                        });
                    }
                    if (excludeProductIds.Contains(Convert.ToString(product?["id"])))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"采购的例外商品【{product?["fname"]}】 无法提交总部",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 校验期初单据类型
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="result"></param>
        private void CheckQCBillType(DynamicObject[] dataEntities, ValidationResult result)
        {
            foreach (var dataEntity in dataEntities)
            {
                string billtype = Convert.ToString(dataEntity["fbilltypeid"]);

                //只判断总部预置的期初不允许提交至总部
                if (billtype == "ydj_purchaseorder_qc")
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = "期初采购订单不允许提交到总部！",
                        DataEntity = dataEntity
                    });
                }
            }
        }

        private void CheckChannel(DynamicObject[] dataEntities, ValidationResult result)
        {
            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);

            //task:38746 第4、5点
            foreach (var dataEntity in dataEntities)
            {
                bool purchaseOrderIsChg = IsPurchaseOrderChg(getChgPurDys, dataEntity);

                //加载引用数据
                var entrys = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var fchannel = entry["fchannel_ref"] as DynamicObject;
                    if (fchannel == null) continue;
                    // http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //当前向总部采购的大客户已失效, 不允许向总部采购 ! 如需向总部采购，需走招商系统流程生效此大客户。-调整为不校验
                    if (purchaseOrderIsChg)
                    {
                        continue;
                    }
                    if (Convert.ToString(fchannel["fforbidstatus"]).EqualsIgnoreCase("true"))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage =
                                $"当前向总部采购的大客户 " + fchannel["fname"] + " 已失效, 不允许向总部采购 ! 如需向总部采购，需走招商系统流程生效此大客户。<br/>",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        private void CheckPurMul(DynamicObject[] dataEntities, ValidationResult result)
        {
            var purCommon = new PurCommon(this.Context);
            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);
            //采购数量】必须为【采购件数】的倍数 验证
            foreach (var dataEntity in dataEntities)
            {
                var billtypeObj = dataEntity["fbilltypeid_ref"] as DynamicObject;
                var billtypeName = Convert.ToString(billtypeObj?["fname"]);
                bool purchaseOrderIsChg = IsPurchaseOrderChg(getChgPurDys, dataEntity);
                if (billtypeName == "总部手工单")
                {
                    continue;
                }


                //采购数量】必须为【采购件数】的倍数 验证
                if (!purCommon.CheckPurMul(dataEntity, out var entryErrorMessage))
                {
                    // http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //【采购数量】必须为【采购件数】的倍数校验；-调整不校验
                    if (purchaseOrderIsChg)
                    {
                        continue;
                    }

                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = entryErrorMessage,
                        DataEntity = dataEntity
                    });
                }
            }
        }

        private void CheckResultBrand(DynamicObject[] dataEntities, ValidationResult result)
        {
            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);

            var findPurChgEntrys = new List<DynamicObject>();

            if (getChgPurDys != null && getChgPurDys.Any())
            {
                findPurChgEntrys = getChgPurDys.SelectMany(s => s["fentity"] as DynamicObjectCollection).ToList();
            }

            /*
             采购订单 增加 当 商品行的"业绩品牌" = "通配品牌", 不允许提交至总部, 要提示错误信息, "采购订单XXXX存在业绩品牌为"通配品牌", 不允许提交至总部 !"
             */
            var entrys = dataEntities.SelectMany(s => s["fentity"] as DynamicObjectCollection);

            foreach (var entry in entrys)
            {
                var series = entry["fresultbrandid_ref"] as DynamicObject;
                if (Convert.ToString(series?["fname"]) == "通配品牌")
                {
                    var dataEntity = entry.Parent as DynamicObject;
                    //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //采购订单存在业绩品牌为"通配品牌", 不允许提交至总部；-调整为不校验
                    if (findPurChgEntrys != null && findPurChgEntrys.Any())
                    {
                        var findPurChgEntry = findPurChgEntrys.FirstOrDefault(x => Convert.ToString(x["id"]).Equals(Convert.ToString(entry["id"])));
                        if (findPurChgEntry != null)
                        {
                            continue;
                        }
                    }
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"采购订单{dataEntity["fbillno"]}存在业绩品牌为\"通配品牌\", 不允许提交至总部",
                        DataEntity = dataEntity
                    });
                }
            }
        }

        private void CheckTopSupplier(DynamicObject[] dataEntities, ValidationResult result)
        {
            // 非总部经销商不允许提交总部
            foreach (var dataEntity in dataEntities)
            {
                // 表头的供应商 对应基础资料里 单据头"销售组织"为空时, 不允许提交到总部 

                var supplier = dataEntity["fsupplierid_ref"] as DynamicObject;

                var orgId = Convert.ToString(supplier?["forgid"]);

                if (orgId.IsNullOrEmptyOrWhiteSpace())
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = "当前供应商不为总部供应商, 不允许提交至总部!",
                        DataEntity = dataEntity
                    });
                }
            }
        }
        /// <summary>
        /// 判断送达方是否可以下单
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="result"></param>
        private void CheckDeliverCanPur(DynamicObject[] dataEntities, ValidationResult result)
        {
            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);

            //当只有【新商】=“否”且【下单权限】=“是”才允许下单，否则需提示：：“对不起，当前送达方不允许向总部采购，请联系总部跟单处理！”。
            foreach (var dataEntity in dataEntities)
            {
                var deliver = dataEntity["fdeliverid_ref"] as DynamicObject;

                if (deliver.IsNullOrEmpty()) continue;
                //新商
                var fisnew = Convert.ToBoolean(deliver["fisnew"]);
                //下单权限
                var forderdisable = Convert.ToBoolean(deliver["forderdisable"]);

                bool purchaseOrderIsChg = IsPurchaseOrderChg(getChgPurDys, dataEntity);

                if (!(fisnew == false && forderdisable == true))
                {
                    // http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //判断送达方是否可以下单：当只有【新商】=“否”且【下单权限】=“是”才允许下单，否则需提示：“对不起，当前送达方不允许向总部采购，请联系总部跟单处理！”-调整不校验
                    if (purchaseOrderIsChg)
                    {
                        continue;
                    }

                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = "对不起，当前送达方不允许向总部采购，请联系总部跟单处理！",
                        DataEntity = dataEntity
                    });
                }
            }
        }

        private void CheckSelection(DynamicObject[] dataEntities, ValidationResult result)
        {
            // 启用选配校验辅助属性值判断
            var entrys = dataEntities.SelectMany(s => s["fentity"] as DynamicObjectCollection);
            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);
            var findPurChgEntrys = new List<DynamicObject>();
            if (getChgPurDys != null && getChgPurDys.Any())
            {
                findPurChgEntrys = getChgPurDys.SelectMany(s => s["fentity"] as DynamicObjectCollection).ToList();
            }
            foreach (var entry in entrys)
            {
                // 如果对应《采购订单》商品明细行的商品  对应《商品》基础资料 勾选上"允许选配" 且  《采购订单》商品明细对应行 "辅助属性"为空时, 不允许提交总部

                var product = entry["fmaterialid_ref"] as DynamicObject;
                if (product == null) continue;

                var isPresetProp = Convert.ToBoolean(product["fispresetprop"]);
                var attrInfo = Convert.ToString(entry["fattrinfo"]);
                if (isPresetProp && attrInfo.IsNullOrEmptyOrWhiteSpace())
                {
                    var dataEntity = entry.Parent as DynamicObject;
                    // http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //判断商品明细是否选配：如果对应《采购订单》商品明细行的商品对应《商品》基础资料勾选上"允许选配"且《采购订单》商品明细对应行 "辅助属性"为空时, 不允许提交总部；-调整不校验
                    if (findPurChgEntrys != null && findPurChgEntrys.Any())
                    {
                        var purChgEntry = findPurChgEntrys.FirstOrDefault(x => Convert.ToString(x["id"]).Equals(Convert.ToString(entry["id"])));
                        if (purChgEntry != null)
                        {
                            continue;
                        }
                    }
                    if (domainType == Enu_DomainType.Bill)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"明细行 {entry["Fseq"]} 商品 {product["fname"]} 为定制品,  但辅助属性为空, 不允许提交总部!",
                            DataEntity = dataEntity
                        });
                    }
                    else
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"采购订单 {dataEntity["fbillno"]} 商品 {product["fname"]} 为定制品,  但辅助属性为空, 不允许提交总部! ",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 判断是否存在已变更单未保存 生成变更单的数据，此单不能提交总部
        /// </summary>
        /// <returns></returns>
        private void CheckOrderChange(DynamicObject[] dataEntities, ValidationResult result)
        {
            // 如果【变更状态】= "变更中" 且 还没有保存生成对应的《采购订单变更单》点时 , 要报错提示 "当前采购订单变更后未保存, 不允许提交至总部进行变更!"

            var matchDataEntities = dataEntities.Where(s => Convert.ToString(s["fchangestatus"]) == "1");
            if (!matchDataEntities.Any())
            {
                return;
            }

            var fbillnos = matchDataEntities.Select(s => Convert.ToString(s["fbillno"])).ToList();
            var purOrderObjs = this.Context.LoadBizDataByACLFilter("ydj_purchaseorder_chg", $" fsourcenumber in ({fbillnos.JoinEx(",", true)}) and fstatus ='B' ");

            foreach (var dataEntity in matchDataEntities)
            {
                var fbillno = Convert.ToString(dataEntity["fbillno"]);

                if (!purOrderObjs.Any(s => Convert.ToString(s["fsourcenumber"]).EqualsIgnoreCase(fbillno)))
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"采购订单【{fbillno}】变更后未保存, 不允许提交至总部进行变更!",
                        DataEntity = dataEntity
                    });
                }
            }
        }

        /// <summary>
        /// 判断采购变更单是否新增、删除 商品行，或者关闭、反关闭，如果都没有则不允许提交变更到总部
        /// </summary>
        /// <returns></returns>
        private void CheckChangeCountOrClose(DynamicObject[] dataEntities, ValidationResult result)
        {
            //判断采购变更单是否新增、删除 商品行，或者关闭、反关闭，如果都没有则不允许提交变更到总部 
            var matchDataEntities = dataEntities.Where(s => Convert.ToString(s["fchangestatus"]) == "1");
            if (!matchDataEntities.Any())
            {
                return;
            }

            var fbillnos = matchDataEntities.Select(s => Convert.ToString(s["fbillno"])).ToList();
            var purOrderObjs = this.Context.LoadBizDataByACLFilter("ydj_purchaseorder_chg", $" fsourcenumber in ({fbillnos.JoinEx(",", true)}) and fstatus ='B' ").OrderByDescending(o => o["fcreatedate"]);

            foreach (var dataEntity in matchDataEntities)
            {
                var fbillno = Convert.ToString(dataEntity["fbillno"]);

                var purOrderObj = purOrderObjs.FirstOrDefault(s => Convert.ToString(s["fsourcenumber"]).EqualsIgnoreCase(fbillno));

                if (purOrderObj != null)
                {
                    var entry = purOrderObj["fentity"] as DynamicObjectCollection;
                    //判断商品采购数量有无改变
                    bool changeqty = entry.Any(o => !Convert.ToDecimal(o["fbizqty"]).Equals(Convert.ToDecimal(o["fbizqty_chg"])));
                    //判断变更单有无新建、删除的商品明细
                    bool changecount = entry.Any(o => !Convert.ToString(o["fentrychange"]).EqualsIgnoreCase("entrychange_01"));
                    //判断变更单有关闭、反关闭的商品明细 
                    bool changeclose = entry.Any(o => !Convert.ToString(o["fclosestatus_e"]).EqualsIgnoreCase(Convert.ToString(o["fclosestatus_e_chg"])));
                    if (!(changecount || changeclose || changeqty))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"采购订单【{fbillno}】商品明细无变化，不允许提交至总部!",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 判断是否存在铁架床配件商品，配件号为空不允许单独提交总部
        /// </summary>
        /// <returns></returns>
        private void CheckHaveTGC(DynamicObject[] dataEntities, ValidationResult result, Enu_DomainType domainType, bool isMain)
        {
            //fmaterialidnew 主件商品字段
            //fmaterialid    配件商品字段
            var matKey = "fmaterialid";
            var msg = "为配件不允许单独下单, 必须与主商品配套下单! ! !";
            if (isMain)
            {
                matKey = "fmaterialidnew";
                msg = "需配套铁架床排骨架, 不允许单独下单, 请从销售合同配套后再向总部采购 ! ! !";
            }
            bool exist = true;
            foreach (var item in dataEntities)
            {
                var fentity = item["fentity"] as DynamicObjectCollection;
                //找到没有配件号的商品、或者配件组合号不为空 但是只有配件无主件的商品
                var noentity = fentity.Where(o => Convert.ToString(o["fpartscombnumber"]).IsNullOrEmptyOrWhiteSpace()).ToList();
                //配件组合号
                var partscombnumberLst = fentity.Where(o => !Convert.ToString(o["fpartscombnumber"]).IsNullOrEmptyOrWhiteSpace()).Select(o => Convert.ToString(o["fpartscombnumber"])).Distinct().ToList<string>();
                foreach (var partscombnumber in partscombnumberLst)
                {
                    //获取配件主商品、子商品
                    var Main = fentity.Where(f => Convert.ToBoolean(f["fiscombmain"]) && partscombnumber == Convert.ToString(f["fpartscombnumber"])).ToList();
                    var Sub = fentity.Where(f => !Convert.ToBoolean(f["fiscombmain"]) && partscombnumber == Convert.ToString(f["fpartscombnumber"])).ToList();
                    if (Main.Count == 0)
                    {
                        noentity.AddRange(Sub);
                    }
                    if (Sub.Count == 0)
                    {
                        noentity.AddRange(Main);
                    }
                }
                var Dic = noentity.ToDictionary(k => Convert.ToInt32(k["fseq"]), v => Convert.ToString(v["fmaterialid"]));
                List<string> materialIds = Dic.Values.ToList<string>();
                string sql = $@"SELECT  {matKey}  FROM t_sel_fittingsmap 
                            INNER JOIN t_sel_fittingsmapentry ON t_sel_fittingsmapentry.fid = t_sel_fittingsmap.fid and t_sel_fittingsmapentry.fdisable != 1
                            INNER JOIN SER_YDJ_CATEGORY ON SER_YDJ_CATEGORY.fid =t_sel_fittingsmap.fcategoryid
                            WHERE SER_YDJ_CATEGORY.fname ='铁架床' AND fmatchbyproduct = '1' AND {matKey} in ('{string.Join("','", materialIds)}')";
                var NomaterialIds = this.DBService.ExecuteDynamicObject(this.Context, sql).Select(o => Convert.ToString(o[matKey])).ToList<string>().Distinct();
                var rows = new List<int>();
                foreach (var pro in NomaterialIds)
                {
                    var product = noentity.Where(o => Convert.ToString(o["fmaterialid"]).EqualsIgnoreCase(pro)).Select(o => o["fmaterialid_ref"] as DynamicObject).FirstOrDefault();
                    rows = Dic.Where(o => o.Value.EqualsIgnoreCase(pro)).Select(k => Convert.ToInt32(k.Key)).ToList();
                    foreach (var r in rows)
                    {
                        var newData = product?.Parent as DynamicObject;
                        if (domainType == Enu_DomainType.Bill)
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"当前订单明细行 {r} 商品 {product?["fname"]}  {msg}",
                                DataEntity = item
                            });
                        }
                        else
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"采购订单 {newData?["fbillno"]}明细行 {r} 商品 {product?["fname"]} {msg} ",
                                DataEntity = item
                            });
                        }
                    }
                }

            }
        }



        /// <summary>
        /// 新渠道床架与新渠道排骨架要按照 1:1 数量下单
        /// </summary>
        /// <returns></returns>
        private void CheckProduct(DynamicObject[] dataEntities, ValidationResult result)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            //只统计业绩品牌为新渠道的商品行，其它的不需要比较
            //#75244 【250491】 【慕思现场4.10-4.14】新渠道系列拆分Z2 慕思经典-甄选 ，Z5 慕思经典-优选
            var NewBal = this.Context.LoadBizDataByFilter("ydj_series", $" fisnewchannel='1'").Select(o => Convert.ToString(o["id"])).ToList();
            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);
            foreach (var dataEntity in dataEntities)
            {
                bool purchaseOrderIsChg = IsPurchaseOrderChg(getChgPurDys, dataEntity);

                var fentitys = dataEntity["fentity"] as DynamicObjectCollection;
                //var fentitysre = fentitys.Where(o=> NewBal.Contains(Convert.ToString(o["fresultbrandid"])));
                //获取商品对应类别 
                //75660 【250565】 【慕思现场-4.23-需求】调整新渠道床架与新渠道排骨架1:1校验只要含配件即允许下单。  只判断非配件组合商品是否 1:1 下单，存在配件组合的先过滤。
                var fentitysre = fentitys.Where(o => Convert.ToString(o["fpartscombnumber"]).IsNullOrEmptyOrWhiteSpace() && NewBal.Contains(Convert.ToString((o["fmaterialid_ref"] as DynamicObject)?["fseriesid"])));
                Dictionary<string, int> ProDic = fentitysre.ToDictionary(k => Convert.ToString(k["Id"]), v => Convert.ToInt32(v["fbizqty"]));
                var productService = this.Context.Container.GetService<IProductService>();
                int sumCJ = 0;
                int sumPGJ = 0;
                foreach (var dic in ProDic)
                {
                    var productid = fentitys.Where(o => o["Id"].ToString().EqualsIgnoreCase(dic.Key.ToString())).Select(o => Convert.ToString(o["fmaterialid"])).FirstOrDefault();
                    //获取商品类别名称
                    List<string> categorylist = productService.LoadProductParentCategoryIds(this.Context, productid);
                    var categorynameobj = this.Context.LoadBizDataByFilter("ydj_category", $"fid in('{string.Join("','", categorylist)}') and (fnumber like '1001%' or fnumber like '1002%') ");

                    //http://dmp.jienor.com:81/zentao/story-view-5966.html
                    //当前层级，存在这个备注的类别，则当前商品不做1:1校验
                    var matObj = fentitys.Where(o => o["Id"].ToString().EqualsIgnoreCase(dic.Key.ToString())).Select(o => (o["fmaterialid_ref"]) as DynamicObject).FirstOrDefault();
                    var objItem = categorynameobj.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(matObj["fcategoryid"])) && Convert.ToString(a["fdescription"]).Contains("转采购新渠道商品例外(不控制1:1)")).FirstOrDefault();
                    if (objItem != null)
                    {
                        continue;
                    }
                    List<string> categorynamelist = categorynameobj.Select(o => o["fname"].ToString()).ToList();
                    if (categorynamelist.Contains("床架"))
                    {
                        sumCJ += dic.Value;
                    }
                    if (categorynamelist.Contains("排骨架"))
                    {
                        sumPGJ += dic.Value;
                    }
                }

                if (sumCJ != sumPGJ && (sumCJ != 0 || sumPGJ != 0))
                {
                    // http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //判断新渠道床架与新渠道排骨架要按照1:1数量下单；-调整不校验
                    if (purchaseOrderIsChg)
                    {
                        continue;
                    }
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = "新渠道的床架必须搭配新渠道的排骨架一起采购, 当前采购订单未到1:1采购数量要求不允许向总部采购!",
                        DataEntity = dataEntity
                    });
                }
            }

        }
        private void CheckCloseStatus(DynamicObject[] dataEntities, ValidationResult result)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }
            //总部合同为空是前提条件，这种情况才校验关闭状态不允许提交至总部，因为采购订单变更提交至总部的时候 行关闭状态可能是手动关闭自动关闭，这种情况不允许被校验 不能提交至中部
            var matchDataEntites = dataEntities.Where(s => Convert.ToString(s["fhqderstatus"]).IsNullOrEmptyOrWhiteSpace());
            if (matchDataEntites == null || !matchDataEntites.Any())
            {
                return;
            }
            List<int> CloseStates = new List<int>() { (int)CloseStatus.Auto, (int)CloseStatus.Manual, (int)CloseStatus.Part };
            foreach (var dataEntity in matchDataEntites)
            {
                var fentity = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in fentity)
                {
                    var containsStatus = entry["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace() ? false : CloseStates.Contains(Convert.ToInt32(entry["fclosestatus_e"]));
                    //判断行关闭状态为 “自动关闭、手动关闭、部分关闭”时 需要校验 不能提交至总部  
                    if (containsStatus)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"采购订单{dataEntity?["fbillno"]},第 {Convert.ToString(entry["fseq"])}行商品{Convert.ToString((entry["fmaterialid_ref"] as DynamicObject)?["fname"])}已【{GetCloseStatusStr(Convert.ToString(entry["fclosestatus_e"]))}】，不允许提交总部！",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 返回对应状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private string GetCloseStatusStr(string status)
        {
            string result = "";
            switch (status)
            {
                case "0":
                    result = "正常";
                    break;
                case "1":
                    result = "整单关闭";
                    break;
                case "2":
                    result = "部分关闭";
                    break;
                case "3":
                    result = "自动关闭";
                    break;
                case "4":
                    result = "手动关闭";
                    break;
                default:
                    result = "关闭";
                    break;
            }
            return result;
        }

        /// <summary>
        /// 判断 商品业绩品牌是否存在于 送达方品牌配置中
        /// </summary>
        /// <returns></returns>
        private void CheckDeliverBrand(DynamicObject[] dataEntities, ValidationResult result)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var matchDataEntites = dataEntities.Where(s => !Convert.ToString(s["fdeliverid"]).IsNullOrEmptyOrWhiteSpace());
            if (matchDataEntites == null || !matchDataEntites.Any())
            {
                return;
            }

            var deliverIds = matchDataEntites.Select(s => Convert.ToString(s["fdeliverid"])).ToList();

            var deliverObjs = this.Context.LoadBizDataById("bas_deliver", deliverIds);

            //http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
            //获取当前数据包当中的采购变更单
            var getChgPurDys = GetPurchaseOrderChangeDynamicObjects(dataEntities);

            foreach (var dataEntity in matchDataEntites)
            {
                var deliverId = Convert.ToString(dataEntity["fdeliverid"]);
                var deliverObj = deliverObjs.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(deliverId));
                //获取送达方品牌信息
                var deliverentry = deliverObj?["fentry"] as DynamicObjectCollection;
                var DeliverBrands = deliverentry.Select(o => o["fserieid"]);

                bool purchaseOrderIsChg = IsPurchaseOrderChg(getChgPurDys, dataEntity);

                var fentity = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in fentity)
                {
                    //不判断自动关闭的明细行 http://dmp.jienor.com:81/zentao/bug-view-31205.html
                    if (Convert.ToString(entry["fclosestatus_e"]).EqualsIgnoreCase("3")) continue;
                    // http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //当前业绩品牌不在送达方的品牌授权中, 不允许提交总部； -调整不校验
                    if (purchaseOrderIsChg)
                    {
                        continue;
                    }
                    //找到一条不包含于送达方品牌信息明细中的商品行，则抛出
                    if (!DeliverBrands.Contains(entry["fresultbrandid"]))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"采购订单{dataEntity?["fbillno"]} , 商品{Convert.ToString((entry["fmaterialid_ref"] as DynamicObject)?["fname"])}的业绩品牌不在送达方的品牌授权中, 不允许提交总部! <br>",
                            DataEntity = dataEntity
                        });
                    }
                }

                //遍历传过来的商品明细，业绩品牌不存在于送达方品牌信息中则直接返回。 
            }
        }


        /// <summary>
        ///新增需求 --要优先判断活动商品是否有效，如果有效就执行原逻辑，
        //存在过期的商品，如果还满足子组号的比例，则把原过期的商品去掉活动标签，
        //不存在过期的商品，则还是执行原逻辑。
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        public void UpdatePurOrderProd(DynamicObjectCollection fentry, List<string> combinemessage)
        {
            //var fentry = purchaseorder[0]["fentity"] as DynamicObjectCollection;
            var combinenumberList = fentry.Select(c => Convert.ToString(c["fcombinenumber"]).Trim()).Distinct();//已经匹配到套餐
            if (combinenumberList == null || combinenumberList.Count() <= 0) return;

            var promotioncombine = this.Context.LoadBizDataByFilter("bas_promotioncombine", $"fnumber in('{string.Join("','", combinenumberList)}')");
            var date = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            //记录过期商品集合

            foreach (var item in combinenumberList)
            {
                List<string> expireprod = new List<string>();
                var combine = promotioncombine.Where(c => c["fnumber"].ToString() == item).FirstOrDefault();
                if (combine == null) continue;
                //套餐明细商品
                var fcombineentry = combine["fcombineentry"] as DynamicObjectCollection;
                //订单明细商品
                var orderprod = fentry.Where(c => fcombineentry.Select(m => m["fproductid"].ToString()).Contains(c["fmaterialid"].ToString()));
                //1 先查看套餐商品是否有过期时间
                foreach (var com in fcombineentry)
                {
                    if (date < DateTime.Parse(com["fproductbegindate"].ToString()) || date >= DateTime.Parse(com["fproductenddate"].ToString()))
                    {
                        expireprod.Add(com["fproductid"].ToString());
                    }
                }
                //没有过期商品,说明套餐正常，进行下次循环判断
                if (expireprod.Count <= 0) continue;

                //有过期商品的话，则需要
                //1 判断子组号是否满足套餐，不满足套餐就要去掉套餐标签
                var combinefeoupnumber = fcombineentry.Select(c => c["fgroupnumber"].ToString()).Distinct();
                //过期商品的子组号
                //var expireprodgroupnumber = fcombineentry.Where(c => expireprod.Contains(c["fproductid"].ToString())).Select(c => c["fgroupnumber"].ToString()).Distinct();

                var orderentry = fentry.Where(c => c["fcombinenumber"].ToString() == item);
                //判断订单里面打标签的商品去掉过期商品 是否还满足套餐
                var orderotherprod = orderprod.Where(c => !expireprod.Contains(c["fmaterialid"].ToString()));
                var expireprodgroupnumber = fcombineentry.Where(c => orderotherprod.Select(m => m["fmaterialid"].ToString()).Contains(c["fproductid"].ToString())).Select(c => c["fgroupnumber"].ToString()).Distinct();

                if (combinefeoupnumber.Count() != expireprodgroupnumber.Count())
                {
                    foreach (var order in orderentry)
                    {
                        //促销活动
                        order["fpromotion"] = "";
                        //组合促销编号
                        order["fcombinenumber"] = "";
                        //描述
                        order["fcombineremark"] = "";
                        //折扣率
                        order["fcombinerate"] = 0;
                        //套餐组合基数
                        order["fcombineqty"] = 0;
                    }
                    combinemessage.Add(combine["fdescription"].ToString());
                }
                //2 满足子组号的话，则需要去掉过期商品的套餐标签
                else
                {
                    foreach (var expire in expireprod)
                    {
                        var model = orderentry.Where(c => c["fmaterialid"].ToString() == expire).FirstOrDefault();
                        if (model != null)
                        {
                            //促销活动
                            model["fpromotion"] = "";
                            //组合促销编号
                            model["fcombinenumber"] = "";
                            //描述
                            model["fcombineremark"] = "";
                            //折扣率
                            model["fcombinerate"] = 0;
                            //套餐组合基数
                            model["fcombineqty"] = 0;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取采购变更单
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private List<DynamicObject> GetPurchaseOrderChangeDynamicObjects(DynamicObject[] dataEntities)
        {
            List<DynamicObject> dataEntity_CHG = dataEntities.Where(o => Convert.ToString(o["fchangestatus"]) == "1"
                                                                         && (Convert.ToString(o["fhqderstatus"]).EqualsIgnoreCase("03") || (Convert.ToString(o["fhqderstatus"]).EqualsIgnoreCase("02") && isTopOper))
                                                //&& (Convert.ToString(o["fhqderchgstatus"]).IsNullOrEmptyOrWhiteSpace() || Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("03") || Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("02"))
                                                ).ToList();

            return dataEntity_CHG;
        }

        /// <summary>
        /// 当前数据包是否是采购变更单的数据包
        /// </summary>
        /// <param name="getPurChgDys"></param>
        /// <param name="purchaseOrderDy"></param>
        /// <returns></returns>
        private bool IsPurchaseOrderChg(IEnumerable<DynamicObject> getPurChgDys, DynamicObject purchaseOrderDy)
        {
            bool isTrue = false;
            if (getPurChgDys == null || !getPurChgDys.Any())
            {
                isTrue = false;
            }
            else if (getPurChgDys != null && getPurChgDys.Any())
            {
                var findPurchaseChgDy = getPurChgDys.FirstOrDefault(x => Convert.ToString(x["id"]).Equals(Convert.ToString(purchaseOrderDy["id"])));

                if (findPurchaseChgDy != null)
                {
                    isTrue = true;
                }
                else
                {
                    isTrue = false;
                }
            }

            return isTrue;
        }

    }
}