using System;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.PUR.PurchaseOrderChg
{
    /// <summary>
    /// 采购订单变更单：同步OMS
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder_chg")]
    [OperationNo("synctomusioms")]
    [ThirdSystemId("musi")]
    public class TransferToMuSiOMS : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();
            switch (extFieldId)
            {
                // 变更状态
                case "alterflag":
                    {
                        //删除就是传2，更新就是传1； 新增明细 是会被过滤的 不会传过去
                        e.Cancel = true;
                        e.Result = "";

                        var changetype = Convert.ToString(dataEntity["fentrychange"]);
                        if (changetype == "entrychange_01")
                        {
                            e.Result = "1";
                        }
                        else if (changetype == "entrychange_03")
                        {
                            e.Result = "2";
                        }
                        var fclosestatus_e_chg = Convert.ToString(dataEntity?["fclosestatus_e_chg"]);
                        if (fclosestatus_e_chg == "4")
                        {
                            //如果是关闭状态的明细 则把变更状态 更新为 删除，接口就会把alterFlag 传2 即为取消
                            e.Result = "2";
                        }
                    }
                    break;

            }
        }

    }
}
