using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Poco;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration
{
    /// <summary>
    /// 调用保存前事件参数对象
    /// </summary>
    public class BeforeSaveEventArgs : CancelEventArgs
    {
        /// <summary>
        /// 保存前的业务对象
        /// </summary>
        public IEnumerable<DynamicObject> DataEntitys { get; set; }

        ///// <summary>
        ///// 操作选项
        ///// </summary>
        //public Dictionary<string, object> Option { get; set; }

        /// <summary>
        /// 外部数据
        /// </summary>
        public JArray ExternalDatas { get; set; }

        /// <summary>
        /// 操作结果
        /// </summary>
        public IOperationResult Result { get; set; }
    }
}
