using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration
{
    /// <summary>
    /// 同步数据至慕思中台的基类插件
    /// </summary>
    public abstract class AbstractSyncDataToMuSiPlugIn : ISyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 上下文
        /// </summary>
        protected UserContext UserContext { get; set; }

        /// <summary>
        /// 表单对象
        /// </summary>
        protected HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 当前同步对象映射配置
        /// </summary>
        protected DynamicObject BillMappingObject { get; set; }

        /// <summary>
        /// 网关
        /// </summary>
        [InjectProperty]
        protected IHttpServiceInvoker Gateway { get; set; }

        /// <summary>
        /// 初始化插件上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hForm"></param>
        /// <param name="billMapObject"></param>
        public void InitializePlugIn(UserContext userCtx, HtmlForm hForm, DynamicObject billMapObject)
        {
            this.UserContext = userCtx;
            this.HtmlForm = hForm;
            this.BillMappingObject = billMapObject;
        }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {

        }

        /// <summary>
        /// 向第三方系统发送请求前事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void BeforeSendEvent(BeforeSendEventArgs e)
        {

        }

        /// <summary>
        /// 来源单据打包前事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void BeforePackSourceBill(BeforePackSourceBillEventArgs e)
        {

        }

        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {

        }

        /// <summary>
        /// 来源单据打包后事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void AfterPackSourceBill(AfterPackSourceBillEventArgs e)
        {

        }

        /// <summary>
        /// 向第三方系统发送请求后，解析第三方接口返回值事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void ParseResult(ParseResultEventArgs e)
        {

        }

        /// <summary>
        /// 向第三方系统发送请求后事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void AfterSendEvent(AfterSendEventArgs e)
        {

        }

        /// <summary>
        /// 获取引用对象实体
        /// </summary>
        /// <param name="maps"></param>
        /// <param name="formId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public DynamicObject GetReferenceDataEntity(Dictionary<string, Dictionary<string, DynamicObject>> maps,
            string formId, string id)
        {
            if (id.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }

            if (maps == null)
            {
                return this.UserContext.LoadBizDataById(formId, id);
            }

            if (!maps.TryGetValue(formId, out var dic))
            {
                dic = new Dictionary<string, DynamicObject>(StringComparer.OrdinalIgnoreCase);
                maps[formId] = dic;
            }

            if (!dic.TryGetValue(id, out var obj))
            {
                obj = this.UserContext.LoadBizDataById(formId, id);
                dic[id] = obj;
            }

            return obj;
        }
    }
}
