using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Utils;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.SchedulerTask
{
    public class MuSiMQEnqueueHelper
    {

        /// <summary>
        /// 计划同步任务参数
        /// </summary>
        protected MuSiMQEnqueueTaskParamter JobParameter { get; set; }

        protected ILogServiceEx LogService { get; set; }

        protected IDBService DBService { get; set; }

        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 比较字段集合
        /// </summary>
        protected List<HtmlField> CompareFields { get; set; }
        protected UserContext UserContext { get; set; }

        /// <summary>
        /// 消息队列用户上下文（用于区分主库）
        /// </summary>
        protected UserContext MQContext { get; set; }

        /// <summary>
        /// （异步）主动推送数据去MQ时入口，会检查数据是否变动，如符合推送条件则会推送
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlFormId"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="pluginid">计划任务对应的fpluginid</param>
        public Task PushDataToMQAsync(UserContext userCtx, string htmlFormId, DynamicObject[] dataEntitys, string pluginid)
        {
            return Task.Run(() =>
            {
                PushDataToMQ(userCtx, htmlFormId, dataEntitys, pluginid);
            });
        }
        /// <summary>
        /// （同步）主动推送数据去MQ时入口，会检查数据是否变动，如符合推送条件则会推送
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlFormId"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="pluginid">计划任务对应的fpluginid</param>
        public void PushDataToMQ(UserContext userCtx, string htmlFormId, DynamicObject[] dataEntitys, string pluginid)
        {
            if (dataEntitys == null || !dataEntitys.Any()) return;
            this.UserContext = userCtx;
            this.MQContext = userCtx.CreateSlaveDBContext(true, userCtx.TopCompanyId);
            this.MetaModelService = this.MQContext.Container.GetService<IMetaModelService>();
            this.DBService = this.MQContext.Container.GetService<IDBService>();
            this.LogService = this.MQContext.Container.GetService<ILogServiceEx>();
            var formMeta = this.MetaModelService.LoadFormModel(this.MQContext, htmlFormId);

            this.CompareFields = GetCompareFields(formMeta, this.GetCompareFieldIds(pluginid));

            if (this.CompareFields.IsNullOrEmpty() || !this.CompareFields.Any())
            {
                return;
            }
            var companyGrps = dataEntitys.GroupBy(s => Convert.ToString(s["fmainorgid"]));

            var mqsyncrecordMeta = this.MetaModelService.LoadFormModel(this.MQContext, "ms_mqsyncrecord");
            var mqsyncrecordDt = mqsyncrecordMeta.GetDynamicObjectType(this.MQContext);
            var dm = this.MQContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.MQContext, mqsyncrecordDt);

            var prepareSaveDataService =
                this.MQContext.Container.GetService<IPrepareSaveDataService>();

            foreach (var companyGrp in companyGrps)
            {
                string companyId = companyGrp.Key;

                var bizObjIds = companyGrp.Select(s => Convert.ToString(s["id"])).ToList();
                var enqueuedNewestBizObjs = GetEnqueuedNewestBizObjs(formMeta, bizObjIds);
                var enqueueingBizObjs = this.MQContext.LoadBizDataById(formMeta.Id, bizObjIds);

                List<DynamicObject> savingMQSyncRecords = new List<DynamicObject>();

                foreach (var enqueueingBizObj in enqueueingBizObjs)
                {
                    var bizObjId = Convert.ToString(enqueueingBizObj["id"]);
                    var enqueuedNewestBizObj =
                        enqueuedNewestBizObjs.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(bizObjId));

                    if (CanEnqueue(formMeta, enqueueingBizObj, enqueuedNewestBizObj))
                    {
                        var record = new DynamicObject(mqsyncrecordDt);

                        record["fbizformid"] = formMeta.Id;
                        record["fbizobjno"] = enqueueingBizObj[formMeta.NumberFldKey];
                        record["fbizobjid"] = enqueueingBizObj["id"];
                        record["fcreatedate"] = DateTime.Now;
                        record["fdescription"] = "";
                        record["fcompanyid"] = companyId;
                        //默认排队中
                        record["fsyncstatus"] = "0";
                        record["fsnapshot"] = enqueueingBizObj.ToJson();

                        savingMQSyncRecords.Add(record);
                    }
                }

                if (savingMQSyncRecords.Any())
                {
                    prepareSaveDataService.PrepareDataEntity(this.MQContext, mqsyncrecordMeta, savingMQSyncRecords.ToArray(), OperateOption.Create());

                    dm.Save(savingMQSyncRecords);
                }
            }
        }

        /// <summary>
        /// 获取指定计划任务对应的比较字段参数标识
        /// </summary>
        /// <param name="htmlFormId">formId</param>
        /// <returns></returns>
        public string GetCompareFieldIds(string pluginid)
        {
            var ftaskparameter = this.MQContext.ExecuteDynamicObject($"select top 1 ftaskparameter from t_bas_task where fmainorgid='{this.UserContext.TopCompanyId}'and fpluginid='{pluginid}'", null);
            if (ftaskparameter != null && ftaskparameter.Any())
            {
                MuSiMQEnqueueTaskParamter result = Convert.ToString(ftaskparameter.First()["ftaskparameter"])?.FromJson<MuSiMQEnqueueTaskParamter>();
                return result?.fcomparefieldids;
            }
            return string.Empty;
        }

        /// <summary>
        /// 获取比较字段集合
        /// </summary>
        /// <param name="formMeta"></param>
        /// <returns></returns>
        private List<HtmlField> GetCompareFields(HtmlForm formMeta, string compareFieldIds)
        {
            var fldIds = compareFieldIds?.SplitKey(",");

            List<HtmlField> flds = new List<HtmlField>();

            foreach (var fldId in fldIds)
            {
                var fld = formMeta.GetField(fldId);
                if (fld != null && !flds.Contains(fld))
                {
                    flds.Add(fld);
                }
            }

            return flds;
        }
        /// <summary>
        /// 获取已在队列中的最新的业务对象
        /// </summary>
        /// <param name="formMeta"></param>
        /// <param name="bizObjIds"></param>
        /// <returns></returns>
        private List<DynamicObject> GetEnqueuedNewestBizObjs(HtmlForm formMeta, List<string> bizObjIds)
        {
            using (var tran = MQContext.CreateTransaction())
            {
                var tmpTableName = this.DBService.CreateTempTableWithDataList(this.MQContext, bizObjIds,false);

                try
                {
                    string sql = $@"
                            select t0.fid as id, t0.fbizformid, t0.fbizobjid, t0.fsnapshot from t_ms_mqsyncrecord t0
                            inner join 
                            (
                                select max(fid) as fid from t_ms_mqsyncrecord t1 
                                where exists(select 1 from {tmpTableName} TMPXX where TMPXX.fid=t1.fbizobjid)
                                and t1.fbizformid='{formMeta.Id}'
                                group by t1.fbizobjid
                            ) tmp on t0.fid=tmp.fid
                            ";
                    var enqueuedBizObjs = this.MQContext.ExecuteDynamicObject(sql, new List<SqlParam>());

                    tran.Complete();

                    var jArray = new JArray();

                    foreach (var bizObj in enqueuedBizObjs)
                    {
                        string fsnapshot = Convert.ToString(bizObj["fsnapshot"]);
                        if (fsnapshot.IsNullOrEmptyOrWhiteSpace())
                        {
                            fsnapshot = "{}";
                        }

                        var jObj = JObject.Parse(fsnapshot);

                        jArray.Add(jObj);
                    }

                    var dt = formMeta.GetDynamicObjectType(this.MQContext);
                    List<DynamicObject> lstDataEntities = new List<DynamicObject>();
                    var dcSerializer = this.MQContext.Container.GetService<IDynamicSerializer>();
                    dcSerializer.Sync(dt, lstDataEntities, jArray, neglectPkid: false);

                    return lstDataEntities;
                }
                catch (Exception ex)
                {
                    this.LogService?.Error("加入慕思消息队列计划任务快照还原失败", ex);

                    return new List<DynamicObject>();
                }
                finally
                {
                    this.DBService.DeleteTempTableByName(this.MQContext, tmpTableName, true);
                }
            }
        }
        /// <summary>
        /// 能否加入队列
        /// </summary>
        /// <param name="newDataEntity"></param>
        /// <param name="oldDataEntity"></param>
        /// <returns></returns>
        private bool CanEnqueue(HtmlForm formMeta, DynamicObject newDataEntity, DynamicObject oldDataEntity)
        {
            if (oldDataEntity == null) return true;

            if (this.CompareFields.IsNullOrEmpty()) return false;

            // 判断单据头
            if (CanEnqueueWithHead(formMeta.HeadEntity, newDataEntity, oldDataEntity))
            {
                return true;
            }

            // 判断单据体、子单据体
            foreach (var entryEntity in formMeta.EntryList)
            {
                if (CanEnqueueWithEntry(entryEntity, newDataEntity, oldDataEntity))
                {
                    return true;
                }
            }

            return false;
        }
        private bool CanEnqueueWithHead(HtmlHeadEntity headEntity, DynamicObject newDataEntity, DynamicObject oldDataEntity)
        {
            // 判断单据头
            var headFlds = this.CompareFields.Where(s => s.EntityKey.EqualsIgnoreCase(headEntity.Id));
            foreach (var fld in headFlds)
            {
                if (!CompareFieldValue(fld, newDataEntity, oldDataEntity))
                {
                    return true;
                }
            }

            return false;
        }

        private bool CanEnqueueWithEntry(HtmlEntryEntity entryEntity, DynamicObject newDataEntity, DynamicObject oldDataEntity)
        {
            var entryFlds = this.CompareFields.Where(s => s.EntityKey.EqualsIgnoreCase(entryEntity.Id));
            if (entryFlds.Count() == 0)
            {
                return false;
            }

            var newEntryRows = (DynamicObjectCollection)newDataEntity[entryEntity.PropertyName];
            var oldEntryRows = (DynamicObjectCollection)oldDataEntity[entryEntity.PropertyName];
            // 单据体数量不同，直接返回true
            if (newEntryRows.Count != oldEntryRows.Count)
            {
                return true;
            }

            foreach (var newEntryRow in newEntryRows)
            {
                var enId = Convert.ToString(newEntryRow["id"]);
                var oldEntryRow =
                    oldEntryRows.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(enId));
                if (oldEntryRow == null)
                {
                    return true;
                }

                foreach (var fld in entryFlds)
                {
                    if (!CompareFieldValue(fld, newEntryRow, oldEntryRow))
                    {
                        return true;
                    }
                }

                foreach (var subEntryEntity in entryEntity.SubEntryList)
                {
                    if (CanEnqueueWithEntry(subEntryEntity, newEntryRow, oldEntryRow))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private bool CompareFieldValue(HtmlField fld, DynamicObject newDataEntity, DynamicObject oldDataEntity)
        {
            // 数字字段特殊比较
            if (fld is HtmlDecimalField)
            {
                var newVal = Convert.ToDecimal(newDataEntity[fld.PropertyName]);
                var oldVal = Convert.ToDecimal(oldDataEntity[fld.PropertyName]);

                return newVal == oldVal;
            }
            else
            {
                var newVal = Convert.ToString(newDataEntity[fld.PropertyName]).Trim();
                var oldVal = Convert.ToString(oldDataEntity[fld.PropertyName]).Trim();

                return newVal.EqualsIgnoreCase(oldVal);
            }
        }
    }
}
