using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.SchedulerTask
{
    /// <summary>
    /// 同步数据到慕思
    /// </summary>
    [InjectService]
    [TaskSvrId("transfertomusi")]
    [Caption("同步数据到慕思")]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class TransferToMuSiTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 计划任务配置时的参数设置界面
        /// </summary>
        public override string ParamFormId => "si_transfertomusitaskparam";

        /// <summary>
        /// 计划同步任务参数
        /// </summary>
        protected Dictionary<string, object> JobParameter { get; set; }
        /// <summary>
        /// 消息队列用户上下文（用于区分主库）
        /// </summary>
        protected UserContext MQContext { get; set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        protected ILogServiceEx LogService { get; set; }

        /// <summary>
        /// 执行数据打包同步分发逻辑
        /// </summary>
        protected override async Task DoExecute()
        {
            this.LogService = this.UserContext.Container.GetService<ILogServiceEx>();

            this.JobParameter = Convert.ToString(this.TaskObject.Parameter).FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>();
            var allFieldMapBillObjs = this.LoadAllLinkFieldMapBills();

            this.MQContext = this.UserContext.CreateSlaveDBContext(true, this.UserContext.TopCompanyId);

            //todo:
            //1. 根据当前调度方案找到集成参数，并且读取所有待同步的业务映射对象
            //2. 构建CommonListDTO，调用标准系统数据同步操作，以便于手工从列表上点击同步达到相同效果
            //3. 采用流式分批机制来调用系统操作（并且按业务对象顺序）
            //4. 业务对象顺序，尽量自动做到自动分析前后，防止死循环嵌套

            foreach (var fieldMapObj in allFieldMapBillObjs)
            {
                var myFormId = fieldMapObj["fmyobjectid"] as string;
                var formMeta = this.MetaModelService.LoadFormModel(this.UserContext, myFormId);
                try
                {
                    await SyncData(fieldMapObj, formMeta);
                }
                catch (BusinessException ex)
                {
                    this.WriteLog(ex.Message, formMeta);
                }
                catch (Exception ex)
                {
                    this.WriteLog($"业务对象同步出现未知错误：{ex.Message}, stacktrace:{ex.StackTrace}", formMeta);
                    this.LogService.Error("同步数据到慕思计划任务失败", ex);
                }
            }
        }

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="fieldMapObj"></param>
        /// <param name="formMeta"></param>
        /// <returns></returns>
        private async Task SyncData(DynamicObject fieldMapObj, HtmlForm formMeta)
        {
            //todo:同步某个特定业务对象的数据至对方系统
            string tempFilterString = GetTempFilterString(formMeta);

            // 获取需要同步的经销商
            var agents = GetSyncAgents(fieldMapObj, formMeta, tempFilterString);
            // 总记录数
            long lTotalBills = 0;
            decimal progress = 0;
            decimal allAgentCount = agents.Count;
            decimal index = 1;

            this.WriteLog($"需要同步经销商数({allAgentCount})", formMeta);

            foreach (var agent in agents)
            {
                lTotalBills += await SyncDataByAgent(agent, fieldMapObj, formMeta, tempFilterString);

                progress = index / allAgentCount;

                this.WriteLog($"[{agent["fnumber"]}/{agent["fname"]}]数据同步完成(总进度{progress:P})", formMeta);
                this.SaveLog();

                index++;

                this.SetTaskProgress(progress, $"[{agent["fnumber"]}/{agent["fname"]}]数据同步完成！");
            }

            this.SetTaskProgress(99, "全部数据同步完成！");

            if (lTotalBills == 0)
            {
                this.WriteLog($"没有数据需要同步，可能原因是待同步对象自上次同步以来没有更新！", formMeta);
            }
            else
            {
                this.WriteLog($"本次共同步{lTotalBills}条数据！", formMeta);
            }
        }

        /// <summary>
        /// 根据经销商同步数据
        /// </summary>
        /// <param name="agent">经销商</param>
        /// <param name="fieldMapObj"></param>
        /// <param name="formMeta"></param>
        /// <returns></returns>
        private async Task<long> SyncDataByAgent(DynamicObject agent, DynamicObject fieldMapObj, HtmlForm formMeta, string tempFilterString)
        {
            string agentId = agent["id"].ToString();
            // 日志title
            string title = $"[{agent["fnumber"]}/{agent["fname"]}]";

            var agentCtx = this.UserContext.CreateAgentDBContext(agentId);

            SqlBuilderParameter sqlpara = new SqlBuilderParameter(agentCtx, formMeta);
            sqlpara.SelectedFieldKeys.Add("test");
            sqlpara.PageCount = -1;
            sqlpara.PageIndex = -1;
            sqlpara.QueryUserFieldOnly = false;
            sqlpara.IsDistinct = true;
            sqlpara.NoIsolation = false;
            sqlpara.NoColorSetting = true;
            sqlpara.ReadDirty = true;
            sqlpara.IsShowForbidden = true;

            //修改时间
            var modifyDateFld = formMeta.GetField("fmodifydate");
            var musiSyncDateFld = formMeta.GetField("fmusisyncdate");
            //通过参数兼容历史同步逻辑，如果计划任务参数不开启同步临时表，则IdLst 为空。
            var fusetemp = Convert.ToBoolean(this.JobParameter?.GetValue("fusetemp", false));

            if (!fusetemp)
            {
                if (modifyDateFld != null && musiSyncDateFld != null)
                {
                    sqlpara.AppendFilterString($"({modifyDateFld.FieldName}>{musiSyncDateFld.FieldName} or {musiSyncDateFld.FieldName} is null or fmusisyncstatus<>'02' )");
                }
            }
            else
            {
                sqlpara.AppendFilterString($"( 1=2 {tempFilterString} )");
            }

            var filterString = this.JobParameter.GetString("filter");
            if (!filterString.IsNullOrEmptyOrWhiteSpace())
            {
                sqlpara.AppendFilterString($"({filterString})");
            }

            if (!fieldMapObj["ffilterstring"].IsNullOrEmptyOrWhiteSpace())
            {
                sqlpara.AppendFilterString($"({fieldMapObj["ffilterstring"]})");
            }

            //考虑计划任务中的同步日期范围限定
            if (formMeta.ElementType == (int)HtmlElementType.HtmlForm_BillForm)
            {
                var dateField = formMeta.GetField(formMeta.BizDateFldKey);
                if (dateField != null)
                {
                    var startBillDate = Convert.ToString(this.JobParameter?.GetValue("startbilldate", null));
                    var endBillDate = Convert.ToString(this.JobParameter?.GetValue("endbilldate", null));
                    DateTime dtStart, dtEnd;
                    if (DateTime.TryParse(startBillDate, out dtStart))
                    {
                        sqlpara.AppendFilterString($"{dateField.Id}>=@startBillDate");
                        sqlpara.AddParameter(new SqlParam("startBillDate", System.Data.DbType.DateTime, dtStart.Date));
                    }
                    if (DateTime.TryParse(endBillDate, out dtEnd))
                    {
                        sqlpara.AppendFilterString($"{dateField.Id}<@endBillDate");
                        sqlpara.AddParameter(new SqlParam("endBillDate", System.Data.DbType.DateTime, dtEnd.Date));
                    }
                }
            }

            //取得处理批量的参数，默认为100单一个批次
            var procBatchSize = this.JobParameter.GetInt("batchSize");
            if (procBatchSize == 0)
            {
                procBatchSize = 100;
            }

            var querObj = QueryService.BuilQueryObject(sqlpara);

            long lTotalBills = 0; // 累计数量
            int batchTimes = 1;     // 批次
            int allCount = 0;   // 总数量

            using (var reader = this.DBService.ExecuteReader(agentCtx, querObj.AllCountSql, sqlpara.DynamicParams))
            {
                if (reader.Read())
                {
                    allCount = reader.GetInt32(0);
                }
            }

            if (allCount == 0)
            {
                this.WriteLog($"{title}无数据同步(总进度100%)", formMeta);
                this.SaveLog();

                return 0;
            }

            List<SelectedRow> lstBatchRows = new List<SelectedRow>();

            var bizObjs = agentCtx.ExecuteDynamicObject(querObj.SqlNoPage, sqlpara.DynamicParams);

            foreach (var bizOjb in bizObjs)
            {
                var pkId = bizOjb["fbillhead_id"] as string;
                if (pkId.IsEmptyPrimaryKey()) continue;

                lstBatchRows.Add(new SelectedRow()
                {
                    PkValue = pkId,
                });

                if ((lstBatchRows.Count % procBatchSize) == 0 && lstBatchRows.Count > 0)
                {
                    var result = await this.DoSyncData(agentCtx, lstBatchRows, formMeta, fieldMapObj);
                    this.Result.MergeResult(result);

                    if (result.IsSuccess)
                    {
                        this.WriteLog($"{title}第{batchTimes}批成功：" + result.ToString(), formMeta);
                    }
                    else
                    {
                        this.WriteLog($"{title}第{batchTimes}批失败：" + result.ToString(), formMeta);
                    }

                    lTotalBills += lstBatchRows.Count;

                    decimal progress = lTotalBills * 1.0M / allCount;

                    this.WriteLog($"{title}第{batchTimes}批完成(总进度{progress:P})", formMeta);
                    this.SaveLog();

                    lstBatchRows.Clear();
                    batchTimes++;
                }
            }

            if (lstBatchRows.Any())
            {
                var result = await this.DoSyncData(agentCtx, lstBatchRows, formMeta, fieldMapObj);
                this.Result.MergeResult(result);

                if (result.IsSuccess)
                {
                    this.WriteLog($"{title}第{batchTimes}批成功：" + result.ToString(), formMeta);
                }
                else
                {
                    this.WriteLog($"{title}第{batchTimes}批失败：" + result.ToString(), formMeta);
                }

                lTotalBills += lstBatchRows.Count;

                this.WriteLog($"{title}第{batchTimes}批完成(总进度100.00%)", formMeta);
                this.SaveLog();

                lstBatchRows.Clear();
            }

            return lTotalBills;
        }

        /// <summary>
        /// 获取有数据要同步的经销商
        /// </summary>
        /// <returns></returns>
        private List<DynamicObject> GetSyncAgents(DynamicObject fieldMapObj, HtmlForm formMeta, string tempFilterString)
        {
            string bizFormFitler = " 1=1 ";

            var filterString = this.JobParameter.GetString("filter");
            if (!filterString.IsNullOrEmptyOrWhiteSpace())
            {
                bizFormFitler += $" and ({filterString})";
            }

            if (!fieldMapObj["ffilterstring"].IsNullOrEmptyOrWhiteSpace())
            {
                bizFormFitler += $" and ({fieldMapObj["ffilterstring"]})";
            }

            //补偿机制：对于修改时间小于同步时间 但是需要同步的数据的同步补偿

            //修改时间
            var modifyDateFld = formMeta.GetField("fmodifydate");
            var musiSyncDateFld = formMeta.GetField("fmusisyncdate");
            var fusetemp = Convert.ToBoolean(this.JobParameter?.GetValue("fusetemp", false));

            if (!fusetemp)
            {
                if (modifyDateFld != null && musiSyncDateFld != null)
                {
                    bizFormFitler += $" and ({modifyDateFld.FieldName}>{musiSyncDateFld.FieldName} or {musiSyncDateFld.FieldName} is null or fmusisyncstatus<>'02' )";
                }
            }
            else
            {
                bizFormFitler += $" and (1=2 {tempFilterString})";
            }

            // 排除全量测试组织
            var testCompanyIds = this.UserContext.GetTestCompanyIds();
            if (testCompanyIds.Any())
            {
                bizFormFitler += $" and fmainorgid not in ({testCompanyIds.JoinEx(",", true)})";
            }

            string sql = $@"
select o.fid as id, o.fnumber, o.fname 
from t_bas_organization as o with(nolock)
where o.ftopcompanyid='{this.UserContext.TopCompanyId}' and o.forgtype='4'
and o.fid in (
    select fmainorgid 
    from {formMeta.BillHeadTableName} as t0 with(nolock) where {bizFormFitler}
)";

            return this.UserContext.ExecuteDynamicObject(sql, new List<SqlParam>()).ToList();
        }

        /// <summary>
        /// 执行数据同步逻辑
        /// </summary>
        /// <param name="lstBatchData"></param>
        /// <param name="fieldMapObj"></param>
        /// <param name="bizForm"></param>
        /// <returns></returns>
        private async Task<IOperationResult> DoSyncData(UserContext userCtx, IEnumerable<SelectedRow> lstBatchData, HtmlForm bizForm, DynamicObject fieldMapObj)
        {
            CommonListDTO listDto = new CommonListDTO()
                .SetSelectedRows(lstBatchData)
                .SetFormId(bizForm.Id)
                .SetOperationNo("synctomusi")
                .SetTaskId(this.TaskId)
                .SetOption("__fieldMapObj__", fieldMapObj)
                .SetOption("extAppId", fieldMapObj["fextappid"]);

            Dictionary<string, object> dctHeader = new Dictionary<string, object>();
            dctHeader["X-AppId"] = userCtx.AppId;
            dctHeader["X-CompanyId"] = userCtx.Company;

            //交给本地操作行为执行，目的是具体同步的代码与按钮操作代码进行复用
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var objResult = await Task.Run(() =>
                gateway.InvokeLocal<object>(userCtx, listDto, Enu_HttpMethod.Post, dctHeader));
            if (objResult is DynamicDTOResponse)
            {
                var procResult = (objResult as DynamicDTOResponse).OperationResult;
                this.LogService.Info("慕思同步数据：" + procResult.ToString());

                return procResult;
            }

            return new OperationResult { SimpleMessage = "操作失败！" };
        }

        private List<string> GetIdLst(HtmlForm formMeta)
        {
            //获取从库上下文，在从库中找到数据
            var userCtx = this.MQContext;

            var models = new List<string>();

            var fusetemp = Convert.ToBoolean(this.JobParameter?.GetValue("fusetemp", false));
            if (!fusetemp) return models;

            long maxId = 0;
            using (var reader = userCtx.ExecuteReader($"select ISNULL(max(fid), 0) from T_MS_MQSYNCINGRECORD with(nolock) where fbizformid='{formMeta.Id}'",
                new List<SqlParam>()))
            {
                if (reader.Read())
                {
                    maxId = reader.GetInt64(0);
                }
            }
            if (maxId == 0)
            {
                return new List<string>();
            }

            SqlBuilderParameter para = new SqlBuilderParameter(userCtx, formMeta);
            para.SelectedFieldKeys.Add("fmainorgid");
            para.PageCount = -1;
            para.PageIndex = -1;
            para.QueryUserFieldOnly = false;
            para.IsDistinct = true;
            para.NoIsolation = true;
            para.NoColorSetting = true;
            para.ReadDirty = true;
            para.IsShowForbidden = true;

            // 排除全量测试组织
            var testCompanyIds = userCtx.GetTestCompanyIds();
            if (testCompanyIds.Any())
            {
                para.AppendFilterString($"fmainorgid not in ({testCompanyIds.JoinEx(",", true)})");
            }

            para.AppendFilterString(
                $"exists(select 1 from T_MS_MQSYNCINGRECORD s with(nolock) where s.fid<={maxId} and s.fbizformid='{formMeta.Id}' and s.fbizobjid=t0.fid)");

            var queryObject = QueryService.BuilQueryObject(para);

            var dynObjs = userCtx.ExecuteDynamicObject(queryObject.Sql, para.DynamicParams).Select(o => Convert.ToString(o["fbillhead_id"]))?.Distinct()?.ToList();

            // 清空数据
            userCtx.ExecuteDynamicObject($"delete from T_MS_MQSYNCINGRECORD where fid <= {maxId} and fbizformid='{formMeta.Id}'", new List<SqlParam>());

            if (dynObjs.Any()) models = dynObjs;

            return models;
        }

        private string GetTempFilterString(HtmlForm formMeta)
        {
            string tempFilterString = "";
            var tempIds = GetIdLst(formMeta);

            if (tempIds.Any())
            {
                if (tempIds.IsGreaterThan(50))
                {
                    //用临时表关联查询
                    var tempTable = this.DBService.CreateTempTableWithDataList(this.UserContext, tempIds);
                    tempFilterString = $" or exists (select 1 from {tempTable} as tt with(nolock) where tt.fid = t0.fid) ";
                }
                else
                {
                    tempFilterString = $" or t0.fid in ({tempIds.JoinEx(",", true)}') ";
                }
            }

            return tempFilterString;
        }

        /// <summary>
        /// 加载所有业务映射
        /// </summary>
        /// <returns></returns>
        private IEnumerable<DynamicObject> LoadAllLinkFieldMapBills()
        {
            // 指定外部应用
            var onlyExtAppId = this.JobParameter?.GetString("extAppId");
            var syncBizFormId = this.JobParameter?.GetString("syncBizFormId");
            if (onlyExtAppId.IsNullOrEmptyOrWhiteSpace() || syncBizFormId.IsNullOrEmptyOrWhiteSpace()) return new DynamicObject[] { };

            var muSiBizObjMapService = this.UserContext.Container.GetService<IMuSiBizObjMapService>();
            var syncBizForm = this.MetaModelService.LoadFormModel(this.UserContext, syncBizFormId);

            return muSiBizObjMapService.GetBizObjMaps(this.UserContext, syncBizForm, onlyExtAppId,
                Enu_MuSiSyncDir.CurrentToMuSi, Enu_MuSiSyncTimePoint.SyncTimer);
        }
    }
}
