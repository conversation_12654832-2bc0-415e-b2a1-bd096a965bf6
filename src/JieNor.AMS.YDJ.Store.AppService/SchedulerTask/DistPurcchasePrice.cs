using JieNor.Framework;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    /// 二级分销采购价目计算
    /// </summary>
    [InjectService]
    [TaskSvrId("distpurchaseprice")]
    [Caption("二级分销采购价目计算（在总部执行）")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class DistPurchasePrice : AbstractScheduleWorker
    {
        protected override async Task DoExecute()
        {
            var ctx = this.UserContext;
            try
            {
                var priceCalculateService = ctx.Container.GetService<DistPurchasePriceService>();
                //分组查询
                var orgStrSql = $@"select distinct fid ,fnumber,fname from t_bas_agent with(nolock) where fisreseller='1' and fforbidstatus='0'";
                using (DataTable grpOrgs = this.DBService.ExecuteDataTable(ctx, orgStrSql))
                {
                    foreach (DataRow item in grpOrgs.Rows)
                    {
                        try
                        {
                            var fmainorgid = (string)item["fid"];
                            if (!fmainorgid.IsNullOrEmptyOrWhiteSpace())
                            {
                                var ctxX = ctx.CreateAgentDBContext(fmainorgid);
                                var result = priceCalculateService?.PriceCalculate(ctxX, this.Option);
                                this.WriteLog($"经销商 {item["fnumber"]}({item["fname"]}) ：{result.SimpleMessage}");
                            }
                        }
                        catch (Exception ex) {
                            this.WriteLog($"经销商 {item["fnumber"]}({item["fname"]})二级分销采购价目计算报错 ：{ex.Message}");
                        }
                       
                    }
                }
            }
            catch (Exception e)
            {
                this.WriteLog("二级分销采购价目计算！" + e.Message);
            }
        }
    }
}
