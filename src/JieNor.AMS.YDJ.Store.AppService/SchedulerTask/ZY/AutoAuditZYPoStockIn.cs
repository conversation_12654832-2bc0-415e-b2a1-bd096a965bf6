using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.E3;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask.ZY
{
    /// <summary>
    /// 【直营】一件代发采购入库单自动审核
    /// </summary>
    [InjectService]
    [TaskSvrId("autoauditzypostockin")]
    [Caption("【直营】一件代发采购入库单自动审核（在总部执行）")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class AutoAuditZYPoStockIn : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {

            var userCtx = this.UserContext;
            var tmpTableName = string.Empty;

            try
            {
                //根据采购入库单找源头的销售合同，通过销售合同下推出库单
                // 获取符合条件的销售合同
                E3Commom e3Commom = new E3Commom(userCtx);
                var result = e3Commom.AutoAuditZYPoStockIn();
                if (result.SrvData != null)
                {
                    this.WriteLog("【直营】一件代发采购入库单自动审核（在总部执行）：" + result.SrvData.ToJson());
                    var srvdata = result.SrvData.ToJson().FromJson<Dictionary<string, List<string>>>();
                    srvdata.TryGetValue("successNo", out List<string> successNo);
                    srvdata.TryGetValue("failNo", out List<string> failNo);
                    srvdata.TryGetValue("message", out List<string> updMessage);
                    this.WriteLog("成功对象编码：" + string.Join(",", successNo));
                    this.WriteLog("失败对象编码：" + string.Join(",", failNo));
                    this.WriteLog("执行消息内容：" + string.Join(",", updMessage));
                    //this.WriteLog(result.SimpleMessage);
                }
                if (!result.IsSuccess)
                    return;
                var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
                var htmlForm = metaModelService.LoadFormModel(this.UserContext, "ms_pulldata");

                var dt = htmlForm.GetDynamicObjectType(this.UserContext);
                var dm = this.UserContext.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserContext, dt);

                List<DynamicObject> dynObjs = new List<DynamicObject>();
                if (result != null && result.IsSuccess)
                {
                    if (result.SrvData != null)
                    {
                        var srvdata = result.SrvData.ToJson().FromJson<Dictionary<string, List<string>>>();
                        srvdata.TryGetValue("successNo", out List<string> successNo);
                        srvdata.TryGetValue("failNo", out List<string> failNo);
                        this.WriteLog("成功对象编码！" + string.Join(",", successNo));
                        this.WriteLog("失败对象编码！" + string.Join(",", failNo));
                        foreach (var dataItem in failNo)
                        {
                            var dynObj = (DynamicObject)dt.CreateInstance();
                            string json = result.ToJson();
                            dynObj["fjson"] = json;
                            dynObj["fmd5"] = SecurityUtil.HashString(json);
                            dynObj["fdatatype"] = "autoauditzypostockin";
                            dynObj["fcreatedate"] = BeiJingTime.Now;
                            dynObj["ftranid"] = "";
                            dynObj["fbizformid"] = "";
                            dynObj["fbizobjno"] = dataItem;
                            dynObj["fbizobjid"] = "";
                            dynObj["fopstatus"] = "0";
                            dynObjs.Add(dynObj);
                        }
                    }
                }
                if (dynObjs.Any())
                {
                    var prepareSaveDataService = this.UserContext.Container.GetService<IPrepareSaveDataService>();
                    prepareSaveDataService.PrepareDataEntity(this.UserContext, htmlForm, dynObjs.ToArray(), this.Option);
                    this.UserContext.SaveBizData(htmlForm.Id, dynObjs);
                }
            }
            catch (Exception e)
            {
                this.WriteLog("一件代发采购入库单自动审核异常！" + e.Message);
            }
        }

        /// <summary>
        /// 获取符合条件的采购入库单
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetPendingPurchaseOrdersAsync()
        {
            string sql = "select T_STK_POSTOCKIN.fid,T_STK_POSTOCKIN.fmainorgid from T_STK_POSTOCKIN with(nolock) where fpiecesendtag=1 and fstatus<>'E' and fcancelstatus=0";
            return this.DBService.ExecuteDynamicObject(this.UserContext, sql);
        }
    }
}
