using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    /// 针对采购订单变更中，总部变更状态=终审，或驳回。需要审核采购订单变更单；
    /// 注意该计划任务在总部执行（会自动按经销商进行处理），不要放经销商组织进行（上万的经销商，调度出问题不好排查）
    /// </summary>
    [InjectService]
    [TaskSvrId("agentautoaudit")]
    [Caption("招商下发经销商自动审核（在总部执行）")]
    [Browsable(false)]
    public class AgentAutoAudit : AbstractScheduleWorker
    {
        protected IAgentService AgentService { get; set; }

        protected IHttpServiceInvoker Gateway { get; set; }

        protected ILogServiceEx LogService { get; set; }

        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {
            this.Gateway = this.UserContext.Container.GetService<IHttpServiceInvoker>();
            this.AgentService = this.UserContext.Container.GetService<IAgentService>();
            this.LogService = this.UserContext.Container.GetService<ILogServiceEx>();

            this.ResetSysAdminUserContext();

            AutoAudit();

            Initialize();
        }

        private void Initialize()
        {
            // 搜索未初始化的
            var notInitializeAgents = this.UserContext.LoadBizDataByFilter("bas_agent", " fstatus='E' and fisnotprerole='1'");

            if (notInitializeAgents == null || !notInitializeAgents.Any())
            {
                this.WriteLog("未找到相关需要初始化的经销商数据");
                return;
            }

            int batchSize = 5;
            int loopTimes = Convert.ToInt32(Math.Ceiling(notInitializeAgents.Count * 1.0 / 5));

            for (int i = 0; i < loopTimes; i++)
            {
                var agents = notInitializeAgents.Skip(i * batchSize).Take(batchSize);

                try
                {
                    var result = this.AgentService.Initialize(this.UserContext, agents);

                    this.WriteLog("经销商 {0} 初始化成功：{1}".Fmt(agents.Select(s => $"{s["fnumber"]}-{s["fname"]}").JoinEx(",", false), result));
                }
                catch (Exception ex)
                {
                    this.WriteLog("经销商 {0} 初始化失败：{1}".Fmt(agents.Select(s => $"{s["fnumber"]}-{s["fname"]}").JoinEx(",", false), ex.Message + Environment.NewLine + ex.StackTrace));

                    this.LogService.Error($"经销商 {agents.Select(s => $"{s["fnumber"]}-{s["fname"]}").JoinEx(",", false)} 初始化失败", ex);
                }
            }
        }

        private void AutoAudit()
        {
            var agentObjs = this.UserContext.LoadBizDataByFilter("bas_agent", " fstatus = 'D' AND fcreatorid ='sysadmin'");
            if (agentObjs == null || agentObjs.Count == 0)
            {
                this.WriteLog("未找到相关需要自动审核的经销商数据");
                return;
            }

            var option = new Dictionary<string, object>
            {
                // 不做初始化
                {"__IgnoreInitialize__", true}
            };

            foreach (var agent in agentObjs)
            {
                var result = this.Gateway.InvokeBillOperation(this.UserContext, "bas_agent", new[] { agent }, "audit", option);
                //this.Result.MergeResult(result);
                this.WriteLog("经销商 {0} 审核：{1}".Fmt(agent["fnumber"], result));
            }

            if (agentObjs.Any())
            {
                // 更新《主经销商配置表》的【企业微信主体经销商】 
                this.AgentService.UpdateQYWXMainAgent(this.UserContext, agentObjs);

                // 更新《门店》（解决拉取顺序先后有问题时，互相反写）
                var agentIds = agentObjs.Select(s => Convert.ToString(s["id"])).ToList();
                var stores = this.UserContext.LoadBizDataByNo("bas_store", "fagentid", agentIds);
                var storeService = this.UserContext.Container.GetService<IStoreService>();
                storeService.UpdateAgentAndDeliver(this.UserContext, stores);

                if (stores.Any())
                {
                    this.UserContext.SaveBizData("bas_store", stores);
                }

                this.AgentService.RewriteCrmDistributor(this.UserContext, agentObjs);

                this.AgentService.UpdateCompany(this.UserContext, agentObjs);
            }
        }

        /// <summary>
        /// 重置为系统管理员
        /// </summary>
        private void ResetSysAdminUserContext()
        {
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = this.UserContext.Product;
            session.Company = this.UserContext.Company;
            session.BizOrgId = this.UserContext.Company;
            session.TopCompanyId = this.UserContext.TopCompanyId;
            session.ParentCompanyId = this.UserContext.ParentCompanyId;
            session.Companys = this.UserContext.Companys.ToList();
            session.Id = this.UserContext.Id;

            this.UserContext.SetUserSession(session);
        }
    }
}