using System.Collections.Generic;

namespace JieNor.AMS.YDJ.Store.AppService.Clients.Ewc.Config
{
    /// <summary>
    /// 客户配置信息
    /// </summary>
    public class CustomerConfig
    {
        /// <summary>
        /// 客户列表
        /// </summary>
        public List<CustomerInfo> Customers { get; set; }
    }

    /// <summary>
    /// 客户信息
    /// </summary>
    public class CustomerInfo
    {
        /// <summary>
        /// 客户标识
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 客户编码
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 客户密钥
        /// </summary>
        public string SecretKey { get; set; }

        /// <summary>
        /// 企业标识
        /// </summary>
        public string CompanyId { get; set; }
    }
}