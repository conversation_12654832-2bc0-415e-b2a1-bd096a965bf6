using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Validation.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单变更申请校验器
    /// </summary>
    public class InitiateChangeApplyValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 业务表单模型
        /// </summary>
        private HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            this.HtmlForm = formInfo;

            //检查已经正在变更申请中，不允许再次发起。
            CheckPurChaseOrderApplyChgStatus(userCtx, dataEntities);

            // 检查二级采购订单的数据状态是否是审核状态
            CheckPurchaseOrderStatusIsAuditStatus(userCtx, dataEntities);

            CheckPurchaseOrderLeaveOneOrderStatus(userCtx, dataEntities);

            //检查采购订单已经关闭，就不允许发起变更申请。
            CheckPurchaseOrderStatusCloseStatus(userCtx, dataEntities);

            return this.Result;
        }

        /// <summary>
        /// 检查采购订单关联的一级销售合同.【数据状态】是否等于“已审核”
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void CheckPurchaseOrderLeaveOneOrderStatus(UserContext userCtx, DynamicObject[] dataEntities)
        {
            var leaveOneOrderNo = dataEntities.Select(x=>Convert.ToString(x["fonelvoderno"])).ToList();

            if (leaveOneOrderNo != null && leaveOneOrderNo.Any())
            {
                var distinctLeaveOneOrderIdList = leaveOneOrderNo.Distinct().ToList();

                var parentCompanyId = userCtx.ParentCompanyId;

                var parentAgentAdminCtx = userCtx.CreateAdminDbContext(parentCompanyId);

                var sqlStr = $" fbillno in ({string.Join(",", distinctLeaveOneOrderIdList.Select(x=>$"'{x}'"))}) and fmainorgid = '{parentCompanyId}' and fcancelstatus = '0' ";

                var loadLeaveOneOrderTempDys = parentAgentAdminCtx.LoadBizBillHeadDataByACLFilter("ydj_order",sqlStr, "fbillno,fstatus,fchangestatus");

                if (loadLeaveOneOrderTempDys != null && loadLeaveOneOrderTempDys.Any())
                {
                    foreach (var dataEntity in dataEntities)
                    {
                        var leaveOneOrderNumber = Convert.ToString(dataEntity["fonelvoderno"]);
                        //如果一级销售合同号为空
                        if (leaveOneOrderNumber.IsNullOrEmptyOrWhiteSpace())
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"当前采购订单还未提交至一级经销商，请检查!",
                                DataEntity = dataEntity,
                            });
                        }
                        else
                        {
                            var findLeaveOneOrderDy = loadLeaveOneOrderTempDys.FirstOrDefault(x=>Convert.ToString(x["fbillno"]).Equals(leaveOneOrderNumber));

                            if (findLeaveOneOrderDy != null)
                            {
                                var orderNo = Convert.ToString(findLeaveOneOrderDy["fbillno"]);
                                var status = Convert.ToString(findLeaveOneOrderDy["fstatus"]);
                                if (!status.EqualsIgnoreCase("E"))
                                {
                                    this.Result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $"一级合同{orderNo}未审核，暂无法发起变更申请，请核查！",
                                        DataEntity = dataEntity,
                                    });
                                }

                                //变更状态 '0':'正常','1':'变更中','2':'变更完成','3':'变更已提交'
                                var changeStatus = Convert.ToString(findLeaveOneOrderDy["fchangestatus"]);
                                if (changeStatus.EqualsIgnoreCase("1"))
                                {
                                    this.Result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $"一级合同{orderNo}在变更中，暂无法发起变更申请，请核查！",
                                        DataEntity = dataEntity,
                                    });
                                }
                            }
                            else
                            {
                                this.Result.Errors.Add(new ValidationResultEntry()
                                {
                                    ErrorMessage = $"当前采购订单没有找到对应的一级销售合同，请检查!",
                                    DataEntity = dataEntity,
                                });
                            }
                        }

                    }
                }
                else
                {
                    foreach (var dataEntity in dataEntities)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"当前采购订单没有找到对应的一级销售合同，请检查!",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
            else
            {
                foreach (var dataEntity in dataEntities)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"当前采购订单还未提交至一级经销商，请检查!",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 已经正在变更申请中，不允许再次发起。
        /// 当采购订单存在【变更申请状态】=变更中 的采购订单变更申请单时。
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void CheckPurChaseOrderApplyChgStatus(UserContext userCtx, DynamicObject[] dataEntities)
        {
            var purchaseOrderIdList = dataEntities.Select(x=>Convert.ToString(x["id"])).ToList();

            var sqlStr = $" fsourceid in ({string.Join(",", purchaseOrderIdList.Select(x=>$"'{x}'"))}) and fmainorgid = '{userCtx.Company}' ";

            var loadPurChaseOrderApplyChgTempDys = userCtx.LoadBizBillHeadDataByACLFilter("ydj_purchaseorderapply_chg", sqlStr,"fbillno,fsourceid,fchangeapplystatus,fsourcenumber");

            if (loadPurChaseOrderApplyChgTempDys != null && loadPurChaseOrderApplyChgTempDys.Any())
            {
                foreach (var dataEntity in dataEntities)
                {
                    var purchaseOrderId = Convert.ToString(dataEntity["id"]);

                    var findChangIngStatusDy = loadPurChaseOrderApplyChgTempDys.FirstOrDefault(x =>
                        Convert.ToString(x["fsourceid"]).Equals(purchaseOrderId) &&
                        Convert.ToString(x["fchangeapplystatus"]).Equals("01"));

                    //当采购订单存在【变更申请状态】=变更中 的采购订单变更申请单时。
                    if (findChangIngStatusDy != null)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"当前采购订单正在变更申请中，请等待一级经销商处理，谢谢！",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }

        }





        /// <summary>
        /// 检查二级采购订单的数据状态是否是审核状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void CheckPurchaseOrderStatusIsAuditStatus(UserContext userCtx, DynamicObject[] dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var status = Convert.ToString(dataEntity["fstatus"]);
                if (status.EqualsIgnoreCase("E"))
                {
                    continue;
                }
                else
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"采购订单不等于“已审核”，无法发起变更申请，请核查！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 检查采购订单已经关闭，就不允许发起变更申请。
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void CheckPurchaseOrderStatusCloseStatus(UserContext userCtx, DynamicObject[] dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                //单据头关闭状态
                var headCloseStatus = Convert.ToString(dataEntity["fclosestatus"]);

                //自动关闭状态
                var autoCloseStatus = Convert.ToString(Convert.ToInt32(CloseStatus.Auto));

                //手动关闭状态
                var manualCloseStatus = Convert.ToString(Convert.ToInt32(CloseStatus.Manual));

                //整单关闭状态
                var wholeCloseStatus = Convert.ToString(Convert.ToInt32(CloseStatus.Whole));

                //当采购订单【单据头.关闭状态】为以下几个时：整单关闭、手工关闭、自动关闭。
                if (headCloseStatus.Equals(autoCloseStatus) || headCloseStatus.Equals(manualCloseStatus) ||
                    headCloseStatus.Equals(wholeCloseStatus))
                {
                    

                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"采购订单已经关闭，不允许发起变更申请，请检查",
                        DataEntity = dataEntity,
                    });
                }
            }
        }
    }
}