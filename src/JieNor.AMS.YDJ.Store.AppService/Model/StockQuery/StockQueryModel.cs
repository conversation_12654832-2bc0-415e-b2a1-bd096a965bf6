using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Model.StockQuery
{
    public class StockQueryModel
    {
        /// <summary>
        /// 总条数
        /// </summary>
        public int totalcount { get; set; }
        /// <summary>
        /// 总页数
        /// </summary>
        public int totalpage { get; set; }
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<MaterialList> list { get; set; }
    }
    public class MaterialList
    {
        /// <summary>
        /// 商品id
        /// </summary>
        public object fmaterial { get; set; }
        /// <summary>
        /// 商品类别
        /// </summary>
        public object fcategory { get; set; }
        /// <summary>
        /// 基本单位
        /// </summary>
        public object funit { get; set; }
        /// <summary>
        /// 库存单位
        /// </summary>
        public object fstockunit { get; set; }
        /// <summary>
        /// 包装规则
        /// </summary>
        public string fpackrule { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public object fstorehouse { get; set; }
        /// <summary>
        /// 仓位
        /// </summary>
        public object fstorelocation { get; set; }
        /// <summary>
        /// 库存状态
        /// </summary>
        public object fstockstatus { get; set; }
        /// <summary>
        /// 辅助属性
        /// </summary>
        public object fattrinfo { get; set; }
        /// <summary>
        /// 定制说明
        /// </summary>
        public string fcustomdesc { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        public string flotno { get; set; }
        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string fmtono { get; set; }
        /// <summary>
        /// 货主类型
        /// </summary>
        public object fownertype { get; set; }
        /// <summary>
        /// 货主
        /// </summary>
        public string fownerid { get; set; }
        /// <summary>
        /// 基本单位数量
        /// </summary>
        public decimal fqty { get; set; }
        /// <summary>
        /// 库存量
        /// </summary>
        public decimal fstockqty { get; set; }
        /// <summary>
        /// 基本单位可用量
        /// </summary>
        public decimal fusableqty { get; set; }
        /// <summary>
        /// 可用量
        /// </summary>
        public decimal fstockusableqty { get; set; }
        /// <summary>
        /// 基本单位预留量
        /// </summary>
        public decimal freserveqty { get; set; }
        /// <summary>
        /// 预留量
        /// </summary>
        public decimal fstockreserveqty { get; set; }
        /// <summary>
        /// 基本单位在途量
        /// </summary>
        public decimal fintransitqty { get; set; }
        /// <summary>
        /// 在途量
        /// </summary>
        public decimal fstockintransitqty { get; set; }

        public List<BarCodeInfo> barcodeinfos { get; set; }
    }
    public class BarCodeInfo
    {
        /// <summary>
        /// 条码
        /// </summary>
        public string barcode { get; set; }
        /// <summary>
        /// 仓库id
        /// </summary>
        public object store { get; set; }
        /// <summary>
        /// 仓位id
        /// </summary>
        public object storelocation { get; set; }
        /// <summary>
        /// 扫描人
        /// </summary>
        public object scanuser { get; set; }
        /// <summary>
        /// 扫描时间
        /// </summary>
        public string scantime { get; set; }
    }

    public class BarCodeModel
    {
        /// <summary>
        /// 条码
        /// </summary>
        public string fbarcode { get; set; }
        /// <summary>
        /// 打包类型
        /// </summary>
        public string fpackagetype { get; set; }
        /// <summary>
        /// 当前状态
        /// </summary>
        public string fnowstatus { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        public string fstore { get; set; }
        /// <summary>
        /// 仓位
        /// </summary>
        public string fstorelocation { get; set; }

        /// <summary>
        /// 总包数
        /// </summary>
        public int fpackcount { get; set; }
        /// <summary>
        /// 包序号
        /// </summary>
        public int fpackindex { get; set; }
        /// <summary>
        /// 商品信息
        /// </summary>
        public List<MaterialInfo> materialinfos { get; set; }
    }

    public class MaterialInfo
    {
        /// <summary>
        /// 商品
        /// </summary>
        public object fmaterial { get; set; }
        /// <summary>
        /// 基本单位
        /// </summary>
        public object funit { get; set; }
        /// <summary>
        /// 库存单位
        /// </summary>
        public object fstockunit { get; set; }
        /// <summary>
        /// 辅助属性
        /// </summary>
        public object fattrinfo { get; set; }
        /// <summary>
        /// 定制说明
        /// </summary>
        public string fcustomdesc { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        public string flotno { get; set; }
        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string fmtono { get; set; }
        /// <summary>
        /// 货主类型
        /// </summary>
        public string fownertype { get; set; }
        /// <summary>
        /// 货主
        /// </summary>
        public string fownerid { get; set; }
        /// <summary>
        /// 规格说明
        /// </summary>
        public string fmtrlmodel { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        public object fbrandid { get; set; }
        /// <summary>
        /// 序列
        /// </summary>
        public object fseriesid { get; set; }
        /// <summary>
        /// 套件组合号
        /// </summary>
        public string fsuitcombnumber { get; set; }
        /// <summary>
        /// 配件组合号
        /// </summary>
        public string fpartscombnumber { get; set; }
        /// <summary>
        /// 沙发组合号
        /// </summary>
        public string fsofacombnumber { get; set; }
        /// <summary>
        /// 库存数量
        /// </summary>
        public decimal fstockqty { get; set; }

    }
}
