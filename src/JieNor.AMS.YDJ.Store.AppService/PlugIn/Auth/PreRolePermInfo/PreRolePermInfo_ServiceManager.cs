using JieNor.AMS.YDJ.Store.AppService.Plugin.MP;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Permission;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    public partial class PreRolePermInfo
    {
        /// <summary>
        /// 经销商角色--服务主管数据权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<DataRowAuthInfo>>> GetAgentDataRowAuth_ServiceManager()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct fbizobjid ,ffiltertype,'"' + fbizobjid + '|' + ffiltertype + '|' + fexpress + '|'+ fdesc + '|' + fsuperiorperm  + '|' + fbdfldfilter  + '|' + fbdfldfilter_txt    +  '",'  
            from t_sec_roledatarowacl 
            where fcompanyid = '企业id' and froleId = '角色id'
            order by  fbizobjid ,ffieldid
            */
            var fldItems = new List<string>()
            {
            };
            var result = GetDataRowPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 经销商角色--服务主管字段权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<FieldAuthInfo>>> GetAgentRoleFldPerm_ServiceManager()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct fbizobjid ,ffieldid,'"' + fbizobjid + ';' + ffieldid + ';' + ffieldname + ';'+ fvisible + ';' + fmodify   +  '",'  
            from t_sec_rolefieldacl  where fcompanyid = '企业id' and froleId = '角色id'
            order by  fbizobjid ,ffieldid
            */
            var fldItems = new List<string>()
            {
"stk_inventoryverify;fbugunitprice;采购单价(折前);0;1",
"stk_inventoryverify;fpkbuyamount;盈亏采购总额;0;1",

"ydj_purchaseorder;factrefundamount;实退金额;0;1",
"ydj_purchaseorder;famount;金额;0;1",
"ydj_purchaseorder;fconfirmamount;待确认金额;0;1",
"ydj_purchaseorder;fdealamount;货品原值;0;1",
"ydj_purchaseorder;fdealamount_e;成交金额;0;1",
"ydj_purchaseorder;fdealprice;成交单价;0;1",
"ydj_purchaseorder;fdepthdiscount;深度护理折扣;0;1",
"ydj_purchaseorder;fdistamount;折扣额;0;1",
"ydj_purchaseorder;fdistamount_e;折扣额;0;1",
"ydj_purchaseorder;fdistrate;折扣率;0;1",
"ydj_purchaseorder;fexpenserebate;费用支持返利;0;1",
"ydj_purchaseorder;ffbillamount;成交金额;0;1",
"ydj_purchaseorder;fnewdiscount;新品折扣;0;1",
"ydj_purchaseorder;fotherdiscount;其他折扣;0;1",
"ydj_purchaseorder;fpaidamount;已结算金额;0;1",
"ydj_purchaseorder;fpayamount;待结算金额;0;1",
"ydj_purchaseorder;fpinvoicemount;开票金额;0;1",
"ydj_purchaseorder;fprice;采购单价;0;1",
"ydj_purchaseorder;frefundamount;申请退货金额;0;1",
"ydj_purchaseorder;fsapdiscount;SAP折扣总额;0;1",
"ydj_purchaseorder;fsettleamount;已付金额;0;1",
"ydj_purchaseorder;fstardiscount;星级折扣/上市折扣;0;1",
"ydj_purchaseorder;ftaxprice;含税出厂价;0;1",

"ydj_purchaseorder_chg;factrefundamount;实退金额;0;1",
"ydj_purchaseorder_chg;famount;金额;0;1",
"ydj_purchaseorder_chg;famount_chg;金额(新);0;1",
"ydj_purchaseorder_chg;fconfirmamount;待确认金额;0;1",
"ydj_purchaseorder_chg;fdealamount;货品原值;0;1",
"ydj_purchaseorder_chg;fdealamount_chg;货品原值;0;1",
"ydj_purchaseorder_chg;fdealamount_e;成交金额;0;1",
"ydj_purchaseorder_chg;fdealamount_e_chg;成交金额(新);0;1",
"ydj_purchaseorder_chg;fdealprice;成交单价;0;1",
"ydj_purchaseorder_chg;fdealprice_chg;成交单价(新);0;1",
"ydj_purchaseorder_chg;fdepthdiscount;深度护理折扣;0;1",
"ydj_purchaseorder_chg;fdistamount;折扣额;0;1",
"ydj_purchaseorder_chg;fdistamount_e_chg;折扣额(新);0;1",
"ydj_purchaseorder_chg;fdistrate;折扣率;0;1",
"ydj_purchaseorder_chg;fdistrate_chg;折扣(新);0;1",
"ydj_purchaseorder_chg;fexpenserebate;费用支持返利;0;1",
"ydj_purchaseorder_chg;ffbillamount;成交金额;0;1",
"ydj_purchaseorder_chg;ffbillamount_chg;订单金额(新);0;1",
"ydj_purchaseorder_chg;fnewdiscount;新品折扣;0;1",
"ydj_purchaseorder_chg;fotherdiscount;其他折扣;0;1",
"ydj_purchaseorder_chg;fpaidamount;已结算金额;0;1",
"ydj_purchaseorder_chg;fpayamount;待结算金额;0;1",
"ydj_purchaseorder_chg;fpayamount_chg;待结算金额(新);0;1",
"ydj_purchaseorder_chg;fpinvoicemount;开票金额;0;1",
"ydj_purchaseorder_chg;fprice;采购单价;0;1",
"ydj_purchaseorder_chg;frefundamount;申请退货金额;0;1",
"ydj_purchaseorder_chg;fsapdiscount;SAP折扣总额;0;1",
"ydj_purchaseorder_chg;fsettleamount;已付金额;0;1",
"ydj_purchaseorder_chg;fstardiscount;星级折扣/上市折扣;0;1",
"ydj_purchaseorder_chg;ftaxprice;含税出厂价;0;1",

"rpt_stocksynthesize;fcostamt;成本;0;1",
"rpt_stocksynthesize;fcostprice;成本价;0;1",

"stk_inventorybalance;fcostamt;成本;0;1",
"stk_inventorybalance;fcostprice;成本价;0;1",
"stk_inventorybalance;finicostamt;期初总成本;0;1",
"stk_inventorybalance;finicostprice;期初成本价;0;1",

"stk_inventorylist;fcostamt;成本;0;1",
"stk_inventorylist;fcostprice;成本价;0;1",

"stk_inventorytransfer;fcostamt;总成本(加权平均);0;1",
"stk_inventorytransfer;fcostprice;单位成本(加权平均);0;1",

"stk_inventoryverify;fcostamt;总成本(加权平均);0;1",
"stk_inventoryverify;fcostprice;单位成本(加权平均);0;1",
"stk_inventoryverify;fbizpdprice;盘点单价;0;1",
"stk_inventoryverify;fpdprice;基本单位盘点单价;0;1",
"stk_inventoryverify;fbizprice;账存单价;0;1",
"stk_inventoryverify;fprice;基本单位账存单价;0;1",
"stk_inventoryverify;famount;账存金额;0;1",
"stk_inventoryverify;fpdamount;盘点金额;0;1",
"stk_inventoryverify;fpyamount;盘盈金额;0;1",
"stk_inventoryverify;fpkamount;盘亏金额;0;1",

"stk_otherstockin;fcostamt;总成本(加权平均);0;1",
"stk_otherstockin;fcostprice;单位成本(加权平均);0;1",

"stk_otherstockout;fcostamt;总成本(加权平均);0;1",
"stk_otherstockout;fcostprice;单位成本(加权平均);0;1",

"stk_postockin;fcostamt;总成本(加权平均);0;1",
"stk_postockin;fcostprice;单位成本(加权平均);0;1",
"stk_postockin;famount;成交金额;0;1",
"stk_postockin;fpoamount;金额;0;1",
"stk_postockin;fpoprice;采购单价;0;1",
"stk_postockin;fprice;成交单价;0;1",

"stk_postockreturn;fcostamt;总成本(加权平均);0;1",
"stk_postockreturn;fcostprice;单位成本(加权平均);0;1",
"stk_postockreturn;famount;成交金额;0;1",
"stk_postockreturn;fpoamount;金额;0;1",
"stk_postockreturn;fpoprice;采购单价;0;1",
"stk_postockreturn;fprice;成交单价;0;1",

"stk_sostockout;fcostamt;总成本(加权平均);0;1",
"stk_sostockout;fcostprice;单位成本(加权平均);0;1",
"stk_sostockout;fpurfacamount;采购折前金额;0;1",
"stk_sostockout;fpurfacprice;采购单价（折前）;0;1",

"stk_sostockreturn;fcostamt;总成本(加权平均);0;1",
"stk_sostockreturn;fcostprice;单位成本(加权平均);0;1",

"ydj_order;fcost;成本金额;0;1",
"ydj_order;fcostprice;成本价;0;1",
"ydj_order;ftransfercostprice;成本价格;0;1",
"ydj_order;ftargetagentamount;发货结算金额;0;1",
"ydj_order;fotherfee;其他费用;0;1",
"ydj_order;fpurfacamount;采购折前金额;0;1",
"ydj_order;fpurfacprice;采购单价（折前）;0;1",

"ydj_order_chg;fcost;成本金额;0;1",
"ydj_order_chg;fcostprice;成本价;0;1",
"ydj_order_chg;fpurfacamount;采购折前金额;0;1",
"ydj_order_chg;fpurfacamount_chg;采购折前金额（新）;0;1",
"ydj_order_chg;fpurfacprice;采购单价（折前）;0;1",
"ydj_order_chg;fpurfacprice_chg;采购单价（折前）（新）;0;1",
"ydj_order_chg;ftransfercostprice;成本价格;0;1",

"ydj_transferorderapply;fcostprice;成本价格;0;1",

"ydj_purchaseprice;fpurprice;采购价;0;1",

"ydj_stockoutreport;fcost;单位成本;0;1",
"ydj_stockoutreport;fcostamt;总成本;0;1",

"rpt_orderdetail;fcostprice;单位成本;0;1",
"rpt_orderdetail;fcost;总成本;0;1",

"rpt_orderbalance;fcostprice;单位成本;0;1",
"rpt_orderbalance;fcost;总成本;0;1",

"rpt_stocksynthesize;fpurfacprice;采购单价（折前）;0;1",
"rpt_stocksynthesize;fpurfacamount;采购折前金额;0;1",
"rpt_stocksynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_stocksynthesize;fpurdealamount;采购折后金额;0;1",
"rpt_stocksynthesize;funifysaleprice;统一零售价（折前）;0;1",
"rpt_stocksynthesize;funifysaleamount;统一零售金额;0;1",
"rpt_stocksynthesize;fsellprice;经销价（折前）;0;1",
"rpt_stocksynthesize;fsellamount;经销金额;0;1",
"rpt_stocksynthesize;freprice;分销价（折前）;0;1",
"rpt_stocksynthesize;freamount;分销金额;0;1",
"rpt_stocksynthesize;fterprice;终端零售价（折前）;0;1",
"rpt_stocksynthesize;fteramount;终端零售金额;0;1",

"rpt_pricesynthesize;fpurfacprice;采购单价（折前）;0;1",
"rpt_pricesynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_pricesynthesize;freprice;分销价（折前）;0;1",
"rpt_pricesynthesize;funitcostprice;单位成本;0;1",

"rpt_stockageanalysis;fpurfacprice;采购单价(折前);0;1",
"rpt_stockageanalysis;fpurfacamount;采购折前金额;0;1",
"rpt_stockageanalysis;fpurdealprice;采购单价（折后）;0;1",
"rpt_stockageanalysis;fpurdealamount;采购折后金额;0;1",
"rpt_stockageanalysis;freprice;分销价（折前）;0;1",
"rpt_stockageanalysis;freamount;分销金额;0;1",
"rpt_stockageanalysis;funitcostprice;单位成本价;0;1",
"rpt_stockageanalysis;funitcostamount;总成本;0;1",

"rpt_orderbalance;fpurfacprice;采购单价(折前);0;1",
"rpt_orderbalance;fpurfacamount;采购折前金额;0;1",
"rpt_orderbalance;fpurdealprice;采购单价（折后）;0;1",
"rpt_orderbalance;fpurdealamount;采购折后金额;0;1",

"ydj_stockoutreport;fpurfacprice;采购单价(折前);0;1",
"ydj_stockoutreport;fpurfacamount;采购折前金额;0;1",
"ydj_stockoutreport;fpurdealprice;采购单价（折后）;0;1",
"ydj_stockoutreport;fpurdealamount;采购折后金额;0;1",

"rpt_purchasedetail;fdealamount;金额;0;1",
"rpt_purchasedetail;fdealprice;单价;0;1",

"rpt_stockdetail;fincostamt;收入总成本(加权平均);0;1",
"rpt_stockdetail;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stockdetail;fpoprice;采购单价(折前);0;1",
"rpt_stockdetail;fpopriceafter;采购单价(折后);0;1",
"rpt_stockdetail;fstockintotal;收入总成本(折前);0;1",
"rpt_stockdetail;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stockdetail;fstockouttotal;发出总成本(折前);0;1",
"rpt_stockdetail;fstockouttotalafter;发出总成本(折后);0;1",

"rpt_stocksummary;fincostamt;收入总成本(加权平均);0;1",
"rpt_stocksummary;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stocksummary;fpoprice;采购单价(折前);0;1",
"rpt_stocksummary;fpopriceafter;采购单价(折后);0;1",
"rpt_stocksummary;fstockintotal;收入总成本(折前);0;1",
"rpt_stocksummary;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stocksummary;fstockouttotal;发出总成本(折前);0;1",
"rpt_stocksummary;fstockouttotalafter;发出总成本(折后);0;1",

            };
            var result = GetFieldPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 经销商角色--服务主管权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：权限项 </returns>
        public static List<Tuple<string, List<string>>> GetAgentRolePermItem_ServiceManager()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct t0.fbizobjid ,fpermititemid , '"' + fbizobjid + ';' + fpermititemid + '",'
            from t_sec_rolefuncacl t0
            where t0.froleId = '角色id'  and fallow = '1'
            order by t0.fbizobjid ,fpermititemid
            */
            var permItems = new List<string>()
            {
                "dashboard_mytask;fw_view",
                "ste_afterfeedback;fw_view",
                "stk_sostockout;fw_view",
                "sys_datawidgetlist;fw_view",
                "sys_datawidgettype;fw_view",
                "ydj_customer;fw_modify",
                "ydj_customer;fw_new",
                "ydj_customer;fw_view",
                "ydj_customer;fw_viewrecord",
                "ydj_dept;fw_view",
                "ydj_order;fw_view",
                "ydj_product;fw_view",
                "ydj_service;fw_delete",
                "ydj_service;fw_modify",
                "ydj_service;fw_new",
                "ydj_service;fw_operatelog",
                "ydj_service;fw_view",
                "ydj_service;fw_viewrecord",
                "ydj_service;ydj_cancelservice",
                "ydj_service;ydj_push",
                "ydj_service;ydj_servicevist",
                "ydj_service;ydj_setstatus01",
                "ydj_service;ydj_setstatus02",
                "ydj_service;ydj_setstatus03",
                "ydj_service;ydj_setstatus04",
                "ydj_serviceitem;fw_view",
                "ydj_staff;fw_view",
                "ydj_vist;fw_view",
                "ms_markingassistant;fw_view",
                "ms_crmdistributor;fw_view",
                "ser_engineer;fw_view",
                "ydj_city;fw_view",
                "bas_deliver;fw_view",
                "ydj_followerrecord;fw_view",
                "ydj_followerrecord;fw_new",
                "ydj_vist;fw_new",
                "ydj_vist;fw_view",
                "dashboard_service;fw_view",
                "dashboard_service;mobile_view",
                "dashboard_service;myself",
                "dashboard_service;mydepartment",
                "dashboard_service;mycompany",
                "dashboard_feedback;fw_view",
                "dashboard_feedback;mobile_view",
                "dashboard_feedback;myself",
                "dashboard_feedback;mydepartment",
                "dashboard_feedback;mycompany",
            };

            var result = GetPermItems(permItems);

            return result;
        }

        /// <summary>
        /// 经销商角色--服务主管小程序权限
        /// </summary>
        /// <returns>列表： {ftabbar};{fgroup};{fmenuid};{fmenuname}</returns>
        public static List<MPTabbarModel> GetAgentRoleMPMenu_ServiceManager()
        {
            /*
             select '"' + ftabbar + ';' + fgroup + ';' + fmenuid + ';' + fmenuname  +  '",'  
            from t_mp_rolemenu t0  with(nolock) 
            where t0.froleId=@roleId and fisallow=1
             */

            var menuItems = new List<string>()
            {
                "工作台;销售管理;720326618328993798;商品图册",
                "工作台;销售管理;720565024824889424;我的客户",
                "工作台;订单管理;720565024824889427;合同单",
                "工作台;服务管理;807915975830802438;服务单",
                "工作台;服务管理;807915975830802439;售后反馈单",
                "首页;快捷入口;918587513702187020;录客户",
                "首页;快捷入口;918587603690979334;录服务单",
                "首页;快捷入口;918587639514529798;录售后单",
                "首页;快捷入口;1039134275344138246;服务预约",
            };


            var mpMenu = GetMPTabbars(menuItems);

            return mpMenu;
        }
    }
}
