using JieNor.AMS.YDJ.Store.AppService.Plugin.MP;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Permission;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    public partial class PreRolePermInfo
    {
        /// <summary>
        /// 经销商角色--老板数据权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<DataRowAuthInfo>>> GetAgentDataRowAuth_Boss()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct fbizobjid ,ffiltertype,'"' + fbizobjid + '|' + ffiltertype + '|' + fexpress + '|'+ fdesc + '|' + fsuperiorperm  + '|' + fbdfldfilter  + '|' + fbdfldfilter_txt    +  '",'  
            from t_sec_roledatarowacl 
            where fcompanyid = '企业id' and froleId = '角色id'
            order by  fbizobjid ,ffieldid
            */
            var fldItems = new List<string>()
            {
                "",
            };
            var result = GetDataRowPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 经销商角色--老板字段权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<FieldAuthInfo>>> GetAgentRoleFldPerm_Boss()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct fbizobjid ,ffieldid,'"' + fbizobjid + ';' + ffieldid + ';' + ffieldname + ';'+ fvisible + ';' + fmodify  +   '",'  
            from t_sec_rolefieldacl  where fcompanyid = '企业id' and froleId = '角色id'
            order by  fbizobjid ,ffieldid
            */
            var fldItems = new List<string>()
            {
"rpt_pricesynthesize;fpurfacprice;采购单价（折前）;0;1",
"rpt_pricesynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_pricesynthesize;funitcostprice;单位成本(加权平均);0;1",
"rpt_purchasedetail;fdealamount;金额;0;1",
"rpt_purchasedetail;fdealprice;单价;0;1",

"rpt_stockageanalysis;fpurdealamount;采购折后金额;0;1",
"rpt_stockageanalysis;fpurdealprice;采购单价（折后）;0;1",
"rpt_stockageanalysis;fpurfacamount;采购折前金额;0;1",
"rpt_stockageanalysis;fpurfacprice;采购单价（折前）;0;1",
"rpt_stockageanalysis;funitcostamount;总成本;0;1",
"rpt_stockageanalysis;funitcostprice;单位成本（加权平均）;0;1",

"rpt_stockdetail;fincostamt;收入总成本(加权平均);0;1",
"rpt_stockdetail;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stockdetail;fpoprice;采购单价(折前);0;1",
"rpt_stockdetail;fpopriceafter;采购单价(折后);0;1",
"rpt_stockdetail;fstockintotal;收入总成本(折前);0;1",
"rpt_stockdetail;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stockdetail;fstockouttotal;发出总成本(折前);0;1",
"rpt_stockdetail;fstockouttotalafter;发出总成本(折后);0;1",

"rpt_stocksummary;fincostamt;收入总成本(加权平均);0;1",
"rpt_stocksummary;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stocksummary;fpoprice;采购单价(折前);0;1",
"rpt_stocksummary;fpopriceafter;采购单价(折后);0;1",
"rpt_stocksummary;fstockintotal;收入总成本(折前);0;1",
"rpt_stocksummary;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stocksummary;fstockouttotal;发出总成本(折前);0;1",
"rpt_stocksummary;fstockouttotalafter;发出总成本(折后);0;1",

"rpt_stocksynthesize;fcostamt;成本;0;1",
"rpt_stocksynthesize;fcostprice;成本价;0;1",
"rpt_stocksynthesize;fpurdealamount;采购折后金额;0;1",
"rpt_stocksynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_stocksynthesize;fpurfacamount;采购折前金额;0;1",
"rpt_stocksynthesize;fpurfacprice;采购单价（折前）;0;1",


"stk_inventorybalance;fcostamt;成本;0;1",
"stk_inventorybalance;fcostprice;成本价;0;1",
"stk_inventorybalance;finicostamt;期初总成本;0;1",
"stk_inventorybalance;finicostprice;期初成本价;0;1",

"stk_inventorylist;fcostamt;成本;0;1",
"stk_inventorylist;fcostprice;成本价;0;1",

"stk_inventorytransfer;fcostamt;总成本(加权平均);0;1",
"stk_inventorytransfer;fcostprice;单位成本(加权平均);0;1",

"stk_inventoryverify;fcostamt;总成本(加权平均);0;1",
"stk_inventoryverify;fcostprice;单位成本(加权平均);0;1",
"stk_inventoryverify;fbizpdprice;盘点单价;0;1",
"stk_inventoryverify;fpdprice;基本单位盘点单价;0;1",
"stk_inventoryverify;fbizprice;账存单价;0;1",
"stk_inventoryverify;fprice;基本单位账存单价;0;1",
"stk_inventoryverify;famount;账存金额;0;1",
"stk_inventoryverify;fpdamount;盘点金额;0;1",
"stk_inventoryverify;fpyamount;盘盈金额;0;1",
"stk_inventoryverify;fpkamount;盘亏金额;0;1",
"stk_inventoryverify;fbugunitprice;(折前);0;1",
"stk_inventoryverify;fpkbuyamount;盈亏采购总额;0;1",

"stk_otherstockin;fcostamt;总成本(加权平均);0;1",
"stk_otherstockin;fcostprice;单位成本(加权平均);0;1",

"stk_otherstockout;fcostamt;总成本(加权平均);0;1",
"stk_otherstockout;fcostprice;单位成本(加权平均);0;1",

"stk_postockin;fcostamt;总成本(加权平均);0;1",
"stk_postockin;fcostprice;单位成本(加权平均);0;1",
"stk_postockin;famount;成交金额;0;1",
"stk_postockin;fpoamount;金额;0;1",
"stk_postockin;fpoprice;采购单价;0;1",
"stk_postockin;fprice;成交单价;0;1",

"stk_postockreturn;fcostamt;总成本(加权平均);0;1",
"stk_postockreturn;fcostprice;单位成本(加权平均);0;1",
"stk_postockreturn;famount;成交金额;0;1",
"stk_postockreturn;fpoamount;金额;0;1",
"stk_postockreturn;fpoprice;采购单价;0;1",
"stk_postockreturn;fprice;成交单价;0;1",

"stk_sostockout;fcostamt;总成本(加权平均);0;1",
"stk_sostockout;fcostprice;单位成本(加权平均);0;1",
"stk_sostockout;fpurfacamount;采购折前金额;0;1",
"stk_sostockout;fpurfacprice;采购单价（折前）;0;1",

"stk_sostockreturn;fcostamt;总成本(加权平均);0;1",
"stk_sostockreturn;fcostprice;单位成本(加权平均);0;1",

"ydj_order;fcost;成本金额;0;1",
"ydj_order;fcostprice;成本价;0;1",
"ydj_order;fpurfacamount;采购折前金额;0;1",
"ydj_order;fpurfacprice;采购单价（折前）;0;1",
"ydj_order;ftransfercostprice;成本价格;0;1",

"ydj_order_chg;fcost;成本金额;0;1",
"ydj_order_chg;fcostprice;成本价;0;1",
"ydj_order_chg;fpurfacamount;采购折前金额;0;1",
"ydj_order_chg;fpurfacamount_chg;采购折前金额（新）;0;1",
"ydj_order_chg;fpurfacprice;采购单价（折前）;0;1",
"ydj_order_chg;fpurfacprice_chg;采购单价（折前）（新）;0;1",
"ydj_order_chg;ftransfercostprice;成本价格;0;1",

"ydj_purchaseorder;famount;金额;0;1",
"ydj_purchaseorder;fdealamount_e;成交金额;0;1",
"ydj_purchaseorder;fdealprice;成交单价;0;1",
"ydj_purchaseorder;fprice;采购单价;0;1",

"ydj_purchaseorder_chg;famount;金额;0;1",
"ydj_purchaseorder_chg;famount_chg;金额(新);0;1",
"ydj_purchaseorder_chg;fdealamount_e;成交金额;0;1",
"ydj_purchaseorder_chg;fdealamount_e_chg;成交金额(新);0;1",
"ydj_purchaseorder_chg;fdealprice;成交单价;0;1",
"ydj_purchaseorder_chg;fdealprice_chg;成交单价(新);0;1",
"ydj_purchaseorder_chg;fprice;采购单价;0;1",
"ydj_purchaseorder_chg;fprice_chg;单价(新);0;1",

"ydj_stockoutreport;fcost;单位成本;0;1",
"ydj_stockoutreport;fcostamt;总成本;0;1",

"rpt_orderdetail;fcostprice;单位成本;0;1",
"rpt_orderdetail;fcost;总成本;0;1",

"rpt_orderbalance;fcostprice;单位成本;0;1",
"rpt_orderbalance;fcost;总成本;0;1",
"rpt_orderbalance;fpurfacprice;采购单价(折前);0;1",
"rpt_orderbalance;fpurfacamount;采购折前金额;0;1",
"rpt_orderbalance;fpurdealprice;采购单价（折后）;0;1",
"rpt_orderbalance;fpurdealamount;采购折后金额;0;1",

"ydj_stockoutreport;fpurfacprice;采购单价(折前);0;1",
"ydj_stockoutreport;fpurfacamount;采购折前金额;0;1",
"ydj_stockoutreport;fpurdealprice;采购单价（折后）;0;1",
"ydj_stockoutreport;fpurdealamount;采购折后金额;0;1",

"ydj_transferorderapply;fcostprice;成本价格;0;1",
            };
            var result = GetFieldPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 经销商角色--老板权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：权限项 </returns>
        public static List<Tuple<string, List<string>>> GetAgentRolePermItem_Boss()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct t0.fbizobjid ,fpermititemid , '"' + fbizobjid + ';' + fpermititemid + '",'
            from t_sec_rolefuncacl t0
            where t0.froleId = '角色id'  and fallow = '1'
            order by t0.fbizobjid ,fpermititemid
            */
            var permItems = new List<string>()
            {
                "bas_billdataaccesscontrol;fw_view",
"bas_billnosetting;fw_view",
"bas_bizwarnscheme;fw_view",
"bas_deliver;fw_view",
"bas_msglog;fw_view",
"bas_priceadjust;fw_view",
"bas_printpaper;fw_view",
"bas_relatedinfo;fw_view",
"bas_store;fw_view",
"bas_storesysparam;fw_modify",
"bas_storesysparam;fw_new",
"bas_storesysparam;fw_view",
"bd_billnodedefine;fw_view",
"bd_billtype;fw_view",
"bd_enumdata;fw_view",
"bd_recordlist;fw_view",
"bpm_businessapproval;fw_view",
"bpm_flowinstance;fw_view",
"bpm_flowmsg;fw_view",
"bpm_flowproxy;fw_view",
"coo_incomedisburse;fw_view",
"dashboard_mytask;fw_view",
"im_notice;fw_view",
"mp_assignright;fw_allotrole",
"mp_assignright;fw_new",
"pur_systemparam;fw_modify",
"pur_systemparam;fw_new",
"pur_systemparam;fw_view",
"rpt_costpool;fw_export",
"rpt_costpool;fw_presetfilteredit",
"rpt_costpool;fw_print",
"rpt_costpool;fw_view",
"rpt_costpoolmx;fw_export",
"rpt_costpoolmx;fw_presetfilteredit",
"rpt_costpoolmx;fw_print",
"rpt_costpoolmx;fw_view",
"rpt_categorystocksummary;fw_view",
"rpt_categorystocksummary;fw_export",
"rpt_categorystocksummary;fw_presetfilteredit",
"rpt_categorystocksummary;fw_print",
"rpt_categorystockdetailmary;fw_view",
"rpt_categorystockdetailmary;fw_export",
"rpt_categorystockdetailmary;fw_presetfilteredit",
"rpt_categorystockdetailmary;fw_print",
"rpt_deliverlist;fw_export",
"rpt_deliverlist;fw_presetfilteredit",
"rpt_deliverlist;fw_print",
"rpt_deliverlist;fw_view",
"rpt_orderbalance;fw_export",
"rpt_orderbalance;fw_presetfilteredit",
"rpt_orderbalance;fw_print",
"rpt_orderbalance;fw_view",
"rpt_orderdetail;fw_export",
"rpt_orderdetail;fw_presetfilteredit",
"rpt_orderdetail;fw_print",
"rpt_orderdetail;fw_view",
"rpt_ordertrackingform;fw_export",
"rpt_ordertrackingform;fw_presetfilteredit",
"rpt_ordertrackingform;fw_print",
"rpt_ordertrackingform;fw_view",
"rpt_profitanalysis;fw_export",
"rpt_profitanalysis;fw_presetfilteredit",
"rpt_profitanalysis;fw_print",
"rpt_profitanalysis;fw_view",
"rpt_purchaseanalysis;fw_export",
"rpt_purchaseanalysis;fw_presetfilteredit",
"rpt_purchaseanalysis;fw_print",
"rpt_purchaseanalysis;fw_view",
"rpt_purchasedetail;fw_export",
"rpt_purchasedetail;fw_presetfilteredit",
"rpt_purchasedetail;fw_print",
"rpt_purchasedetail;fw_view",
"rpt_purchaseexecute;fw_export",
"rpt_purchaseexecute;fw_presetfilteredit",
"rpt_purchaseexecute;fw_print",
"rpt_purchaseexecute;fw_view",
"rpt_purchaseplan;fw_export",
"rpt_purchaseplan;fw_presetfilteredit",
"rpt_purchaseplan;fw_print",
"rpt_purchaseplan;fw_view",
"rpt_productcontrast;fw_view",
"rpt_productcontrast;fw_export",
"rpt_productcontrast;fw_presetfilteredit",
"rpt_productcontrast;fw_print",
"rpt_purchaseprice;fw_view",
"rpt_purchaseratesanalysis;fw_export",
"rpt_purchaseratesanalysis;fw_presetfilteredit",
"rpt_purchaseratesanalysis;fw_print",
"rpt_purchaseratesanalysis;fw_view",
"rpt_salarycommission;fw_export",
"rpt_salarycommission;fw_presetfilteredit",
"rpt_salarycommission;fw_print",
"rpt_salarycommission;fw_view",
"rpt_salesrpt;fw_view",
"rpt_stockdetail;fw_export",
"rpt_stockdetail;fw_presetfilteredit",
"rpt_stockdetail;fw_print",
"rpt_stockdetail;fw_view",
"rpt_stocksummary;fw_export",
"rpt_stocksummary;fw_presetfilteredit",
"rpt_stocksummary;fw_print",
"rpt_stocksummary;fw_view",
"rpt_stocksynthesize;fw_export",
"rpt_stocksynthesize;fw_presetfilteredit",
"rpt_stocksynthesize;fw_print",
"rpt_stocksynthesize;fw_view",
"rpt_storesalesconversionrate;fw_export",
"rpt_storesalesconversionrate;fw_presetfilteredit",
"rpt_storesalesconversionrate;fw_print",
"rpt_storesalesconversionrate;fw_view",
"rpt_userbusinesspermisse;fw_view",
"sal_dealeraccount;fw_view",
"sal_dealerdetail;fw_view",
"sal_dealersetup;fw_modify",
"sal_dealersetup;fw_new",
"sal_dealersetup;fw_view",
"sec_assignright;fw_allotrole",
"sec_assignright;fw_new",
"sec_role;fw_delete",
"sec_role;fw_forbid",
"sec_role;fw_import",
"sec_role;fw_modify",
"sec_role;fw_new",
"sec_role;fw_operatelog",
"sec_role;fw_presetfilteredit",
"sec_role;fw_unforbid",
"sec_role;fw_view",
"sec_role;fw_viewrecord",
"sec_user;fw_view",
"sel_category;fw_view",
"sel_constraint;fw_view",
"sel_prop;fw_view",
"sel_propvalue;fw_view",
"sel_range;fw_view",
"sel_suite;fw_view",
"sel_type;fw_view",
"ste_afterfeedback;fw_view",
"ste_channel;fw_view",
"ste_goal;fw_view",
"ste_registfee;fw_view",
"stk_inventorybase;fw_view",
"stk_inventorylist;fw_export",
"stk_inventorylist;fw_view",
"stk_inventorytransfer;fw_view",
"stk_inventoryverify;fw_view",
"stk_otherstockin;fw_view",
"stk_otherstockout;fw_view",
"stk_postockin;fw_view",
"stk_postockreturn;fw_view",
"stk_reservebill;fw_view",
"stk_reservebill_history;fw_view",
"stk_sostockout;fw_view",
"stk_sostockreturn;fw_view",
"stk_stockparam;fw_modify",
"stk_stockparam;fw_new",
"stk_stockparam;fw_view",
"sys_cfunction;fw_view",
"sys_company;fw_view",
"sys_datawidgetlist;fw_view",
"sys_datawidgettype;fw_view",
"sys_opserconfig;fw_view",
"sys_reportshell;fw_new",
"ydj_achievement;fw_view",
"ydj_achievement;mobile_view",
"ydj_achievement;mycompany",
"ydj_achievement;myself",
"ydj_achievement;mysubordinates",
"ydj_bank;fw_view",
"ydj_banknum;fw_view",
"ydj_brand;fw_view",
"ydj_category;fw_view",
"ydj_closedaccounts;fw_view",
"ydj_collectreceipt;fw_view",
"ydj_contactunit;fw_view",
"ydj_costaccounting;fw_view",
"ydj_customer;fw_view",
"ydj_customerrecord;fw_view",
"ydj_deliveryway;fw_view",
"ydj_dept;fw_view",
"ydj_designscheme;fw_view",
"ydj_employeesalesrevenue;fw_export",
"ydj_employeesalesrevenue;fw_presetfilteredit",
"ydj_employeesalesrevenue;fw_print",
"ydj_employeesalesrevenue;fw_view",
"ydj_expenseitem;fw_view",
"ydj_followerrecord;fw_view",
"ydj_headstatement;fw_audit",
"ydj_headstatement;fw_cancel",
"ydj_headstatement;fw_change",
"ydj_headstatement;fw_close",
"ydj_headstatement;fw_delete",
"ydj_headstatement;fw_export",
"ydj_headstatement;fw_import",
"ydj_headstatement;fw_listattach",
"ydj_headstatement;fw_modify",
"ydj_headstatement;fw_new",
"ydj_headstatement;fw_operatelog",
"ydj_headstatement;fw_presetfilteredit",
"ydj_headstatement;fw_print",
"ydj_headstatement;fw_reserveinventory",
"ydj_headstatement;fw_sharelayout",
"ydj_headstatement;fw_submit",
"ydj_headstatement;fw_submitchange",
"ydj_headstatement;fw_syncchange",
"ydj_headstatement;fw_tasktmpl",
"ydj_headstatement;fw_unaudit",
"ydj_headstatement;fw_uncancel",
"ydj_headstatement;fw_unchange",
"ydj_headstatement;fw_unclose",
"ydj_headstatement;fw_unsubmit",
"ydj_headstatement;fw_view",
"ydj_headstatement;fw_viewrecord",
"ydj_headstatement;ydj_headstatement_confirmbill",
"ydj_headstatement;ydj_headstatement_unconfirmbill",
"ydj_innerpartrank;fw_view",
"ydj_linkprogress;fw_view",
"ydj_marketstatement;fw_view",
"ydj_order;fw_view",
"ydj_order;fw_querychangeapply",
"ydj_order_chg;fw_view",
"ydj_orderapply_chg;fw_submit",
"ydj_orderapply_chg;fw_audit",
"ydj_orderapply_chg;fw_unaudit",
"ydj_orderapply_chg;fw_view",
"ydj_orderapply_chg;fw_modify",
"ydj_orderapply_chg;fw_operatelog",
"ydj_orderapply_chg;fw_orderapplychgrejected",
"ydj_payreceipt;fw_view",
"ydj_position;fw_view",
"ydj_price;fw_view",
"ydj_product;fw_view",
"ydj_productcategory;fw_view",
"ydj_productpromotion;fw_view",
"ydj_purchaseorder;fw_view",
"ydj_purchaseorder;fw_querychangeapply",
"ydj_purchaseorder;fw_initiatechangeapply",
"ydj_purchaseorder_chg;fw_view",
"ydj_purchaseorderapply_chg;fw_submit",
"ydj_purchaseorderapply_chg;fw_view",
"ydj_purchaseorderapply_chg;fw_modify",
"ydj_purchaseorderapply_chg;fw_operatelog",
"ydj_purchaseorderapply_chg;fw_savesubmit",
"ydj_purchaseprice;fw_view",
"ydj_purpriceadjust;fw_view",
"ydj_rank;fw_view",
"ydj_salescontrol;fw_view",
"ydj_series;fw_view",
"ydj_service;fw_view",
"ydj_serviceitem;fw_view",
"ydj_staff;fw_view",
"ydj_stockoutreport;fw_export",
"ydj_stockoutreport;fw_presetfilteredit",
"ydj_stockoutreport;fw_print",
"ydj_stockoutreport;fw_view",
"ydj_stockstatus;fw_view",
"ydj_storehouse;fw_view",
"ydj_storestatement;fw_view",
"ydj_supplier;fw_view",
"ydj_target;fw_view",
"ydj_target;mobile_view",
"ydj_target;mycompany",
"ydj_target;myself",
"ydj_target;mysubordinates",
"ydj_transferorderapply;fw_view",
"ydj_trend;fw_view",
"ydj_trend;mycompany",
"ydj_trend;mydepartment",
"ydj_trend;myself",
"ydj_trend;mysubordinates",
"ydj_unit;fw_view",
"ydj_vist;fw_view",
"ms_markingassistant;fw_view",
"ydj_collectreceipt_mid;fw_view",
"ydj_combopromotion;fw_view",
"ydj_payreceipt_mid;fw_view",
"ydj_historybill;fw_audit",
"ydj_historybill;fw_change",
"ydj_historybill;fw_delete",
"ydj_historybill;fw_distribute",
"ydj_historybill;fw_export",
"ydj_historybill;fw_forbid",
"ydj_historybill;fw_import",
"ydj_historybill;fw_listattach",
"ydj_historybill;fw_modify",
"ydj_historybill;fw_new",
"ydj_historybill;fw_operatelog",
"ydj_historybill;fw_presetfilteredit",
"ydj_historybill;fw_print",
"ydj_historybill;fw_settopreset",
"ydj_historybill;fw_sharelayout",
"ydj_historybill;fw_submit",
"ydj_historybill;fw_submitchange",
"ydj_historybill;fw_tasktmpl",
"ydj_historybill;fw_unaudit",
"ydj_historybill;fw_unchange",
"ydj_historybill;fw_undistribute",
"ydj_historybill;fw_unforbid",
"ydj_historybill;fw_unsubmit",
"ydj_historybill;fw_view",
"ydj_historybill;fw_viewrecord",
"ydj_renewtype;fw_view",
            };

            var result = GetPermItems(permItems);

            return result;
        }

        public static List<MPTabbarModel> GetAgentRoleMPMenu_Boss()
        {
            /*
             select '"' + ftabbar + ';' + fgroup + ';' + fmenuid + ';' + fmenuname  +  '",'  
            from t_mp_rolemenu t0  with(nolock) 
            where t0.froleId=@roleId and fisallow=1
             */

            var menuItems = new List<string>()
            {
                "工作台;销售管理;720326618328993798;商品图册",
                "工作台;销售管理;720561097932935181;小区楼盘",
                "工作台;销售管理;720561606232248333;购物车",
                "工作台;销售管理;720565024824889423;我的商机",
                "工作台;销售管理;720565024824889424;我的客户",
                "工作台;订单管理;720565024824889427;合同单",
                "工作台;订单管理;720565024824889428;收款单",
                "工作台;订单管理;720565024824889429;退款单",
                "工作台;企业管理;720565024824889431;审批",
                "工作台;企业管理;720565024824889432;渠道伙伴",
                "首页;快捷入口;720590220789157913;录商机",
                "首页;快捷入口;720590220793352192;录合同",
                "工作台;企业管理;732316900327034886;员工管理",
                "工作台;销售管理;758711323021414406;库存明细查询",
                "工作台;销售管理;801768148641648646;经典案例",
                "工作台;销售管理;807915410342154246;客户图库",
                "工作台;服务管理;807915975830802438;服务单",
                "工作台;服务管理;807915975830802439;售后反馈单",
                "工作台;企业管理;807916152440360972;统计分析",
                "首页;快捷入口;813016334223937542;扫一扫",
                "首页;快捷入口;918587513702187020;录客户",
                "首页;快捷入口;918587603690979334;录服务单",
                "首页;快捷入口;918587639514529798;录售后单",
                "工作台;销售管理;994614394863353856;促销活动",
            };


            var mpMenu = GetMPTabbars(menuItems);

            return mpMenu;
        }

    }
}
