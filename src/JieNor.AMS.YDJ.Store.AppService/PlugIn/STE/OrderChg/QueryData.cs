using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.OrderChg
{
    /// <summary>
    /// 销售合同变更单：查询数据
    /// </summary>
    [InjectService]
    [FormId("ydj_order_chg")]
    [OperationNo("QueryData")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "afterListData":
                    afterListData(e);
                    break;
            }
        }

        /// <summary>
        /// 处理列表查询的数据
        /// </summary>
        /// <param name="e"></param>
        private void afterListData(OnCustomServiceEventArgs e)
        {
            var listData = e.EventData as List<Dictionary<string, object>>;
            if (listData == null || listData.Count <= 0)
            {
                return;
            }

            //保护列表客户信息，当前销售合同并没有微信号，如果今后加了微信号按当前的需求要清空微信号
            var protecteDataService = this.Container.GetService<IProtecteDataService>();
            protecteDataService.Init(this.Context, "bas_storesysparam", "fenableprotectecusinfo");
            protecteDataService.MaskFields(new[] { "fphone", "fphone_chg" }, listData, 3, 4);
        }
    }
}
