using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：取消受理
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("CancelAccase")]
    public class CancelAccase : UpdateBizStatus
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public override string OperationName { get { return "取消受理"; } }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var billNo = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fstatus = Convert.ToString(newData["fstatus"]);
                if (fstatus == "D" || fstatus == "E")
                {
                    billNo = Convert.ToString(newData["fbillno"]);
                    return false;
                }
                return true;
            }).WithMessage("编号为{0}的单据已提交或已审核，不能再取消受理!", (billObj, propObj) => billNo));
        }
    }
}