using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：复制
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("Copy")]
    public class Copy : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "aftercreateuidata":
                    var eventData = e.EventData as JObject;
                    if (eventData != null)
                    {
                        eventData["fdrawentity"] = new JArray();
                    }
                    break;
                default:
                    break;
            }
        }
    }
}