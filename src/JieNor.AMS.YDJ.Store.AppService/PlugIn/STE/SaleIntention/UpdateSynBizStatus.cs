using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：更新业务状态，该操作主要用于接收采购订单发起的协同业务状态更新
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("UpdateSynBizStatus")]
    public class UpdateSynBizStatus : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //单据参数，json格式字符串（对象数组）
            var orderParamStr = this.GetQueryOrSimpleParam<string>("orderParam", "");
            if (orderParamStr.IsNullOrEmptyOrWhiteSpace()) return;

            //根据多个流水号批量查询订单信息
            StringBuilder sbInTranId = new StringBuilder();
            List<SqlParam> paramList = new List<SqlParam>();
            List<Dictionary<string, string>> orderParamList = orderParamStr?.FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
            for (int i = 0; i < orderParamList.Count; i++)
            {
                var paramName = "@tranid" + i;
                sbInTranId.Append(paramName).Append(",");
                paramList.Add(new SqlParam(paramName, System.Data.DbType.String, orderParamList[i]["tranId"]));
            }
            if (sbInTranId.IsNullOrEmptyOrWhiteSpace()) return;

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            string where = $"fmainorgid=@fmainorgid and ftranid in({sbInTranId.ToString().TrimEnd(',')})";
            paramList.Add(new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company));

            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, paramList);
            var dataEntitys = dm.SelectBy(dataReader).OfType<DynamicObject>();
            if (dataEntitys != null && dataEntitys.Count() > 0)
            {
                this.Option.SetIgnoreOpLogFlag();
                var syncService = this.Container.GetService<ISynergyService>();

                foreach (var dataEntity in dataEntitys)
                {
                    string tranId = Convert.ToString(dataEntity["ftranid"]);
                    Dictionary<string, string> orderParam = orderParamList.FirstOrDefault(t => t["tranId"].EqualsIgnoreCase(tranId));

                    string bizStatus = orderParam["bizStatus"];
                    string opCode = orderParam["opCode"];
                    string opName = orderParam["opName"];
                    DateTime opDate = DateTime.Now;
                    string opDateStr = orderParam.GetValue("opDate", "");
                    if (!DateTime.TryParse(opDateStr, out opDate))
                    {
                        opDate = DateTime.Now;
                    }
                    if (bizStatus.IsNullOrEmptyOrWhiteSpace()) continue;

                    //设置业务状态
                    dataEntity["fbizstatus"] = bizStatus;

                    //调用服务生成协同日志
                    syncService.WriteLog(this.Context, this.HtmlForm, dataEntity["id"] as string, opCode, opName, this.Context.CallerContext, opDate);
                }

                //保存
                dm.Save(dataEntitys);

                //标记成功
                this.Result.IsSuccess = true;
            }
        }
    }
}