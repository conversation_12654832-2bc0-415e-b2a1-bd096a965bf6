using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.SaleIntention
{
    /// <summary>
    /// 销售意向单：作废
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("cancel")]
    public class Cancel : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
             
            CloseCustomerRecord(e.DataEntitys);
        }

        /// <summary>
        /// 关闭销售机会
        /// </summary>
        /// <param name="dataEntities"></param>
        private void CloseCustomerRecord(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0) return;

            var saleIntentionNos = dataEntities.Select(s => Convert.ToString(s["fbillno"]));

            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customerrecord");

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var reader = this.Context.GetPkIdDataReader(htmlForm, $"fmainorgid='{this.Context.Company}' and fintentionno in ({string.Join(",", saleIntentionNos.Select(s => $"'{s}'"))})", new SqlParam[] { });
            var customerRecords = dm.SelectBy(reader)?.OfType<DynamicObject>()?.ToList();
            if (customerRecords == null || customerRecords.Count <= 0) return;

            ICustomerRecordService customerRecordService = this.Container.GetService<ICustomerRecordService>();
            customerRecordService.Close(this.Context, customerRecords, $"销售意向作废.");

            //将选中的数据存放在读写引擎中
            dm.Save(customerRecords);
        }

         


    }
}
