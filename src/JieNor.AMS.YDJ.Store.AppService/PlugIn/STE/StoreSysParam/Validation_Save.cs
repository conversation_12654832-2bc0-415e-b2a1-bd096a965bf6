using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.StoreSysParam
{
    public class Validation_Save : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }
        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }
            var sysProfile = userCtx.Container.GetService<ISystemProfile>();
            var forderoverpaymentdisout = sysProfile.GetSystemParameter<bool>(userCtx, "bas_storesysparam", "forderoverpaymentdisout", false);
            foreach (var item in dataEntities)
            {
                var orderoverpaymentdisout = Convert.ToBoolean(item["forderoverpaymentdisout"]);
                //原先是关闭状态,现在是开启,才去校验
                if (orderoverpaymentdisout&&!forderoverpaymentdisout) {
                    var orderlist = userCtx.LoadBizBillHeadDataByACLFilter("ydj_order", " funreceived < 0 and fcancelstatus='0'", "fbillno");
                    if (orderlist != null && orderlist.Count > 0)
                    {
                        throw new BusinessException($"存在超额收款的销售合同,需要先行处理。</br>{ string.Join("</br>", orderlist.Select(id => $"{id["fbillno"]}")) }");
                    }
                }
            }

            return result;
        }
    }
}
