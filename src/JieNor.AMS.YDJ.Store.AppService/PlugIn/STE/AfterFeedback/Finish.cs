using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.DataTransferObject.Poco;
using System.Text;
using System.Data;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.AfterFeedback
{
    /// <summary>
    /// 售后反馈单：完成
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("finish")]
    public class Finish : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //处理中且总部已审核
                if (Convert.ToString(newData["ffeedstatus"]).EqualsIgnoreCase("aft_service_02") || Convert.ToString(newData["ffeedstatus"]).EqualsIgnoreCase("aft_service_04"))
                {
                    return true;
                }
                return false;
            }).WithMessage(@"仅售后状态为【待处理】或【总部已审核】的才可进行该操作！"));
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!newData["fhandleconclusion"].IsNullOrEmptyOrWhiteSpace() && newData["fhandleconclusion"].Equals("res_type_04"))
                {
                    if (newData["fauthcity"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中城市不能为空！";
                        return false;
                    }
                    //else if (newData["fdeliver"].IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    errorMessage = "返厂申请中送达方不能为空！";
                    //    return false;
                    //}
                    //else if (newData["fengineerid"].IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    errorMessage = "返厂申请中售后工程师不能为空！";
                    //    return false;
                    //}
                    else if (newData["fengineerphone"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中工程师电话不能为空！";
                        return false;
                    }
                    else if (newData["fengineeraddress"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中退货地址不能为空！";
                        return false;
                    }
                    //else if (newData["fseltypename"].IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    errorMessage = "返厂申请中产品型号不能为空！";
                    //    return false;
                    //}
                    //else if (newData["fspecifica"].IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    errorMessage = "返厂申请中尺寸规格不能为空！";
                    //    return false;
                    //}
                    //else if (newData["fprocount"].IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    errorMessage = "返厂申请中商品数量不能为空！";
                    //    return false;
                    //}
                    else if (newData["ffeedtype"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中返厂售后类型不能为空！";
                        return false;
                    }
                    else if (newData["freturnreson"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中产品返厂原因不能为空！";
                        return false;
                    }
                    else if (newData["fsapnumber"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中SAP订单号不能为空！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if ((newData["finstitutiontype"].Equals("dutyunit_type_01") && !newData["fdutysupplierid"].IsNullOrEmptyOrWhiteSpace())
                || (newData["finstitutiontype"].Equals("dutyunit_type_02") && !newData["fdutycustomerid"].IsNullOrEmptyOrWhiteSpace())
                || (newData["finstitutiontype"].Equals("dutyunit_type_03") && !newData["fdutystaffid"].IsNullOrEmptyOrWhiteSpace())
                || (newData["finstitutiontype"].Equals("dutyunit_type_04") && !newData["fdutydeptid"].IsNullOrEmptyOrWhiteSpace())
                )
                {
                    return true;
                }
                return false;
            }).WithMessage(@"处理结论中【责任单位类型】和【责任单位】不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!newData["fhandleconclusion"].IsNullOrEmptyOrWhiteSpace() && !newData["fscheme"].IsNullOrEmptyOrWhiteSpace())
                {
                    return true;
                }
                return false;
            }).WithMessage(@"处理结论中【内部处理结论】和【内部处理方式】不能为空！"));
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys.Any())
            {
                foreach (var data in e.DataEntitys)
                {
                    data["ffeedstatus"] = "aft_service_05";//状态改为已完成
                    data["ffinishdate"] = DateTime.Now;
                    data["fclosedate"] = null;
                }
                var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, dt);
                var preService = this.Container.GetService<IPrepareSaveDataService>();
                preService.PrepareDataEntity(this.Context, HtmlForm, e.DataEntitys, OperateOption.Create());
                dm.Save(e.DataEntitys);
                StringBuilder sqlWhere = new StringBuilder();
                List<SqlParam> param = new List<SqlParam>();
                sqlWhere.Append(string.Join(",", e.DataEntitys.Select((x, i) => $"@fid{i}")));
                param.AddRange(e.DataEntitys.Select((x, i) => new SqlParam($"@fid{i}", System.Data.DbType.String, x["id"])));
                param.Add(new SqlParam("@company", DbType.String, this.Context.Company));
                var hasNotVist = this.Context.ExecuteDynamicObject("select f.fid from t_ste_afterfeedback f where f.fid in(" + sqlWhere + ") and not exists(select 1 from t_ydj_vist v where v.fsourcetype='ste_afterfeedback' and v.fsourcenumber=f.fbillno and v.fmainorgid=@company)", param);
                if (hasNotVist.Any())
                {
                    var convertService = this.Container.GetService<IConvertService>();
                    var result = convertService.Push(this.Context, new BillConvertContext()
                    {
                        RuleId = "ste_afterfeedback2ydj_vist",
                        SourceFormId = "ste_afterfeedback",
                        TargetFormId = "ydj_vist",
                        SelectedRows = hasNotVist.Select(x => new SelectedRow { PkValue = x["fid"].ToString() }).ToConvertSelectedRows()
                    });
                    var convertResult = result.SrvData as ConvertResult;
                    if (convertResult.TargetDataObjects != null && convertResult.TargetDataObjects.Count() > 0)
                    {
                        var invokeSave = this.Gateway.InvokeBillOperation(this.Context, "ydj_vist", convertResult.TargetDataObjects, "save", new Dictionary<string, object>());
                        invokeSave?.ThrowIfHasError(true, $"自动生成回访单失败！");
                    }
                }
            }
        }
    }
}