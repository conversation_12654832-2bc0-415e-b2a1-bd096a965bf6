using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.AfterFeedback
{
    /// <summary>
    /// 售后反馈单：选单
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("QuerySelector")]
    public class QuerySelector : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            // 只限于销售合同
            var targetFormId = this.GetQueryOrSimpleParam("targetFormId", "");
            //if (!targetFormId.EqualsIgnoreCase("ydj_purchaseorder")) return;

            var orderFileter = string.Empty;

            // 当存在源单时，只能选择当前源单的数据
            var sourceNumber = this.GetQueryOrSimpleParam("sourceNumber", "");
            if (!sourceNumber.IsNullOrEmptyOrWhiteSpace())
            {
                orderFileter += $" AND o.fbillno='{sourceNumber}' ";
            }

            var sourceentryid = this.GetQueryOrSimpleParam("sourceentryid", "");
            if (!sourceentryid.IsNullOrEmptyOrWhiteSpace())
            {
                orderFileter += $" AND po.fentryid='{sourceentryid}' ";
            }


            string filterString = string.Empty;
            if (targetFormId.EqualsIgnoreCase("ydj_purchaseorder"))
            {
                filterString = $@" 
                        fid in 
                        (
                        select o.fid from t_ydj_purchaseorder o inner join t_ydj_poorderentry po on o.fid=po.fid where 1=1 AND o.fstatus='E' AND po.fbizinstockqty>0 {orderFileter}
                        ) ";
            }

            this.SimpleData["filterString"] = filterString;
        }
    }
}
