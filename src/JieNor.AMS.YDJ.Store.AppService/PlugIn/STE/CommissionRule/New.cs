using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CommissionRule
{
    /// <summary>
	/// 提成规则
	/// </summary>
	[InjectService]
    [FormId("ydj_commissionrule")]
    [OperationNo("New")]
    public class New : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            foreach (var dataEntity in e.DataEntitys)
            {
                dataEntity["fyear"] = string.Format("fy_{0}", DateTime.Now.Year);
            }
        }
    }
}
