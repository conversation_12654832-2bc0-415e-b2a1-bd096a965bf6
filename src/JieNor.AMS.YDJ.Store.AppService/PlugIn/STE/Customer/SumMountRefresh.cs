using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Customer
{
    /// <summary>
    /// 客户：成交总额
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("SumMountrefresh")]
    public class SumMountRefresh : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
        }
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if(e.DataEntitys ==null || e.DataEntitys.Length ==0)
            {
                return;
            }

            var dataEntity = e.DataEntitys;
            //得到编号
            var fid = dataEntity[0]["Id"];
            //根据编号查询出对应的所有销售合同（汇总成交总额=（成交金额-实退金额））
            string sql = string.Format(@"select sum(b.fdealprice * (b.fbizqty - b.fbizrefundqty)) from t_ydj_order a with(nolock) 
                                       inner join t_ydj_orderentry b with(nolock) on a.fid=b.fid 
                                    where fcustomerid='{0}' and fstatus='E' and b.fomsprogress!='-1'", fid);
            DynamicObjectCollection data = this.DBService.ExecuteDynamicObject(this.Context, sql);
            //得到汇总统计后的成交总额
            var SumMount = decimal.Round(Convert.ToDecimal(data[0]["Property0"]), 2, MidpointRounding.AwayFromZero);
            //更新“成交总额”
            string sqls = string.Format("update t_ydj_customer set fdealamount={0} where fid='{1}'", SumMount, fid);
            this.DBService.ExecuteDynamicObject(this.Context, sqls);
            this.Result.IsSuccess = true;
            this.Result.SrvData = SumMount;
        }
    }
}
