using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Customer
{
    /// <summary>
    /// 校正客户帐户余额
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("CorrectBalance")]
    public class CorrectBalance : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            var allCustIds = e.DataEntitys.Select(o => Convert.ToString(o["id"]))
                .Distinct();

            var custService = this.Container.GetService<ICustomerBalanceService>();
            custService?.CorrectAccountBalance(this.Context, allCustIds);
            this.Result.SimpleMessage = "客户账户余额校正操作执行成功！";
        }
    }
}
