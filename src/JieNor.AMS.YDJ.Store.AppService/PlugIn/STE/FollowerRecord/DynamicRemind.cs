using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.FollowerRecord
{
    /// <summary>
    /// 动态提醒消息插件
    /// </summary>
    [InjectService]
    [FormId("ydj_followerrecord")]
    public class DynamicRemind : AbstractDynamicRemindPlugIn
    {
        public override void OnLoadDynamicLog(OnDynamicRemindLoadLogArgs args)
        {
            args.ChangedSql = true;
            args.SqlText=$@"
select fid id,'{HtmlForm.Id}' as fbizformid,fnexttime as freminddate,fdescription as fcontent,fmainorgid 
from {this.HtmlForm.BillHeadTableName} 
where fmainorgid='{this.Context.Company}'
and fnexttime>='{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.000' 
and fnexttime<='{DateTime.Now.ToString("yyyy-MM-dd")} 23:59:59.999'
";

        }

        public override void OnProcessTask(OnDynamicRemindProcessArgs args)
        {
            args.Cancel = true;

            if (string.IsNullOrWhiteSpace(args.DynRecordId) || false == "viewbill".EqualsIgnoreCase(args.OpCode))
            {
                return;
            }

            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            var dataEntity = dm.Select(args.DynRecordId) as DynamicObject;

            if (dataEntity == null)
            {
                throw new BusinessException($"{this.HtmlForm.Caption}不存在或已被删除!");
            }

            var sourceType = Convert.ToString(dataEntity["fsourcetype"]);
            var sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);

            if (string.IsNullOrWhiteSpace(sourceType) || string.IsNullOrWhiteSpace(sourceNumber))
            {
                throw new BusinessException($"{this.HtmlForm.Caption}不存在源单类型或源单编号!");
            }

            var metaModelService = this.Context.Container.GetService<IMetaModelService>();
            var sourceForm = metaModelService.LoadFormModel(this.Context, sourceType);
            var sourceDm = this.Context.Container.GetService<IDataManager>();
            sourceDm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));
            var dataReader = this.Context.GetPkIdDataReaderWithNumber(sourceForm, new[] { sourceNumber });
            var sourceEntity = sourceDm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

            if (sourceEntity == null)
            {
                throw new BusinessException($"{this.HtmlForm.Caption}没有找到源单或源单已删除!");
            }

            //打开动态对应业务对象编辑页面
            var action = this.Context.ShowSpecialForm(sourceForm,
                sourceEntity,
                false,
                Guid.NewGuid().ToString(),
                Enu_OpenStyle.Default,
                Enu_DomainType.Bill);

            if (action != null)
            {
                args.Actions.Add(action);
                args.IsLoadedAction = true;
            }
        }
    }
}
