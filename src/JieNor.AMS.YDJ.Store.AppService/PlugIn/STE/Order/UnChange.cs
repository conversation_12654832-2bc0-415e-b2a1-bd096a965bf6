using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Helpers;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：取消变更
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("unchange")]
    public class UnChange : AbstractOperationServicePlugIn
    {
        private List<DynamicObject> chgBills { get; set; }
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entrys = (DynamicObjectCollection)newData["fentry"];

                // 有非标审批状态=待审批的
                var hasAuditingUnstdStatus =
                    entrys.Any(s => Convert.ToString(s["funstdtypestatus"]).EqualsIgnoreCase("02"));

                return hasAuditingUnstdStatus == false;
            }).WithMessage("{0}", (billObj, propObj) => "存在商品行的非标审批状态为待审批，不允许取消变更"));
            e.Rules.Add(new Validation_UnChange());
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            var billNos = e.DataEntitys.Select(t => Convert.ToString(t["fbillno"])).ToList();
            this.chgBills = this.Context.LoadBizDataByFilter("ydj_order_chg", " fsourcenumber in ('{0}') and fstatus != 'E'".Fmt(
                string.Join("','", billNos)));
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            
            this.Context.SaveBizData("ydj_order", e.DataEntitys);

            var result = ReserveUtil.UpdateReserve(this.Context, this.HtmlForm, e.DataEntitys, this.Option);

            this.Result.MergeResult(result);

            OrderQtyWriteBackHelper.WriteBackOrderAllQty(this.Context, e.DataEntitys);

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys, forceUpdate: true);

            ProductDelistingHelper.DealDelistingDataByUnChange(this.Context, this.HtmlForm, this.OperationNo, this.chgBills, e.DataEntitys.ToList());
            
        }


    }
}
