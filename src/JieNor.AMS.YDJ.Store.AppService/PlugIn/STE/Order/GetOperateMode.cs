using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：获取商品发布企业对应的运营模式
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("GetOperateMode")]
    public class GetOperateMode : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var productId = this.GetQueryOrSimpleParam<string>("productId", "");
            if (productId.IsNullOrEmptyOrWhiteSpace()) return;

            //var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            //var dm = this.GetDataManager();
            //dm.InitDbContext(this.Context, productForm.GetDynamicObjectType(this.Context));
            //var product = dm.Select(productId) as DynamicObject;
            //if (product == null) return;

            //var publishField = productForm?.GetField(productForm.PublishCIdFldKey) as HtmlCompanyField;
            //var publishcid = publishField?.DynamicProperty?.GetValue<string>(product);
            //var publishpid = publishField?.ProductIdDynamicProperty?.GetValue<string>(product);
            //if (!publishcid.IsNullOrEmptyOrWhiteSpace() && !publishpid.IsNullOrEmptyOrWhiteSpace())
            //{
            //    var supplierForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_supplier");
            //    dm = this.GetDataManager();
            //    dm.InitDbContext(this.Context, supplierForm.GetDynamicObjectType(this.Context));

            //    var where = "fmainorgid=@fmainorgid and fcoocompanyid=@fcoocompanyid and fcooproductid=@fcooproductid";
            //    var sqlParam = new List<SqlParam>
            //    {
            //        new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
            //        new SqlParam("fcoocompanyid", System.Data.DbType.String, publishcid),
            //        new SqlParam("fcooproductid", System.Data.DbType.String, publishpid)
            //    };
            //    var dataReader = this.Context.GetPkIdDataReader(supplierForm, where, sqlParam);
            //    var supplier = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            //    if (supplier != null)
            //    {
            //        this.Result.SrvData = supplier["foperationmode"];
            //        this.Result.IsSuccess = true;
            //    }
            //}

            var productInfoService = this.Container.GetService<IProductInfoService>();
            this.Result.SrvData = productInfoService.GetProductSupplierOperateMode(this.Context, productId);
            this.Result.IsSuccess = true;
        }
    }
}