using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Api;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：订单关闭
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("orderclose")]
    public class OrderClose : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype", "frenewtype" });
        }
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data)
                   .IsTrue((newData, oldData) => Convert.ToString(newData["flockstate"]) != "1")//锁定状态
                   .WithMessage("销售合同[{0}]锁定状态[已锁定],必须未锁定才能执行订单关闭！", (dataObj, propObj) => dataObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data)
                 .IsTrue((newData, oldData) => Convert.ToString(newData["fclosestate"]) != "1")//关闭状态
                 .WithMessage("销售合同[{0}]关闭状态[已关闭],必须未关闭才能执行订单关闭！", (dataObj, propObj) => dataObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var isApproving = new OrderCommon(this.Context).IsTransferOrderApproving(newData);
                return !isApproving;
            }).WithMessage("销售合同[{0}]中的商品明细存在转单申请单为审批中时无法关闭！", (dataObj, propObj) => dataObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data)
                 .IsTrue((newData, oldData) => Convert.ToString(newData["fchangestatus"]) != "1")//变更中状态
                 .WithMessage("销售合同[{0}]变更状态[变更中],订单变更流程中，不允许订单关闭！", (dataObj, propObj) => dataObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data)
             .IsTrue((newData, oldData) => Convert.ToString(newData["fstatus"]) != "D")//提交状态
             .WithMessage("销售合同[{0}]订单状态[已提交],不允许订单关闭！", (dataObj, propObj) => dataObj["fbillno"]));

            //错误消息
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fbilltype = newData["fbilltype_ref"] as DynamicObject;
                var v6swjBillTypeName = Convert.ToString(fbilltype["fname"]);

                if (v6swjBillTypeName == "v6定制柜合同")
                {
                    var omsservice = Convert.ToBoolean(newData["fomsservice"]);
                    if (!omsservice.IsNullOrEmptyOrWhiteSpace() && omsservice)
                    {
                        var strSql = $@"select a.fid from T_YDJ_PURCHASEORDER as a inner join T_YDJ_POORDERENTRY as b on a.fid=b.fid where fsourceinterid='{newData["id"]}' and a.fcancelstatus=0 ";
                        List<string> fids = new List<string>();
                        using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
                        {
                            while (dr.Read())
                            {
                                fids.Add(dr["fid"].ToString());
                            }
                        }
                        if (fids.Count > 0)
                        {
                            errorMessage = "对不起，当前销售合同存在下游未作废采购订单，禁止关闭！";
                            return false;
                        }
                    }

                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //当焕新订单标记=是，【结算进度】=已收款，不允许点<订单关闭>按钮。行明细上也不能点击关闭
                var frenewalflag = Convert.ToBoolean(newData["frenewalflag"]);
                var fsettlprogress = Convert.ToString(newData["fsettlprogress"]);
                if (frenewalflag && fsettlprogress == Enu_RenewalSettleProgress.已收款)
                {
                    return false;
                }
                return true;
            }).WithMessage("销售合同【{0}】的结算进度[已收款]，不允许订单关闭！", (billObj, propObj) => billObj["fbillno"]));

            e.Rules.Add(new Validation_InventoryTransfer(null));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                //设置【关闭日期】=“当前日期”，【关闭状态】=“已关闭”，【关闭人】=“当前用户” 
                dataEntity["fclosestate"] = "1";
                dataEntity["fclosedate"] = DateTime.Now;
                dataEntity["fcloseid"] = this.Context.UserId;

                var entryDatas = (dataEntity["fentry"] as DynamicObjectCollection).ToList();

                // 销售数量为0时不允许关闭
                foreach (var entry in entryDatas)
                {
                    if (Convert.ToDecimal(entry["fbizqty"]) == 0)
                    {
                        this.Result.ComplexMessage.WarningMessages.Add($"第{entry["fseq"]}行商品明细的销售数量为0，不允许关闭。");
                    }
                }

                // 将不是【自动关闭】且【销售数量】>0 的明细行设置为【手动关闭】
                entryDatas
                    .FindAll(t => Convert.ToString(t["fclosestatus_e"]) != CloseStatusConst.Auto && Convert.ToDecimal(t["fbizqty"]) > 0)
                    .ForEach(t => t["fclosestatus_e"] = CloseStatusConst.Manual);

                // 最终走统一的关闭状态计算逻辑
                Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(dataEntity, this.Context);
            }

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);

            ProductDelistingHelper.DealDelistingDataByCommon(this.Context, this.HtmlForm, this.OperationNo, e.DataEntitys.ToList());
            //当焕新订单类型.【允许发起收款】=Y  并且  【结算进度】=收款中，才允许会调用。
            var datas = e.DataEntitys.Where(o => Convert.ToString(o["fsettlprogress"]).EqualsIgnoreCase("1")
                                         && Convert.ToBoolean((o["frenewtype_ref"] as DynamicObject)?["fisincome"] ?? false) == true
            ).ToList();

            if (datas.Any())
            {
                foreach (var dataEntity in datas)
                {
                    var dto = new PushRenewalOrderDTO(this.Context, dataEntity, 2);
                    dataEntity["fsettlprogress"] = Enu_RenewalSettleProgress.收款中; //收款中 
                    MuSiMemberApi.PushRenewalOrder(this.Context, dto);
                }

                //调用焕新订单合同推送
                //var _result = this.Gateway.InvokeBillOperation(this.Context, "ydj_order", datas, "RenewalReceipt",  new Dictionary<string, object>());
                //if (!_result.IsSuccess)
                //{
                //    this.Result.ComplexMessage.SuccessMessages.Add(_result.ComplexMessage.ToString());
                //}
            }

            //OMS定制
            IMuSiService muSiService = this.Container.GetService<IMuSiService>();
            foreach (var item in e.DataEntitys)
            {
                var omsservice = Convert.ToBoolean(item["fomsservice"]);
                var fbilltype = item["fbilltype_ref"] as DynamicObject;
                var v6swjBillTypeName = Convert.ToString(fbilltype["fname"]);
                if (!item["fbilltype"].IsNullOrEmptyOrWhiteSpace() && v6swjBillTypeName == "v6定制柜合同" && omsservice)
                {
                    //实时调用接口将数据传到定制OMS中台。
                    muSiService.OMSSyncAsync(this.Context, this.HtmlForm, new DynamicObject[] { item });
                }
            }


            this.AddRefreshPageAction();
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add("订单关闭成功！");
        }

    }
}