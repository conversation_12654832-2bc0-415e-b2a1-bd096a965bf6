using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：结算
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("NewSettle")]
    public class NewSettle : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 是否是协同结算
        /// </summary>
        public bool IsSyn { get; set; }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, DynamicObject>;
            if (eventData == null || eventData.Item2 == null) return;
            var dataEntity = eventData.Item2;

            //事件名称
            switch (e.EventName.ToLower())
            {
                case "onloadsynctarget":

                    var productEntry = dataEntity?["fentry"] as DynamicObjectCollection;
                    var productId = productEntry?.FirstOrDefault(o => Convert.ToString(o["foperationmode"]).EqualsIgnoreCase("1"))?["fproductid"];
                    if (productId.IsNullOrEmptyOrWhiteSpace()) return;

                    var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
                    var dm = this.GetDataManager();
                    dm.InitDbContext(this.Context, productForm.GetDynamicObjectType(this.Context));
                    var product = dm.Select(productId) as DynamicObject;
                    if (product == null) return;

                    var publishField = productForm?.GetField(productForm.PublishCIdFldKey) as HtmlCompanyField;
                    var publishcid = publishField?.DynamicProperty?.GetValue<string>(product);
                    var publishpid = publishField?.ProductIdDynamicProperty?.GetValue<string>(product);

                    var supplierService = this.Container.GetService<ISupplierService>();
                    var target = supplierService.GetSyncTargetSEP(this.Context, publishcid, publishpid);
                    if (target == null) return;

                    e.Cancel = true;
                    e.Result = target;
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var fsettletype = this.GetQueryOrSimpleParam<string>("fsettletype", "");
            var isSyn = this.GetQueryOrSimpleParam<bool>("fissyn", false);
            var way = this.GetQueryOrSimpleParam<string>("fway", "");
            var myBankId = this.GetQueryOrSimpleParam<string>("fmybankid", "");
            var synBankId = this.GetQueryOrSimpleParam<string>("fsynbankid", "");
            var fcontactunitid = this.GetQueryOrSimpleParam<string>("fcontactunitid", "");
            var receiptno = this.GetQueryOrSimpleParam<string>("freceiptno", "");

            var spService = this.Container.GetService<ISystemProfile>();
            var enableMustInputBankId = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablemustinputbankid", true);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if ((way.EqualsIgnoreCase("payway_06") ||
                    way.EqualsIgnoreCase("payway_07") ||
                    way.EqualsIgnoreCase("payway_08") ||
                    (enableMustInputBankId && way.EqualsIgnoreCase("payway_11"))) &&
                    myBankId.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择银行账号！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if ((way.EqualsIgnoreCase("payway_06") ||
                    (enableMustInputBankId && way.EqualsIgnoreCase("payway_11"))) &&
                     isSyn && synBankId.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择对方银行！"));

            //支付方式为“商场代收”，“代收单位”不能为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (way.EqualsIgnoreCase("payway_13") && fcontactunitid.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("支付方式为“商场代收”，“代收单位”不能为空!"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (fsettletype.EqualsIgnoreCase("退款") 
                    && this.GetQueryOrSimpleParam<decimal>("fsettleamount", 0) > Convert.ToDecimal(newData["freceivable"]))
                {
                    return false;
                }
                return true;
            }).WithMessage("本次结算额不允许大于确认已收金额！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (fsettletype.EqualsIgnoreCase("收款")
                    && this.GetQueryOrSimpleParam<decimal>("fsettleamount", 0) > Convert.ToDecimal(newData["funreceived"]))
                {
                    return false;
                }
                return true;
            }).WithMessage("本次结算额不允许大于未收金额！"));

            //string receiptMsg = "【收款小票号】必填！";
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    var profileService = this.Container.GetService<ISystemProfile>();
            //    var mustinvoicenumber = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fmustinvoicenumber", false); //收款必填小票号
            //    var receiptNo = Convert.ToString(newData["freceiptno"]);
            //    if (receiptNo.IsNullOrEmptyOrWhiteSpace())
            //    {
            //        if (mustinvoicenumber)
            //        {
            //            receiptMsg = "【收款小票号】必填！";
            //            return false;
            //        }
            //    }
            //    else
            //    {
            //        decimal money = Convert.ToDecimal(newData["fsettleamount"]);
            //        if (money > 0.01M)
            //        {
            //            var incomeObjs = this.Context.LoadBizDataByFilter("coo_incomedisburse", $" freceiptno='{receiptNo}' AND famount='{money}'");
            //            if (incomeObjs != null && incomeObjs.Any())
            //            {
            //                receiptMsg = "该笔收款金额的“收款小票号”录入重复，请检查！";
            //                return false;
            //            }
            //        }
            //    }
            //    return true;
            //}).WithMessage(receiptMsg));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            //销售单据（意向及合同）不审核可退款
            var canrefund = false;
            var spService = this.Container.GetService<ISystemProfile>();
            string sysProfileValue = spService.GetProfile(this.Context, "fw", $"bas_storesysparam_parameter");
            if (!sysProfileValue.IsNullOrEmptyOrWhiteSpace())
            {
                var storeSysParam = JObject.Parse(sysProfileValue);
                if (storeSysParam != null && storeSysParam["fcanrefund"] != null)
                {
                    canrefund = Convert.ToBoolean(storeSysParam["fcanrefund"]);
                }
            }
            if (canrefund == false)
            {
                if (Convert.ToString(dataEntity["fstatus"]) != "E")
                {
                    throw new BusinessException("所属销售合同单并未审核，请审核后再结算!");
                }
            }

            //是否是协同
            this.IsSyn = false;

            //协同账户余额服务
            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();

            //代收单位是否是“商场”
            var fcontactunitid = this.GetQueryOrSimpleParam<string>("fcontactunitid", "");
            var contactUnit = synAccountBalanceService.GetContactUnitById(this.Context, fcontactunitid);
            var contactUnitIsMall = contactUnit != null && Convert.ToString(contactUnit["ftype"]).EqualsIgnoreCase("contactunittype_01");

            //反写订单信息
            this.UpdateOrder(dataEntity, contactUnitIsMall);

            if (this.IsSyn)
            {

            }
            else
            {
                if (contactUnitIsMall)
                {
                    this.Result.SimpleMessage = "结算成功！";
                }
                else
                {
                    this.Result.SimpleMessage = "结算成功，等待财务确认！";
                }
            }
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 反写订单信息
        /// </summary>
        /// <param name="order"></param>
        /// <param name="contactUnitIsMall"></param>
        private void UpdateOrder(DynamicObject order, bool contactUnitIsMall)
        {
            decimal fsettleamount = this.GetQueryOrSimpleParam<decimal>("fsettleamount", 0);
            var fsettletype = this.GetQueryOrSimpleParam<string>("fsettletype", "");

            //代收单位如果是“商场”则自动确认收支记录
            if (contactUnitIsMall)
            {
                switch (fsettletype.Trim().ToLower())
                {
                    case "收款":
                        //增加已收金额
                        order["freceivable"] = Convert.ToDecimal(order["freceivable"]) + fsettleamount;
                        //扣减未收金额
                        order["funreceived"] = Convert.ToDecimal(order["funreceived"]) - fsettleamount;
                        break;
                    case "退款":
                        //扣减已收金额
                        order["freceivable"] = Convert.ToDecimal(order["freceivable"]) - fsettleamount;
                        //增加未收金额
                        order["funreceived"] = Convert.ToDecimal(order["funreceived"]) + fsettleamount;
                        break;
                    default:
                        break;
                }
            }
            else
            {
                //switch (fsettletype.Trim().ToLower())
                //{
                //    case "收款":
                //        //扣减未收金额
                //        order["funreceived"] = Convert.ToDecimal(order["funreceived"]) - fsettleamount;
                //        //增加待确认金额
                //        order["fconfirmamount"] = Convert.ToDecimal(order["fconfirmamount"]) + fsettleamount;
                //        break;
                //    case "退款":
                //        //增加未收金额
                //        order["funreceived"] = Convert.ToDecimal(order["funreceived"]) + fsettleamount;
                //        //扣减待确认金额
                //        order["fconfirmamount"] = Convert.ToDecimal(order["fconfirmamount"]) - fsettleamount;
                //        break;
                //    default:
                //        break;
                //}
            }
            //已收金额
            var freceivable = Convert.ToDecimal(order["freceivable"]);
            //成交金额
            var fdealamount = Convert.ToDecimal(order["fdealamount"]);
            //结算状态
            if (freceivable == 0)
            {
                //全款未收
                order["freceiptstatus"] = "receiptstatus_type_01";
            }
            else if (freceivable < fdealamount)
            {
                //部分收款
                order["freceiptstatus"] = "receiptstatus_type_02";
            }
            else if (freceivable == fdealamount)
            {
                //全款已收
                order["freceiptstatus"] = "receiptstatus_type_03";
            }

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(order);
        }
    }
}