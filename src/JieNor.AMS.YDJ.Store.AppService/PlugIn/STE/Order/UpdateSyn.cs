using System;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：更新协同信息，该操作主要用于接收K3【订购意向书】发起的协同更新
    /// 目前主要协同更新销售合同单据头【备注】、商品明细【备注】字段值。
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("UpdateSyn")]
    public class UpdateSyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var billDataJson = this.GetQueryOrSimpleParam<string>("billData");
            if (billDataJson.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"参数 billData 为空，请检查！");
            }
            JArray billData = null;
            try
            {
                billData = JArray.Parse(billDataJson);
            }
            catch (Exception ex)
            {
                throw new BusinessException($"参数 billData 格式错误：{ex.Message}，请检查！");
            }
            if (billData == null || !billData.Any())
            {
                throw new BusinessException($"参数 billData 为空，请检查！");
            }

            //交易流水号
            var tranIds = billData
                .Where(o => !o["ftranid"].IsNullOrEmptyOrWhiteSpace())
                .Select(o => Convert.ToString(o["ftranid"]))
                .Distinct()
                .ToList();
            if (!tranIds.Any())
            {
                throw new BusinessException($"参数 billData 中的交易流水号 ftranid 为空，请检查！");
            }

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            string where = $"fmainorgid=@fmainorgid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            if (tranIds.Count == 1)
            {
                where += " and ftranid=@ftranid";
                sqlParam.Add(new SqlParam("@ftranid", System.Data.DbType.String, tranIds[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < tranIds.Count; i++)
                {
                    paramNames.Add($"@ftranid{i}");
                    sqlParam.Add(new SqlParam($"@ftranid{i}", System.Data.DbType.String, tranIds[i]));
                }
                where += $" and ftranid in({string.Join(",", paramNames)})";
            }

            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, sqlParam);
            var dataEntitys = dm.SelectBy(dataReader).OfType<DynamicObject>();
            if (dataEntitys == null || !dataEntitys.Any())
            {
                throw new BusinessException($"根据交易流水号【{string.Join(",", tranIds)}】查不到销售合同，请检查！");
            }

            //需要保存的销售合同数据包
            var saveDatas = new List<DynamicObject>();

            foreach (var dataEntity in dataEntitys)
            {
                //根据销售合同的交易流水号匹配到协同数据包
                var tranId = Convert.ToString(dataEntity["ftranid"]);
                var synBillData = billData.FirstOrDefault(o => Convert.ToString(o["ftranid"]).EqualsIgnoreCase(tranId));
                if (synBillData == null) continue;

                //处理单据头
                this.ProcessBillHead(dataEntity, synBillData);

                //处理商品明细
                this.ProcessEntry(dataEntity, synBillData);

                saveDatas.Add(dataEntity);
            }

            ////生成主键ID
            //var pkService = this.Container.GetService<IDataEntityPkService>();
            //pkService.AutoSetPrimaryKey(this.Context, saveDatas, dm.DataEntityType);

            //保存
            dm.Save(saveDatas);

            //标记成功
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add($"{this.HtmlForm.Caption}协同信息更新成功！");
        }

        /// <summary>
        /// 处理单据头
        /// </summary>
        /// <param name="dataEntity">销售合同数据包</param>
        /// <param name="synBillData">协同数据包</param>
        private void ProcessBillHead(DynamicObject dataEntity, JToken synBillData)
        {
            //备注
            var description = synBillData.GetJsonValue<string>("fdescription");

            //if (!description.IsNullOrEmptyOrWhiteSpace())
            //{
            //    dataEntity["fdescription"] = description;
            //}

            dataEntity["fdescription"] = description;
        }

        /// <summary>
        /// 处理商品明细
        /// </summary>
        /// <param name="dataEntity">销售合同数据包</param>
        /// <param name="synBillData">协同数据包</param>
        private void ProcessEntry(DynamicObject dataEntity, JToken synBillData)
        {
            var synEntrys = synBillData.GetJsonValue<JArray>("fentry");
            if (synEntrys == null || !synEntrys.Any()) return;

            var entrys = dataEntity["fentry"] as DynamicObjectCollection;
            if (entrys == null || !entrys.Any()) return;

            foreach (var entry in entrys)
            {
                //根据商品明细的交易流水号匹配到协同明细
                var tranId = Convert.ToString(entry["ftranid"]);
                var synEntry = synEntrys.FirstOrDefault(o => Convert.ToString(o["ftranid"]).EqualsIgnoreCase(tranId));
                if (synEntry == null) continue;

                //明细备注
                var description = synEntry.GetJsonValue<string>("fdescription");

                //if (!description.IsNullOrEmptyOrWhiteSpace())
                //{
                //    entry["fdescription"] = description;
                //}

                entry["fdescription"] = description;
            }
        }
    }
}