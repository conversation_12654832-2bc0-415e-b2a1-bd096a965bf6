using System;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("checkproductseriesisnewchannel")]
    public class CheckProductSeriesIsNewChannel : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var productId = this.GetQueryOrSimpleParam<string>("productId", "");
            var seriesId = this.GetQueryOrSimpleParam<string>("seriesId", "");
            var rowId = this.GetQueryOrSimpleParam<string>("rowId", "");

            var seriesDy = this.Context.LoadBizBillHeadDataById("ydj_series", seriesId, "fname,fnumber,fmainorgid,fisnewchannel");

            var resultDic = new Dictionary<string,object>();

            resultDic.Add("rowId",rowId);

            if (seriesDy != null)
            {
                var mainOrgId = Convert.ToString(seriesDy["fmainorgid"]);
                if (this.Context.TopCompanyId.EqualsIgnoreCase(mainOrgId))
                {
                    if (Convert.ToString(seriesDy["fisnewchannel"])=="1"|| Convert.ToString(seriesDy["fisnewchannel"])=="true")
                    {
                        resultDic.Add("seriesIsNewChannel", true);
                    }
                    else
                    {
                        resultDic.Add("seriesIsNewChannel", false);
                    }
                }
                else
                {
                    resultDic.Add("seriesIsNewChannel", false);
                }
            }
            else
            {
                resultDic.Add("seriesIsNewChannel", false);
            }

            Result.IsSuccess = true;
            Result.SrvData = resultDic;

        }
    }
}