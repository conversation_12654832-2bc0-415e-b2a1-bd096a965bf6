using System;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getdirectsalegivewayisnotzeroparam")]
    public class GetDirectSaleGiveAwayIsNotZeroParam : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var agentDy = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fdirectsalesgiveawaynotzero,ftoppiecesendtag");
            var resultDic = new Dictionary<string, object>();
            if (agentDy != null)
            {
                var fdirectsalesgiveawaynotzero = Convert.ToBoolean(Convert.ToInt32(agentDy["fdirectsalesgiveawaynotzero"]));
                var ftoppiecesendtag = Convert.ToBoolean(Convert.ToInt32(agentDy["ftoppiecesendtag"]));
                resultDic.Add("fdirectsalesgiveawaynotzero",fdirectsalesgiveawaynotzero);
                resultDic.Add("ftoppiecesendtag",ftoppiecesendtag);
            }
            else
            {
                resultDic.Add("fdirectsalesgiveawaynotzero",false);
                resultDic.Add("ftoppiecesendtag",false);
            }

            this.Result.SrvData = resultDic;
            this.Result.IsSuccess = true;
        }
    }
}