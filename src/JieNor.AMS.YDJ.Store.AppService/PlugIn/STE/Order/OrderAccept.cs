using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：受理
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("accept")]
 public   class OrderAccept : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var acceptStatus = Convert.ToString(newData["facceptstatus"]);
                return string.IsNullOrWhiteSpace(acceptStatus) || acceptStatus == "0";
            }).WithMessage("销售合同[{0}]必须为未受理状态才能受理！", (dataObj, propObj) => dataObj["fbillno"]));
        }


        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //获取表单模型
            var motaModelService = this.Container.TryGetService<IMetaModelService>();
            //加载量尺模型数据
            var orderFrom = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            //操作数据库
            var dm = this.Container.GetService<IDataManager>();
            //初始化上下文
            dm.InitDbContext(this.Context, orderFrom.GetDynamicObjectType(this.Context));

            //用户点击受理
            //自动将受理状态从未受理改为已受理
            //将受理日期更新为当前操作时间
            //受理人更新为当前用户
            foreach (var item in e.DataEntitys)
            {
                //自动将受理状态从未受理改为已受理
                item["facceptstatus"] = "1";
                //受理人更新为当前用户
                item["facceptperson"] = this.Context.UserId;
                
                //将受理日期更新为当前操作时间
                item["facceptdate"] = DateTime.Now;
               
            }
            dm.InitDbContext(this.Context, orderFrom.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);
            this.AddRefreshPageAction();
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "受理成功！";

        }
    }
}
