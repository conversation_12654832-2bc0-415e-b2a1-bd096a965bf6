using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockPick;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 一件代发，下推出库单
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("piecesendorderpushsostock")]
    public class PieceSendOrderPushSoStock : AbstractOperationServicePlugIn
    {

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var result = AutoPushSoStockOut(e.DataEntitys.Where(x => Convert.ToString(x["fstatus"]) == "E").ToArray());
            if (result > 0)
                this.Result.IsSuccess = true;
            else
            {
                this.Result.IsSuccess = false;
                this.Result.SrvData = "2";//没有满足  “出现货”且“自提”的商品明细  。进行其他逻辑判断
            }
        }

        /// <summary>
        /// 将“出现货”且“自提”的商品明细自动下推生成销售出库单（自动 保存，提交，审核）
        /// </summary>
        /// <param name="dataEntitys"></param>
        private int AutoPushSoStockOut(DynamicObject[] dataEntitys)
        {
            if (dataEntitys.Length <= 0)
            {
                this.Result.IsSuccess = false;
                return 0;
            }

            //选中商品行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);

            var soStockOutForm = this.MetaModelService.LoadFormModel(this.Context, "stk_sostockout");
            var soStockOutBills = new List<DynamicObject>();

            BillConvertContext billCvtCtx = null;
            var ruleId = "ydj_order2stk_sostockout";
            var sourceFormId = "ydj_order";
            var targetFormId = "stk_sostockout";
            var selRows = new List<SelectedRow>();

            foreach (var dataEntity in dataEntitys)
            {
                //是否存在“出现货”且“自提”的商品明细行
                var entrys = dataEntity["fentry"] as DynamicObjectCollection;

                selRows.AddRange(entrys.Where(x => selectRowIds.Select(c => c.Id.ToString()).ToList().Contains(Convert.ToString(x["id"]))).Select(x =>
                {
                    var sel = new SelectedRow
                    {
                        PkValue = dataEntity["id"].ToString(),
                        BillNo = dataEntity["fbillno"].ToString(),
                        EntityKey = "fentry",
                        EntryPkValue = Convert.ToString(x["id"])
                    };
                    return sel;
                }).ToList());

            }

            billCvtCtx = new BillConvertContext()
            {
                RuleId = ruleId,
                SourceFormId = sourceFormId,
                TargetFormId = targetFormId,
                SelectedRows = selRows.ToConvertSelectedRows()
            };

            var count = billCvtCtx.SelectedRows.Count();
            if (count == 0)
            {
                return count;
            }

            var orderCommon = new OrderCommon(this.Context);
            if (!orderCommon.IsSaleTransferOrder(dataEntitys[0]))
            {
                //判断是否允许出库  备注：改为在提交按钮判断-2022/04/15
                //var newfsumamount = Convert.ToDouble(fsumamount) * fproportionoratioamount / 100;
                //if (Convert.ToDouble(freceivable) < newfsumamount)
                //    throw new BusinessException("当前订单确认已收金额不足" + newfsumamount + "元,暂不允许出库!");
            }
            this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), dataEntitys, false);
            orderCommon.CheckTransferOrderApprovingAndHasShipperAgentByEntry(dataEntitys, selectRowIds.Select(_ => _.Id).ToArray());
            var convertService = this.Container.GetService<IConvertService>();
            var result = convertService.Push(this.Context, billCvtCtx);
            var convertResult = result.SrvData as ConvertResult;


            StockPickSetting setting = new StockPickSetting
            {
                ActiveEntityKey = "fentity",
                QtyFieldKey = "fqty",
                PlanQtyFieldKey = "fplanqty",
                StockQtyFieldKey = "fstockqty",
                PriceFieldKey = "",
                AmountFieldKey = ""
            };
            var stockPickService = this.Container.GetService<IStockPickService>();

            if (convertResult.TargetDataObjects != null && convertResult.TargetDataObjects.Count() > 0)
            {

                soStockOutBills.AddRange(convertResult.TargetDataObjects);
            }

            Dictionary<string, object> keys = new Dictionary<string, object>();
            keys.Add("e3Auto", "true");
            if (soStockOutBills.Count > 0)
            {
                var invokeSave = this.Gateway.InvokeBillOperation(this.Context,
                    soStockOutForm.Id,
                    soStockOutBills,
                    "save",
                    keys);
                invokeSave?.ThrowIfHasError(true, $"自动保存销售出库单失败！");

                if (invokeSave.IsSuccess)
                {
                    var invokeSubmit = this.Gateway.InvokeBillOperation(this.Context,
                        soStockOutForm.Id,
                        soStockOutBills,
                        "submit",
                        keys);
                    invokeSubmit?.ThrowIfHasError(true, $"自动提交销售出库单失败！");
                    if (invokeSubmit.IsSuccess)
                    {
                        var invokeAudit = this.Gateway.InvokeBillOperation(this.Context,
                            soStockOutForm.Id,
                            soStockOutBills,
                            "audit",
                            keys);
                        invokeAudit?.ThrowIfHasError(true, $"自动审核销售出库单失败！");
                        return 1;
                    }
                }
            }
            return 0;
        }
    }
}
