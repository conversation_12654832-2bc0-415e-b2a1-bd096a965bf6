using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.Enums;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同商品明细：关闭
    /// 作者：zpf
    /// 日期：2022-03-10
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("bizclose")]
    public class EntryBizClose : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var errorMessage = "";
            //选中的商品明细行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds.IsNullOrEmptyOrWhiteSpace() ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(rowIds);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var flag = true;
                // 加载数据
                var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
                refObjMgr?.Load(this.Context, new[] { newData }, true, this.HtmlForm, new List<string> { "fproductid" });
                var entries = newData["fentry"] as DynamicObjectCollection;
                if (entries != null && entries.Count > 0)
                {
                    var rows = 0;
                    foreach (var entry in entries)
                    {
                        rows++;
                        if (!selectRowIds.ToArray().Contains(Convert.ToString(entry["id"])))
                        {
                            continue;
                        }
                        var status = entry["ftransferorderstatus"].ToString();
                        if (!status.IsNullOrEmptyOrWhiteSpace() && (Convert.ToInt32(status) == (int)TransferOrderStatus.Approving || Convert.ToInt32(status) == (int)TransferOrderStatus.Approved))
                        {
                            errorMessage += $"商品明细第{rows}行, 商品 {JNConvert.ToStringAndTrim((entry["fproductid_ref"] as DynamicObject)?["fname"])} 转单申请单为审批中！不允许执行后续操作 ！";
                            flag = false;
                        }
                    }
                }
                return flag;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(new Validation_InventoryTransfer(selectRowIds));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            // 套件与沙发商品是否成套采购
            var profileService = this.Container.GetService<ISystemProfile>();
            var isPdk = profileService.GetSystemParameter(this.Context, "pur_systemparam", "fispdk", false);

            //选中的商品明细行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds.IsNullOrEmptyOrWhiteSpace() ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(rowIds);

            foreach (var dataEntity in e.DataEntitys)
            {
                var entryDatas = dataEntity["fentry"] as DynamicObjectCollection;

                var selEntryDatas = entryDatas
                    .Where(t => !selectRowIds.Any() || selectRowIds.Contains(t["id"].ToString()))
                    .ToList();

                // 销售数量为0时不允许关闭
                foreach (var entry in selEntryDatas)
                {
                    if (Convert.ToDecimal(entry["fbizqty"]) == 0)
                    {
                        this.Result.ComplexMessage.WarningMessages.Add($"第{entry["fseq"]}行商品明细的销售数量为0，不允许关闭。");
                    }
                }

                // 将不是【自动关闭】且【销售数量】>0 的明细行设置为【手动关闭】
                selEntryDatas
                    .FindAll(t => Convert.ToString(t["fclosestatus_e"]) != CloseStatusConst.Auto && Convert.ToDecimal(t["fbizqty"]) > 0)
                    .ForEach(t => t["fclosestatus_e"] = CloseStatusConst.Manual);

                // 最终走统一的关闭状态计算逻辑
                Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(dataEntity, this.Context, "bizclose", isCalcEntryClose: true);

                // #36178 套件与沙发组合的同组商品一起关闭或反关闭
                Core.Helpers.DocumentStatusHelper.SetSuitCombProductCloseStatus(
                    isPdk, this.HtmlForm, dataEntity, entryDatas, selEntryDatas, this.Context);
            }

            this.Context.SaveBizData("ydj_order", e.DataEntitys);

            //自动释放预留
            ReserveReleaseSetting setting = new ReserveReleaseSetting() { SelectEntryRow = selectRowIds, ReleaseType = 0, ReleaseWay = 4 };
            var releaseService = this.Context.Container.GetService<IReserveReleaseService>();
            var result = releaseService.Release(this.Context, setting, this.HtmlForm, e.DataEntitys, this.Option);

            ProductDelistingHelper.DealDelistingDataByCommon(this.Context, this.HtmlForm, this.OperationNo, e.DataEntitys.ToList(), selectRowIds);

        }


    }

    /// <summary>
    /// 销售合同商品明细：反关闭
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("unclose")]
    public class EntryUnClose : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //选中的商品明细行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds.IsNullOrEmptyOrWhiteSpace() ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(rowIds);

            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entrys = (newData["fentry"] as DynamicObjectCollection)
                    .Where(t => selectRowIds.Count == 0 || selectRowIds.Contains(t["id"].ToString()))
                    .ToList();
                var flag = true;
                foreach (var entry in entrys)
                {
                    if (Convert.ToString(entry["fclosestatus_e"]) != CloseStatusConst.Manual)
                    {
                        errorMessage += $"第{entry["fseq"]}行明细的【行关闭状态】不是“手动关闭”，不允许反关闭！";
                        flag = false;
                    }
                }
                return flag;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var flag = true;
                // 加载数据
                var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
                refObjMgr?.Load(this.Context, new []{ newData }, true, this.HtmlForm, new List<string> { "fproductid" });
                var entries = newData["fentry"] as DynamicObjectCollection;
                if (entries != null && entries.Count > 0)
                {
                    var rows = 0;
                    foreach (var entry in entries)
                    {
                        rows++;
                        if (!selectRowIds.ToArray().Contains(Convert.ToString(entry["id"])))
                        {
                            continue;
                        }
                        var status = entry["ftransferorderstatus"].ToString();
                        if (!status.IsNullOrEmptyOrWhiteSpace() && (Convert.ToInt32(status) == (int)TransferOrderStatus.Approving || Convert.ToInt32(status) == (int)TransferOrderStatus.Approved))
                        {
                            errorMessage += $"商品明细第{rows}行, 商品 {JNConvert.ToStringAndTrim((entry["fproductid_ref"] as DynamicObject)?["fname"])} 转单申请单为审批中！不允许执行后续操作 ！";
                            flag = false;
                        }
                    }
                }
                return flag;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            // 套件与沙发商品是否成套采购
            var profileService = this.Container.GetService<ISystemProfile>();
            var isPdk = profileService.GetSystemParameter(this.Context, "pur_systemparam", "fispdk", false);

            //选中的商品明细行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            //是否将手动关闭且采购数量不为0的明细行关闭状态更新
            var modifyCloseStatus = this.GetQueryOrSimpleParam<string>("modifyCloseStatus", "") == "1";
            var selectRowIds = rowIds.IsNullOrEmptyOrWhiteSpace() ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(rowIds);

            foreach (var dataEntity in e.DataEntitys)
            {
                var entryDatas = dataEntity["fentry"] as DynamicObjectCollection;
                foreach (var item in entryDatas)
                {
                    if (modifyCloseStatus && int.Parse(Convert.ToString(item["fclosestatus_e"])) == (int)CloseStatus.Manual)
                    {
                        //使后续进入更正关闭状态
                        item["fclosestatus_e"] = (int)CloseStatus.Default;
                    }
                }

                var selEntryDatas = entryDatas
                    .Where(t => !selectRowIds.Any() || selectRowIds.Contains(t["id"].ToString()))
                    .ToList();

                // 销售数量为0时不允许反关闭
                foreach (var entry in selEntryDatas)
                {
                    if (Convert.ToDecimal(entry["fbizqty"]) == 0)
                    {
                        this.Result.ComplexMessage.WarningMessages.Add($"第{entry["fseq"]}行商品明细的销售数量为0，不允许反关闭。");
                    }
                }

                // 最终走统一的关闭状态计算逻辑
                Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(dataEntity, this.Context, isCalcEntryClose: true);

                // #36178 套件与沙发组合的同组商品一起关闭或反关闭
                Core.Helpers.DocumentStatusHelper.SetSuitCombProductCloseStatus(
                    isPdk, this.HtmlForm, dataEntity, entryDatas, selEntryDatas, this.Context);
            }

            this.Context.SaveBizData("ydj_order", e.DataEntitys);

            ////取消释放预留
            //ReserveReleaseSetting setting = new ReserveReleaseSetting() { SelectEntryRow = selectRowIds, ReleaseType = 1, ReleaseWay = 4 };
            //var releaseService = this.Context.Container.GetService<IReserveReleaseService>();
            //var result = releaseService.Release(this.Context, setting, this.HtmlForm, e.DataEntitys, this.Option);

            ProductDelistingHelper.DealDelistingDataByCommon(this.Context, this.HtmlForm, this.OperationNo, e.DataEntitys.ToList(), selectRowIds);
        }
    }
}