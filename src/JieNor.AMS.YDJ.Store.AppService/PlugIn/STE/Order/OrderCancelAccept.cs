using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：受理
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("cancelaccept")]
 public   class OrderCancelAccept : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data)
                   .IsTrue((newData, oldData) => Convert.ToString(newData["facceptstatus"]) == "1")
                   .WithMessage("销售合同[{0}]必须为已受理状态才能取消受理！", (dataObj, propObj) => dataObj["fbillno"]));
        }


        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            e.DataEntitys = checkStoreStatement(e.DataEntitys);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //获取表单模型
            var motaModelService = this.Container.TryGetService<IMetaModelService>();
            //加载量尺模型数据
            var orderFrom = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            //操作数据库
            var dm = this.Container.GetService<IDataManager>();
            //初始化上下文
            dm.InitDbContext(this.Context, orderFrom.GetDynamicObjectType(this.Context));

            //用户点击取消受理
            //将受理状态更新为未受理
            //清空受理日期与受理人
            foreach (var item in e.DataEntitys)
            {
                //将受理状态更新为未受理
                item["facceptstatus"] = "0";
                //清空受理日期与受理人
                item["facceptperson"] =string.Empty;              
                item["facceptdate"] =null;
            }
            dm.InitDbContext(this.Context, orderFrom.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);
            this.AddRefreshPageAction();
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "取消受理成功！";

        }

        /// <summary>
        /// 检查销售合同是否加入店面结算对账单明细
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject[] checkStoreStatement(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return dataEntities;
            }

            var orderIds = dataEntities.Select(x => Convert.ToString(x["id"])).ToList();
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_storestatement");
            var multiValueQuertyService = this.Container.GetService<IMultiValueQueryService>();
            var where = "fmainorgid=@fmainorgid";
            var sqlParams = new List<SqlParam> { new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company) };
            var storeEntities = multiValueQuertyService.Select(this.Context, where, sqlParams, htmlForm, "forderid", orderIds);

            if (storeEntities == null || storeEntities.Count <= 0)
            {
                return dataEntities;
            }

            orderIds = storeEntities.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                                    .Select(x => Convert.ToString(x["forderid"]))
                                    .Where(x => false == string.IsNullOrWhiteSpace(x))
                                    .Distinct()
                                    .ToList();

            if (orderIds == null || orderIds.Count <= 0)
            {
                return dataEntities;
            }

            var validEntities = new List<DynamicObject>();
            foreach (var dataEntity in dataEntities)
            {
                var orderId = Convert.ToString(dataEntity["id"]);
                if (orderIds.Contains(orderId))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"编号[{dataEntity["fbillno"]}]的单据已关联{htmlForm.Caption},不允许取消受理!");
                    continue;
                }
                validEntities.Add(dataEntity);
            }
            return validEntities.ToArray();
        }
    }
}
