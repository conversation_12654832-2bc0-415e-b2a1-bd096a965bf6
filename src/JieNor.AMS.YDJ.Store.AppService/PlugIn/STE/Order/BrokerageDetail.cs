using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：佣金详情（打开费用申请编辑页面）
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("brokeragedetail")]
    public class BrokerageDetail : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                throw new BusinessException($"请先保存后再执行该操作！");
            }

            var targetForm = this.MetaModelService.LoadFormModel(this.Context, "ste_registfee");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, targetForm.GetDynamicObjectType(this.Context));

            var sqlFormat = @"
select t.fid from {0} t
where exists(select 1 from {1} te inner join {2} tmp on tmp.fid=te.fsourceinterid and te.fsourceformid='{3}' and te.fid=t.fid)
and t.fmainorgid='{4}'
";
            var orderIds = e.DataEntitys
                            .Select(x => Convert.ToString(x["id"]))
                            .Distinct()
                            .ToList();
            var dbService = this.Container.GetService<IDBService>();
            List<DynamicObject> downstreamBills = null;
            using (var tran = this.Context.CreateTransaction())
            {
                var tableName = dbService.CreateTempTableWithDataList(this.Context, orderIds,false);
                var sql = string.Format(sqlFormat,
                                        targetForm.BillHeadTableName,
                                        targetForm.GetEntryEntity("fentry").TableName,
                                        tableName,
                                        this.HtmlForm.Id,
                                        this.Context.Company);
                var dataReader = dbService.ExecuteReader(this.Context, sql);
                downstreamBills = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                tran.Complete();

                dbService.DeleteTempTableByName(Context, tableName, true);
            }

            if (downstreamBills != null && downstreamBills.Any())
            {
                foreach (var downstreamBill in downstreamBills)
                {
                    //打开目标单据编辑页面的指令
                    var action = this.Context.ShowSpecialForm(targetForm,
                        downstreamBill,
                        false,
                        this.OperationContext.PageId,
                        Enu_OpenStyle.Default,
                        Enu_DomainType.Bill);
                    this.OperationContext.Result.HtmlActions.Add(action);
                }
                this.OperationContext.Result.IsSuccess = true;
            }
            else
            {
                this.OperationContext.Result.SimpleMessage = $"当前{this.HtmlForm.Caption}没有关联的付款单！";
                this.OperationContext.Result.IsSuccess = false;
            }

            this.OperationContext.Result.IsShowMessage = true;
        }
    }
}