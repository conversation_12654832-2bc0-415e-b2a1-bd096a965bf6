using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：检查是否有变更记录
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("CheckChangeRecord")]
    public class CheckChangeRecord: AbstractOperationServicePlugIn
    {

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            HasChangeRecord(this.Context, e);
        }
        /// <summary>
        /// 验证是否已生成变更单
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="result"></param>
        private void HasChangeRecord(UserContext ctx, BeginOperationTransactionArgs e)
        {
            var billno = this.GetQueryOrSimpleParam<string>("billno");
            if (string.IsNullOrWhiteSpace(billno))
            {
                throw new BusinessException("单据编号不能为空!");
            }
            string strSql = @"select t1.fsourcenumber, t1.fmainorgid from T_YDJ_ORDER_CHG t1 with(nolock)
                            where t1.fsourcenumber = '{0}' and t1.fmainorgid='{1}' and t1.fstatus={2}".Fmt(string.Join("','", billno), ctx.Company, "'B'");
            var objs = ctx.ExecuteDynamicObject(strSql, null);
            if (objs == null || !objs.Any())
            {
                this.Result.SrvData = billno;
            }
            else
            {
                this.Result.SrvData = "";
            }
            this.Result.IsSuccess = true;
        }
    }
}
