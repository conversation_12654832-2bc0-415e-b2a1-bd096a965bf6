using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.SaleIntention;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：联查
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("LinkFormSearch")]
    public class LinkFormSearch : LinkFormSearchBase
    {
        protected override void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            var pkids = dataEntities.Select(o => o["id"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (pkids.Count <= 0) return;

            //过滤条件
            var filterStr = "";
            if (pkids.Count == 1)
            {
                filterStr = $"fsoorderinterid='{pkids[0]}'";
            }
            else
            {
                filterStr = $"fsoorderinterid in('{string.Join("','", pkids)}')";
            }

            var metaModelService = userContext.Container.GetService<IMetaModelService>();

            var insIndex = 0;
            for (int i = 0; i < linkFormDatas.Count; i++)
            {
                if (linkFormDatas[i]["formId"].Equals("stk_sostockout"))
                {
                    insIndex = i + 1;
                }
            }
            //销售退货通知单
            var returnNoticeForm = metaModelService.LoadFormModel(this.Context, "sal_returnnotice");
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", returnNoticeForm.Id },
                { "formCaption", returnNoticeForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr }
            });

            //销售退货单
            var stockReturnForm = metaModelService.LoadFormModel(this.Context, "stk_sostockreturn");
            if (insIndex > 0)
            {
                linkFormDatas.Insert(insIndex, new Dictionary<string, object>
                {
                    { "formId", stockReturnForm.Id },
                    { "formCaption", stockReturnForm.Caption },
                    { "flag", "nextForm" },
                    { "filterString", filterStr }
                });
            }
            else
            {
                linkFormDatas.Add(new Dictionary<string, object>
                {
                    { "formId", stockReturnForm.Id },
                    { "formCaption", stockReturnForm.Caption },
                    { "flag", "nextForm" },
                    { "filterString", filterStr }
                });
            }


            //收支记录
            var incomeDisburseForm = metaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            filterStr = pkids.Count == 1 ? $"fsourceformid='{this.HtmlForm.Id}' and fsourceid='{pkids[0]}'" :
                                           $"fsourceformid='{this.HtmlForm.Id}' and fsourceid in ({string.Join(",", pkids.Select(x => $"'{x}'"))})";
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", incomeDisburseForm.Id },
                { "formCaption", incomeDisburseForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr },
                { "visible",1}
            });

            //联查采购订单
            LinkPurchaseorder(dataEntities, metaModelService, linkFormDatas);
            // 联查发货扫描任务、收货扫描任务
            LinkScanTask(dataEntities, metaModelService, linkFormDatas);
            //预留单
            LinkReserveBill(userContext, dataEntities, linkFormDatas);
            //联查采购入库单
            LinkPoStockIn(dataEntities, metaModelService, linkFormDatas);
        }

        /// <summary>
        /// 联查采购订单
        /// </summary>
        private void LinkPurchaseorder(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            var id = dataEntities.Select(o => o["id"]).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            var strSql = $@"select distinct fid from t_ydj_poorderentry with(nolock) where fsourceinterid ='{id[0]}'";
            List<string> fids = new List<string>();
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    fids.Add(dr["fid"].ToString());
                }
            }
            var purFlag = -1;
            if (fids.Count == 0)
            {
                for (int i = 0; i < linkFormDatas.Count; i++)
                {
                    foreach (var val in linkFormDatas[i])
                    {
                        if (val.Value.ToString() == "ydj_purchaseorder")
                        {
                            purFlag = i;
                            linkFormDatas[purFlag]["filterString"] = " 1=2";
                            return;
                        }
                    }
                }
                return;
            }

            //找到采购订单下标用于修改
            for (int i = 0; i < linkFormDatas.Count; i++)
            {
                foreach (var val in linkFormDatas[i])
                {
                    if (val.Value.ToString() == "ydj_purchaseorder")
                    {
                        purFlag = i;
                        goto ModifyData;
                    }
                }
            }
        ModifyData:
            if (purFlag < 0)
            {
                var purchaseorder = metaModelService.LoadFormModel(this.Context, "ydj_purchaseorder");
                string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));

                linkFormDatas.Add(new Dictionary<string, object>
                    {
                        { "formId", purchaseorder.Id },
                        { "formCaption", purchaseorder.Caption },
                        { "flag", "nextForm" },
                        { "filterString", filterStr },
                        { "visible",1}
                    });
            }
            else
            {
                var purchaseorder = metaModelService.LoadFormModel(this.Context, "ydj_purchaseorder");
                string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));

                linkFormDatas[purFlag] = (new Dictionary<string, object>
                    {
                        { "formId", purchaseorder.Id },
                        { "formCaption", purchaseorder.Caption },
                        { "flag", "nextForm" },
                        { "filterString", filterStr },
                        { "visible",1}
                    });
            }

        }

        /// <summary>
        /// 联查发货扫描任务、收货扫描任务
        /// </summary>
        private void LinkScanTask(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            var fbillnos = dataEntities.Select(o => o["fbillno"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (fbillnos.Count <= 0) return;

            //过滤条件
            var filterStr = "";
            if (fbillnos.Count == 1)
            {
                filterStr = $"fsourceformid='ydj_order' and fsourcebillno='{fbillnos[0]}'";
            }
            else
            {
                filterStr = $"fsourceformid='ydj_order' and fsourcebillno in('{string.Join("','", fbillnos)}')";
            }
            //联查发货扫描任务
            var deliveryForm = metaModelService.LoadFormModel(this.Context, "bcm_deliveryscantask");
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", deliveryForm.Id },
                { "formCaption", deliveryForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr }
            });

            //联查收货扫描任务
            var receptionForm = metaModelService.LoadFormModel(this.Context, "bcm_receptionscantask");
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", receptionForm.Id },
                { "formCaption", receptionForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr }
            });
        }

        /// <summary>
        /// 联查采购入库单
        /// </summary>
        private void LinkPoStockIn(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            if (!this.Context.IsDirectSale) return;

            var idList = dataEntities.Select(o => o["id"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (idList.Count == 0) return;

            // 查询采购入库单fid
            var strSql = $@"select distinct fid from t_stk_postockinentry with(nolock) where fsourceinterid ='{idList[0]}'";
            List<string> fids = new List<string>();
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    fids.Add(dr["fid"].ToString());
                }
            }

            var stockInFlag = -1;
            if (fids.Count == 0)
            {
                for (int i = 0; i < linkFormDatas.Count; i++)
                {
                    foreach (var val in linkFormDatas[i])
                    {
                        if (val.Value.ToString() == "stk_postockin")
                        {
                            stockInFlag = i;
                            linkFormDatas[stockInFlag]["filterString"] = " 1=2";
                            return;
                        }
                    }
                }
                return;
            }

            // 找到采购入库单下标用于修改
            for (int i = 0; i < linkFormDatas.Count; i++)
            {
                foreach (var val in linkFormDatas[i])
                {
                    if (val.Value.ToString() == "stk_postockin")
                    {
                        stockInFlag = i;
                        goto ModifyData;
                    }
                }
            }
        ModifyData:
            var poStockInForm = metaModelService.LoadFormModel(this.Context, "stk_postockin");
            string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));
            var stockInDict = new Dictionary<string, object>
    {
        { "formId", poStockInForm.Id },
        { "formCaption", poStockInForm.Caption },
        { "flag", "nextForm" },
        { "filterString", filterStr },
        { "visible", 1 }
    };

            if (stockInFlag < 0)
            {
                linkFormDatas.Add(stockInDict);
            }
            else
            {
                linkFormDatas[stockInFlag] = stockInDict;
            }
        }


    }
}