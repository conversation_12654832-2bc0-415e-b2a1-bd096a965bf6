using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Leads
{
    /// <summary>
    /// 获取某字段的常用值
    /// </summary>
    [InjectService]
    [FormId("ydj_leads")]
    [OperationNo("getrecentfielddata")]
    public class GetRescentCloseReason:AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            var fieldId = this.GetQueryOrSimpleParam<string>("fieldKey");
            if (!fieldId.EqualsIgnoreCase("fclosereason"))
            {
                return;
            }

            var lstPresetFlag = new List<bool>()
            {
                true,
                true,
                true
            };
            var lstPreset = new List<Dictionary<string, object>>
            {
                new Dictionary<string,object>{{"fclosereason","客户联系不上"}},
                new Dictionary<string,object>{{ "fclosereason", "客户预算不足"}},
                new Dictionary<string,object>{{ "fclosereason", "没有购买意向"}},
            };
            var resultData = this.Result.SrvData as List<Dictionary<string, object>>;

            if (resultData == null) resultData = new List<Dictionary<string, object>>();

            
            foreach(var fieldValItem in resultData)
            {
                object closeReason = "";
                fieldValItem.TryGetValue("fclosereason", out closeReason);
                if(Convert.ToString(closeReason).EqualsIgnoreCase("客户联系不上"))
                {
                    lstPresetFlag[0] = false;
                }
                if (Convert.ToString(closeReason).EqualsIgnoreCase("客户预算不足"))
                {
                    lstPresetFlag[1] = false;
                }
                if (Convert.ToString(closeReason).EqualsIgnoreCase("没有购买意向"))
                {
                    lstPresetFlag[2] = false;
                }
            }

            for(int i = 0; i < lstPresetFlag.Count; i++)
            {
                if (lstPresetFlag[i])
                {
                    resultData.Add(lstPreset[i]);
                }
            }
            this.Result.SrvData = resultData;
        }
    }
}
