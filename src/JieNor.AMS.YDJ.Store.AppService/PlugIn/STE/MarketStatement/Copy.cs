using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.MarketStatement
{
    [InjectService]
    [FormId("ydj_marketstatement")]
    [OperationNo("Copy")]
    public class Copy : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                if (dataEntity.DynamicObjectType.Properties.ContainsKey("fsharedtrackno"))
                {
                    dataEntity["fsharedtrackno"] = "";
                }
                if (dataEntity.DynamicObjectType.Properties.ContainsKey("fsubmitsharetime"))
                {
                    dataEntity["fsubmitsharetime"] = null;
                }
                if (dataEntity.DynamicObjectType.Properties.ContainsKey("fcollaborativesharestatus"))
                {
                    dataEntity["fcollaborativesharestatus"] = "";
                } 
                if (dataEntity.DynamicObjectType.Properties.ContainsKey("fupdatestatustime"))
                {
                    dataEntity["fupdatestatustime"] = null;
                } 
                if (dataEntity.DynamicObjectType.Properties.ContainsKey("fsharedapprover"))
                {
                    dataEntity["fsharedapprover"] = "";
                } 
                if (dataEntity.DynamicObjectType.Properties.ContainsKey("fsyncmessage"))
                {
                    dataEntity["fsyncmessage"] = "";
                }
            }

            this.Result.IsSuccess = true;
        }
    }
}