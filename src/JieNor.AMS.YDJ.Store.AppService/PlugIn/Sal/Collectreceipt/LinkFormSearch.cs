using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Collectreceipt
{
    /// <summary>
    /// 销售合同：联查
    /// </summary>
    [InjectService]
    [FormId("ydj_collectreceipt")]
    [OperationNo("LinkFormSearch")]
    public class LinkFormSearch : LinkFormSearchBase
    {
        protected override void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            var pkids = dataEntities.Select(o => o["id"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (pkids.Count <= 0) return;


            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            //收支记录
            var incomeDisburseForm = metaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            //过滤条件
            var filterStr = pkids.Count == 1 ? $"fsourceformid='{this.HtmlForm.Id}' and fsourceid='{pkids[0]}'" :
                                           $"fsourceformid='{this.HtmlForm.Id}' and fsourceid in ({pkids.JoinEx(",", true)})";
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", incomeDisburseForm.Id },
                { "formCaption", incomeDisburseForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr },
                { "visible",1}
            });
        }
    }
}
