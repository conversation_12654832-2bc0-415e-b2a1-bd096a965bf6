using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.AccountTransfer
{
    /// <summary>
    /// 账户转账单：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_accounttransfer")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fdate"]).NotEmpty().WithMessage("转账日期不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fdisburseaccount"]).NotEmpty().WithMessage("转出账户不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fincomeaccount"]).NotEmpty().WithMessage("转入账户不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fcustomerid"]).NotEmpty().WithMessage("客户不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                decimal amount = 0;
                decimal.TryParse(Convert.ToString(newData["famount"]), out amount);
                return !(amount < 0.01M);
            }).WithMessage("转账金额必须大于0！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return !Convert.ToString(newData["fincomeaccount"]).EqualsIgnoreCase(Convert.ToString(newData["fdisburseaccount"]));
            }).WithMessage("转入账户不允许和转出账户相同！"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            
        }
    }
}