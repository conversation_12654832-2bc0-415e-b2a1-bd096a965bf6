using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Promotion
{
    /// <summary>
    /// 促销活动：发布
    /// </summary>
    [InjectService]
    [FormId("ydj_productpromotion|ydj_combopromotion")]
    [OperationNo("Publish")]
    public class Publish : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */

            string message = $"{this.HtmlForm.GetNumberField().Caption}为【{{0}}】的{this.HtmlForm.Caption} 不是 未发布，不允许{this.OperationName}！";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fpublishstatus = Convert.ToString(newData["fpublishstatus"]);

                return fpublishstatus.EqualsIgnoreCase("0");
            }).WithMessage(message, (billObj, propObj) => propObj["fnumber"]));

            if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_productpromotion"))
            {
                e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
                {
                    var fruletype = Convert.ToString(newData["fruletype"]);
                    if (fruletype.EqualsIgnoreCase("ruletype_02"))
                    {
                        var productEntrys = newData["fproductentry"] as DynamicObjectCollection;
                        if (productEntrys.Any(s => Convert.ToDecimal(s["fprice"]) <= 0))
                        {
                            return false;
                        }
                    }

                    return true;
                }).WithMessage("特价商品销售价必须大于0。"));

                //在商品促销<发布>时，如果是满赠促销，则需要校验《赠品》不能为空，并提示：“当前活动为满赠促销，赠品不能为空！”。
                e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
                {
                    var fruletype = Convert.ToString(newData["fruletype"]);
                    if (fruletype.EqualsIgnoreCase("ruletype_01"))
                    {
                        var fgiftentry = newData["fgiftentry"] as DynamicObjectCollection;
                        return fgiftentry.Any(s => !Convert.ToString(s["fmaterialid"]).IsNullOrEmptyOrWhiteSpace());
                    }

                    return true;
                }).WithMessage("当前活动为满赠促销，赠品不能为空！"));
            }
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                dataEntity["fpublishstatus"] = "1";
            }

            this.Context.SaveBizData(this.HtmlForm.Id, e.DataEntitys);
        }
    }
}