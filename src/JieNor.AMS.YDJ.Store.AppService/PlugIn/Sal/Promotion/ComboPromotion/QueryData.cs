using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Promotion.ComboPromotion
{
    [InjectService]
    [FormId("ydj_combopromotion")]
    [OperationNo("QueryData")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                //case OnCustomServiceEventArgs.AfterCreateUIData:
                //    this.AfterListData(e);
                //    break;
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    PrepareQueryBuilderParameter(e);
                    break;
            }
        }
        //private void AfterListData(OnCustomServiceEventArgs e)
        //{
        //    var listData = e.EventData as List<Dictionary<string, object>>;
        //}

        private void PrepareQueryBuilderParameter(OnCustomServiceEventArgs e)
        {
            var param = e.EventData as SqlBuilderParameter;

            var dynamicParamStr = (this.OperationContext as ListOperationContext)?.DynamicParam;
            if (!dynamicParamStr.IsNullOrEmptyOrWhiteSpace())
            {
                var dynamicParam = JObject.Parse(dynamicParamStr);

                var deptId = dynamicParam.GetJsonValue<string>("deptId");
                var sourceFormId = dynamicParam.GetJsonValue<string>("sourceFormId");

                if (sourceFormId.EqualsIgnoreCase("ydj_order"))
                {
                    param.EnableDataRowACL = false;
                    if (deptId.IsNullOrEmptyOrWhiteSpace())
                    {
                        param.AppendFilterString("1=2");
                    }
                    else
                    {
                        // 按指定部门进行过滤
                        param.AppendFilterString($"exists(select 1 from t_ydj_combopromotiondept dept with(nolock) where t0.fid=dept.fid and dept.fdeptid='{deptId}')");
                    }
                }
            }

        }
    }
}
