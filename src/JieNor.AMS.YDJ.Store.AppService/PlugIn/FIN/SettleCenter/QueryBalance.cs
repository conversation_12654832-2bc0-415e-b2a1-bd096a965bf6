using System;
using System.Linq;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SettleCenter
{
    /// <summary>
    /// 账户信息：查询账户余额
    /// </summary>
    [InjectService]
    [FormId("pay_settleorder")]
    [OperationNo("QueryBalance")]
    public class QueryBalance : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //var toAuthSimpleData = this.SimpleData;
            //toAuthSimpleData["accountId"] = this.Context.AccountId;

            ////转发到AC站点
            //var response = this.Gateway.Invoke(this.Context,
            //    TargetSEP.AuthService,
            //    new CommonBillDTO()
            //    {
            //        FormId = "pay_accountinfo",
            //        OperationNo = "QueryBalance",
            //        SimpleData = toAuthSimpleData
            //    }) as CommonBillDTOResponse;
            //response?.OperationResult?.ThrowIfHasError(true, $"系统未返回任何响应！");

            //var authSrvData = response?.OperationResult?.SrvData;
            //if (authSrvData != null)
            //{
            //    this.Result.SrvData = JObject.Parse(Convert.ToString(authSrvData));
            //}
            //this.Result.IsSuccess = true;

            var customerId = this.GetQueryOrSimpleParam<string>("customerId");
            DynamicObject customer = null;

            if (!string.IsNullOrWhiteSpace(customerId))
            {
                var metaModelService = this.Container.GetService<IMetaModelService>();
                var htmlForm = metaModelService.LoadFormModel(this.Context,"ydj_customer");
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                customer = dm.Select(customerId) as DynamicObject;
            }
            else
            {
                var customerService = this.Container.GetService<ICustomerService>();
                customer = customerService.GetCustomerInfo(this.Context);
            }

            if (customer == null)
            {
                throw new BusinessException("没有找到相关商户信息!");
            }

            decimal balance = 0;

            var fentries = customer["fentry"] as DynamicObjectCollection;

            if (fentries != null && fentries.Count > 0)
            {
                foreach(var fentry in fentries)
                {
                    if(Convert.ToString(fentry["fpurpose"])== "settleaccount_type_01")
                    {
                        balance = Convert.ToDecimal(fentry["fbalance_e"]);
                        break;
                    }
                }
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = new { balance = balance };
        }
    }
}