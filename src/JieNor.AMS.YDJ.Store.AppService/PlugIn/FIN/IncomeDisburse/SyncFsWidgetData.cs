//using JieNor.Framework;
//using JieNor.Framework.CustomException;
//using JieNor.Framework.DataTransferObject.FileServer;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.SuperOrm.DataEntity;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse
//{
//    /// <summary>
//    /// 响应文件服务器组件上传后保存文件的事件
//    /// </summary>
//    [InjectService]
//    [FormId("coo_incomedisburse")]
//    [OperationNo("fsw_uploadfile")]
//    public class SyncFSWidgetUploadData : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 执行操作事务
//        /// </summary>
//        /// <param name="e"></param>
//        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
//        {            
//            var tranId = this.GetQueryOrSimpleParam<string>("CooTranId");

//            var fsData = this.GetQueryOrSimpleParam<string>("fsData")?
//                .FromJson<IEnumerable<FSWidgetFileInfo>>() ?? new FSWidgetFileInfo[] { };

//            if (tranId.IsNullOrEmptyOrWhiteSpace()
//                || fsData.Any() == false) return;

//            var dm = this.GetDataManager();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
//            var pkIdReader = this.Context.GetPkIdDataReader(this.HtmlForm, "ftranid=@tranid",
//                new SqlParam[]
//                {
//                    new SqlParam("tranid", System.Data.DbType.String, tranId)
//                });
//            var billDataObj = dm.SelectBy(pkIdReader).OfType<DynamicObject>()
//                .FirstOrDefault();
//            if (billDataObj.IsNullOrEmpty()) return;

//            var voucherImageField = this.HtmlForm.GetField("fimage") as HtmlMulImageField;

//            if (voucherImageField == null) return;

//            var voucherImages = voucherImageField.DynamicProperty.GetValue<string>(billDataObj);
//            var voucherImagesItems = voucherImages.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
//                .ToList();

//            var voucherImagesDescItems = voucherImageField.TxtDynamicProperty.GetValue<string>(billDataObj)
//                .Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
//                .ToList();

//            foreach (var fileInfo in fsData)
//            {
//                if (voucherImagesItems.Contains(fileInfo.FileId, StringComparer.OrdinalIgnoreCase)) continue;
//                voucherImagesItems.Add(fileInfo.FileId);
//            }
//            voucherImageField.DynamicProperty.SetValue(billDataObj, string.Join(",", voucherImagesItems));
//            dm.Save(billDataObj);
//        }
//    }

//    /// <summary>
//    /// 响应文件组件初始化时的文件详情数据准备
//    /// </summary>
//    [InjectService]
//    [FormId("coo_incomedisburse")]
//    [OperationNo("fsw_initfile")]
//    public class SyncFSWidgetInitData : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 执行操作事务
//        /// </summary>
//        /// <param name="e"></param>
//        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
//        {
//            var tranId = this.GetQueryOrSimpleParam<string>("CooTranId");

//            if (tranId.IsNullOrEmptyOrWhiteSpace()) return;

//            var dm = this.GetDataManager();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
//            var pkIdReader = this.Context.GetPkIdDataReader(this.HtmlForm, "ftranid=@tranid",
//                new SqlParam[]
//                {
//                    new SqlParam("tranid", System.Data.DbType.String, tranId)
//                });
//            var billDataObj = dm.SelectBy(pkIdReader).OfType<DynamicObject>()
//                .FirstOrDefault();
//            if (billDataObj.IsNullOrEmpty()) return;

//            var voucherImageField = this.HtmlForm.GetField("fimage") as HtmlMulImageField;

//            if (voucherImageField == null) return;

//            var voucherImages = voucherImageField.DynamicProperty.GetValue<string>(billDataObj);
            
//            List<FSWidgetFileInfo> lstFileData = new List<FSWidgetFileInfo>();
//            int i = 1;
//            foreach (var fileId in voucherImages.Split(','))
//            {
//                if (fileId.IsNullOrEmptyOrWhiteSpace()) continue;

//                var creatorIdObj = billDataObj["fcreatorid_ref"] as DynamicObject;

//                FSWidgetFileInfo fsFile = new FSWidgetFileInfo();
//                fsFile.Id = "";
//                fsFile.FileId = fileId;
//                fsFile.FileName = "缴款凭据"+i.ToString();
//                fsFile.FileFormat = "jpg/png";
//                fsFile.FileSize = "";
//                fsFile.Uploader = creatorIdObj?["fname"] as string;
//                fsFile.UploaderId = billDataObj["fcreatorid"] as string;
//                fsFile.UploadTime = (DateTime)billDataObj["fcreatedate"];
//                fsFile.Description = "";
//                fsFile.CooSrcId = "";
//                lstFileData.Add(fsFile);
//            }

//            this.Result.SrvData = lstFileData.ToJson();
//        }
//    }
//}
