//using System;
//using System.Linq;
//using System.Collections.Generic;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.CustomException;
//using JieNor.Framework.SuperOrm.DataManager;
//using JieNor.Framework.DataTransferObject;
//using JieNor.Framework.DataTransferObject.Poco;
//using JieNor.Framework.MetaCore.Validator;
//using JieNor.Framework.SuperOrm;
//using JieNor.Framework.MetaCore.FormOp.FormService;
//using JieNor.AMS.YDJ.DataTransferObject.Enums;
//using JieNor.AMS.YDJ.Core.Interface;
//using JieNor.AMS.YDJ.Store.AppService.Helper;
//using JieNor.Framework.Interface.CloudChain;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IncomeDisburse
//{
//    /// <summary>
//    /// 收支记录：红冲，生成一条对应的红冲收支记录
//    /// </summary>
//    [InjectService]
//    [FormId("coo_incomedisburse")]
//    [OperationNo("Invalid")]
//    public class Invalid : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 预处理校验规则
//        /// </summary>
//        /// <param name="e"></param>
//        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
//        {
//            base.PrepareValidationRules(e);

//            /*
//                定义表头校验规则
//            */
//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                if (Convert.ToBoolean(newData["fissyn"]) && !Convert.ToString(newData["fcreatecompanyid"]).EqualsIgnoreCase(this.Context.Company))
//                {
//                    return false;
//                }
//                return true;

//            }).WithMessage("创建方不是当前企业，不允许红冲！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                if (!Convert.ToString(newData["fbizstatus"]).EqualsIgnoreCase("bizstatus_02"))
//                {
//                    return false;
//                }
//                return true;

//            }).WithMessage("状态不是【已确认】，不允许红冲！"));

//            var errorMessage = "";
//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                if (Convert.ToString(newData["fpurpose"]).EqualsIgnoreCase("bizpurpose_04"))
//                {
//                    errorMessage = "用途是【红冲】，不允许红冲！";
//                    return false;
//                }
//                if (Convert.ToString(newData["fpurpose"]).EqualsIgnoreCase("bizpurpose_08"))
//                {
//                    errorMessage = "用途是【账户转账】，不允许红冲！";
//                    return false;
//                }
//                return true;

//            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            Convert.ToBoolean(newData["fissyn"])).WithMessage("非协同收支记录不允许红冲！请反确认非协同收支记录！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                var statementStatus = Convert.ToString(newData["fstatementstatus"]);
//                return statementStatus != "2" && statementStatus != "3";
//            }).WithMessage("收支记录处于对账中或已对账状态，不允许红冲！"));
//        }

//        /// <summary>
//        /// 调用操作事物前触发的事件
//        /// </summary>
//        /// <param name="e"></param>
//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
//            {
//                if (this.Result.ComplexMessage.ErrorMessages.Count <= 0)
//                {
//                    this.Result.ComplexMessage.WarningMessages.Add("请选择一行或多行数据！");
//                    this.Result.IsSuccess = false;
//                }
//                return;
//            }

//            //收支记录模型
//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

//            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
//            var incomeDisburseService = this.Container.GetService<IIncomeDisburseService>();

//            foreach (var dataEntity in e.DataEntitys)
//            {
//                //获取红冲收支记录数据包
//                List<DynamicObject> redIncomeDisburses = new List<DynamicObject>();
//                redIncomeDisburses.Add(this.GetRedIncomeDisburseObject(dataEntity));

//                //保存前预处理
//                prepareService?.PrepareDataEntity(this.Context, this.HtmlForm, redIncomeDisburses.ToArray(), OperateOption.Create());
//                //初始化收支记录
//                incomeDisburseService.InitIncomeDisburse(this.Context, redIncomeDisburses);

//                //发送协同更新请求，生成对方红冲收支记录
//                this.SendSynInvalid(dataEntity, redIncomeDisburses);

//                //生成本地红冲记录
//                dm.Save(redIncomeDisburses);

//                //更新源收支记录状态为“已红冲”
//                dataEntity["fbizstatus"] = "bizstatus_03";
//                dm.Save(dataEntity);

//                //RewriteOrder(redIncomeDisburses);
//            }

//            this.AddRefreshPageAction();
//            this.Result.IsSuccess = true;
//        }

//        /// <summary>
//        /// 发送协同红冲请求，生成对方红冲收支记录
//        /// </summary>
//        /// <param name="incomeDisburse"></param>
//        private void SendSynInvalid(DynamicObject incomeDisburse, List<DynamicObject> incomeDisburses)
//        {
//            //如果不是协同收支记录，则无需发送协同请求
//            //if (!Convert.ToBoolean(incomeDisburse["fissyn"])) return;

//            //协同企业ID
//            var cooCompanyId = incomeDisburse["fcoocompanyid"] as string;
//            var cooProductId = incomeDisburse["fcooproductid"] as string;

//            /*
//             * 支持未协同的收支记录发送协同红冲操作
//             * 1.客户 或 供应商 一开始没有建立协同关系时，在本地产生了一些收支记录。
//             * 2.后面又和其他企业建立了协同关系，此时在之前的收支记录上面做红冲操作（协同关系已经建立）
//             */
//            if (cooCompanyId.IsNullOrEmptyOrWhiteSpace() || cooProductId.IsNullOrEmptyOrWhiteSpace())
//            {
//                //与之关联的协同对象（客户 或 供应商）
//                var syncObjFormId = "ydj_supplier";
//                var syncObjId = incomeDisburse["fsupplierid"] as string;
//                if (syncObjId.IsNullOrEmptyOrWhiteSpace())
//                {
//                    syncObjFormId = "ydj_customer";
//                    syncObjId = incomeDisburse["fcustomerid"] as string;
//                }
//                if (!syncObjFormId.IsNullOrEmptyOrWhiteSpace() && !syncObjId.IsNullOrEmptyOrWhiteSpace())
//                {
//                    var syncObjForm = this.MetaModelService?.LoadFormModel(this.Context, syncObjFormId);
//                    var syncObjDm = this.Container.GetService<IDataManager>();
//                    syncObjDm.InitDbContext(this.Context, syncObjForm.GetDynamicObjectType(this.Context));
//                    var syncObj = syncObjDm.Select(syncObjId) as DynamicObject;
//                    if (syncObj != null)
//                    {
//                        cooCompanyId = Convert.ToString(syncObj["fcoocompanyid"]);
//                        cooProductId = Convert.ToString(syncObj["fcooproductid"]);
//                    }
//                }
//            }
//            if (cooCompanyId.IsNullOrEmptyOrWhiteSpace() || cooProductId.IsNullOrEmptyOrWhiteSpace()) return;



//            ////数据发送时采用异步消息模式发送，消息中指定回调类型
//            //var responseResult = this.Gateway.Invoke(
//            //    this.Context,
//            //    new TargetSEP(cooCompanyId, cooProductId),
//            //    new CommonBillDTO()
//            //    {
//            //        FormId = "coo_incomedisburse",
//            //        OperationNo = "InvalidSynergy",
//            //        BillData = this.GetRedIncomeDisburseBillDatas(incomeDisburses),
//            //        ExecInAsync = false,
//            //        AsyncMode = (int)Enu_AsyncMode.Background,
//            //        SimpleData = new Dictionary<string, string>
//            //        {

//            //        }
//            //    }) as CommonBillDTOResponse;
//            //responseResult?.OperationResult?.ThrowIfHasError(true, $"协同收支记录红冲失败，对方系统未返回任何响应！");

//            //协同生成收支记录（不分经销模式） 
//            this.SynergismRecord(new TargetSEP(cooCompanyId, cooProductId), incomeDisburses);
//        }

//        /// <summary>
//        ///  协同生成收支记录（不分经销模式） 
//        /// </summary>
//        /// <param name="target"></param>
//        /// <param name="incomeDisburses"></param>
//        private void SynergismRecord(TargetSEP target, List<DynamicObject> incomeDisburses)
//        {
//            if (incomeDisburses == null || incomeDisburses.Count == 0) return;

//            ////发布基础数据到云链系统 
//            //var chainDataSyncService = this.Container.GetService<IChainDataSyncService>();
//            //var chainDataJson = chainDataSyncService.PublishDataToChainAndPack(
//            //        this.Context, HtmlForm, incomeDisburses, target, new List<string> { "fcustomerid", "fway", "fdeptid", "fexpenseitemid" });

//            //数据发送时采用异步消息模式发送，消息中指定回调类型
//            var responseResult = this.Gateway.Invoke(
//                this.Context,
//                target,
//                new CommonBillDTO()
//                {
//                    FormId = "coo_incomedisburse",
//                    OperationNo = "SaveSynRecord",
//                    BillData = this.GetRedIncomeDisburseBillDatas(incomeDisburses),
//                    ExecInAsync = false,
//                    AsyncMode = (int)Enu_AsyncMode.Background,
//                    SimpleData = new Dictionary<string, string>()
//                    {
//                      { "cooCompanyId", target.CompanyId },
//                      //{ "chainDataJson", chainDataJson }
//                    }
//                }) as CommonBillDTOResponse;
//            responseResult?.OperationResult?.ThrowIfHasError(true, $"协同收支记录红冲失败，对方系统未返回任何响应！");
//        }

//        /// <summary>
//        /// 获取本地红冲收支记录数据包
//        /// </summary>
//        /// <param name="incomeDisburse"></param>
//        /// <returns></returns>
//        private DynamicObject GetRedIncomeDisburseObject(DynamicObject incomeDisburse)
//        {
//            DynamicObject billHead = new DynamicObject(this.HtmlForm.GetDynamicObjectType(this.Context));
//            billHead["fdate"] = DateTime.Today;
//            billHead["fway"] = incomeDisburse["fway"];
//            billHead["faccount"] = incomeDisburse["faccount"];
//            billHead["fpurcompany"] = incomeDisburse["fpurcompany"];
//            billHead["fpurcompanyid"] = incomeDisburse["fpurcompanyid"];
//            billHead["fsalecompany"] = incomeDisburse["fsalecompany"];
//            billHead["fsalecompanyid"] = incomeDisburse["fsalecompanyid"];
//            billHead["fcreatecompany"] = incomeDisburse["fcreatecompany"];
//            billHead["fcreatecompanyid"] = incomeDisburse["fcreatecompanyid"];
//            billHead["fpurpose"] = "bizpurpose_04";
//            billHead["fbizstatus"] = "bizstatus_01";
//            billHead["foldtranid"] = incomeDisburse["ftranid"];
//            billHead["forderno"] = incomeDisburse["forderno"];
//            billHead["famount"] = incomeDisburse["famount"];
//            billHead["fimage"] = incomeDisburse["fimage"];
//            billHead["fdescription"] = $"红冲收支记录流水号为：{incomeDisburse["ftranid"]} 的记录。";
//            billHead["fmybankid"] = incomeDisburse["fmybankid"];
//            billHead["fsynbankid"] = incomeDisburse["fsynbankid"];
//            billHead["fsynbankname"] = incomeDisburse["fsynbankname"];
//            billHead["fsynbanknum"] = incomeDisburse["fsynbanknum"];
//            billHead["fsynaccountname"] = incomeDisburse["fsynaccountname"];
//            billHead["fverificstatus"] = "verificstatus_01";

//            //红冲收支记录的账户方向 与 源收支记录的金额方向相反
//            var direction = Convert.ToString(incomeDisburse["fdirection"]).Trim().ToLower();
//            switch (direction)
//            {
//                case "direction_01":
//                    billHead["fdirection"] = "direction_02";
//                    break;
//                case "direction_02":
//                    billHead["fdirection"] = "direction_01";
//                    break;
//                default:
//                    break;
//            }

//            //红冲收支记录的收支方向 与 源收支记录的收支方向相反
//            var bizDirection = Convert.ToString(incomeDisburse["fbizdirection"]).Trim().ToLower();
//            switch (bizDirection)
//            {
//                case "bizdirection_01":
//                    billHead["fbizdirection"] = "bizdirection_02";
//                    break;
//                case "bizdirection_02":
//                    billHead["fbizdirection"] = "bizdirection_01";
//                    break;
//                default:
//                    break;
//            }

//            billHead["fsupplierid"] = incomeDisburse["fsupplierid"];
//            billHead["fcustomerid"] = incomeDisburse["fcustomerid"];
//            billHead["fcustomerphone"] = incomeDisburse["fcustomerphone"];
//            billHead["fcoocompanyid"] = incomeDisburse["fcoocompanyid"];
//            billHead["fcooproductid"] = incomeDisburse["fcooproductid"];
//            billHead["fsourcetranid"] = incomeDisburse["fsourcetranid"];
//            billHead["fsourceid"] = incomeDisburse["fsourceid"];
//            billHead["fsourcenumber"] = incomeDisburse["fsourcenumber"];
//            billHead["fsourceformid"] = incomeDisburse["fsourceformid"];
//            billHead["fisself"] = incomeDisburse["fisself"];
//            billHead["fissyn"] = incomeDisburse["fissyn"];
//            billHead["foperationmode"] = incomeDisburse["foperationmode"];
//            billHead["fdeptid"] = incomeDisburse["fdeptid"];
//            billHead["fcontactunittype"] = incomeDisburse["fcontactunittype"];
//            billHead["fcontactunitid"] = incomeDisburse["fcontactunitid"];
//            billHead["freducedbrokerage"] = incomeDisburse["freducedbrokerage"];

//            return billHead;
//        }

//        /// <summary>
//        /// 获取协同方红冲收支记录数据包
//        /// </summary>
//        /// <param name="redIncomeDisburses"></param>
//        /// <returns></returns>
//        private string GetRedIncomeDisburseBillDatas(List<DynamicObject> redIncomeDisburses)
//        {
//            List<Dictionary<string, object>> billDatas = new List<Dictionary<string, object>>();

//            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_banknum");
//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

//            foreach (var redIncomeDisburse in redIncomeDisburses)
//            {
//                Dictionary<string, object> billData = new Dictionary<string, object>();
//                billData["ftranid"] = redIncomeDisburse["ftranid"];
//                billData["fdate"] = redIncomeDisburse["fdate"];
//                billData["fway"] = redIncomeDisburse["fway"];
//                billData["faccount"] = redIncomeDisburse["faccount"];
//                billData["fpurcompany"] = redIncomeDisburse["fpurcompany"];
//                billData["fpurcompanyid"] = redIncomeDisburse["fpurcompanyid"];
//                billData["fsalecompany"] = redIncomeDisburse["fsalecompany"];
//                billData["fsalecompanyid"] = redIncomeDisburse["fsalecompanyid"];
//                billData["fcreatecompany"] = redIncomeDisburse["fcreatecompany"];
//                billData["fcreatecompanyid"] = redIncomeDisburse["fcreatecompanyid"];
//                billData["fpurpose"] = redIncomeDisburse["fpurpose"];
//                billData["fbizstatus"] = redIncomeDisburse["fbizstatus"];
//                billData["foldtranid"] = redIncomeDisburse["foldtranid"];
//                billData["forderno"] = "";
//                billData["fdirection"] = redIncomeDisburse["fdirection"];
//                billData["famount"] = redIncomeDisburse["famount"];
//                billData["fimage"] = redIncomeDisburse["fimage"];
//                billData["fdescription"] = redIncomeDisburse["fdescription"];
//                billData["fverificstatus"] = redIncomeDisburse["fverificstatus"];

//                //对换银行账号信息
//                var myBankId = Convert.ToString(redIncomeDisburse["fmybankid"]);
//                if (!myBankId.IsNullOrEmptyOrWhiteSpace())
//                {
//                    var myBank = dm.Select(myBankId) as DynamicObject;
//                    if (myBank != null)
//                    {
//                        billData["fsynbankname"] = myBank["fbankname"];
//                        billData["fsynbanknum"] = myBank["fbanknum"];
//                        billData["fsynaccountname"] = myBank["fname"];
//                    }
//                }
//                billData["fmybankid"] = redIncomeDisburse["fsynbankid"];
//                billData["fsynbankid"] = myBankId;

//                billData["fsupplierid"] = "";
//                billData["fcustomerid"] = "";
//                billData["fcoocompanyid"] = this.Context.Company;
//                billData["fcooproductid"] = this.Context.Product;
//                billData["fsourcetranid"] = redIncomeDisburse["fsourcetranid"];
//                billData["fsourceid"] = "";
//                billData["fsourcenumber"] = "";

//                switch (Convert.ToString(redIncomeDisburse["fsourceformid"]).Trim().ToLower())
//                {
//                    case "ydj_purchaseorder":
//                        billData["fsourceformid"] = "ydj_saleintention";
//                        break;
//                    case "ydj_saleintention":
//                        billData["fsourceformid"] = "ydj_purchaseorder";

//                        if (Convert.ToString(redIncomeDisburse["foperationmode"])
//                            .EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == true)
//                        {
//                            billData["fsourceformid"] = redIncomeDisburse["fsourceformid"];
//                        }

//                        break;
//                    case "ydj_supplier":
//                        billData["fsourceformid"] = "ydj_customer";
//                        break;
//                    case "ydj_customer":
//                        billData["fsourceformid"] = "ydj_supplier";
//                        break;
//                    case "ydj_order":
//                        billData["fsourceformid"] = "ydj_order";
//                        break;
//                    default:
//                        break;
//                }

//                var bizDirection = Convert.ToString(redIncomeDisburse["fbizdirection"]).Trim().ToLower();
//                switch (bizDirection)
//                {
//                    case "bizdirection_01":
//                        billData["fbizdirection"] = "bizdirection_02";
//                        break;
//                    case "bizdirection_02":
//                        billData["fbizdirection"] = "bizdirection_01";
//                        break;
//                    default:
//                        break;
//                }

//                billData["fisself"] = false;
//                billData["fissyn"] = true; //redIncomeDisburse["fissyn"]; //之前未协同的收支记录也要支持红冲协同
//                billData["foperationmode"] = redIncomeDisburse["foperationmode"];

//                //SetSaleMan(this.Context, redIncomeDisburse, billData);

//                //SetExpenseEntry(redIncomeDisburse, billData);

//                //SetK3Org(this.Context, redIncomeDisburse, billData);

//                //// 协同【已扣佣金】
//                //billData["freducedbrokerage"] = redIncomeDisburse["freducedbrokerage"];

//                billDatas.Add(billData);
//            }

//            return billDatas.ToJson();
//        }

//        /// <summary>
//        /// 设置K3的组织ID
//        /// </summary>
//        private void SetK3Org(UserContext userCtx, DynamicObject incomeDisburse, Dictionary<string, object> billData)
//        {
//            //单据头部门
//            var deptId = Convert.ToString(incomeDisburse["fdeptid"]);
//            var deptIds = new List<string> { deptId };

//            //加载部门对应的K3组织ID
//            var deptOrgMapKv = DirectSynergyHelper.GetK3OrgIdsByDeptIds(userCtx, deptIds);

//            var orgData = new Dictionary<string, string>();
//            deptOrgMapKv.TryGetValue(deptId, out orgData);
//            var orgId = "";
//            orgData?.TryGetValue("orgId", out orgId);

//            billData["fsaleorgid"] = orgId; //K3销售组织ID
//            billData["freceiveorgid"] = orgId; //K3收款组织ID

//            var deptNumber = "";
//            if (!deptId.IsNullOrEmptyOrWhiteSpace())
//            {
//                var deptObj = userCtx.LoadBizDataById("ydj_dept", deptId);
//                deptNumber = Convert.ToString(deptObj?["fnumber"]);
//            }
//            billData["fdeptnumber"] = deptNumber; //麦浩部门编码，用于匹配K3的部门
//        }

//        private void SetExpenseEntry(DynamicObject source, Dictionary<string, object> target)
//        {
//            DynamicObjectCollection sourceEntryCollection = source["fexpenseentry"] as DynamicObjectCollection;

//            if (sourceEntryCollection == null || sourceEntryCollection.Count <= 0)
//            {
//                return;
//            }

//            var sourceList = sourceEntryCollection.Where(x => !string.IsNullOrWhiteSpace(Convert.ToString(x["fexpenseitemid"])) && Convert.ToDecimal(x["famount"]) > 0)
//                .ToList();

//            if (sourceList == null || sourceList.Count <= 0)
//            {
//                return;
//            }

//            List<Dictionary<string, object>> targetEntryCollection = null;

//            if (target.Keys.Contains("fexpenseentry"))
//            {
//                targetEntryCollection = target["fexpenseentry"] as List<Dictionary<string, object>>;
//            }

//            if (targetEntryCollection == null)
//            {
//                targetEntryCollection = new List<Dictionary<string, object>>();
//                target["fexpenseentry"] = targetEntryCollection;
//            }

//            foreach (var sourceItem in sourceList)
//            {
//                Dictionary<string, object> targetItem = new Dictionary<string, object>();
//                targetItem["fexpenseitemid"] = sourceItem["fexpenseitemid"];
//                targetItem["famount_ee"] = sourceItem["famount"];
//                targetItem["fdescription_ee"] = sourceItem["fdescription"];
//                targetItem["fexpenseentry_ftranid"] = sourceItem["ftranid"];
//                targetEntryCollection.Add(targetItem);
//            }
//        }

//        /// <summary>
//        /// 设置销售员
//        /// </summary>
//        /// <param name="userCtx"></param>
//        /// <param name="incomeDisburse"></param>
//        /// <param name="billData"></param>
//        private void SetSaleMan(UserContext userCtx, DynamicObject incomeDisburse, Dictionary<string, object> billData)
//        {
//            var fsourceid = Convert.ToString(incomeDisburse["fsourceid"]);
//            var fsourceformid = Convert.ToString(incomeDisburse["fsourceformid"]);

//            if (!fsourceformid.EqualsIgnoreCase("ydj_order") && !fsourceformid.EqualsIgnoreCase("ydj_saleintention")) return;

//            var sourceOrder = this.Context.LoadBizDataById(fsourceformid, fsourceid);

//            // 增加销售员 
//            string fstaffid = Convert.ToString(sourceOrder["fstaffid"]);
//            if (!fstaffid.IsNullOrEmptyOrWhiteSpace())
//            {
//                var seller = this.Context.LoadBizDataById("ydj_staff", fstaffid);
//                billData["fsalemanid"] = seller?["id"];
//                billData["fsalemannumber"] = seller?["fnumber"];
//                billData["fsalemanname"] = seller?["fname"];
//            }
//            else
//            {
//                billData["fsalemanid"] = string.Empty;
//                billData["fsalemannumber"] = string.Empty;
//                billData["fsalemanname"] = string.Empty;
//            }
//        }

//        ///// <summary>
//        ///// 反写销售合同
//        ///// </summary>
//        ///// <param name="incomeDisburses">收支记录</param>
//        //private void RewriteOrder(IEnumerable<DynamicObject> incomeDisburses)
//        //{
//        //    if (incomeDisburses == null || incomeDisburses.Count() == 0) return;

//        //    var orderIds = incomeDisburses
//        //        .Where(s => Convert.ToString(s["fsourceformid"]).EqualsIgnoreCase("ydj_order"))
//        //        .Select(s => Convert.ToString(s["fsourceid"]));

//        //    if (orderIds.Count() == 0) return;

//        //    List<DynamicObject> sourceOrders = this.Context.LoadBizDataById("ydj_order", orderIds);
//        //    HashSet<DynamicObject> savedOrders = new HashSet<DynamicObject>();

//        //    foreach (var incomeDisburse in incomeDisburses)
//        //    {
//        //        string fsourceformid = Convert.ToString(incomeDisburse["fsourceformid"]);
//        //        if (!fsourceformid.EqualsIgnoreCase("ydj_order")) continue;

//        //        string fsourceid = Convert.ToString(incomeDisburse["fsourceid"]);

//        //        var sourceOrder = sourceOrders.FirstOrDefault(s => Convert.ToString(s["Id"]).EqualsIgnoreCase(fsourceid));

//        //        var freducedbrokerage = Convert.ToDecimal(incomeDisburse["freducedbrokerage"]);
//        //        if (freducedbrokerage > 0)
//        //        {
//        //            // 红冲【账户方向】=减 且【业务状态】=已确认的收支记录后，《收支记录》的【已扣佣金】立即反写（累减）《销售合同.财务信息》的【已扣拥金】；
//        //            sourceOrder["freducedbrokerage"] = Convert.ToDecimal(sourceOrder["freducedbrokerage"]) - Convert.ToDecimal(incomeDisburse["freducedbrokerage"]);

//        //            savedOrders.Add(sourceOrder);
//        //        }
//        //    }

//        //    if (savedOrders.Count > 0)
//        //    {
//        //        var dm = this.Container.GetService<IDataManager>();
//        //        dm.InitDbContext(this.Context, this.MetaModelService.LoadFormModel(this.Context, "ydj_order").GetDynamicObjectType(this.Context));

//        //        dm.Save(savedOrders);
//        //    }
//        //}
//    }
//}