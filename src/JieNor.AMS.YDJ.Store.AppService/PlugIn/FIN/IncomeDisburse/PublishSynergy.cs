//using System;
//using System.Linq;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.SuperOrm.DataManager;
//using JieNor.AMS.YDJ.Core.Interface;
//using JieNor.Framework.CustomException;
//using JieNor.AMS.YDJ.DataTransferObject.Enums;
//using JieNor.Framework.MetaCore.FormMeta;
//using Newtonsoft.Json.Linq;
//using JieNor.Framework.SuperOrm.Serialization;
//using System.Collections.Generic;
//using JieNor.Framework.MetaCore.FormOp.FormService;
//using System.Text;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IncomeDisburse
//{
//    /// <summary>
//    /// 收支记录：协同发布订购意向书的时候  把对应的收支记录协同过来。
//    /// </summary>
//    [InjectService]
//    [FormId("coo_incomedisburse")]
//    [OperationNo("PublishSynergy")]
//    public class PublishSynergy : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 调用操作事物前触发的事件
//        /// </summary>
//        /// <param name="e"></param>
//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            var orderParamStr = this.GetQueryOrSimpleParam<string>("billDatas");
//            if (orderParamStr.IsNullOrEmptyOrWhiteSpace()) return;
//            //根据多个流水号批量查询订单信息
//            StringBuilder sbInTranId = new StringBuilder();
//            List<SqlParam> paramList = new List<SqlParam>();
//            List<Dictionary<string, string>> orderParamList = orderParamStr?.FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
//            for (int i = 0; i < orderParamList.Count; i++)
//            {
//                var paramName = "@tranid" + i;
//                sbInTranId.Append(paramName).Append(",");
//                paramList.Add(new SqlParam(paramName, System.Data.DbType.String, orderParamList[i]["ftranid"]));
//            }
//            if (sbInTranId.IsNullOrEmptyOrWhiteSpace()) return;

//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
//            string where = $"fmainorgid=@fmainorgid and ftranid in({sbInTranId.ToString().TrimEnd(',')})";
//            paramList.Add(new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company));

//            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, paramList);
//            var dataEntitys = dm.SelectBy(dataReader).OfType<DynamicObject>();
//            var etranid = dataEntitys.Where(n => !Convert.ToString(n["ftranid"]).IsNullOrEmptyOrWhiteSpace())
//             .Select(n => Convert.ToString(n["ftranid"])).Distinct().ToList(); //已经存在对应流水号的采购订单
//            //找流水号为空的数据进行创建 采购订单
//            var newParamList = orderParamList.Where(t => !etranid.Contains(Convert.ToString(t["ftranid"]))).ToList(); //需要创建的采购订单集合

//            //新加收支记录
//            List<DynamicObject> incomeList = new List<DynamicObject>();
//            var metaModel = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "coo_incomedisburse");
//            var syncService = this.Container.GetService<ISynergyService>();
//            //要根据源单流水号去查到对应的 采购订单数据 
//            var fsourcetranidList = orderParamList.Select(n => "'" + n["fsourcetranid"] + "'");
//            string paramstr = string.Join(",", fsourcetranidList);
//            var orderList = this.Context.LoadBizDataByFilter("ydj_purchaseorder", "fmainorgid = '{0}' and ftranid in ({1})".Fmt(this.Context.Company, paramstr));
//            foreach (var incomeParam in newParamList)
//            {
//                //新增收支记录
//                DynamicObject incomeobj = metaModel.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
//                incomeobj["ftranid"] = Convert.ToString(incomeParam["ftranid"]);
//                incomeobj["fdate"] = Convert.ToString(incomeParam["fdate"]);
//                incomeobj["fway"] = Convert.ToString(incomeParam["fway"]);
//                incomeobj["faccount"] = Convert.ToString(incomeParam["faccount"]);
//                incomeobj["fpurcompany"] = Convert.ToString(incomeParam["fpurcompany"]);
//                incomeobj["fpurcompanyid"] = Convert.ToString(incomeParam["fpurcompanyid"]);
//                incomeobj["fsalecompany"] = Convert.ToString(incomeParam["fsalecompany"]);
//                incomeobj["fsalecompanyid"] = Convert.ToString(incomeParam["fsalecompanyid"]);
//                incomeobj["fcreatecompany"] = Convert.ToString(incomeParam["fcreatecompany"]);
//                incomeobj["fcreatecompanyid"] = Convert.ToString(incomeParam["fcreatecompanyid"]);
//                incomeobj["fpurpose"] = Convert.ToString(incomeParam["fpurpose"]);
//                incomeobj["fbizstatus"] = Convert.ToString(incomeParam["fbizstatus"]);
//                incomeobj["fdirection"] = Convert.ToString(incomeParam["fdirection"]);
//                incomeobj["famount"] = Convert.ToString(incomeParam["famount"]);
//                incomeobj["fdescription"] = Convert.ToString(incomeParam["fdescription"]);
//                incomeobj["fcoocompanyid"] = Convert.ToString(incomeParam["fcoocompanyid"]);
//                incomeobj["fcooproductid"] = Convert.ToString(incomeParam["fcooproductid"]);
//                incomeobj["foperationmode"] = Convert.ToString(incomeParam["foperationmode"]);
//                incomeobj["fisself"] = Convert.ToString(incomeParam["fisself"]);
//                incomeobj["fbizdirection"] = Convert.ToString(incomeParam["fbizdirection"]);
//                incomeobj["fverificstatus"] = Convert.ToString(incomeParam["fverificstatus"]);

//                var order = orderList.Where(n => Convert.ToString(n["ftranid"]).EqualsIgnoreCase(Convert.ToString(incomeParam["fsourcetranid"]))).FirstOrDefault();
//                if (order.IsNullOrEmptyOrWhiteSpace())
//                {
//                    continue;
//                }
//                incomeobj["fsourceformid"] = "ydj_purchaseorder";
//                incomeobj["fsourcenumber"] = Convert.ToString(order["fbillno"]);
//                incomeobj["forderno"] = Convert.ToString(order["fbillno"]);
//                incomeobj["fsourceid"] = Convert.ToString(order["id"]);
//                incomeobj["fsourcetranid"] = Convert.ToString(order["ftranid"]);
//                incomeList.Add(incomeobj);
//                //还没保存，记录流水号
//                syncService.WriteLog(this.Context, this.HtmlForm, incomeParam["ftranid"] as string, "publish", "订购意向书协同发布", this.Context.CallerContext);
//            }
//            var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, incomeList, "SaveSynergy", null);
//            if (result.ComplexMessage.ErrorMessages.Count() > 0)
//            {
//                this.Result.ComplexMessage.ErrorMessages.AddRange(result.ComplexMessage.ErrorMessages);
//                return;
//            }
//            this.Result.IsSuccess = true;
//        }
//    }
//}