using JieNor.AMS.YDJ.Store.AppService.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.MarketStatement;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.Utils;


namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.Registfee
{
    [InjectService]
    [FormId("ste_registfee")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;
            var extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]);
            switch (extFieldId)
            {
                //成本中心
                case "zyCostCenter":
                    e.Cancel = true;
                    var fsourceinterid = Convert.ToString(dataEntity["fsourceinterid"]);
                    var order = GetOrderInfo(fsourceinterid);
                    if (order != null)
                    {
                        //焕新订单标记
                        var frenewalflag = Convert.ToBoolean(order?["frenewalflag"]);
                        //【关联门店】对应《门店》表头的【招商经销商】传给中台, 如果该部门没有【关联门店】则默认当前企业对应的经销商的《经销商》表头的【招商经销商】 (如果存在多个时随机给一个就行, 但保证每次给都是同一个就行)
                        var dealerObj = frenewalflag ? GetStore(order) : GetDealer(order);

                        e.Result = Convert.ToString(dealerObj?["fnumber"]);
                    }
                    break;
                //身份证号
                case "zyIdCard":
                    e.Cancel = true;
                    var isCard = GetSteChannelInfo(Convert.ToString(dataEntity["frelatecusid"]));
                    //当【合作渠道.合作主体类型=个人】，则取【合作渠道.身份证号】
                    if (isCard != null && Convert.ToString(isCard["fchanneltype"]) == "0")
                    {
                        e.Result = isCard["fidcard"];
                    }

                    break;
                //销售组织编码
                case "zySalesOrg":
                    e.Cancel = true;
                    e.Result = GetSaleOrgCode(e.DataEntity);
                    break;
                //合作渠道编码
                case "zyChannelNo":
                    e.Cancel = true;
                    var channel = GetSteChannelInfo(Convert.ToString(dataEntity["frelatecusid"]));
                    if (channel != null)
                    {
                        //合作渠道-sap供应商编码
                        e.Result = channel["fsuppliercode"];
                    }

                    break;
                //来票类型
                case "zyLplx":
                    e.Cancel = true;
                    //当【发票=空值】传编码：004 （后补发票）；反之，传003（正常来票）
                    var finvoicefile = Convert.ToString(dataEntity["finvoicefile"]);
                    if (finvoicefile.IsNullOrEmptyOrWhiteSpace())
                    {
                        e.Result = "004";
                    }
                    else
                    {
                        e.Result = "003";
                    }

                    break;
                //备注
                case "zyComments":
                    e.Cancel = true;
                    var frelatetype = Convert.ToString(bizEntity["frelatetype"]);
                    if (frelatetype == "ste_channel")
                    {
                        var channelInfo = GetSteChannelInfo(Convert.ToString(bizEntity["frelatecusid"]));
                        //传：付+合同渠道名称+销售佣金 示例：付张三销售佣金
                        e.Result = "付" + Convert.ToString(channelInfo["fname"]) + "销售佣金";
                    }

                    break;
                // 附件集合
                case "statementFile":
                    e.Cancel = true;
                    e.Result = new List<ImgModel>();
                    List<ImgModel> result = new List<ImgModel>();
                    //附件
                    if (!dataEntity["finvoicefile"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var imgIds = Convert.ToString(dataEntity["finvoicefile"]).Split(',');
                        var imgTxts = Convert.ToString(dataEntity["finvoicefile_txt"]).Split(',');
                        for (int i = 0; i < imgIds.Length; i++)
                        {
                            if (!imgIds[i].IsNullOrEmptyOrWhiteSpace())
                            {
                                var name = (i + 1 > imgTxts.Length ? imgIds[i] : imgTxts[i]);
                                result.Add(new ImgModel
                                {
                                    code = imgIds[i],
                                    name = name.IsNullOrEmptyOrWhiteSpace() ? imgIds[i] : name,
                                    //zyFileData = StreamUtility.ConvertImageToBase64(imgIds[i]?.GetSignedFileUrl()),
                                    zyFileData = imgIds[i]?.GetSignedFileUrl(),
                                    fileAddr = imgIds[i]?.GetSignedFileUrl()
                                });
                            }
                        }
                    }

                    //发票
                    if (!dataEntity["fevidence"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var imgIds = Convert.ToString(dataEntity["fevidence"]).Split(',');
                        var imgTxts = Convert.ToString(dataEntity["fevidence_txt"]).Split(',');
                        for (int i = 0; i < imgIds.Length; i++)
                        {
                            if (!imgIds[i].IsNullOrEmptyOrWhiteSpace())
                            {
                                var name = (i + 1 > imgTxts.Length ? imgIds[i] : imgTxts[i]);
                                result.Add(new ImgModel
                                {
                                    code = imgIds[i],
                                    name = name.IsNullOrEmptyOrWhiteSpace() ? imgIds[i] : name,
                                    //zyFileData = StreamUtility.ConvertImageToBase64(imgIds[i]?.GetSignedFileUrl()),
                                    zyFileData = imgIds[i]?.GetSignedFileUrl(),
                                    fileAddr = imgIds[i]?.GetSignedFileUrl()
                                });
                            }
                        }
                    }

                    e.Result = result;
                    break;
            }
        }

        /// <summary>
        /// 获取部门对应门店记录的 招商经销商
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject GetDealer(DynamicObject dataEntities)
        {
            var deptId = Convert.ToString(dataEntities["fdeptid"]);
            if (deptId == null)
            {
                return null;
            }

            //获取合同对应门店
            var store = Convert.ToString(dataEntities?["fstore"]);
            DynamicObject DealerObj = null;
            // 如果该部门没有【关联门店】则默认当前企业对应的经销商的《经销商》表头的【招商经销商】 (如果存在多个时随机给一个就行, 但保证每次给都是同一个就行)
            if (store.IsNullOrEmptyOrWhiteSpace())
            {
                return GetDealerByAgent();
            }
            else
            {
                var strSql =
                    $@"SELECT top 1 t_ms_crmdistributor.fid,t_ms_crmdistributor.fnumber,t_ms_crmdistributor.fname,store.fnumber as stroreNumber FROM t_bd_department dept 
                        INNER JOIN dbo.T_BAS_STORE store ON store.fid = dept.fstore
                        INNER JOIN t_ms_crmdistributor ON t_ms_crmdistributor.fid = store.foutcrmdistributorid
                        WHERE dept.fid = '{deptId}' AND dept.fmainorgid ='{this.UserContext.Company}' ";

                var dbService = this.UserContext.Container.GetService<IDBService>();
                DealerObj = dbService.ExecuteDynamicObject(this.UserContext, strSql).FirstOrDefault();

                //如果门店的招商经销商没有值  再走经销商 的逻辑
                if (Convert.ToString(DealerObj?["fname"]).IsNullOrEmptyOrWhiteSpace())
                {
                    return GetDealerByAgent();
                }
            }

            return DealerObj;
        }

        private DynamicObject GetDealerByAgent()
        {
            DynamicObject DealerObj = null;
            GetResultBrandData re = new GetResultBrandData();
            var crmdistributorids = re.GetBaseDataNameById(this.UserContext, "bas_agent", this.UserContext.Company,
                "fcrmdistributorid");
            var crmdistributoridLst = crmdistributorids.Split(',');
            if (crmdistributoridLst.Length > 0)
            {
                var crmdistributorid = Convert.ToString(crmdistributoridLst[0]);
                DealerObj = this.UserContext.LoadBizBillHeadDataById("ms_crmdistributor", crmdistributorid,
                    "fid,fnumber,fname");
            }

            return DealerObj;
        }

        /// <summary>
        /// 获取当前数据包所在的经销商档案的销售组织编码
        /// </summary>
        /// <param name="orderDy"></param>
        /// <returns></returns>
        private string GetSaleOrgCode(DynamicObject orderDy)
        {
            var saleOrgId = string.Empty;
            var agentId = Convert.ToString(orderDy["fmainorgid"]);
            if (!agentId.IsNullOrEmptyOrWhiteSpace())
            {
                var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", agentId, "fsaleorgid");
                if (agentDy != null)
                {
                    saleOrgId = Convert.ToString(agentDy["fsaleorgid"]);
                    var saleOrgDy = this.UserContext.LoadBizBillHeadDataById("bas_organization", saleOrgId, "fnumber");
                    if (saleOrgDy != null)
                    {
                        saleOrgId = Convert.ToString(saleOrgDy["fnumber"]);
                    }
                }
            }

            // orderDy
            return saleOrgId;
        }

        /// <summary>
        /// 获取门店编码
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject GetStore(DynamicObject dataEntities)
        {
            var deptId = Convert.ToString(dataEntities["fdeptid"]);
            if (deptId == null)
            {
                return null;
            }

            //获取合同对应门店
            var store = Convert.ToString(dataEntities?["fstore"]);
            //焕新订单标记
            var frenewalflag = Convert.ToBoolean(dataEntities?["frenewalflag"]);

            //焕新订单返回门店编码
            var strSql = $@"SELECT top 1 store.fnumber  FROM t_bd_department dept 
                        INNER JOIN dbo.T_BAS_STORE store ON store.fid = dept.fstore
                        WHERE dept.fid = '{deptId}' AND dept.fmainorgid ='{this.UserContext.Company}' ";

            var dbService = this.UserContext.Container.GetService<IDBService>();
            var DealerObj = dbService.ExecuteDynamicObject(this.UserContext, strSql).FirstOrDefault();

            return DealerObj;
        }

        /// <summary>
        /// 获取合作渠道信息
        /// </summary>
        /// <param name="channelId"></param>
        /// <returns></returns>
        private DynamicObject GetSteChannelInfo(string channelId)
        {
            var channel = this.UserContext.LoadBizDataById("ste_channel", new List<string> { channelId })
                .FirstOrDefault();

            return channel;
        }

        /// <summary>
        /// 获取销售合同信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private DynamicObject GetOrderInfo(string id)
        {
            var order = this.UserContext.LoadBizDataById("ydj_order", new List<string> { id })
                .FirstOrDefault();

            return order;
        }
    }
}