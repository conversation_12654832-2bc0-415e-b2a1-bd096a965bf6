using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using Senparc.Weixin.Helpers.Extensions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.ChargeDialog
{
    /// <summary>
    /// 金蝶协同的账户信息
    /// </summary>
    [InjectService]
    [FormId("coo_inpourdialog")]
    [OperationNo("SynergyAccountData")]
    public class SynergyAccountData : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var fid = this.GetQueryOrSimpleParam<string>("fid");
            var fnumber = this.GetQueryOrSimpleParam<string>("fnumber");
            var fname = this.GetQueryOrSimpleParam<string>("fname");
            this.Result.IsSuccess = false;
            if (fid.IsNullOrWhiteSpace() || fnumber.IsNullOrWhiteSpace() || fname.IsNullOrWhiteSpace())
            {
                this.Result.SimpleMessage = "协同账户失败：没有有效的账户！";
                return;
            }
            //添加账户
            AddAccountInfo(fid, fname);

            this.Result.IsSuccess = true;
            this.Result.SrvData = $@"{fid}";
        }

        #region 事务前操作
        /// <summary>
        ///  添加非预置账户
        /// </summary>
        /// <param name="fid"></param>
        /// <param name="fnumber"></param>
        private void AddAccountInfo(string fid, string fname)
        {
            var seleceSql = "select count(1) from t_bd_enumdataentry where fid = 'e3959c5a41004049b5853d43bb5881a0'; ";
            int seq = 100;//默认六条数据，排序从100开始
            using (var reader = this.DBService.ExecuteReader(this.Context, seleceSql))//查出数据库已存在的总数
            {
                if (reader.Read()) { seq = Convert.ToInt32(reader[0]); }
            }

            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            var delsql = $@"delete from t_bd_enumdataentry where fentryid ='{fid}';";//删除历史数据
            var index = dbServiceExt.Execute(this.Context, delsql);

            ++seq;
            var insterSql = $@"insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) 
                            values('{fid}','e3959c5a41004049b5853d43bb5881a0',{seq},'0','0','','{fname}','0');";
            dbServiceExt.Execute(this.Context, insterSql);

            //保存前清掉缓存
            var comboSvc = this.Container.GetService<IComboDataService>();
            comboSvc.ClearCache(this.Context, "结算单账户类型");
            
            //调用辅助资料保存操作
            var context = this.Context;
            StringBuilder where = new StringBuilder(" fid = 'e3959c5a41004049b5853d43bb5881a0'");
            var enumdata = this.MetaModelService.LoadFormModel(context, "bd_enumdata");//表单模型
            var dm = context.Container.GetService<IDataManager>();//获取服务实例
            dm.InitDbContext(context, enumdata.GetDynamicObjectType(context));//初始化数据
            var dataReader = context.GetPkIdDataReader(enumdata, where.ToString(), new List<SqlParam>());//过滤返回数据
            var dataEnitie = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            List<DynamicObject> dataEnities = new List<DynamicObject>();
            dataEnities.Add(dataEnitie);
            this.Gateway.InvokeBillOperation(this.Context, "bd_enumdata", dataEnities, "save", new Dictionary<string, object>());
        }

        #endregion

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var accountId = this.GetQueryOrSimpleParam<string>("fid");
            AccountSetSupplierAndCustomer(this.Context, accountId);
        }

        #region 事务后操作
        /// <summary>
        /// 调用财务管理保存(保存的时候会添加)或直接添加进客户和供应商的账户列表
        /// 指定账户添加进客户和供应商的账户列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="fid"></param>
        /// <returns></returns>
        public void AccountSetSupplierAndCustomer(UserContext userCtx, string accountId)
        {
            if (accountId.IsNullOrEmptyOrWhiteSpace()) { return; }
            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            //读取 账户设置（经销商）设置信息
            var dealerSetupMeta = metaModelService.LoadFormModel(userCtx, "sal_dealersetup");
            var spService = userCtx.Container.GetService<ISystemProfile>();
            var sysProfileValue = spService?.GetProfile(userCtx, "fw", $"sal_dealersetup_parameter");

            //系统参数有值，给参数赋值.会给供应商客户添加账户
            //反之，直接给客户和供应商添加账户
            if (!sysProfileValue.IsNullOrEmptyOrWhiteSpace())
            {
                List<DynamicObject> sysProfileObjs = new List<DynamicObject>();
                var billItems = new JArray();
                billItems.Add(JObject.Parse(sysProfileValue));
                var dcSerializer = userCtx.Container.GetService<IDynamicSerializer>();
                dcSerializer.Sync(dealerSetupMeta.GetDynamicObjectType(userCtx), sysProfileObjs, billItems);
                if (sysProfileObjs.Count > 0)
                {
                    foreach (var data in sysProfileObjs)
                    {
                        //data["id"] = Guid.NewGuid().ToString();
                        var htmlEntry = dealerSetupMeta.GetEntryEntity("fentry");
                        var synEntrys = htmlEntry.DynamicProperty.GetValue<DynamicObjectCollection>(data);
                        var existEntrys = synEntrys.ToArray();
                        var seqi = existEntrys.Count();
                        var existEntry = existEntrys.FirstOrDefault(o => accountId.EqualsIgnoreCase(o["fpurpose"] as string));
                        if (existEntry == null)
                        {
                            existEntry = htmlEntry.DynamicObjectType.CreateInstance() as DynamicObject;
                            existEntry["fpurpose"] = accountId;
                            existEntry["fseq"] = ++seqi;
                            synEntrys.Add(existEntry);
                        }
                    }
                    //if (Convert.ToString(sysProfileObjs[0]["id"]).IsNullOrEmptyOrWhiteSpace()) { return; }
                    //var reu = this.Gateway.InvokeBillOperation(this.Context, "sal_dealersetup", sysProfileObjs, "save", new Dictionary<string, object>());

                    var dealerSetup = sysProfileObjs[0];

                    UpdatePurposeAlias(sysProfileObjs.ToArray());
                    var setups = dealerSetup["fentry"] as DynamicObjectCollection;
                    if (setups == null || setups.Count <= 0) return;

                    UpdateCustomerInfo(setups, dealerSetup);
                    UpdateSupplierInfo(setups, dealerSetup);
                }
            }
            else
            {
                //所有的供应商和客户，添加新的账户
                var supplierFrom = this.MetaModelService.LoadFormModel(userCtx, "ydj_supplier");//供应商
                var dm = userCtx.Container.GetService<IDataManager>();//获取服务实例
                dm.InitDbContext(userCtx, supplierFrom.GetDynamicObjectType(userCtx));//初始化数据

                string where = "fmainorgid = @fmainorgid";
                var sqlParam = new SqlParam[] { new SqlParam("fmainorgid", System.Data.DbType.String, userCtx.Company) };

                var dataReader = userCtx.GetPkIdDataReader(supplierFrom, where, sqlParam);//过滤返回数据
                var supplierdata = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

                //获取账户信息
                var accountSynService = this.Container.GetService<ISynAccountBalanceService>();
                var allAccountInfo = accountSynService.GetAllAccount(this.Context);
                var seq = allAccountInfo.Count();
                var accountData = allAccountInfo.Where(p => Convert.ToString(p.AccountId).EqualsIgnoreCase(accountId)).FirstOrDefault();
                if (accountData.IsNullOrEmptyOrWhiteSpace()) { return; }
                ++seq;
                foreach (var dataEntity in supplierdata)
                {
                    var htmlEntry = supplierFrom.GetEntryEntity("fentry");
                    var synEntrys = htmlEntry.DynamicProperty.GetValue<DynamicObjectCollection>(dataEntity);
                    var existEntrys = synEntrys.ToArray();
                    var existEntry = existEntrys.FirstOrDefault(o => accountData.AccountId.EqualsIgnoreCase(o["fpurpose"] as string));
                    if (existEntry == null)
                    {
                        existEntry = htmlEntry.DynamicObjectType.CreateInstance() as DynamicObject;
                        existEntry["fpurpose"] = accountData.AccountId;
                        existEntry["id"] = Guid.NewGuid().ToString();
                        existEntry["fispayment"] = accountData.CanUseInOrderPay;
                        existEntry["fissyninit"] = false;
                        existEntry["fseq"] = seq;
                        synEntrys.Add(existEntry);
                    }
                }
                dm.Save(supplierdata);

                //客户 ydj_customer
                seq = allAccountInfo.Count();
                var customerFrom = this.MetaModelService.LoadFormModel(userCtx, "ydj_customer");//客户
                var dmCustomer = userCtx.Container.GetService<IDataManager>();//获取服务实例
                dmCustomer.InitDbContext(userCtx, customerFrom.GetDynamicObjectType(userCtx));//初始化数据
                var customerdataReader = userCtx.GetPkIdDataReader(customerFrom, where, sqlParam);//过滤返回数据
                var customerdata = dmCustomer.SelectBy(customerdataReader).OfType<DynamicObject>().ToList();
                ++seq;
                foreach (var dataEntity in customerdata)
                {
                    var htmlEntry = customerFrom.GetEntryEntity("fentry");
                    var synEntrys = htmlEntry.DynamicProperty.GetValue<DynamicObjectCollection>(dataEntity);
                    var existEntrys = synEntrys.ToArray();
                    var existEntry = existEntrys.FirstOrDefault(o => accountData.AccountId.EqualsIgnoreCase(o["fpurpose"] as string));

                    if (existEntry == null)
                    {
                        existEntry = htmlEntry.DynamicObjectType.CreateInstance() as DynamicObject;
                        existEntry["fpurpose"] = accountData.AccountId;
                        existEntry["id"] = Guid.NewGuid().ToString();
                        existEntry["fisbalance"] = accountData.NoConfirmInPay;
                        existEntry["fispayment"] = accountData.CanUseInOrderPay;
                        existEntry["fcredit"] = accountData.CreditLimit;
                        existEntry["fiscash"] = accountData.IsCash;
                        existEntry["fissyninit"] = false;
                        existEntry["fseq"] = seq;
                        synEntrys.Add(existEntry);
                    }
                }
                dmCustomer.Save(customerdata);
            }
        }

        private void UpdatePurposeAlias(DynamicObject[] dataEntities)
        {
            var salhtmlFormmetaModelService = this.Context.Container.GetService<IMetaModelService>();
            var salhtmlForm = salhtmlFormmetaModelService.LoadFormModel(this.Context, "sal_dealersetup");
            var loadReferenceObjectManager = this.Container.GetService<LoadReferenceObjectManager>();
            loadReferenceObjectManager.Load(this.Context, salhtmlForm.GetDynamicObjectType(this.Context), dataEntities, false);
            var datas = dataEntities.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                .Select(x =>
                {
                    var alias = Convert.ToString(x["falias"]);
                    var fpurpose = x["fpurpose_ref"] as DynamicObject;
                    var purposeName = Convert.ToString(fpurpose["fenumitem"]);
                    var purposeId = Convert.ToString(x["fpurpose"]);
                    var category = Convert.ToString(fpurpose["fcategory"]);
                    return new
                    {
                        alias = alias,
                        purposeName = purposeName,
                        purposeId = purposeId,
                        category = category
                    };
                })
                .Where(x => false == string.IsNullOrWhiteSpace(x.alias) && false == string.Equals(x.alias, x.purposeName, StringComparison.OrdinalIgnoreCase))
                .Distinct(x => x.purposeId)
                .ToList();

            if (datas == null || datas.Count <= 0)
            {
                return;
            }

            var dataTable = new DataTable();
            dataTable.Columns.Add("fid");
            dataTable.Columns.Add("fname");

            dataTable.BeginLoadData();
            foreach (var data in datas)
            {
                dataTable.LoadDataRow(new object[] { data.purposeId, data.alias }, true);
            }
            dataTable.EndLoadData();

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "bd_enumdata");

            using (var tran = this.Context.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var dbService = this.Container.GetService<IDBService>();
                var tableName = dbService.CreateTempTableWithDataTable(this.Context, dataTable,0,null,null,false);
                var dbServiceEx = this.Container.GetService<IDBServiceEx>();
                var htmlEntry = htmlForm.GetEntryEntity("fentity");
                var strSql = $@"update {htmlEntry.TableName} as t0 set (fenumitem)=(
	                            select t1.fname 
	                            from {tableName} t1 
	                            where t0.{htmlEntry.PkFieldName}=t1.fid)";
                var result = dbServiceEx.Execute(this.Context, strSql);
                tran.Complete();

                DBService.DeleteTempTableByName(Context, tableName, false);
            }
               
            var comboSvc = this.Container.GetService<IComboDataService>();
            foreach (var name in datas.Select(x => x.category).Distinct())
            {
                comboSvc.ClearCache(this.Context, name);
            }
        }

        /// <summary>
        /// 应用到所有供应商上
        /// </summary>
        /// <param name="setups"></param>
        /// <param name="dealerSetup"></param>
        private void UpdateSupplierInfo(DynamicObjectCollection setups, DynamicObject dealerSetup)
        {
            //应用到所有客户上
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_supplier");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            string where = $@"fmainorgid=@fmainorgid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            var suppliers = dm.SelectBy(dataReader).OfType<DynamicObject>();
            if (suppliers == null || !suppliers.Any()) return;

            var htmlEntry = htmlForm.GetEntryEntity("fentry");

            foreach (var supplier in suppliers)
            {
                var synEntrys = supplier["fentry"] as DynamicObjectCollection;

                foreach (var setup in setups)
                {
                    var existEntry = synEntrys.FirstOrDefault(o => Convert.ToString(setup["fpurpose"]).EqualsIgnoreCase(o["fpurpose"] as string));
                    if (existEntry == null)
                    {
                        existEntry = htmlEntry.DynamicObjectType.CreateInstance() as DynamicObject;
                        synEntrys.Add(existEntry);
                    }
                    existEntry["fpurpose"] = setup["fpurpose"];
                    existEntry["fispayment"] = setup["fispayment"];
                    existEntry["fseq"] = 0;
                }

                //重新排序
                var sortEntry = synEntrys.OrderBy(o => o["fpurpose"]).ToList();
                for (int i = 0; i < sortEntry.Count; i++)
                {
                    sortEntry[i]["fseq"] = i + 1;
                }
            }

            //生成主键ID
            var pkService = this.Container.GetService<IDataEntityPkService>();
            pkService.AutoSetPrimaryKey(this.Context, suppliers, dm.DataEntityType);

            //保存客户账户信息
            dm.Save(suppliers);
        }

        /// <summary>
        /// 应用到所有客户上
        /// </summary>
        /// <param name="setups"></param>
        /// <param name="dealerSetup"></param>
        private void UpdateCustomerInfo(DynamicObjectCollection setups, DynamicObject dealerSetup)
        {
            //应用到所有客户上
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_customer");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            string where = $@"fmainorgid=@fmainorgid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            var customers = dm.SelectBy(dataReader).OfType<DynamicObject>();
            if (customers == null || !customers.Any()) return;

            var htmlEntry = htmlForm.GetEntryEntity("fentry");

            foreach (var customer in customers)
            {
                customer["fissetup"] = dealerSetup["fissetup"];

                var synEntrys = customer["fentry"] as DynamicObjectCollection;

                foreach (var setup in setups)
                {
                    var existEntry = synEntrys.FirstOrDefault(o => Convert.ToString(setup["fpurpose"]).EqualsIgnoreCase(o["fpurpose"] as string));
                    if (existEntry == null)
                    {
                        existEntry = htmlEntry.DynamicObjectType.CreateInstance() as DynamicObject;
                        synEntrys.Add(existEntry);
                    }
                    existEntry["fpurpose"] = setup["fpurpose"];
                    existEntry["fisbalance"] = setup["fisbalance"];
                    existEntry["fispayment"] = setup["fispayment"];
                    existEntry["fseq"] = 0;
                }

                //重新排序
                var sortEntry = synEntrys.OrderBy(o => o["fpurpose"]).ToList();
                for (int i = 0; i < sortEntry.Count; i++)
                {
                    sortEntry[i]["fseq"] = i + 1;
                }
            }

            //生成主键ID
            var pkService = this.Container.GetService<IDataEntityPkService>();
            pkService.AutoSetPrimaryKey(this.Context, customers, dm.DataEntityType);

            //保存客户账户信息
            dm.Save(customers);
        }

        #endregion

    }
}
