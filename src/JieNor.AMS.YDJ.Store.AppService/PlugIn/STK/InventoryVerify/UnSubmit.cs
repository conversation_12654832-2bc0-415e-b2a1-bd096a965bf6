using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryVerify
{
    /// <summary>
    /// 盘点单：撤销
    /// </summary>
    [InjectService]
    [FormId("stk_inventoryverify")]
    [OperationNo("UnSubmit")]
    public class UnSubmit: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;  //弹窗提示
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var sql = @"SELECT a.fentryid FROM t_bcm_cscantaskentity a left join t_bcm_countscantask b on a.fid = b.fid WHERE b.fmainorgid = '{0}' AND a.flinkformid='stk_inventoryverify' and a.flinkbillno = '{1}' ".Fmt(this.Context.Company, Convert.ToString(newData["fbillno"]));
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                return data == null || !data.Any();
            }).WithMessage("当前盘点单【{0}】已经生成了盘点扫描任务，请您删除后再进行撤销操作！", (newData, oldData) => newData["fbillno"]));
        }
    }
}
