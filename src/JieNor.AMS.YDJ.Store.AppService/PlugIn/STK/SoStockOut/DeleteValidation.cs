using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.DataTransferObject;
using Newtonsoft.Json.Linq;
using System.Data;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework.Enums;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockOut
{
    /// <summary>
    /// 销售出库单：删除校验器
    /// </summary>
    public class DeleteValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 业务表单模型
        /// </summary>
        private HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            this.HtmlForm = formInfo;

            this.CheckStockOut(dataEntities);

            return this.Result;
        }

        /// <summary>
        /// 校验【是否已发生退货】
        /// </summary>
        private void CheckStockOut(DynamicObject[] dataEntitys)
        {
            var ids = dataEntitys?.Select(o => Convert.ToString(o["id"]))?.ToList();
            if (ids == null || !ids.Any()) return;

            var sqlText = "";
            List<SqlParam> sqlParams = new List<SqlParam>();
            if (ids.Count == 1)
            {
                sqlParams.Add(new SqlParam("@fid", DbType.String, ids[0]));
                sqlText = $"select top 1 fsooutstockinterid from t_stk_sostockreturnentry with(nolock) where fsooutstockinterid=@fid";
            }
            else
            {
                StringBuilder sqlWhere = new StringBuilder();
                sqlWhere.Append(string.Join(",", ids.Select((x, i) => $"@fid{i}")));
                sqlParams.AddRange(ids.Select((x, i) => new SqlParam($"@fid{i}", System.Data.DbType.String, x)));
                sqlText = $"select distinct fsooutstockinterid from t_stk_sostockreturnentry with(nolock) where fsooutstockinterid in({sqlWhere})";
            }
            var dynObjs = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParams);
            if (!dynObjs.Any()) return;
            var errResult = from x in dataEntitys
                            join y in dynObjs on x["id"] equals y["fsooutstockinterid"]
                            select new ValidationResultEntry
                            {
                                ErrorMessage = $"{this.HtmlForm.Caption}【{x["fbillno"]}】已发生退货！",
                                DataEntity = x
                            };

            if (errResult!=null&&errResult.Any())
            {
                this.Result.Errors.AddRange(errResult);
            }
        }
    }
}