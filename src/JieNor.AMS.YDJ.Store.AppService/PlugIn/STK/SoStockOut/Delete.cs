using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockOut;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SoStockOut
{
    /// <summary>
    /// 销售出库单：删除
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("Delete")]
    public class Delete: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(new DeleteValidation());

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newdata, olddata) =>
            {
                //获取服务单
                var allService = this.Context.LoadBizDataByFilter("ydj_service", " fsourcenumber in ('{0}') and fcancelstatus='0' and fservicetype='fres_type_01' ".Fmt(string.Join("','", newdata["fbillno"])));
                if (allService.Count() >= 1)
                    {
                        return false;
                    }
                return true;
            }).WithMessage("对不起，当前出库单存在下游服务单，禁止删除！"));

        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            DirectHelper.Delete(this.Context, e.DataEntitys);
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //base.EndOperationTransaction(e);
            this.Result.IsSuccess = false;
            var notifyService = this.Context.Container.GetService<IStockOutNotifyService>();
            notifyService.Notify(this.Context, e.DataEntitys, this.HtmlForm, this.OperationNo);

            var svc = this.Container.GetService<IReserveUpdateService>();
            var opResult = svc.DeleteReserve(this.Context, this.HtmlForm, e.DataEntitys, this.Option);
            this.Result.MergeResult(opResult);

            //获取源头单据销售合同信息
            var fsourcenumbers = e.DataEntitys.Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            var orders = this.Context.LoadBizDataByFilter("ydj_order", " fbillno in ('{0}') ".Fmt(string.Join("','", fsourcenumbers)));

            if (orders.Count > 0)
            {
                foreach (var order in orders)
                {
                    Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(order,this.Context);
                }
                this.Context.SaveBizData("ydj_order", orders);
            }

            var entitys = e.DataEntitys.SelectMany(o => o["fentity"] as DynamicObjectCollection).ToList();
            var ids = entitys.Select(o => Convert.ToString(o?["fsoorderentryid"])).ToList();
            // 反写销售合同【已推出库数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackTransOutQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys, ids);
            this.Result.IsSuccess = true;
        }
    }
}
