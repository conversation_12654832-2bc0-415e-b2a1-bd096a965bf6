using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockReturn
{
    /// <summary>
    /// 销售退货单：检查退货单
    /// </summary>
    [InjectService]
    [FormId("stk_sostockreturn")]
    [OperationNo("CheckReturnOrder")]
    public class CheckReturnOrder : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var salorderid = this.GetQueryOrSimpleParam<string>("salorderid");//销售合同内码
            if (string.IsNullOrWhiteSpace(salorderid))
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "请传入正确的销售合同内码！";
                return;
            }


            //销售管理参数【销售退换货允许变更合同】
            var profileService = this.Container.GetService<ISystemProfile>();
            var returnmodifyorder = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "freturnmodifyorder", false);
            if (!returnmodifyorder)
            {
                this.Result.IsSuccess = true;
                this.Result.SimpleMessage = "";
                return;
            }

            //根据销售合同内码查询关联的提交状态的销售退货单
            var sql = @"SELECT A.fid,A.fbillno,M.fname proName,B.fbizqty,C.FSeq  
                        FROM T_STK_SOSTOCKRETURN AS A WITH(NOLOCK) 
                        INNER JOIN T_STK_SOSTOCKRETURNENTRY AS B WITH(NOLOCK) ON A.fid=B.fid 
                        INNER JOIN T_YDJ_ORDERENTRY AS C WITH(NOLOCK) ON B.fsoorderentryid=C.fentryid 
                        INNER JOIN T_YDJ_ORDER AS D WITH(NOLOCK) ON C.fid=D.fid 
                        INNER JOIN T_BD_MATERIAL AS M WITH(NOLOCK) ON B.fmaterialid=M.fid 
                        WHERE D.fid='{0}' AND A.fstatus='D' AND A.freturntype='sostockreturn_biztype_01'  
                        ORDER BY C.FSeq".Fmt(salorderid);
            var targetObjs = this.Context.ExecuteDynamicObject(sql, null);
            List<Dictionary<string, string>> result = new List<Dictionary<string, string>>();
            foreach (var item in targetObjs)
            {
                Dictionary<string, string> rsItem = new Dictionary<string, string>();
                rsItem.Add("ReturnNo", Convert.ToString(item["fbillno"]));
                rsItem.Add("ProductName", Convert.ToString(item["proName"]));
                rsItem.Add("ReturnQty", Convert.ToInt32(item["fbizqty"]).ToString());
                rsItem.Add("LineNo", Convert.ToString(item["FSeq"]));
                result.Add(rsItem);
            }

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "";
            this.Result.SrvData = result;
        }
    }
}
