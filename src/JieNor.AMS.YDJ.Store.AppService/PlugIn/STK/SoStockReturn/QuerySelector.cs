using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockReturn
{
    /// <summary>
    /// 销售退货单：动态列基础资料字段弹窗查询操作
    /// </summary>
    [InjectService]
    [FormId("stk_sostockreturn")]
    [OperationNo("QuerySelector")]
    public class QuerySelector : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            // 只限于销售出库
            var targetFormId = this.GetQueryOrSimpleParam("targetFormId", "");
            if (!targetFormId.EqualsIgnoreCase("stk_sostockout")) return;

            var sostockoutFileter = string.Empty;

            // 当存在源单时，只能选择当前源单的数据
            var sourceNumber = this.GetQueryOrSimpleParam("sourceNumber", "");
            if (!sourceNumber.IsNullOrEmptyOrWhiteSpace())
            {
                sostockoutFileter += $" and o.fbillno='{sourceNumber}' ";
            }
            string filterString = $@" 
                fid in 
                (
                    select oe.fid from t_stk_sostockout o with(nolock)
                    inner join t_stk_sostockoutentry oe with(nolock) on o.fid=oe.fid and o.fcancelstatus='0' {sostockoutFileter}
                    left join (
	                    select fsooutstockinterid,fsooutstockentryid,SUM(fbizqty) as fbizqty from t_stk_sostockreturn so with(nolock)
	                    inner join t_stk_sostockreturnentry soe with(nolock) on so.fid=soe.fid
	                    where so.fcancelstatus='0'
	                    group by fsooutstockinterid,fsooutstockentryid
                    ) soe on oe.fid=soe.fsooutstockinterid and oe.fentryid=soe.fsooutstockentryid
                    where oe.fbizqty+ oe.fbizreturnqty> ISNULL(soe.fbizqty,0)
                )";

            this.SimpleData["filterString"] = filterString;
        }
    }
}
