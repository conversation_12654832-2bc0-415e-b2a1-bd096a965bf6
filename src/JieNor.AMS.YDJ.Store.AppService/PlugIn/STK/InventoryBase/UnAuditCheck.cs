using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.IoC;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryBase
{
    /// <summary>
    /// 盘点方案：反审核前判断是否已经生成了下游单据
    /// </summary>
    [InjectService]
    [FormId("stk_inventorybase")]
    [OperationNo("UnAuditCheck")]
    public class UnAuditCheck : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            base.BeginOperationTransaction(e);

            var dataEntities = e.DataEntitys;

            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var ids = dataEntities.Select(x => Convert.ToString(x["id"])).Distinct().ToList();
            var metaModelService = this.Context.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "stk_inventoryverify");
            var dbService = this.Container.GetService<IDBService>();
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            List<DynamicObject> dbEntities = null;

            using (var tran = this.Context.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                string tabelName = dbService.CreateTempTableWithDataList(this.Context, ids,false);
                string sql = string.Format("select t0.fid from {0} t0 inner join {1} t1 on t0.finventbase=t1.fid ", htmlForm.BillHeadTableName, tabelName);
                var dataReader = dbService.ExecuteReader(this.Context, sql);
                dbEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

                tran.Complete();

                dbService.DeleteTempTableByName(Context, tabelName, true);
            }

            //List<DynamicObject> invalidList = new List<DynamicObject>();
            List<DynamicObject> delList = new List<DynamicObject>();

            if (dbEntities != null && dbEntities.Count > 0)
            {
                foreach (var dbEntity in dbEntities)
                {
                    var fstatus = Convert.ToString(dbEntity["fstatus"]);
                    //盘点单数据状态不等于已审核
                    if (string.Equals(fstatus, "D", StringComparison.OrdinalIgnoreCase) || string.Equals(fstatus, "E", StringComparison.OrdinalIgnoreCase))
                    {
                        //var invalidEntities = dataEntities.Where(x => Convert.ToString(x["id"]) == Convert.ToString(dbEntity["finventbase"]));
                        //if (invalidEntities != null)
                        //{
                        //    invalidList.AddRange(invalidEntities);
                        //}
                    }
                    else
                    {
                        var delEntities = dataEntities.Where(x => Convert.ToString(x["id"]) == Convert.ToString(dbEntity["finventbase"]));
                        if (delEntities != null)
                        {
                            delList.AddRange(delEntities);
                        }
                    }
                }
            }

            //if (invalidList != null && invalidList.Count > 0)
            //{
            //    e.DataEntitys = dataEntities.Where(x => !invalidList.Contains(x)).ToArray();
            //}
            //若检测到含已生成盘点单且盘点单数据状态<>“已审核”&&<>"已提交"，则前端弹出选择提示框
            if (delList.Count > 0)
            {
                List<string> num =new List<string>();
                foreach (DynamicObject item in delList)
                {
                    num.AddRange(dbEntities.Where(x => Convert.ToString(x["finventbase"]) == Convert.ToString(item["id"])).Select(x => Convert.ToString(x[htmlForm.NumberFldKey])));
                }
                
                if (num.Count > 0)
                {
                    num = num.Distinct().ToList();

                    this.Result.IsSuccess = false;
                    this.Result.SimpleData.Add("msg", "已生成编号【" + string.Join(",", num) + "】的盘点单，反审核会执行删除盘点单，是否继续？");
                }
            }
        }
    }
}
