using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PoStockIn
{
    /// <summary>
    /// 采购入库单：列表查询
    /// </summary>
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("Query")]
    public class Query : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            var action = this.OperationContext.Result.HtmlActions.FirstOrDefault(s => s is HtmlViewAction) as HtmlViewAction;
            if (action != null)
            {
                ControlFieldsHelper.OnShowFormControlFields(this.Context, "stk_postockin", action);
            }
        }
    }
}
