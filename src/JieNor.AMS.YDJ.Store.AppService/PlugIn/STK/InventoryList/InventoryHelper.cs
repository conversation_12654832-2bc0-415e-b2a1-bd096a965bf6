using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryList
{
    /// <summary>
    /// 即时库存辅助类
    /// </summary>
    public static class InventoryHelper
    {
        /// <summary>
        /// 计算【分步式调拨在途量】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="invIds"></param>
        public static void CalTransferInTransitQty(UserContext userCtx, List<string> invIds = null)
        {
            if (invIds != null && invIds.Any())
            {
                CalculateTransferInTransitQty(userCtx, invIds);
            }
            else
            {
                CalculateTransferInTransitQty(userCtx);
            }
        }

        /// <summary>
        /// 计算指定数据的【分步式调拨在途量】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="invIds"></param>
        /// <returns></returns>
        private static void CalculateTransferInTransitQty(UserContext userCtx, List<string> invIds)
        {
            using (var scope = userCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var dbSvc = userCtx.Container.GetService<IDBService>();
                //查询即时库存数据，存入临时表
                var tmpTableName = dbSvc.CreateTemporaryTableName(userCtx, false);
                var strWhere = "";
                var tmpIdTbl = "";
                if (invIds.Count == 1)
                {
                    strWhere = $"WHERE INV.fmainorgid='{userCtx.Company}' AND INV.fid='{invIds.FirstOrDefault()}' ";
                }
                else if (invIds.Count <= 50)
                {
                    strWhere = $"WHERE INV.fmainorgid='{userCtx.Company}' AND INV.fid IN ({invIds.JoinEx(",", true)}) ";
                }
                else
                {
                    tmpIdTbl = dbSvc.CreateTempTableWithDataList(userCtx, invIds, false);
                    strWhere = $@"INNER JOIN {tmpIdTbl} AS TMP ON INV.fid=TMP.fid 
                                  WHERE INV.fmainorgid='{userCtx.Company}' ";
                }
                var sql = $@"SELECT INV.fid,INV.fmaterialid,INV.fattrinfo,INV.fcustomdesc,INV.fstorehouseid,INV.fstorelocationid,INV.fstockstatus 
                            ,INV.flotno,INV.fmtono,INV.fownertype,INV.fownerid,INV.funitid,INV.fstockunitid,0 AS fqty  
                            INTO {tmpTableName} 
                            FROM T_STK_INVENTORYLIST AS INV WITH(NOLOCK) 
                            {strWhere} ";

                var dbSvcEx = userCtx.Container.GetService<IDBServiceEx>();
                dbSvcEx.Execute(userCtx, sql);

                //使用临时表关联查询所有未作废的库存调拨单
                var tmpUpdateTblName = dbSvc.CreateTemporaryTableName(userCtx, false);
                sql = $@"SELECT TMP.fid,SUM(TSMX.fstockinbizqty) fqty 
                         INTO {tmpUpdateTblName}
                         FROM T_STK_INVTRANSFER AS TS WITH(NOLOCK) 
                         INNER JOIN T_STK_INVTRANSFERENTRY AS TSMX WITH(NOLOCK) ON TS.fid=TSMX.fid 
                         INNER JOIN {tmpTableName} AS TMP ON TMP.fmaterialid=TSMX.fmaterialid AND TMP.fattrinfo=TSMX.fattrinfoto AND TMP.fcustomdesc=TSMX.fcallupcustomdescto
                         	AND TMP.fstorehouseid=TSMX.fstorehouseidto AND TMP.fstorelocationid=TSMX.fstorelocationidto AND TMP.fstockstatus=TSMX.fstockstatusto 
                         	AND TMP.flotno=TSMX.flotno AND TMP.fmtono=TSMX.fmtonoto AND TMP.fownertype=TSMX.fownertypeto AND TMP.fownerid=TSMX.fowneridto 
                         	AND TMP.funitid=TSMX.funitid AND TMP.fstockunitid=TSMX.fstockunitid 
                         WHERE TS.fmainorgid='{userCtx.Company}' AND TS.fstatus!='E' AND TS.fcancelstatus='0' AND TS.fisstockout='1'  
                         GROUP BY TMP.fid ";
                dbSvcEx.Execute(userCtx, sql);

                //根据临时表更新《库存综合查询报表》扩展物理表
                sql = $@"/*dialect*/UPDATE A SET A.findbqty=B.fqty,A.fbizindbqty=B.fqty  
                         FROM T_STK_INVENTORYLIST_EXTENDDATA AS A WITH(NOLOCK) 
                         INNER JOIN {tmpUpdateTblName} AS B ON A.fid=B.fid ";
                dbSvcEx.Execute(userCtx, sql);

                //删除临时表
                dbSvc.DeleteTempTableByName(userCtx, tmpTableName, true);
                dbSvc.DeleteTempTableByName(userCtx, tmpUpdateTblName, true);
                if (!tmpIdTbl.IsNullOrEmptyOrWhiteSpace())
                {
                    dbSvc.DeleteTempTableByName(userCtx, tmpIdTbl, true);
                }

                scope.Complete();
            }
        }

        /// <summary>
        /// 计算所有数据的【分步式调拨在途量】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private static void CalculateTransferInTransitQty(UserContext userCtx)
        {
            using (var scope = userCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                //查询所有未作废并开启了分步式调拨的库存调拨单，获取分步式调拨在途量
                var dbSvc = userCtx.Container.GetService<IDBService>();
                var tmpTableName = dbSvc.CreateTemporaryTableName(userCtx, false);

                var sql = $@"SELECT B.fmaterialid,B.fattrinfoto,B.fcallupcustomdescto fcustomdesc,B.fstorehouseidto fstorehouseid,B.fstorelocationidto fstorelocationid,B.fstockstatusto fstockstatus 
                            ,B.flotno,B.fmtonoto fmtono,B.fownertypeto fownertype,B.fowneridto fownerid,B.funitid,B.fstockunitid,CASE A.fstatus WHEN 'E' THEN 0 ELSE B.fstockinbizqty END fqty 
                            INTO {tmpTableName}
                            FROM T_STK_INVTRANSFER AS A WITH(NOLOCK) 
                            INNER JOIN T_STK_INVTRANSFERENTRY AS B WITH(NOLOCK) ON A.fid=B.fid 
                            WHERE A.fmainorgid='{userCtx.Company}' AND A.fcancelstatus='0' AND A.fisstockout='1' ";

                var dbSvcEx = userCtx.Container.GetService<IDBServiceEx>();
                dbSvcEx.Execute(userCtx, sql);

                //关联查询即时库存表
                var tmpUpdateTblName = dbSvc.CreateTemporaryTableName(userCtx, false);
                sql = $@"SELECT INV.fid,SUM(TMP.fqty) fqty 
                        INTO {tmpUpdateTblName} 
                        FROM T_STK_INVENTORYLIST AS INV WITH(NOLOCK) 
                        INNER JOIN {tmpTableName} AS TMP ON INV.fmaterialid=TMP.fmaterialid AND INV.fattrinfo=TMP.fattrinfoto AND INV.fcustomdesc=TMP.fcustomdesc
                        	AND INV.fstorehouseid=TMP.fstorehouseid AND INV.fstorelocationid=TMP.fstorelocationid AND INV.fstockstatus=TMP.fstockstatus 
                        	AND INV.flotno=TMP.flotno AND INV.fmtono=TMP.fmtono AND INV.fownertype=TMP.fownertype AND INV.fownerid=TMP.fownerid 
                        	AND INV.funitid=TMP.funitid AND INV.fstockunitid=TMP.fstockunitid 
                        WHERE INV.fmainorgid='{userCtx.Company}' 
                        GROUP BY INV.fid ";
                dbSvcEx.Execute(userCtx, sql);

                //根据临时表更新《库存综合查询报表》扩展物理表
                sql = $@"/*dialect*/UPDATE A SET A.findbqty=B.fqty,A.fbizindbqty=B.fqty  
                         FROM T_STK_INVENTORYLIST_EXTENDDATA AS A WITH(NOLOCK) 
                         INNER JOIN {tmpUpdateTblName} AS B ON A.fid=B.fid ";
                dbSvcEx.Execute(userCtx, sql);

                //库存调拨单可能会被删除，需要处理
                sql = $@"/*dialect*/UPDATE A SET A.findbqty=0,A.fbizindbqty=0  
                         FROM T_STK_INVENTORYLIST_EXTENDDATA AS A WITH(NOLOCK)
                         WHERE(A.findbqty > 0 OR A.fbizindbqty > 0) AND NOT EXISTS(SELECT 1 FROM {tmpUpdateTblName} AS TMP WHERE TMP.fid=A.fid) ";
                dbSvcEx.Execute(userCtx, sql);

                //删除临时表
                dbSvc.DeleteTempTableByName(userCtx, tmpTableName, true);
                dbSvc.DeleteTempTableByName(userCtx, tmpUpdateTblName, true);

                scope.Complete();
            }
        }

        /// <summary>
        /// 查询分步式调拨在途量
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="invIds"></param>
        /// <returns></returns>
        public static DynamicObjectCollection QueryTransferInTransitQty(UserContext userCtx, List<string> invIds)
        {
            DynamicObjectCollection result = null;
            if (invIds == null || !invIds.Any())
            {
                return result;
            }

            using (var scope = userCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var dbSvc = userCtx.Container.GetService<IDBService>();
                var strWhere = "";
                var tmpIdTbl = "";
                if (invIds.Count == 1)
                {
                    strWhere = $"WHERE IX.fid='{invIds.FirstOrDefault()}' ";
                }
                else if (invIds.Count <= 50)
                {
                    strWhere = $"WHERE IX.fid IN ({invIds.JoinEx(",", true)}) ";
                }
                else
                {
                    tmpIdTbl = dbSvc.CreateTempTableWithDataList(userCtx, invIds, false);
                    strWhere = $@"INNER JOIN {tmpIdTbl} AS TMP ON IX.fid=TMP.fid ";
                }
                var sql = $@"SELECT IX.fid,IX.fbizindbqty FROM T_STK_INVENTORYLIST_EXTENDDATA AS IX WITH(NOLOCK) {strWhere} ";
                result = dbSvc.ExecuteDynamicObject(userCtx, sql);

                if (!tmpIdTbl.IsNullOrEmptyOrWhiteSpace())
                {
                    dbSvc.DeleteTempTableByName(userCtx, tmpIdTbl, true);
                }

                scope.Complete();
            }
            return result;
        }
    }
}
