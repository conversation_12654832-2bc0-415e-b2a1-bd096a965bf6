using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Supplier
{
    /// <summary>
    /// 供应商：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_supplier")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fname"]).NotEmpty().WithMessage("名称不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["ftype"]).NotEmpty().WithMessage("类型不能为空！"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                //如果是新增，则根据账户辅助资料动态创建多个账户
                if (dataEntity.DataEntityState.FromDatabase == false)
                {
                    var accountSynService = this.Container.GetService<ISynAccountBalanceService>();
                    var allAccountInfo = accountSynService.GetAllAccount(this.Context);

                    var htmlEntry = this.HtmlForm.GetEntryEntity("fentry");
                    var synEntrys = htmlEntry.DynamicProperty.GetValue<DynamicObjectCollection>(dataEntity);
                    var existEntrys = synEntrys.ToArray();
                    synEntrys.Clear();

                    var seq = 1;
                    foreach (var accountInfo in allAccountInfo)
                    {
                        var existEntry = existEntrys.FirstOrDefault(o => accountInfo.AccountId.EqualsIgnoreCase(o["fpurpose"] as string));
                        if (existEntry == null)
                        {
                            existEntry = htmlEntry.DynamicObjectType.CreateInstance() as DynamicObject;
                            existEntry["fpurpose"] = accountInfo.AccountId;
                        }
                        existEntry["fispayment"] = accountInfo.CanUseInOrderPay;
                        existEntry["fissyninit"] = false;
                        existEntry["fseq"] = seq++;
                        synEntrys.Add(existEntry);
                    }
                }
            }
        }
    }
}