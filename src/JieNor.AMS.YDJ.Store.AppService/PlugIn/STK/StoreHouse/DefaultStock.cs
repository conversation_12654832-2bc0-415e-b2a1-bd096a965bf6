using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.StoreHouse
{
    /// <summary>
    /// 仓库：业务单据上获取默认仓库信息
    /// </summary>
    [InjectService]
    [FormId("ydj_storehouse")]
    [OperationNo("getdefaultstockinfo")]
    public class DefaultStock : AbstractOperationServicePlugIn
    {

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            this.Result.IsSuccess = false;

            var srcFormId = this.GetQueryOrSimpleParam<string>("srcformid", "");
            var deptid = this.GetQueryOrSimpleParam<string>("deptid", "");
            if (srcFormId.IsNullOrEmptyOrWhiteSpace() || deptid.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            //var ctrlFormObj = this.Container.GetService<ISystemProfile>().GetSystemParameter(this.Context, "stk_stockparam");
            //var paraMeta = HtmlParser.LoadFormMetaFromCache("sec_adminaccess", Context);
            //var formRows = ctrlFormObj["fctrlbillentity"] as DynamicObjectCollection;
            //if (formRows == null || formRows.Count == 0)
            //{
            //    return ;
            //}

            //var selRow = formRows.FirstOrDefault(f => Convert.ToString(f["fctrlformid"]).EqualsIgnoreCase(srcFormId)
            //                                        && Convert.ToBoolean(f["fisstockctrl"]));
            //if (selRow == null)
            //{
            //    return ;
            //}

            var dbSvc = this.Container.GetService<IDBService>();
            var sql = @"select fstorehouseid from t_bd_department where fid='{0}' ".Fmt(deptid);
            var storeIds = dbSvc.ExecuteDynamicObject(Context, sql);
            if (storeIds == null || storeIds.Count == 0)
            {
                this.Result.IsSuccess = true;
                return ;
            }

            var ids = Convert.ToString(storeIds[0]["fstorehouseid"]);
            if (ids.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.IsSuccess = true;
                return;
            }

            if (ids.IndexOf (',')>-1)
            {
                this.Result.IsSuccess = true;
                return;//多个，则不设置默认值
            }

            sql = @"select fid,fnumber,fname from t_ydj_storehouse where fid='{0}' ".Fmt(ids);
            var data= dbSvc.ExecuteDynamicObject(Context, sql);
            if (data==null || data.Count ==0)
            {
                this.Result.IsSuccess = true;
                return;
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = new { id = data[0]["fid"], number = data[0]["fnumber"], name = data[0]["fname"] };

        }



    }
}