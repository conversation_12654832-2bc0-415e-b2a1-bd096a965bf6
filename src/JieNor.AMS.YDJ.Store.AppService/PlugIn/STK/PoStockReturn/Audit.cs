using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PoStockReturn
{
    /// <summary>
    /// 采购退货单：审核
    /// 作者：zpf
    /// 日期：2022-06-10
    /// </summary>
    [InjectService]
    [FormId("stk_postockreturn")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理规则校验
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            IPurchaseOrderService orderService = this.Container.GetService<IPurchaseOrderService>();
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return orderService.ChangeOrSubmitStatus(this.Context, newData);
            }).WithMessage("对不起，上游采购订单变更中，已禁止此操作！"));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var nowDate = DateTime.Now;
            foreach (var dataEntity in e.DataEntitys)
            {
                //task 70392,【采购退货单】以当前日期更新至【退货日期】
                dataEntity["fdate"] = nowDate;
            }
        }

        public virtual void EndOperationTransaction(EndOperationTransactionArgs e) 
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }
            //获取源头单据采购入库单信息
            var postockinNos = e.DataEntitys.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "stk_postockin").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            var postockins = this.Context.LoadBizDataByFilter("stk_postockin", " fbillno in ('{0}') ".Fmt(string.Join("','", postockinNos)));

            //获取源头单据采购订单信息
            var fsourceNmbers = postockins.Where(t=> !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "ydj_purchaseorder").Select(t=>Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            var purchaseOrders = this.Context.LoadBizDataByFilter("ydj_purchaseorder", " fbillno in ('{0}') ".Fmt(string.Join("','", fsourceNmbers)));

            foreach (var dataEntity in e.DataEntitys)
            {
                //采购退货明细
                var poStockInEntrys = dataEntity["fentity"] as DynamicObjectCollection;
                var poOrderEntryIds = poStockInEntrys.Select(t => t["fpoorderentryid"].ToString()).Distinct().ToList();
                var poOrderInterIds = poStockInEntrys.Select(t => t["fpoorderinterid"].ToString()).Distinct().ToList();
                var purchaseOrder = purchaseOrders.FirstOrDefault(t => poOrderInterIds.Contains(t["id"].ToString()));
                if (!purchaseOrder.IsNullOrEmptyOrWhiteSpace())
                {
                    //订单上的所有明细
                    var purchaseOrderEntrys = (purchaseOrder["fentity"] as DynamicObjectCollection).ToList();
                    List<DynamicObject> productlist = new List<DynamicObject>();
                    foreach (var pur in purchaseOrderEntrys)
                    {
                        if (!pur["fsuitcombnumber"].IsNullOrEmptyOrWhiteSpace())
                        {
                            var product = this.Context.LoadBizDataById("ydj_product", pur["fmaterialid"].ToString(), true);
                            string str = product["fsuiteflag"].ToString();
                            if (product["fsuiteflag"].ToString().ToLower() == "true")
                            {
                                productlist.Add(product);
                            }
                        }
                    }
                    //当前采购入库单明细关联上游单据的订单明细
                    //var currentPurchaseOrderEntrys = (purchaseOrder["fentity"] as DynamicObjectCollection).Where(t => poOrderEntryIds.Contains(t["id"].ToString())).ToList();

                    //purchaseOrder["fclosestatus"] = "2";
                    //if (!purchaseOrderEntrys.IsNullOrEmptyOrWhiteSpace() && !purchaseOrderEntrys.Any(t => Convert.ToDouble(t["fbizreturnqty"]) != 0))
                    //{
                    //    //此时表示所有的采购订单产生的采购入库单都已经反审核了，或者处于未入库状态，即订单关闭状态为正常
                    //    purchaseOrder["fclosestatus"] = "0";
                    //    purchaseOrderEntrys.ForEach(t => t["fclosestatus_e"] = "0");
                    //}
                    //else
                    //{
                    //    //存在部分关闭的场景
                    //    //< !--'0':'正常','1':'整单关闭','2':'部分关闭','3':'自动关闭','4':'手动关闭'-- >
                    //    currentPurchaseOrderEntrys.FindAll(t => Convert.ToDouble(t["fbizreturnqty"]) == 0).ForEach(t => t["fclosestatus_e"] = "0");
                    //    currentPurchaseOrderEntrys.FindAll(t => Convert.ToDouble(t["fbizreturnqty"]) > 0).ForEach(t => t["fclosestatus_e"] = "2");
                    //}
                    Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatusWhitfissuitflag(purchaseOrder, productlist);
                }
            }

            if (purchaseOrders.Count > 0)
            {
                this.Context.SaveBizData("ydj_purchaseorder", purchaseOrders);
            }

            // 反写销售合同【已采购入库数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackPurInQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
        }
    }
}
