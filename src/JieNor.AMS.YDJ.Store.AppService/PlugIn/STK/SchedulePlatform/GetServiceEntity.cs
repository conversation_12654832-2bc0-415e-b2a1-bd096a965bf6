using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SchedulePlatform
{
    /// <summary>
    /// 发货排单平台：获取已排服务明细
    /// </summary>
    [InjectService]
    [FormId("stk_scheduleplatform")]
    [OperationNo("GetServiceEntity")]
    public class GetServiceEntity : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var billId = this.GetQueryOrSimpleParam<string>("billId");
            if (string.IsNullOrWhiteSpace(billId))
            {
                throw new BusinessException("billId不能为空");
            }
            string sql = @"
select sse.fentryid as r__fentryid_s,sse.fseritemid as r__fseritemid_s__id,si.fname as r__fseritemid_s__fname,si.fnumber as r__fseritemid_s__fnumber,si.funitid as r__funitid_s__id,u.fname as r__funitid_s__fname,u.fnumber as r__funitid_s__fnumber,
sse.fprice as r__fprice_s,sse.fqty as r__fqty_s,sse.famount as r__famount_s,sse.fsourceformid as r__fsourceformid_s,(case sse.fsourceformid when N'ydj_order' then N'销售合同' when N'stk_scheduleapply' then N'排单申请单' else '' end) as r__fsourceformid_s_fname,sse.fsourcebillno as r__fsourcebillno_s,sse.fsourceinterid as r__fsourceinterid_s
from t_stk_scheduleserviceentry sse
left join t_ydj_seritem si on sse.fseritemid=si.fid
left join T_YDJ_UNIT u on sse.funitid=u.fid
where sse.fid=@fid
";
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fid",System.Data.DbType.String,billId)
            };
            var dbSerivice = this.Container.GetService<IDBService>();

            List<Dictionary<string, object>> serviceEntities = null;

            using (var dataReader = dbSerivice.ExecuteReader(this.Context, sql, sqlParams))
            {
                serviceEntities = readerDatas(dataReader);
            }

            this.Result.SrvData = new Dictionary<string, object>
            {
                { "id",billId},
                { "fserviceentity",serviceEntities},
            };
            this.Result.IsSuccess = true;
        }

        private List<Dictionary<string, object>> readerDatas(IDataReader dataReader)
        {
            List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();
            while (dataReader.Read())
            {
                var result = new Dictionary<string, object>();
                results.Add(result);
                for (var i = 0; i < dataReader.FieldCount; i++)
                {
                    var name = dataReader.GetName(i);
                    if (name.StartsWith("r__") == false)
                    {
                        continue;
                    }
                    name = name.Substring(3);
                    var value = dataReader.GetValue(i);
                    if (value is DBNull)
                    {
                        value = null;
                    }
                    if (name.Contains("__"))
                    {
                        var ns = name.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);
                        name = ns[0];
                        var v = result.Keys.Contains(name) ? result[name] as Dictionary<string, object> : new Dictionary<string, object>();
                        v[ns[1]] = value;
                        value = v;
                    }
                    result[name] = value;
                }
            }
            return results;
        }
    }
}
