using System;
using System.Linq;
using System.Collections.Generic;
using System.Net.Configuration;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：取消变更
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("unchange")]
    public class UnChange : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            string msg = string.Empty;
            //增加判断如果 【总部变更状态】="提交至总部"或"关闭"时, 点击<取消变更>要报错提示"总部变更状态等于"提交至总部"或"关闭", 不允许取消变更!"
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                msg = string.Empty;
                var entry = newData["fentity"] as DynamicObjectCollection;
                //判断明细 是否存在 【总部变更状态】="提交至总部"或"驳回"
                bool exist = entry.Any(o => Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("01") || Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("03"));
                if (exist)
                {
                    msg += $"总部变更状态等于'提交至总部'或'关闭', 不允许取消变更!";
                    return false;
                }
                else
                {
                    return true;
                }
            }).WithMessage("{0}", (billObj, propObj) => msg));


            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entrys = (DynamicObjectCollection)newData["fentity"];

                // 有非标审批状态=待审批的
                var hasAuditingUnstdStatus =
                    entrys.Any(s => Convert.ToString(s["funstdtypestatus"]).EqualsIgnoreCase("02"));

                return hasAuditingUnstdStatus == false;
            }).WithMessage("{0}", (billObj, propObj) => "存在商品行的非标审批状态为待审批，不允许取消变更"));

            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue(IsIntentOrderNotAudited).WithMessage("采购订单对应的K3《订购意向书》未审核，不允许变更！"));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            Core.Helpers.PurchaseOrderQtyWriteBackHelper.WriteBackPurchaseOrderAllQty(this.Context, e.DataEntitys);

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys, forceUpdate: true);
        }
    }
}