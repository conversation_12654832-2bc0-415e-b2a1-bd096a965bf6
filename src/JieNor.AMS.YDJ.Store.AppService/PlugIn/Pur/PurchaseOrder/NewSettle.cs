using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：退款结算
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("NewSettle")]
    public class NewSettle : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var fsettletype = this.GetQueryOrSimpleParam<string>("fsettletype", "");
            var isSyn = this.GetQueryOrSimpleParam<bool>("fissyn", false);
            var way = this.GetQueryOrSimpleParam<string>("fway", "");
            var myBankId = this.GetQueryOrSimpleParam<string>("fmybankid", "");
            var synBankId = this.GetQueryOrSimpleParam<string>("fsynbankid", "");
            var fcontactunitid = this.GetQueryOrSimpleParam<string>("fcontactunitid", "");
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (fsettletype.EqualsIgnoreCase("退款")
                    && isSyn)
                {
                    return false;
                }
                return true;
            }).WithMessage("目前系统不支持协同采购订单的退款操作！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var spService = this.Container.GetService<ISystemProfile>();
                var enableMustInputBankId = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablemustinputbankid", true);

                if ((way.EqualsIgnoreCase("payway_06") ||
                    way.EqualsIgnoreCase("payway_07") ||
                    way.EqualsIgnoreCase("payway_08") ||
                    (enableMustInputBankId && way.EqualsIgnoreCase("payway_11"))) &&
                    myBankId.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择银行账号！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var spService = this.Container.GetService<ISystemProfile>();
                var enableMustInputBankId = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablemustinputbankid", true);

                if ((way.EqualsIgnoreCase("payway_06") ||
                    (enableMustInputBankId && way.EqualsIgnoreCase("payway_11"))) &&
                     isSyn && synBankId.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择对方银行！"));

            //支付方式为“商场代收”，“代收单位”不能为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (way.EqualsIgnoreCase("payway_13") && fcontactunitid.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("支付方式为“商场代收”，“代收单位”不能为空!"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (fsettletype.EqualsIgnoreCase("退款")
                    && this.GetQueryOrSimpleParam<decimal>("fsettleamount", 0) > Convert.ToDecimal(newData["fpaidamount"]))
                {
                    return false;
                }
                return true;
            }).WithMessage("本次结算额不允许大于已结算金额！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (fsettletype.EqualsIgnoreCase("收款")
                    && this.GetQueryOrSimpleParam<decimal>("fsettleamount", 0) > Convert.ToDecimal(newData["fpayamount"]))
                {
                    return false;
                }
                return true;
            }).WithMessage("本次结算额不允许大于待结算金额！"));
        }
    }
}
