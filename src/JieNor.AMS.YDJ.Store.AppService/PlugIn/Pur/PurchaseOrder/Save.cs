using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.Model.PurchaseOrder;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Pur;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.FileServer;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("Save")]
    public partial class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 当前二级经销商是否不管理库存
        /// </summary>
        private bool IsNotMgrInv { get; set; }
        //Dictionary<string, string> orderEntryIdList = new Dictionary<string, string>(); //删除前的所有 采购订单-商品明细-【销售合同 商品明细分录内码字段】

        private List<string> upOrderEntryIds = new List<string>();
        private List<string> orderFids = new List<string>();
        private ConcurrentDictionary<string, List<string>> BeforeSoOrderEntryIds { get; set; }

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            var purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
            purchaseOrderService.FillUnstdInfo(this.Context, e.DataEntitys);
            purchaseOrderService.CalculateSettlement(this.Context, e.DataEntitys);
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fpodeptid", "fdeliverid", "fbilltypeid" });

        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            if (this.Option.GetVariableValue<string>("NoValidation", string.Empty).EqualsIgnoreCase("1"))
            {
                return;
            }

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fbilltypeid"]).NotEmpty().WithMessage("单据类型不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fdate"]).NotEmpty().WithMessage("订单日期不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fpickdate"]).NotEmpty().WithMessage("交货日期不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fsupplierid"]).NotEmpty().WithMessage("供应商不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return new PurchaseOrderCommon(this.Context).CheckDeptByBillType(newData);
            }).WithMessage("采购部门不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!this.Context.IsSecondOrg)
                {
                    return new PurchaseOrderCommon(this.Context).CheckCityByBillType(newData);
                }
                return true;
            }).WithMessage("送达方与门店的城市不匹配，请切换至相同城市的门店下摆场！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return this.CheckOrderDate(newData);
            }).WithMessage("交货日期填写不合理！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    return true;
                }
                var entry = newData["fentity"] as DynamicObjectCollection;
                if (CheckIsexistLargeSuitNumber(entry))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起当前订单商品行存在排序异常，请点击重新排序后保存！"));

            //协同采购，客户和手机必填
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    if (this.CheckIsSync(newData))
            //    {
            //        if(newData["fcustomerid"].IsNullOrEmptyOrWhiteSpace() || newData["fphone"].IsNullOrEmptyOrWhiteSpace())
            //        {
            //            return false;
            //        }
            //    }
            //    return true;
            //}).WithMessage("客户和手机号不能为空！"));

            //验证手机号码合法性
            //e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            //{
            //    var mobile = Convert.ToString(n["fphone"]);
            //    if (mobile.IsNullOrEmptyOrWhiteSpace()) return true;
            //    return mobile.IsRegMatch(RegExPattern.Regex_Pattern_MobilePhone);
            //}).WithMessage("手机号填写错误！"));

            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    return this.CheckBillTypeIsChange(newData);
            //}).WithMessage("业务类型在单据新增保存后，不允许再次修改！"));

            string paramMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    return true;
                }
                return this.CheckBillTypeParamSet(newData, out paramMessage);
            }).WithMessage("{0}", (billObj, propObj) => paramMessage));

            //采购订单【客户来源】字段是否必录（针对科凡做的特殊控制）
            var sourceMust = this.GetAppConfig("biz:fieldmust.purchaseorder.source") ?? "";
            if (sourceMust.EqualsIgnoreCase("true") || sourceMust == "1")
            {
                var sourceMessage = "";
                e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
                {
                    if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                    {
                        return true;
                    }
                    if (newData["fsource"].IsNullOrEmptyOrWhiteSpace())
                    {
                        sourceMessage = $"{this.HtmlForm.Caption}【{newData["fbillno"]}】【客户来源】字段不能为空！";
                        return false;
                    }
                    return true;
                }).WithMessage("{0}", (billObj, propObj) => sourceMessage));
            }

            var spService = this.Container.GetService<ISystemProfile>();
            var isMustInputMtono = spService.GetSystemParameter(Context, "pur_systemparam", "fismustinputmtono", false);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    return true;
                }
                if (isMustInputMtono == false)
                {
                    return true;
                }
                //判断是否启用[定制品采购跟踪号必填]参数
                DynamicObjectCollection entitys = newData["fentity"] as DynamicObjectCollection;
                if (entitys != null && entitys.Any(x => !x["fcustomdes_e"].IsNullOrEmptyOrWhiteSpace() && x["fmtono"].IsNullOrEmptyOrWhiteSpace()))
                {
                    return false;
                }
                return true;
            }).WithMessage("物流跟踪号必录!"));

            string entryErrorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    return true;
                }
                return CheckEntry(newData, out entryErrorMessage);
            }).WithMessage("{0}", (billObj, propObj) => entryErrorMessage));


            //变更时保存时无需保存业务状态
            //var billNo = string.Empty;
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    var fbizstatus = Convert.ToString(newData["fbizstatus"]);
            //    if (!string.IsNullOrWhiteSpace(fbizstatus) && fbizstatus != "business_status_01" && fbizstatus != "business_status_02")
            //    {
            //        billNo = Convert.ToString(newData["fbillno"]);
            //        return false;
            //    }
            //    return true;
            //}).WithMessage("{0}不是待发送或待受理状态，不更改!", (billObj, propObj) => billNo));

            /*
                定义表体校验规则
            */
            //至少要有一行商品明细
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    return true;
                }
                DynamicObjectCollection entitys = newData["fentity"] as DynamicObjectCollection;
                return !(entitys == null || entitys.Count <= 0);
            }).WithMessage("商品不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    return true;
                }
                var type = Convert.ToString(newData["fsourcetype"]);
                if (!type.IsNullOrEmptyOrWhiteSpace() && type == "ydj_order")
                {
                    //销售合同允许采购的最低金额比例
                    var profileService = this.Context.Container.GetService<ISystemProfile>();
                    var fsourcebillno = Convert.ToString(newData["fsourcenumber"]);
                    //任务37793 修改为单据类型里取数
                    var fproportionbuyeramount = 0;
                    var billTypeService = this.Container.GetService<IBillTypeService>();
                    var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                    var reader = Context.GetPkIdDataReader(orderForm, $@"fbillno='{fsourcebillno}'", new List<SqlParam>() { });
                    var formDt = orderForm.GetDynamicObjectType(Context);
                    var orderdm = Context.Container.GetService<IDataManager>();
                    orderdm.InitDbContext(Context, formDt);
                    var dynObjs = orderdm.SelectBy(reader).OfType<DynamicObject>().FirstOrDefault();
                    var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, orderForm, Convert.ToString(dynObjs["fbilltype"]));
                    if (paramSetObj != null)
                    {
                        int.TryParse(Convert.ToString(paramSetObj["fproportionbuyeramount"]), out fproportionbuyeramount);
                    }
                    else
                    {
                        return true;
                    }

                    var fsourcenumber = Convert.ToString(newData["fsourcenumber"]);
                    var dm = GetOrderDm(fsourcenumber);
                    if (dm.IsNullOrEmptyOrWhiteSpace())
                        throw new BusinessException("选单数据不正确！");

                    var freceivable = dm["freceivable"];
                    var fsumamount = dm["fsumamount"];

                    var newfsumamount = (Convert.ToDouble(fsumamount) - Convert.ToDouble(dm["frefundamount"])) * fproportionbuyeramount / 100;
                    if (Convert.ToDouble(freceivable) < newfsumamount)
                        throw new BusinessException("当前订单确认已收金额不足" + newfsumamount + "元，暂不允许下采购！");
                }
                return true;
            }).WithMessage(" "));

            string errorMessage = "";
            //判断是否存在总部商品 业绩品牌为空的情况
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var entry = newData["fentity"] as DynamicObjectCollection;
                return !CheckIsExistTopProEmptyResultBrand(entry, out errorMessage);
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            //判断是否存在总部商品、非总部商品 同时下单的情况
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    return true;
                }
                errorMessage = "";
                var entry = newData["fentity"] as DynamicObjectCollection;
                return !CheckIsExistTopOrgAndOtherOrg(entry, out errorMessage);
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            //判断是否存在总部商品 送达方为空的情况
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    return true;
                }
                errorMessage = "";
                var entry = newData["fentity"] as DynamicObjectCollection;
                return !CheckIsExistTopProEmptyDeliver(entry, Convert.ToString(newData["fdeliverid"]), out errorMessage);
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            //var unstdData = this.GetQueryOrSimpleParam("unstdData", "");
            //if (!unstdData.IsNullOrEmptyOrWhiteSpace())
            //{
            //    var checkUnstdPriceDTOs = unstdData.FromJson<List<CheckUnstdPriceDTO>>();

            //    // 非标价格校验：
            //    e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //    {
            //        if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
            //        {
            //            return true;
            //        }
            //        if (!newData.DataEntityState.FromDatabase) return true;

            //        var id = newData["id"]?.ToString();
            //        if (id.IsNullOrEmptyOrWhiteSpace()) return true;

            //        var dto = checkUnstdPriceDTOs.FirstOrDefault(s => s.id.EqualsIgnoreCase(id));

            //        // 过滤【是否非标】=是 且 【零售价】=0 且 【非标审核状态】=待审批 
            //        var entryIds = dto.fentity
            //            .Where(s => s.funstdtype && s.fprice <= 0 && s.funstdtypestatus.EqualsIgnoreCase("02"))
            //            .Select(s => s.id);
            //        if (entryIds.IsNullOrEmpty()) return true;

            //        var entrys = newData["fentity"] as DynamicObjectCollection;
            //        foreach (var entry in entrys)
            //        {
            //            string entryId = Convert.ToString(entry["id"]);
            //            decimal price = Convert.ToDecimal(entry["fprice"]);
            //            string unstdTypeStatus = Convert.ToString(entry["funstdtypestatus"]);

            //            var entryDto = dto.fentity.FirstOrDefault(s => s.id.EqualsIgnoreCase(entryId));
            //            if (entryDto == null) continue;

            //            var isSame = entryDto.funstdtypestatus.EqualsIgnoreCase(unstdTypeStatus)
            //                         && entryDto.fprice == price;
            //            if (!isSame) return false;
            //        }

            //        return true;

            //    }).WithMessage(@"存在非标价格更新请先刷新当前页面！"));
            //}

            e.Rules.Add(new Pur.PurchaseOrder.Validation_ChannelType());

            e.Rules.Add(new Pur.PurchaseOrder.Validation_Product());

            e.Rules.Add(new Pur.PurchaseOrder.Validation_Save());

            string msg = string.Empty;
            e.Rules.Add(this.RuleFor("fentity", data => data).IsTrue((newData, oldData) =>
            {
                var materialObj = newData["fmaterialid_ref"] as DynamicObject;
                if (materialObj != null)
                {
                    if (!Convert.ToString(newData["funitid"]).EqualsIgnoreCase(Convert.ToString(materialObj["funitid"])))
                    {
                        msg = $"第{Convert.ToString(newData["fseq"])}行商品【基本单位】与商品基础信息【基本单位】不一致！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => msg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (this.Context.IsDirectSale == false)
                {
                    return true;
                }

                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    return true;
                }
                return !string.IsNullOrWhiteSpace(Convert.ToString(newData["fstoreid"]));
            }).WithMessage("请选择门店名称!"));

        }

        /// <summary>
        /// 判断数字集合是否是连续的
        /// </summary>
        /// <returns></returns>
        public bool IsContinuous(List<int> numList)
        {
            if (numList.Count() == 1) return true;

            numList.Sort((x, y) => -x.CompareTo(y));//降序
            bool result = false;

            for (int i = 0; i < numList.Count() - 1; i++)
            {
                if (numList[i] - numList[i + 1] == 1)
                    result = true;
                else
                {
                    result = false;
                    break;
                }
            }
            return result;
        }

        //判断是否连续且从1开始
        public bool IsContinuousAndFromOne(List<int> numList)
        {
            if (numList.Count() == 1) return true;
            //如果第一个不为1 也算不连续，有可能是删除头几行
            if (numList[0] != 1) return false;

            numList.Sort((x, y) => -x.CompareTo(y));//降序
            bool result = false;

            for (int i = 0; i < numList.Count() - 1; i++)
            {
                if (numList[i] - numList[i + 1] == 1)
                    result = true;
                else
                {
                    result = false;
                    break;
                }
            }
            return result;
        }

        /// <summary>
        /// 是否存在同一组套件商品中，子件在套件之前的情况
        /// </summary>
        /// <returns></returns>
        private bool CheckIsexistLargeSuitNumber(DynamicObjectCollection fentry)
        {
            var productIds = fentry
                            ?.Select(o => Convert.ToString(o["fmaterialid"]))
                            ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                            ?.Distinct()
                            ?.ToList();
            if (productIds == null || !productIds.Any()) return false;

            var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fname,fsuiteflag");
            if (productObjs == null || !productObjs.Any()) return false;

            //套件组合号
            var suitcombnumberLst = fentry
                .Where(f => Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1"))
                .Select(o => Convert.ToString(o["fsuitcombnumber"])).Distinct().ToList<string>();

            //配件组合号
            var partscombnumberLst = fentry.Where(o => !Convert.ToString(o["fpartscombnumber"]).IsNullOrEmptyOrWhiteSpace() && string.IsNullOrWhiteSpace(Convert.ToString(o["fcombinenumber"]))).Select(o => Convert.ToString(o["fpartscombnumber"])).Distinct().ToList<string>();
            //沙发组合号
            var sofacombnumberLst = fentry.Where(o => !Convert.ToString(o["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace() && string.IsNullOrWhiteSpace(Convert.ToString(o["fcombinenumber"]))).Select(o => Convert.ToString(o["fsofacombnumber"])).Distinct().ToList<string>();
            //促销组合
            //var promotionnumberLst = fentry.Where(o => !Convert.ToString(o["fcombinenumber"]).IsNullOrEmptyOrWhiteSpace()).Select(o => Convert.ToString(o["fcombinenumber"])).Distinct().ToList<string>();

            int Mainseq_part = int.MaxValue;
            foreach (var partscombnumber in partscombnumberLst)
            {
                //获取配件主商品
                Mainseq_part = fentry.Where(f => Convert.ToBoolean(f["fiscombmain"]) && partscombnumber == Convert.ToString(f["fpartscombnumber"]))
                .Select(o => Convert.ToInt32(o["fseq"])).FirstOrDefault();

                var FseqLst = fentry.Where(f => partscombnumber == Convert.ToString(f["fpartscombnumber"]))
                     .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();
                //判断配件组合号是否连续
                if (!IsContinuous(FseqLst)) return true;

                foreach (var entry in fentry)
                {
                    if (partscombnumber.EqualsIgnoreCase(Convert.ToString(entry["fpartscombnumber"])))
                    {
                        var fseq = Convert.ToInt32(entry["fseq"]);
                        if (fseq < Mainseq_part)
                        {
                            return true;
                        }
                    }
                }
            }

            //foreach (var promotionnumber in promotionnumberLst)
            //{
            //    var FseqLst = fentry.Where(f => promotionnumber == Convert.ToString(f["fcombinenumber"]))
            //                            .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();
            //    //判断促销组合是否连续
            //    if (!IsContinuous(FseqLst)) return true;
            //}

            foreach (var sofacombnumber in sofacombnumberLst)
            {
                var FseqLst = fentry.Where(f => sofacombnumber == Convert.ToString(f["fsofacombnumber"]))
                                        .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();
                //判断沙发组合号是否连续
                if (!IsContinuous(FseqLst)) return true;
            }

            //var entrys = fentry.GroupBy(o=>Convert.ToString(o["fsuitcombnumber"]));
            int Mainseq = int.MaxValue;
            foreach (var suitcombnumber in suitcombnumberLst)
            {
                Mainseq = fentry
                        .Where(f => Convert.ToString(productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1") && suitcombnumber == Convert.ToString(f["fsuitcombnumber"]))
                        .Select(o => Convert.ToInt32(o["fseq"])).FirstOrDefault();


                var FseqLst = fentry.Where(f => suitcombnumber == Convert.ToString(f["fsuitcombnumber"]))
                    .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();

                //判断是否连续
                if (!IsContinuous(FseqLst)) return true;

                foreach (var entry in fentry)
                {
                    if (suitcombnumber.EqualsIgnoreCase(Convert.ToString(entry["fsuitcombnumber"])))
                    {
                        var fseq = Convert.ToInt32(entry["fseq"]);
                        if (fseq < Mainseq)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 根据套件商品做排序
        /// </summary>
        /// <param name="fentry"></param>
        private void SortBysuit(DynamicObject[] DataEntitys)
        {
            foreach (var DataEntity in DataEntitys)
            {
                DynamicObjectCollection fentry = DataEntity["fentity"] as DynamicObjectCollection;
                if (!fentry.IsNullOrEmpty())
                {
                    var entrys = fentry.OrderByDescending(f => Convert.ToInt32((f["fmaterialid_ref"] as DynamicObject)?["fsuiteflag"]))
                        .OrderBy(g => Convert.ToString(g["fsuitcombnumber"]))
                        .OrderBy(g => Convert.ToString(g["fpartscombnumber"]))
                        .OrderBy(g => Convert.ToString(g["fsofacombnumber"]))
                        .ToList();
                    fentry.Clear();
                    for (int i = 0; i < entrys.Count; i++)
                    {
                        var entry = entrys[i] as DynamicObject;
                        //传过去的行也做排序
                        entry["fseq"] = i + 1;
                        fentry.Add(entry);
                    }
                }
            }
        }


        /// <summary>
        /// 判断是否存在总部商品 送达方为空的情况
        /// </summary>
        /// <param name="entry"></param>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        private bool CheckIsExistTopProEmptyDeliver(DynamicObjectCollection entry, string fdeliverid, out string errorMsg)
        {
            UserContext userCtx = this.Context;
            errorMsg = "";
            var mainorgid = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;
            // var entry = newData["fentry"] as DynamicObjectCollection;
            //至少要有一行商品明细
            if (entry == null || entry.Count <= 0) return false;

            foreach (DynamicObject item in entry)
            {

                var productId = Convert.ToString(item["fmaterialid"]);
                var productObj = userCtx.LoadBizDataById("ydj_product", productId);
                //商品明细中存在总部商品
                if (!mainorgid.EqualsIgnoreCase(Convert.ToString(productObj["fmainorgid"])))
                {
                    continue;
                }
                else
                {
                    if (fdeliverid.IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMsg = "存在总部商品时送达方不能为空，无法保存！";
                        return true;
                    }
                }
            }
            return false;
        }


        //判断是否存在总部商品 业绩品牌为空的情况 存在返回true
        private bool CheckIsExistTopProEmptyResultBrand(DynamicObjectCollection entry, out string errorMsg)
        {
            UserContext userCtx = this.Context;
            errorMsg = "";
            var mainorgid = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;
            // var entry = newData["fentry"] as DynamicObjectCollection;
            //至少要有一行商品明细
            if (entry == null || entry.Count <= 0) return false;

            foreach (DynamicObject item in entry)
            {
                //业绩品牌
                var resultBrandId = Convert.ToString(item["fresultbrandid"]);
                var productId = Convert.ToString(item["fmaterialid"]);
                var productObj = userCtx.LoadBizDataById("ydj_product", productId);
                //商品明细中存在总部商品
                if (!mainorgid.EqualsIgnoreCase(Convert.ToString(productObj["fmainorgid"])))
                {
                    continue;
                }
                else
                {
                    if (resultBrandId.IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMsg = $"第{Convert.ToInt32(item["fseq"])}行商品【{Convert.ToString(productObj["fname"])}】业绩品牌为空，无法保存！";
                        //errorMsg = "存在总部商品业绩品牌为空，无法保存！";
                        return true;
                    }
                }
            }
            return false;
        }

        //判断是否存在总部商品和非总部商品一起下单的情况
        private bool CheckIsExistTopOrgAndOtherOrg(DynamicObjectCollection entry, out string errorMsg)
        {
            UserContext userCtx = this.Context;
            errorMsg = "";
            var mainorgid = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;
            // var entry = newData["fentry"] as DynamicObjectCollection;
            //至少要有一行商品明细
            if (entry == null || entry.Count <= 0) return false;

            //不是二级销经销商需要判断下面的校验
            if (!this.Context.IsSecondOrg)
            {
                bool Havetop = false;
                bool HaveOthertop = false;
                foreach (DynamicObject item in entry)
                {
                    var productId = Convert.ToString(item["fmaterialid"]);
                    var productObj = userCtx.LoadBizDataById("ydj_product", productId);
                    //商品明细中存在总部商品
                    if (mainorgid.EqualsIgnoreCase(Convert.ToString(productObj["fmainorgid"])))
                    {
                        Havetop = true;
                    }
                    else
                    {
                        HaveOthertop = true;
                    }
                }
                //同时存在总部商品和非总部商品
                if (Havetop && HaveOthertop)
                {
                    errorMsg = "当前订单存在非总部商品, 向总部采购时, 不允许采购非总部商品!";
                    return true;
                }
            }

            //二级销经销商需要判断下面的校验
            if (this.Context.IsSecondOrg)
            {
                var productIds = entry.Select(x => Convert.ToString(x["fmaterialid"])).Distinct().ToList();
                var loadProductDys = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fmainorgid");
                var companyId = this.Context.Company;
                var parentCompanyId = this.Context.ParentCompanyId;
                var topCompanyId = this.Context.TopCompanyId;
                var seqList = new List<string>();
                var parentProductOrTopProductSeqList = new List<string>();
                var isProductSelf = false;
                var isProductParentOrTop = false;
                foreach (var item in entry)
                {
                    var materialId = Convert.ToString(item["fmaterialid"]);
                    var findProductDy = loadProductDys.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(materialId));
                    if (findProductDy != null)
                    {
                        var seqStr = Convert.ToString(item["fseq"]);
                        //商品的所在的组织id
                        var productOrgId = Convert.ToString(findProductDy["fmainorgid"]);
                        if (companyId.EqualsIgnoreCase(productOrgId))
                        {

                            seqList.Add(seqStr);
                            isProductSelf = true;
                        }
                        else if (productOrgId.EqualsIgnoreCase(parentCompanyId) || productOrgId.EqualsIgnoreCase(topCompanyId))
                        {
                            parentProductOrTopProductSeqList.Add(seqStr);
                            isProductParentOrTop = true;
                        }
                    }
                }

                if (seqList != null && seqList.Any() && isProductSelf && isProductParentOrTop)
                {
                    var headDy = entry.Parent as DynamicObject;
                    var purchaseOrderNo = Convert.ToString(headDy["fbillno"]);
                    var errorProductMsg = string.Empty;
                    if (parentProductOrTopProductSeqList != null && parentProductOrTopProductSeqList.Any())
                    {
                        errorProductMsg = $"商品明细行第{string.Join("、", parentProductOrTopProductSeqList.Select(x => $"【{x}】"))}行是总部商品或者一级自建商品，";
                    }
                    errorMsg = $"采购订单【{purchaseOrderNo}】的商品明细行第{string.Join("、", seqList.Select(x => $"【{x}】"))}行是自建商品，{errorProductMsg}不允许提交一级经销商";
                    return true;
                }

            }

            return false;
        }

        //根据源单ID找到对应合同
        private DynamicObject GetOrderDm(string fbillno)
        {
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));

            var where = "fbillno=@fbillno";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fbillno", System.Data.DbType.String, fbillno)
            };

            var reader = this.Context.GetPkIdDataReader(purForm, where, sqlParam);
            var purOrder = dm.SelectBy(reader).OfType<DynamicObject>().FirstOrDefault();
            return purOrder;
        }

        /// <summary>
        /// 检查明细物料启用选配码时，保存识别到物料没有进行产品选配生成选配码
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        private bool CheckEntry(DynamicObject dataEntity, out string message)
        {
            message = "";
            //202404022 性能优化：下面的T_BD_AUXPROPVALUEMAP表没有数据，同时慕思的选配类别也不会有 'XPM' 的选配码 ，就不要去查询数据了
            return true;

            DynamicObjectCollection entrys = dataEntity["fentity"] as DynamicObjectCollection;
            if (entrys != null)
            {
                //没有进行产品选配，选配码为空的物料Id
                var materialIdsNull = entrys.Where(p => Convert.ToString(p["fselectionnumber"]).IsNullOrEmptyOrWhiteSpace())
                                            .Select(p => Convert.ToString(p["fmaterialid"])).ToList();
                if (materialIdsNull.Count() == 0)
                {
                    return true;
                }

                //获取物料的辅助属性启用选配码
                var materialIds = entrys.Select(p => Convert.ToString(p["fmaterialid"])).Distinct().ToList();
                var sql = @"SELECT t.fmaterialid as fmaterialid,t2.fname
                            FROM T_BD_AUXPROPVALUEMAP t
                            INNER JOIN t_sel_prop t1 ON t.fauxpropid = t1.fid
                            INNER join t_bd_material t2 ON t.fmaterialid = t2.fid
                            WHERE t1.fnumber = 'XPM' AND t.fmainorgid = '{0}' AND t.fmaterialid in('{1}') ".Fmt(this.Context.Company, string.Join("','", materialIds));
                var uxpropvaluemapObjs = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
                if (uxpropvaluemapObjs.Count() != 0)
                {
                    var mName = new List<string>();
                    foreach (var uxpropvaluemapObj in uxpropvaluemapObjs)
                    {
                        if (materialIdsNull.Contains(Convert.ToString(uxpropvaluemapObj["fmaterialid"])))
                        {
                            mName.Add(Convert.ToString(uxpropvaluemapObj["fname"]));
                        }
                    }
                    if (mName.Count() > 0)
                    {
                        //辅助属性包含选配码 && 没有进行产品选配 提示用户
                        message = @"商品【{0}】,未进行选配，不允许保存！".Fmt(string.Join(",", mName));
                        return false;
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// 检查单据类型参数设置
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        private bool CheckBillTypeParamSet(DynamicObject dataEntity, out string message)
        {
            message = "";

            var billTypeId = Convert.ToString(dataEntity["fbilltypeid"]);
            if (!billTypeId.IsNullOrEmptyOrWhiteSpace())
            {
                var billTypeService = this.Container.GetService<IBillTypeService>();
                var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, this.HtmlForm, billTypeId);
                if (paramSetObj != null)
                {
                    var billTypeObj = billTypeService.GetBillTypeInfor(this.Context, billTypeId);

                    //是否必须要上传图纸
                    var isUploadDraw = Convert.ToBoolean(paramSetObj["fisuploaddraw"]);
                    if (isUploadDraw)
                    {
                        var drawEntitys = dataEntity["fdrawentity"] as DynamicObjectCollection;
                        if (drawEntitys == null || drawEntitys.Count <= 0)
                        {
                            message = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】的单据类型为{billTypeObj.fname}，要求必须上传图纸信息！";
                            return false;
                        }
                    }

                    //终端客户信息必填
                    var customerRequired = Convert.ToBoolean(paramSetObj["fcustomerrequired"]);
                    if (customerRequired)
                    {
                        if (dataEntity["fcustomerid"].IsNullOrEmptyOrWhiteSpace()
                            || dataEntity["fphone"].IsNullOrEmptyOrWhiteSpace()
                            || dataEntity["fprovince"].IsNullOrEmptyOrWhiteSpace()
                            || dataEntity["fcity"].IsNullOrEmptyOrWhiteSpace()
                            || dataEntity["fregion"].IsNullOrEmptyOrWhiteSpace()
                            || dataEntity["faddress"].IsNullOrEmptyOrWhiteSpace())
                        {
                            message = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】的单据类型为{billTypeObj.fname}，要求：
                                        1.客户不能为空！
                                        2.客户手机号不能为空！
                                        3.客户省市区不能为空！
                                        4.客户详细地址不能为空！";
                            return false;
                        }
                    }

                    //主产品必录
                    var mpRequired = Convert.ToBoolean(paramSetObj["fmprequired"]);
                    if (mpRequired)
                    {
                        if (dataEntity["fmainproduct"].IsNullOrEmptyOrWhiteSpace())
                        {
                            message = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】的单据类型为{billTypeObj.fname}，要求主产品必填！";
                            return false;
                        }
                    }

                    //允许商品信息为空
                    var allowProductNull = Convert.ToBoolean(paramSetObj["fallowproductnull"]);
                    if (!allowProductNull)
                    {
                        DynamicObjectCollection entitys = dataEntity["fentity"] as DynamicObjectCollection;
                        if (entitys == null || entitys.Count <= 0)
                        {
                            message = $@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】的单据类型为{billTypeObj.fname}，要求：
                                        1.商品信息不允许为空，至少要录入一行数据！";
                            return false;
                        }
                    }

                    //商品所属分类是否是指定的分类
                    var categoryId = Convert.ToString(paramSetObj["fcategoryid"]);
                    if (!categoryId.IsNullOrEmptyOrWhiteSpace())
                    {
                        var categoryIds = categoryId.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                        var productEntrys = dataEntity["fentity"] as DynamicObjectCollection;
                        if (productEntrys != null && productEntrys.Count > 0)
                        {
                            var productIds = productEntrys.Select(o => Convert.ToString(o["fmaterialid"])).Distinct();

                            var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
                            var dm = this.GetDataManager();
                            dm.InitDbContext(this.Context, productForm.GetDynamicObjectType(this.Context));
                            var productObjs = dm.Select(productIds).OfType<DynamicObject>();
                            if (productObjs != null)
                            {
                                var productNames = new List<string>();
                                foreach (var productObj in productObjs)
                                {
                                    if (!categoryIds.Contains(Convert.ToString(productObj["fcategoryid"]), StringComparer.OrdinalIgnoreCase))
                                    {
                                        productNames.Add(productObj["fname"] as string);
                                    }
                                }
                                if (productNames.Count > 0)
                                {
                                    var categoryField = productForm.GetField("fcategoryid");
                                    message = $"商品【{string.Join(",", productNames)}】的{categoryField.Caption}不符合要求。\r\n请检查{billTypeObj.fname}单据类型中的【商品类别可选范围】设置！";
                                    return false;
                                }
                            }
                        }
                    }
                }
            }

            return true;
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            string fstatus = this.GetQueryOrSimpleParam<string>("fstatus");
            var pkIds = e.DataEntitys.Where(f => !Convert.ToString(f["id"]).IsNullOrEmptyOrWhiteSpace()).Select(f => Convert.ToString(f["id"]));
            if (pkIds == null || !pkIds.Any()) return;

            var dbObjList = this.Context.LoadBizDataById(this.HtmlForm.Id, pkIds);
            BeforeSoOrderEntryIds = new ConcurrentDictionary<string, List<string>>();
            foreach (var dbObj in dbObjList)
            {
                if (!fstatus.IsNullOrEmptyOrWhiteSpace() && !dbObj["fstatus"].ToString().EqualsIgnoreCase(fstatus))
                {
                    throw new BusinessException($"采购订单【{dbObj?["fbillno"].ToString()}】数据状态已被修改,请刷新或关闭重新打开,谢谢！");
                }
                var billPkid = Convert.ToString(dbObj["id"]);

                var entrySrcIds = (dbObj["fentity"] as DynamicObjectCollection).Select(t => Convert.ToString(t["fsoorderentryid"])).Distinct().ToList();
                if (!BeforeSoOrderEntryIds.ContainsKey(billPkid))
                {
                    BeforeSoOrderEntryIds.TryAdd(billPkid, entrySrcIds);
                }
            }
        }
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            ////此处只为让变更生效后自动保存一次最新值，可以不走业务
            //if (this.Option.GetVariableValue<string>("NoValidation", string.Empty).EqualsIgnoreCase("1"))
            //{
            //    return;
            //}
            //SortBysuit(e.DataEntitys);
            if (!this.Context.IsSecondOrg)
            {
                //变更中的采购订单和总部手工单保存不需要校验停购 
                var validData = e.DataEntitys.Where(o => !Convert.ToString(o["fchangestatus"]).EqualsIgnoreCase("1") && !Convert.ToString(o["fbilltypeid"]).EqualsIgnoreCase("ydj_purchaseorder_zb"));
                if (validData != null && validData.Any())
                {
                    var productInfoService = this.Container.GetService<IProductInfoService>();
                    productInfoService.CheckProducteIsEndPurchase(this.Context, "fmaterialid", this.HtmlForm, e.DataEntitys);
                }
            }



            //Dictionary<string, decimal> newEntryDic = new Dictionary<string, decimal>();
            //var fids = new List<string>();
            //foreach (var dataEntity in e.DataEntitys)
            //{
            //    fids.Add(Convert.ToString(dataEntity["id"]));
            //    var entryDatas = dataEntity["fentity"] as DynamicObjectCollection;
            //    foreach (var entry in entryDatas)
            //    {
            //        // 采购数量
            //        var qty = Convert.ToDecimal(entry["fbizqty"]);

            //        newEntryDic.Add(Convert.ToString(entry["id"]), qty);
            //    }
            //}

            //var oldEntryList = new List<DynamicObject>();
            //List<string> oldZeroList = new List<string>();
            //oldEntryList = this.DBService.ExecuteDynamicObject(this.Context, "select fentryid,fsoorderentryid,fbizqty from t_ydj_poorderentry where fid in('" + string.Join("','", fids) + "')").ToList();
            //var sql = "";
            //if (oldEntryList.Any())
            //{
            //    var orderEntryList = this.DBService.ExecuteDynamicObject(this.Context, "select fentryid,ftranspurqty from t_ydj_orderentry where fentryid in('" + string.Join("','", oldEntryList.Select(x => x["fsoorderentryid"]).ToList()) + "')").ToList();
            //    oldEntryList?.ForEach(o =>
            //    {
            //        var oldTransPurQty = Convert.ToDecimal(orderEntryList.Find(s => Convert.ToString(s["fentryid"]) == Convert.ToString(o["fsoorderentryid"]))?["ftranspurqty"]); //修改前的已转采购数
            //        var oldQty = Convert.ToDecimal(o["fbizqty"]);//修改前的采购数量
            //        if (newEntryDic.ContainsKey(Convert.ToString(o["fentryid"])))
            //        {
            //            var newQty = newEntryDic[Convert.ToString(o["fentryid"])]; //修改后的采购数据

            //            var writeTransPurQty = oldTransPurQty - oldQty + newQty;  //需要修改的已转采购数
            //            sql += $" update t_ydj_orderentry set ftranspurqty={writeTransPurQty} where fentryid='{Convert.ToString(o["fsoorderentryid"])}'";
            //        }
            //        else
            //        {
            //            sql += $" update t_ydj_orderentry set ftranspurqty=ftranspurqty-{oldQty} where fentryid='{Convert.ToString(o["fsoorderentryid"])}'";
            //        }
            //    });
            //    if (!sql.IsNullOrEmptyOrWhiteSpace())
            //    {
            //        using (var reader = this.Context.Container.GetService<IDBService>().ExecuteReader(this.Context, sql))
            //        {

            //        }
            //    }
            //}
            //OperateLinkPro(e.DataEntitys);
            var purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
            var seqService = this.Container.GetService<ISequenceService>();

            //// 加载引用数据
            //var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            //refMgr.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, false);
            Dictionary<string, List<FSWidgetFileInfo>> dctFSWidgetData = new Dictionary<string, List<FSWidgetFileInfo>>();
            foreach (var dataEntity in e.DataEntitys)
            {
                if (Convert.ToString(dataEntity["fchangestatus"]).EqualsIgnoreCase("1"))
                {
                    //如果是变更中保存，则关闭状态重新计算，否则变更单不会更新最新关闭状态
                    Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(dataEntity, string.Empty);
                }
                //to do 设置固定行号fseq_e给中台，后面修改排序保存都不会影响fseq_e的值。
                //不再在 保存的时候更新固定字段，而是在提交总部的时候统一生成固定下来
                //SetFseqInit(dataEntity);

                //流水号特殊处理
                if (dataEntity["ftranid"].IsNullOrEmptyOrWhiteSpace())
                {
                    dataEntity["ftranid"] = seqService.GetSequence<string>();
                }
                if (dataEntity["ftoptranid"].IsNullOrEmptyOrWhiteSpace())
                {
                    dataEntity["ftoptranid"] = dataEntity["ftranid"];
                }
                //if (Convert.ToString((dataEntity["fbilltypeid_ref"] as DynamicObject)["fname"]) =="摆场订单")
                //{
                var fentities = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var item in fentities)
                {
                    if (Convert.ToString(item["fsourcebillno"]).IsNullOrEmptyOrWhiteSpace() && Convert.ToString(item["fsaledeptid"]).IsNullOrEmptyOrWhiteSpace())
                    {
                        //针对无源单并且销售部门为空的情况，做赋值
                        item["fsaledeptid"] = dataEntity["fpodeptid"];
                    }
                }
                //}
                //计算汇总金额
                aggregateComputation(dataEntity, purchaseOrderService);
                //业务类型为“协同采购”时，表体商品必须是协同供应商同步过来的，且必须与本地商品发生过关联的商品，反之不用过虑控制
                if (!this.CheckProductEntry(dataEntity)) return;

                SetDetailRow(dataEntity);

                ; List<FSWidgetFileInfo> lstFileWidgets = new List<FSWidgetFileInfo>();
                //设置图纸信息上传人
                DynamicObjectCollection drawEntitys = dataEntity["fdrawentity"] as DynamicObjectCollection;
                if (drawEntitys != null)
                {
                    foreach (var drawEntity in drawEntitys)
                    {
                        if (dataEntity.DataEntityState.FromDatabase == false && drawEntity["ffileid"].IsNullOrEmptyOrWhiteSpace())
                        {
                            //新增的表单，如果没有文件就不要协同了
                            continue;
                        }

                        if (drawEntity["fuploader"].IsNullOrEmptyOrWhiteSpace())
                        {
                            var uploader = this.Context.DisplayName;
                            if (uploader.IsNullOrEmptyOrWhiteSpace())
                            {
                                uploader = this.Context.UserName;
                            }
                            drawEntity["fuploader"] = uploader;
                        }

                        FSWidgetFileInfo fsFile = new FSWidgetFileInfo();
                        fsFile.Id = "";
                        fsFile.FileId = drawEntity["ffileid"] as string;
                        fsFile.FileName = drawEntity["ffilename"] as string;
                        fsFile.FileFormat = drawEntity["ffileformat"] as string;
                        fsFile.FileSize = drawEntity["ffilesize"] as string;
                        fsFile.Uploader = drawEntity["fuploader"] as string;
                        fsFile.UploaderId = drawEntity["fuploaderid"] as string;
                        if (fsFile.UploaderId.IsNullOrEmptyOrWhiteSpace())
                        {
                            fsFile.UploaderId = this.Context.UserId;
                        }
                        var uploadTime = DateTime.Now;
                        if (!DateTime.TryParse(Convert.ToString(drawEntity["fuptime"]), out uploadTime))
                        {
                            uploadTime = DateTime.Now;
                        }
                        fsFile.UploadTime = uploadTime;
                        fsFile.Description = drawEntity["fnote"] as string;
                        fsFile.CooSrcId = drawEntity["fsourceentryid"] as string;
                        lstFileWidgets.Add(fsFile);
                    }

                    if (dataEntity.DataEntityState.FromDatabase == true || lstFileWidgets.Any())
                    {
                        dctFSWidgetData[dataEntity["ftranid"] as string] = lstFileWidgets;
                    }

                    //如果是协同订单，并且已经建立协同关系，则需要协同文件到对方系统
                    if (purchaseOrderService.CheckIsSync(this.Context, dataEntity)
                        && Convert.ToString(dataEntity["fpublishstatus"]).EqualsIgnoreCase("publish_status_02"))
                    {
                        //找出采购方上传的文件，然后协同给销售方
                        List<DynamicObject> synFiles = null;
                        if (drawEntitys != null)
                        {
                            synFiles = drawEntitys.Where(t => t["fsourceentryid"].IsNullOrEmptyOrWhiteSpace()).ToList();
                        }

                        //更新销售方协同文件明细
                        this.SendSynFileUpdate(dataEntity, synFiles);
                    }
                }

            }

            //同步文件令牌至文件服务组件
            if (dctFSWidgetData.Any())
            {
                var fileServer = this.GetFirstWorkFileServer();
                if (fileServer.IsNullOrEmpty())
                {
                    throw new BusinessException("文件服务器配置不正确或未配置！");
                }
                TargetServer ts = new TargetServer()
                {
                    Host = fileServer.Url,
                    Headers = new Dictionary<string, string>
                {
                    {"sysCode",fileServer.SysCode },
                    {"authCode",fileServer.AuthCode }
                },
                };

                var seqSvc = this.Container.GetService<ISequenceService>();

                foreach (var kvpItem in dctFSWidgetData)
                {
                    var fsId = seqSvc.GetSequence<string>();
                    var fsTranId = this.Gateway.InvokePrimitive<FSWidgetDataEntryDTO, string>(this.Context,
                    new FSWidgetDataEntryDTO()
                    {
                        Id = fsId,
                        BillFormId = "ydj_purchaseorder",
                        BillEntity = "fbillhead",
                        BillTranId = kvpItem.Key,
                        FileDetail = kvpItem.Value?.ToJson() ?? ""
                    },
                    ts,
                     Enu_HttpMethod.Post);
                    if (!fsTranId.EqualsIgnoreCase(fsId))
                    {
                        //同步不成功
                    }
                }
            }

        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            //此处只为让变更生效后自动保存一次最新值，可以不走保存后重新自动关闭或反关闭逻辑
            if (this.Option.GetVariableValue<string>("NoValidation", string.Empty).EqualsIgnoreCase("1"))
            {
                return;
            }
            else
            {
                if (this.Context.IsSecondOrg)
                {
                    // 当前二级经销商信息
                    var currentAgent = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fisnotmgrinv");
                    this.IsNotMgrInv = Convert.ToString(currentAgent?["fisnotmgrinv"]) == "1";
                }
                List<SelectedRow> bizCloseIds = new List<SelectedRow>();
                List<SelectedRow> unCloseIds = new List<SelectedRow>();
                foreach (var dataEntity in e.DataEntitys)
                {
                    Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(dataEntity, string.Empty, !IsNotMgrInv);
                    var entryDatas = dataEntity["fentity"] as DynamicObjectCollection;
                    foreach (var entry in entryDatas)
                    {
                        // 采购数量
                        var qty = Convert.ToDecimal(entry["fbizqty"]);
                        // 行关闭状态
                        int.TryParse(Convert.ToString(entry["fclosestatus_e"]), out var entryCloseStatus);
                        if (qty == 0 && entryCloseStatus != (int)CloseStatus.Manual)
                        {
                            bizCloseIds.Add(new SelectedRow
                            {
                                PkValue = Convert.ToString(dataEntity["id"]),
                                EntryPkValue = Convert.ToString(entry["id"]),
                                EntityKey = "fentity"
                            });
                        }
                        else if (entryCloseStatus == (int)CloseStatus.Manual && qty != 0)
                        {
                            unCloseIds.Add(new SelectedRow
                            {
                                PkValue = Convert.ToString(dataEntity["id"]),
                                EntryPkValue = Convert.ToString(entry["id"]),
                                EntityKey = "fentity"
                            });
                        }
                    }
                }

                List<string> oldZeroList = new List<string>();

                //this.Container.GetService<IRepairService>().RepairBillHeadSourceNum(this.Context,e.DataEntitys,"fsourcenumber","","fentity", "fsourcebillno","");
                this.Context.SaveBizData(this.HtmlForm.Id, e.DataEntitys);

                List<string> removeEntrySrcIds = new List<string>();
                foreach (var item in e.DataEntitys)
                {
                    var billPkid = Convert.ToString(item["id"]);
                    if (BeforeSoOrderEntryIds.ContainsKey(billPkid))
                    {
                        var entrySrcIds = (item["fentity"] as DynamicObjectCollection).Select(t => Convert.ToString(t["fsoorderentryid"])).Distinct().ToList();
                        var oldSrcIds = BeforeSoOrderEntryIds[billPkid];
                        var currRmvSrcIds = oldSrcIds.Where(t => !entrySrcIds.Contains(t)).ToList();
                        if (currRmvSrcIds != null && currRmvSrcIds.Any())
                        {
                            removeEntrySrcIds.AddRange(currRmvSrcIds);
                        }
                    }
                }
                // 反写销售合同【已转采购数】
                Core.Helpers.OrderQtyWriteBackHelper.WriteBackTransPurQty(
                    this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo, removeEntrySrcIds);

                var entitys = e.DataEntitys.SelectMany(o => o["fentity"] as DynamicObjectCollection).ToList();
                List<string> ids = entitys.Select(o => Convert.ToString(o?["fsoorderentryid"])).ToList<string>();
                ids.AddRange(removeEntrySrcIds);

                // 反写销售合同【流程状态】
                Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                    this.Context, this.HtmlForm, e.DataEntitys, ids);

                //if (upOrderEntryIds.Any())
                //{
                //    // 反写销售合同【流程状态】
                //    Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                //        this.Context, this.HtmlForm, e.DataEntitys, upOrderEntryIds, orderFids);
                //}
                if (bizCloseIds.Any())
                {
                    List<string> rowIds = bizCloseIds.Select(x => x.EntryPkValue).ToList();
                    var invokeClose = this.Gateway.InvokeListOperation(this.Context,
                        this.HtmlForm.Id,
                        bizCloseIds,
                        "bizclose",
                        new Dictionary<string, object>() {
                        { "rowIds",rowIds.ToJson() },
                        { "IgnoreCheckPermssion","true" }//去掉权限校验
                        });
                    invokeClose?.ThrowIfHasError(true, $"自动关闭数量为0商品行操作异常！");
                }
                if (unCloseIds.Any())
                {
                    List<string> rowIds = unCloseIds.Select(x => x.EntryPkValue).ToList();
                    if (!oldZeroList.IsNullOrEmpty() || oldZeroList.Any())
                    {
                        rowIds = rowIds.Where(x => oldZeroList.Contains(x)).ToList();
                        unCloseIds = unCloseIds.Where(x => oldZeroList.Contains(x.EntryPkValue)).ToList();
                        var invokeUnClose = this.Gateway.InvokeListOperation(this.Context,
                        this.HtmlForm.Id,
                        unCloseIds,
                        "unclose",
                        new Dictionary<string, object>() {
                        { "rowIds",rowIds.ToJson() },
                        { "IgnoreCheckPermssion","true" },//去掉权限校验
                        { "modifyCloseStatus","1"}
                        });
                        invokeUnClose?.ThrowIfHasError(true, $"自动反关闭手工关闭商品行操作异常！");
                    }
                }
            }

            //做参数判断
            bool isAutoSubmitAfterSave = this.GetQueryOrSimpleParam<bool>("autosubmit");
            bool isAutoAuditAfterSave = this.GetQueryOrSimpleParam<bool>("autoaudit");
            var secendconfirm = this.GetQueryOrSimpleParam<string>("secendconfirm");
            if (isAutoSubmitAfterSave || isAutoAuditAfterSave)
            {
                if (!secendconfirm.EqualsIgnoreCase("1"))
                {
                    var commonCombine = new CommonCombine(this.Context, this.Context.Container.GetService<IDBService>());
                    List<string> mustcombine = new List<string>();
                    bool flag = false;
                    (flag, this.Result.SrvData, mustcombine) = commonCombine.GetSalePromotion(this.Context, e.DataEntitys[0]);
                    if (mustcombine.Count > 0)
                    {
                        commonCombine.SaveCombineInfo1(this.Context, e.DataEntitys.ToList(), mustcombine);
                        //ModifyPromotion(new List<DynamicObject>() { targetData }, mustcombine);
                        //ModifyPromotion(e.DataEntitys.ToList(), mustcombine);
                    }
                    var autoSave = isAutoSubmitAfterSave ? "autosubmit" : "autoaudit";
                    this.Result.OptionData.Add("save", this.Result.SrvData);
                    this.Result.OptionData.Add("autoSave", autoSave);

                    if (flag)
                    {
                        this.SimpleData[autoSave] = "false";
                    }
                }
            }
        }

        private void SetFseqInit(DynamicObject dataEntity)
        {
            DynamicObjectCollection entitys = dataEntity["fentity"] as DynamicObjectCollection;
            foreach (var entity in entitys)
            {
                var fseq_e = Convert.ToInt32(entity["fseq_e"]);
                //已设置的不再设置
                if (fseq_e == 0)
                {
                    var fseq = Convert.ToInt32(entity["fseq"]);
                    //如果是新增的行则赋值最大行+1
                    if (entitys.Any(o => Convert.ToInt32(o["fseq_e"]).Equals(Convert.ToInt32(entity["fseq"]))))
                    {
                        entity["fseq_e"] = entitys.Select(o => Convert.ToInt32(o["fseq_e"])).Max() + 1;
                    }
                    else
                    {
                        entity["fseq_e"] = fseq;
                    }
                }
            }
            //判断 fseq_e是否连续 如果不连续 ，说明做了删除行操作此时要重新根据fseq_e 排序 生成新的fseq_e
            var FseqLst = entitys.Select(o => Convert.ToInt32(o["fseq_e"])).ToList<int>();
            //判断配件组合号是否连续
            if (!IsContinuousAndFromOne(FseqLst))
            {
                var fseq = 1;
                var entity_new = entitys.OrderBy(o => Convert.ToInt32(o["fseq_e"]));
                foreach (var et in entitys)
                {
                    foreach (var et_new in entity_new)
                    {
                        if (Convert.ToString(et_new["id"]).EqualsIgnoreCase(Convert.ToString(et["id"])))
                        {
                            et["fseq_e"] = fseq++;
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 设置明细行 上传方
        /// </summary>
        /// <param name="dataEntity"></param>
        private void SetDetailRow(DynamicObject dataEntity)
        {
            DynamicObjectCollection entitys = dataEntity["fentity"] as DynamicObjectCollection;
            foreach (var entity in entitys)
            {
                var fmulfile = Convert.ToString(entity["fmulfile"]);
                var fmulfile_source = Convert.ToString(entity["fmulfile_source"]);
                //已设置的不用再设置，避免金蝶云上传后全部更改为渠道
                if (!fmulfile.IsNullOrEmptyOrWhiteSpace())
                {
                    var index = fmulfile.Split(',').ToList().Count();
                    List<string> fmulfile_sourceList = new List<string>();
                    for (int i = 0; i < index; i++)
                    {
                        fmulfile_sourceList.Add("渠道");
                    }
                    entity["fmulfile_source"] = string.Join(",", fmulfile_sourceList.ToArray());
                }
            }
        }

        private void aggregateComputation(DynamicObject dataEntity, IPurchaseOrderService purchaseOrderService)
        {
            var fentities = dataEntity["fentity"] as DynamicObjectCollection;
            decimal fsumdealamount = 0;
            decimal fsumamount = 0;
            if (fentities != null && fentities.Count > 0)
            {
                foreach (var fentry in fentities)
                {
                    if (Convert.ToString(fentry["fcooeditstatus"]) != "3")
                    {
                        fsumdealamount += Convert.ToDecimal(fentry["fdealamount"]);
                        fsumamount += Convert.ToDecimal(fentry["famount"]);
                    }
                }
            }
            dataEntity["ffbillamount"] = fsumdealamount;
            dataEntity["fdealamount"] = fsumamount;

            var settledAmount = purchaseOrderService.CheckIsSync(this.Context, dataEntity) ?
                                Convert.ToDecimal(dataEntity["fpaidamount"]) :
                                Convert.ToDecimal(dataEntity["fsettleamount"]);

            //结算状态
            //purchaseOrderService.CalculateReceiptStatus(this.Context, dataEntity);
            //【待结算金额】= 【成交金额】-【已付金额】-【待确认金额】
            dataEntity["fpayamount"] = fsumdealamount - settledAmount - Convert.ToDecimal(dataEntity["fconfirmamount"]);
        }

        /// <summary>
        /// 更新销售方协同文件明细
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="synFiles"></param>
        private void SendSynFileUpdate(DynamicObject dataEntity, List<DynamicObject> synFiles)
        {
            //如果此次保存操作是由SyncChange触发的，不需要再协同到对方，因为是对方变更时回调的
            if (isSyncChange())
            {
                return;
            }

            //供应商信息
            var supplier = this.GetSupplierById(dataEntity);

            //客户的协同企业ID
            var customerCompanyId = supplier["fcoocompanyid"] as string;
            var cooProductId = supplier["fcooproductid"] as string;
            if (customerCompanyId.IsNullOrEmptyOrWhiteSpace()) return;

            this.Container.GetService<ILogServiceEx>()?.Info($"文件协同服务：{customerCompanyId}==={cooProductId}");

            //数据发送时采用异步消息模式发送，消息中指定回调类型
            var responseResult = this.Gateway.Invoke(
                this.Context,
                new TargetSEP(customerCompanyId, cooProductId),
                new CommonBillDTO()
                {
                    FormId = "ydj_saleintention",
                    OperationNo = "UpdateSyn",
                    BillData = "",
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string>
                    {
                        { "tranId", dataEntity["ftranid"] as string },
                        { "type", dataEntity["ftype"] as string},
                        { "synFiles", synFiles.ToJson() },
                    }
                }) as CommonBillDTOResponse;
            responseResult?.OperationResult?.ThrowIfHasError(true, $"协同提交失败，对方系统未返回任何响应！");
        }

        /// <summary>
        /// 准备操作关联服务
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            if (isSyncChange() == false)
            {
                e.Services.Add(new FormServiceDesc()
                {
                    ServiceAlias = "filesyn",
                    Condition = "fsourceentryid==null or fsourceentryid=='' or fsourceentryid==' '",
                    ParamString = new
                    {
                        companyFieldKey = "fsupplierid.fcoocompanyid",
                        productFieldKey = "fsupplierid.fcooproductid",
                    }.ToJson()
                });
            }
        }

        /// <summary>
        /// 检查当前单据业务类型是否有被修改
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private bool CheckBillTypeIsChange(DynamicObject dataEntity)
        {
            if (dataEntity.DataEntityState.FromDatabase)
            {
                string id = Convert.ToString(dataEntity["id"]);

                var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_purchaseorder");
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

                var dynObj = dm.Select(id) as DynamicObject;
                if (dynObj == null) throw new BusinessException($"采购订单不存在！");

                return Convert.ToString(dataEntity["fbilltypeid"]).EqualsIgnoreCase(Convert.ToString(dynObj["fbilltypeid"]));
            }

            return true;
        }

        /// <summary>
        /// 获取供应商信息
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private DynamicObject GetSupplierById(DynamicObject dataEntity)
        {
            string supplierId = Convert.ToString(dataEntity["fsupplierid"]);
            if (supplierId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"供应商ID为空！");

            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_supplier");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var supplier = dm.Select(supplierId) as DynamicObject;
            if (supplier == null) throw new BusinessException($"供应商不存在！");

            return supplier;
        }

        /// <summary>
        /// 检查协同采购订单的商品明细是否符合要求
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private bool CheckProductEntry(DynamicObject dataEntity)
        {
            var billTypeId = Convert.ToString(dataEntity["fbilltypeid"]);
            if (!billTypeId.IsNullOrEmptyOrWhiteSpace())
            {
                var billTypeService = this.Container.GetService<IBillTypeService>();
                var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, this.HtmlForm, billTypeId);
                if (paramSetObj != null)
                {
                    var billTypeObj = billTypeService.GetBillTypeInfor(this.Context, billTypeId);

                    //允许商品信息为空
                    var allowProductNull = Convert.ToBoolean(paramSetObj["fallowproductnull"]);
                    if (!allowProductNull)
                    {
                        var purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
                        if (purchaseOrderService.CheckIsSync(this.Context, dataEntity))
                        {
                            DynamicObjectCollection entitys = dataEntity["fentity"] as DynamicObjectCollection;
                            if (entitys != null)
                            {
                                //去重后的商品ID集合
                                var materialIds = entitys
                                    .Select(t => Convert.ToString(t["fmaterialid"]))
                                    .Where(t => !t.IsNullOrEmptyOrWhiteSpace())
                                    .Distinct()
                                    .ToList();
                                if (materialIds == null || !materialIds.Any())
                                {
                                    throw new BusinessException($@"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】的单据类型为{billTypeObj.fname}，要求：
                                    1.商品信息不允许为空，至少要录入一行数据！");
                                }

                                //当前所选供应商信息
                                var supplier = this.GetSupplierById(dataEntity);
                                var supplierCompanyId = Convert.ToString(supplier["fcoocompanyid"]);

                                //检查当前明细中的商品是否是当前所选供应商的协同商品
                                List<string> notSynProductNames = this.GetSupplierNotSynergyProductNames(supplierCompanyId, materialIds);
                                if (notSynProductNames != null && notSynProductNames.Any())
                                {
                                    foreach (var productName in notSynProductNames)
                                    {
                                        this.Result.ComplexMessage.ErrorMessages.Add($"【{productName}】不是供应商【{supplier["fname"]}】的协同商品，请检查！");
                                    }

                                    return false;
                                }
                            }
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// 在指定的商品ID中找出不属于某个供应商的协同商品名称集合
        /// </summary>
        /// <param name="supplierCompanyId"></param>
        /// <param name="productIds"></param>
        /// <returns></returns>
        private List<string> GetSupplierNotSynergyProductNames(string supplierCompanyId, List<string> productIds)
        {
            var strSql = @"
            select distinct flocalproductid from t_coo_product
            where fpublishcompanyid=@companyId";

            StringBuilder sbWhere = new StringBuilder();
            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("@companyId", DbType.String, supplierCompanyId)
            };

            for (int i = 0; i < productIds.Count; i++)
            {
                var paramName = "@pid" + i;
                sbWhere.Append(paramName).Append(",");
                paramList.Add(new SqlParam(paramName, DbType.String, productIds[i]));
            }

            strSql += $" and flocalproductid in({sbWhere.ToString().TrimEnd(',')})";

            List<string> synProductIds = new List<string>();
            var dbService = this.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(this.Context, strSql, paramList))
            {
                while (reader.Read())
                {
                    synProductIds.Add(reader.GetString("flocalproductid"));
                }
            }

            List<string> notSynProductIds = new List<string>();
            List<string> notSynProductNames = new List<string>();

            if (productIds.Count != synProductIds.Count)
            {
                foreach (var productId in productIds)
                {
                    if (!synProductIds.Contains(productId))
                    {
                        notSynProductIds.Add(productId);
                    }
                }
                if (notSynProductIds.Any())
                {
                    var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_product");
                    var dm = this.Container.GetService<IDataManager>();
                    dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                    DynamicObjectCollection productList = dm.Select(notSynProductIds) as DynamicObjectCollection;
                    if (productList != null)
                    {
                        notSynProductNames = productList.Select(t => Convert.ToString(t["fname"])).ToList();
                    }
                }
            }

            return notSynProductNames;
        }

        /// <summary>
        /// 检查订单日期和交货日期
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private bool CheckOrderDate(DynamicObject dataEntity)
        {
            string fdate = Convert.ToString(dataEntity["fdate"]);
            string fpickdate = Convert.ToString(dataEntity["fpickdate"]);
            DateTime orderDt, pickDt;
            if (!DateTime.TryParse(fdate, out orderDt) || !DateTime.TryParse(fpickdate, out pickDt) || orderDt.CompareTo(pickDt) > 0)
                return false;
            return true;
        }

        /// <summary>
        /// 如果是协同变更触发，不需要再协同到对方，因为是对方发起的协同变更
        /// </summary>
        /// <returns></returns>
        private bool isSyncChange()
        {
            //如果此次保存操作是由SyncChange触发的，不需要再协同到对方，因为是对方变更时回调的
            var originalOpNo = this.Option.GetVariableValue<string>("originalOpNo", string.Empty);
            return "SyncChange".EqualsIgnoreCase(originalOpNo);
        }

        /// <summary>
        /// 处理单据关闭状态
        /// 作者：zpf
        /// 日期：2022-06-06
        /// </summary>
        /// <param name="dataEntity"></param>
        private void ProcCloseStatus(DynamicObject dataEntity)
        {
            //如果是变更合同数量，则处理行关闭状态
            /*
             * 新需求：32383
             4. 《采购订单》与《销售合同》点击"变更完成" 时,增加更新单据头的【关闭状态】与明细行的所有【行关闭状态】 , 以下拿《销售合同》为例, 如果是 《采购订单》则用【采购数量】【采购入库数量】【采购退换数量】
               1)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】> 0 且 等于明细行的【销售数量】 时, 明细行的【行关闭状态】= "正常"
               2)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】> 0 且 不等于明细行的【销售数量】 时, 明细行的【行关闭状态】= "部份关闭"
               3)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】= 0  时, 明细行的【行关闭状态】= "自动关闭"

               4) 如果所有明细行的【行关闭状态】="正常"时, 更新单据头的【关闭状态】="正常"
               5) 如果所有明细行的【行关闭状态】="自动关闭"时, 更新单据头的【关闭状态】="整单关闭"
               6) 如果所有明细行的【行关闭状态】="部份关闭"时, 更新单据头的【关闭状态】="部份关闭"
               7) 如果所有明细行的【行关闭状态】同时存"自动关闭"与"手工关闭" 且 不存在"部份关闭"时, 更新单据头的【关闭状态】="整单关闭"
               8) 如果所有明细行的【行关闭状态】不为上述情况时, 更新单据头的【关闭状态】="部份关闭"
            */
            if (Convert.ToString(dataEntity["fchangestatus"]) == "3")
            {
                var entrys = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var fbizqty = Convert.ToDecimal(entry["fbizqty"]);//采购数量
                    var fbizinstockqty = Convert.ToDecimal(entry["fbizinstockqty"]);//采购入库数量                    
                    var fbizreturnqty = Convert.ToDecimal(entry["fbizreturnqty"]);//采购退换数量
                    var orderQty = fbizqty - (fbizinstockqty + fbizreturnqty);
                    if (orderQty > 0 && orderQty - fbizreturnqty == fbizqty)
                    {
                        entry["fclosestatus_e"] = (int)CloseStatus.Default;
                    }
                    else if (orderQty > 0 && orderQty - fbizreturnqty != fbizqty)
                    {
                        entry["fclosestatus_e"] = (int)CloseStatus.Part;
                    }
                    else if (orderQty == 0)
                    {
                        entry["fclosestatus_e"] = (int)CloseStatus.Auto;
                    }
                }

                var totalNormalCount = entrys.Where(t => t["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace() || Convert.ToString(t["fclosestatus_e"]) == "0").Count();//正常状态总数
                var totalAutoCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Auto).ToString()).Count();//自动关闭状态总数
                var totalPartCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Part).ToString()).Count();//部分关闭状态总数
                var totalManuallyCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Manual).ToString()).Count();//手动关闭状态总数

                if (totalNormalCount == entrys.Count)
                {
                    dataEntity["fclosestatus"] = (int)CloseStatus.Default;
                }
                else if (totalAutoCloseCount == entrys.Count || ((totalPartCloseCount > 0 || totalManuallyCloseCount > 0) && totalPartCloseCount == 0))
                {
                    dataEntity["fclosestatus"] = (int)CloseStatus.Whole;
                }
                else
                {
                    dataEntity["fclosestatus"] = (int)CloseStatus.Part;
                }
            }
            if (dataEntity["fclosestatus"].IsNullOrEmptyOrWhiteSpace())
            {
                dataEntity["fclosestatus"] = (int)CloseStatus.Default;
            }
        }
    }

    public class CheckUnstdPriceDTO
    {
        public string id { get; set; }

        public List<CheckUnstdPriceEntry> fentity { get; set; }

        public class CheckUnstdPriceEntry
        {
            public string id { get; set; }

            public bool funstdtype { get; set; }

            public string funstdtypestatus { get; set; }

            public decimal fprice { get; set; }
        }
    }
}