using System;
using System.Linq;
using System.Collections.Generic;
using System.Net.Configuration;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：提交变更
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("SubmitChange")]
    public class SubmitChange : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            string msg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return SynergyFinish(newData, oldData, out msg);
            }).WithMessage("{0}", (billObj, propObj) => msg));

            //增加判断如果 【总部变更状态】="提交至总部"或"驳回"时, 点击<提交变更>要报错提示"总部变更状态等于"提交至总部"或"驳回", 不允许提交变更!"
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                msg = string.Empty;
                var entry = newData["fentity"] as DynamicObjectCollection;
                //判断明细 是否存在 【总部变更状态】="提交至总部"或"驳回"
                bool exist = entry.Any(o => Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("01"));
                if (exist)
                {
                    msg += $"总部变更状态等于'提交至总部', 不允许提交变更!";
                    return false;
                }
                else
                {
                    return true;
                }
            }).WithMessage("{0}", (billObj, propObj) => msg));

            //《采购订单》点击<提交变更>时, 增加判断如果 【总部合同状态】不为空 且 时, 点击<提交变更>要报错提示"当前订单为向总部采购的订单, 变更保存后请点击提交至总部!"
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    msg = string.Empty;
            //    if (!newData["fhqderstatus"].IsNullOrEmptyOrWhiteSpace())
            //    {
            //        msg += $"当前订单为向总部采购的订单, 变更保存后请点击提交至总部!";
            //        return false;
            //    }
            //    else
            //    {
            //        return true;
            //    }
            //}).WithMessage("{0}", (billObj, propObj) => msg));

            string errorMessage = "";
            //只允许自建商品在提交总部前通过【提交变更】进行变更
            //http://dmp.jienor.com:81/zentao/bug-view-29857.html
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    if (!Convert.ToString(newData["fhqderstatus"]).IsNullOrEmptyOrWhiteSpace())
            //    {
            //        return true;
            //    }
            //    errorMessage = "";
            //    var entry = newData["fentity"] as DynamicObjectCollection;
            //    return !CheckIsExistTopOrgAndOtherOrg(entry, out errorMessage);
            //}).WithMessage("{0}", (billObj, propObj) => errorMessage));


            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue(IsIntentOrderNotAudited).WithMessage("采购订单对应的K3《订购意向书》未审核，不允许变更！"));
        }

        //判断是否存在总部商品和非总部商品一起下单的情况
        private bool CheckIsExistTopOrgAndOtherOrg(DynamicObjectCollection entry, out string errorMsg)
        {
            UserContext userCtx = this.Context;
            errorMsg = "";
            var mainorgid = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;
            // var entry = newData["fentry"] as DynamicObjectCollection;
            //至少要有一行商品明细
            if (entry == null || entry.Count <= 0) return false;

            bool Havetop = false;
            bool HaveOthertop = false;
            foreach (DynamicObject item in entry)
            {
                var productId = Convert.ToString(item["fmaterialid"]);
                var productObj = userCtx.LoadBizDataById("ydj_product", productId);
                //商品明细中存在总部商品
                if (mainorgid.EqualsIgnoreCase(Convert.ToString(productObj["fmainorgid"])))
                {
                    Havetop = true;
                }
                else
                {
                    HaveOthertop = true;
                }
            }
            //存在总部商品
            if (Havetop)
            {
                errorMsg = "存在总部商品，请先提交总部后变更!";
                return true;
            }
            return false;
        }

        private bool SynergyFinish(DynamicObject newData, DynamicObject oldData, out string msg)
        {
            string bizstatus = newData["fbizstatus"].ToString();

            if (bizstatus == "business_status_11")
            {
                msg = "非协同完成，不允许提交变更";
                return false;
            }

            if (bizstatus == "business_status_09")
            {
                string changestatus = newData["fchangestatus"].ToString();

                if (changestatus != "1")
                {
                    msg = "协同完成时，变更状态不是变更中，不允许提交变更！";
                    return false;
                }
            }

            msg = string.Empty;
            return true;
        }

        ///// <summary>
        ///// K3《订购意向书》未审核
        ///// </summary>
        ///// <param name="newData"></param>
        ///// <param name="oldData"></param>
        ///// <returns></returns>
        //private bool IsIntentOrderNotAudited(DynamicObject newData, DynamicObject oldData)
        //{
        //    return true;
        //}


        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var changeReason = this.GetQueryOrSimpleParam<string>("changeReason");
            if (string.IsNullOrWhiteSpace(changeReason))
            {
                throw new BusinessException("变更申请原因不能为空！");
            }

            base.BeginOperationTransaction(e);

            foreach (var dataEntity in e.DataEntitys)
            {
                if (dataEntity["fbizstatus"].ToString() == "business_status_09")
                {
                    // 变更后同步数据至K3《订购意向书》
                    //SyncK3IntentOrderChg(dataEntity);
                    AddDynamic(dataEntity);
                }
            }
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            ModifyStatus(e.DataEntitys);
        }

        /// <summary>
        /// 修改变更单状态
        /// </summary>
        /// <param name="fnumber"></param>
        private void ModifyStatus(DynamicObject[] data)
        {
            List<string> sql = new List<string>();
            foreach (var item in data)
            {
                sql.Add($@"/*dialect*/update t_ydj_purchaseorder_chg set fstatus='D' where 
                fid in (select top 1 fid from t_ydj_purchaseorder_chg where 
                fsourcenumber='{Convert.ToString(item["fbillno"])}' and fsourcetype='ydj_purchaseorder' 
                and fstatus='B' order by fid desc) ");
            }

            if (sql != null && sql.Count > 0)
            {
                try
                {
                    var dbSvc = this.Container.GetService<IDBServiceEx>();
                    dbSvc.ExecuteBatch(this.Context, sql);
                }
                catch
                {

                }

            }
        }


        /// <summary>
        /// 同步
        /// </summary>
        /// <param name="dataEntity"></param>
        private void SyncK3IntentOrderChg(DynamicObject dataEntity)
        {
            ////交易流水号
            //var tranId = dataEntity["ftranid"] as string;
            //var billno = dataEntity["fbillno"] as string;
            //var changeReason = this.GetQueryOrSimpleParam<string>("changeReason");

            //供应商的协同企业ID
            var supplier = this.GetSupplierById(dataEntity);
            var cooCompanyId = supplier["fcoocompanyid"] as string;
            var cooProductId = supplier["fcooproductid"] as string;

            //数据发送时采用异步消息模式发送，消息中指定回调类型
            var responseResult = this.Gateway.Invoke(
                this.Context,
                new TargetSEP(cooCompanyId, cooProductId),
                new CommonBillDTO()
                {
                    FormId = "ydj_XIntendOrder",
                    OperationNo = "SendMessage",
                    BillData = BuildBillData(dataEntity),
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    //SimpleData = new Dictionary<string, string>
                    //{
                    //    { "tranid", tranId }, // 交易流水号
                    //    { "billno",billno  }, //订单编号
                    //    { "changeReason", changeReason } // 变更原因
                    //}
                }) as CommonBillDTOResponse;
            var invokeResult = responseResult?.OperationResult;
            if (invokeResult != null && !invokeResult.IsSuccess)
            {
                var errorMsgs = invokeResult.ComplexMessage.ErrorMessages;
                if (errorMsgs?.Count > 0)
                {
                    this.Result.ComplexMessage.ErrorMessages.AddRange(errorMsgs);
                    throw new BusinessException("变更同步数据失败！");
                }
            }
            invokeResult?.ThrowIfHasError(true, $"变更同步数据失败，对方系统未返回任何响应！");
        }

        /// <summary>
        /// 构建数据包
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private string BuildBillData(DynamicObject dataEntity)
        {
            //// 找到快照
            //var metaSnap = this.MetaModelService.LoadFormModel(this.Context, "bas_billchangesnapshot");
            //var dmSnap = this.GetDataManager();
            //dmSnap.InitDbContext(this.Context, metaSnap.GetDynamicObjectType(this.Context));

            //List<DynamicObject> snaps = dmSnap.SelectBy("fbillinterid='{0}' and fmainorgid='{1}' and fworkobject='{2}'".Fmt(
            //    dataEntity["Id"],
            //    this.Context.Company,
            //    this.OperationContext.HtmlForm?.Id)).OfType<DynamicObject>().ToList();

            //DynamicObject snap = snaps.OrderByDescending(f => f["fcreatedate"]).FirstOrDefault();

            //// 明细快照
            //var snapEntities = JObject.Parse(snap["fbillsnapshot"].ToString())?["fentity"] as JArray;

            List<Dictionary<string, object>> billDatas = new List<Dictionary<string, object>>();

            Dictionary<string, object> billData = new Dictionary<string, object>();
            billData["tranid"] = dataEntity["ftranid"];
            billData["billno"] = dataEntity["fbillno"];
            billData["changereason"] = this.GetQueryOrSimpleParam<string>("changeReason");
            billData["customerid"] = dataEntity["fcustomerid"];
            billData["address"] = dataEntity["faddress"];
            billData["phone"] = dataEntity["fphone"];
            billData["pickdate"] = dataEntity["fpickdate"];

            List<Dictionary<string, object>> entitieDatas = new List<Dictionary<string, object>>();
            var entities = dataEntity["fentity"] as DynamicObjectCollection;
            var products = GetDistinctBaseDataList(entities, "fmaterialid");

            foreach (DynamicObject entity in entities)
            {
                Dictionary<string, object> entityData = new Dictionary<string, object>();
                var product = products.FirstOrDefault(p => p["Id"].ToString() == entity["fmaterialid"].ToString());
                if (product == null)
                {
                    continue;
                }

                entityData["changetype"] = "修改";
                entityData["materialid"] = entity["fmaterialid"];
                entityData["mtrlname"] = product["fname"];
                entityData["mtrlnumber"] = product["fnumber"];
                entityData["attrinfo"] = product["fattrinfo"];
                entityData["forproductid"] = entity["fforproductid"];
                entityData["selectionnumber"] = entity["fselectionnumber"];
                entityData["forsuiteselectionid"] = entity["fforsuiteselectionid"];
                entityData["bizqty"] = entity["fbizqty"];
                entityData["bizunitid"] = entity["fbizunitid"];
                entityData["price"] = entity["fprice"];
                entityData["dealamount"] = entity["fdealamount"];
                entityData["distrate"] = entity["fdistrate"];
                entityData["distamount"] = entity["fdistamount"];

                entitieDatas.Add(entityData);
            }

            billData["entity"] = entitieDatas;

            billDatas.Add(billData);

            return billDatas.ToJson();
        }

        /// <summary>
        /// 获取去重后的基础资料集合
        /// </summary>
        /// <param name="entitys">单据明细数据包</param>
        /// <param name="fieldId">基础资料字段Id</param>
        /// <returns>基础资料集合</returns>
        private IEnumerable<DynamicObject> GetDistinctBaseDataList(DynamicObjectCollection entitys, string fieldId)
        {
            var field = this.HtmlForm.GetField(fieldId);
            if (!(field is HtmlBaseDataField)) throw new BusinessException($"{fieldId}不是基础资料字段，请检查！");
            var baseDataField = field as HtmlBaseDataField;
            var baseDataForm = baseDataField.RefHtmlForm(this.Context);

            //获取去重后的基础资料Id
            var baseDataIds = entitys
                .Select(t => baseDataField.DynamicProperty.GetValue<string>(t))
                .Where(t => !t.IsNullOrEmptyOrWhiteSpace())
                .Distinct()
                .ToList();

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, baseDataForm.GetDynamicObjectType(this.Context));
            var baseDatas = dm.Select(baseDataIds).OfType<DynamicObject>();

            return baseDatas;
        }

        /// <summary>
        /// 获取供应商信息
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private DynamicObject GetSupplierById(DynamicObject dataEntity)
        {
            string supplierId = Convert.ToString(dataEntity["fsupplierid"]);
            if (supplierId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"供应商ID为空！");

            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_supplier");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var supplier = dm.Select(supplierId) as DynamicObject;
            if (supplier == null) throw new BusinessException($"供应商不存在！");

            return supplier;
        }


        /// <summary>
        /// 添加动态
        /// </summary>
        private void AddDynamic(DynamicObject dataEntity)
        {
            var changeReason = this.GetQueryOrSimpleParam<string>("changeReason");

            this.Logger.WriteLog(this.Context, new LogEntry
            {
                BillIds = dataEntity["id"] as string,
                BillNos = dataEntity["fbillno"] as string,
                BillFormId = this.HtmlForm.Id,
                OpName = "协同完成时提交变更操作",
                OpCode = this.OperationNo,
                Content = "已提交变更至总部, 变更原因: {0}".Fmt(changeReason),
                DebugData = "已提交变更至总部, 变更原因: {0}".Fmt(changeReason),
                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                Level = Enu_LogLevel.Info.ToString(),
                LogType = Enu_LogType.RecordType_03
            });
        }
    }
}