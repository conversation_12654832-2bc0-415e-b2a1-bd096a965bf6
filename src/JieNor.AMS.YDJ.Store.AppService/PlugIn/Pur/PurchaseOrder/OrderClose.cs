using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：订单关闭
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("orderclose")]
    public class OrderClose : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                var entryDatas = (dataEntity["fentity"] as DynamicObjectCollection).ToList();

                // 采购数量为0时不允许关闭
                foreach (var entry in entryDatas)
                {
                    if (Convert.ToDecimal(entry["fbizqty"]) == 0)
                    {
                        this.Result.ComplexMessage.WarningMessages.Add($"第{entry["fseq"]}行商品明细的采购数量为0，不允许关闭。");
                    }
                }

                // 将不是【自动关闭】且【采购数量】>0 的明细行设置为【手动关闭】
                entryDatas
                    .FindAll(t => Convert.ToString(t["fclosestatus_e"]) != CloseStatusConst.Auto && Convert.ToDecimal(t["fbizqty"]) > 0)
                    .ForEach(t => t["fclosestatus_e"] = CloseStatusConst.Manual);

                // 最终走统一的关闭状态计算逻辑
                Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(dataEntity);
            }

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);

            this.AddRefreshPageAction();
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add("订单关闭成功！");
        }
    }

    /// <summary>
    /// 采购订单：订单反关闭
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("orderunclose")]
    public class OrderUnClose : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                var entryDatas = (dataEntity["fentity"] as DynamicObjectCollection).ToList();

                // 采购数量为0时不允许关闭
                foreach (var entry in entryDatas)
                {
                    if (Convert.ToDecimal(entry["fbizqty"]) == 0)
                    {
                        this.Result.ComplexMessage.WarningMessages.Add($"第{entry["fseq"]}行商品明细的采购数量为0，不允许反关闭。");
                    }
                }

                // 将【手动关闭】且【采购数量】>0 的明细行设置为【正常】
                entryDatas
                    .FindAll(t => Convert.ToString(t["fclosestatus_e"]) == CloseStatusConst.Manual && Convert.ToDecimal(t["fbizqty"]) > 0)
                    .ForEach(t => t["fclosestatus_e"] = CloseStatusConst.Default);

                // 最终走统一的关闭状态计算逻辑
                Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(dataEntity);
            }

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);

            this.AddRefreshPageAction();
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add("订单反关闭成功！");
        }
    }
}