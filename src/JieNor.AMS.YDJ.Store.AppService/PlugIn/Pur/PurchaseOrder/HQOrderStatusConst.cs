using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 总部合同状态常量
    /// </summary>
    public class HQOrderStatusConst
    {
        /// <summary>
        /// 新建
        /// </summary>
        public const string NewCreate = "01";

        /// <summary>
        /// 提交至总部
        /// </summary>
        public const string SubmitToHQ = "02";

        /// <summary>
        /// 已终审
        /// </summary>
        public const string FinalAudited = "03";

        /// <summary>
        /// 排产中(对应已出库)
        /// </summary>
        public const string Scheduling = "04";

        /// <summary>
        /// 驳回
        /// </summary>
        public const string Reject = "05";

        /// <summary>
        /// 总部合同状态键值对
        /// </summary>
        public static Dictionary<string, string> KeyValues = new Dictionary<string, string>
        {
            { NewCreate, "新建" },
            { SubmitToHQ, "提交至总部" },
            { FinalAudited, "已终审" },
            { Scheduling, "排产中" },
            { Reject, "驳回" }
        };
    }
    /// <summary>
    /// 一级合同状态常量
    /// </summary>
    public class OneLvOrderSratusConst
    {

        /// <summary>
        /// 提交至一级
        /// </summary>
        public const string SubmitToHQ = "01";

        /// <summary>
        /// 订单已审核
        /// </summary>
        public const string FinalAudited = "02";

        /// <summary>
        /// 订单已驳回
        /// </summary>
        public const string Reject = "03";

        /// <summary>
        /// 订单已出库
        /// </summary>
        public const string OutStock = "04";
        /// <summary>
        /// 一级合同状态键值对
        /// </summary>
        public static Dictionary<string, string> KeyValues = new Dictionary<string, string>
        {
            { SubmitToHQ, "提交至一级" },
            { FinalAudited, "订单已审核" },
            { Reject, "订单已驳回" },
            { OutStock, "订单已出库" }
        };
    }
}
