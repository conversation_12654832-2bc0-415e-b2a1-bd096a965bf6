using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Service;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Pur;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单提交校验器
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class SubmitValidation : AbstractBaseValidation
    {
        private Enu_DomainType domainType;

        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, formInfo.GetDynamicObjectType(userCtx), dataEntities, false);

            //判断 商品【选配套件】=“是”且没有子件商品 提交时给校验
            CheckSubmitSuitProduct(userCtx, dataEntities, result);

            return result;
        }

        /// <summary>
        /// 判断 商品【选配套件】=“是”且没有子件商品 提交时给校验
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="formInfo"></param>
        /// <param name="result"></param>
        /// <param name="noOrders"></param>
        private void CheckSubmitSuitProduct(UserContext userCtx, DynamicObject[] dataEntities, ValidationResult result)
        {
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return;
            }
            // 所有的商品ID
            var productIds = dataEntities?.SelectMany(o =>
            {
                var entrys = o["fentity"] as DynamicObjectCollection;
                var _productIds = entrys
                .Select(entry => Convert.ToString(entry["fmaterialid"]))
                .Where(productId => !productId.IsNullOrEmptyOrWhiteSpace());
                return _productIds;
            })
            ?.Distinct()
            ?.ToList();
            // 批量加载商品信息
            DynamicObjectCollection productObjs = null;
            if (productIds != null && productIds.Any())
            {
                productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fnumber,fname,fsuiteflag,fmainorgid");
            }
            foreach (var dataEntity in dataEntities) 
            {
                if (productObjs == null || !productObjs.Any()) return;

                var entrys = dataEntity["fentity"] as DynamicObjectCollection;

                var orderService = this.Context.Container.GetService<IOrderService>();
                //套件组合号
                var suitcombnumberLst = entrys
                    .Where(f => Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1"))
                    .Select(o => Convert.ToString(o["fsuitcombnumber"])).Distinct().ToList<string>();

                foreach (var suitcombnumber in suitcombnumberLst)
                {

                    //是否存在提交时 只有套件头 没子件的情况
                    var Isexist = entrys.Any(f => suitcombnumber == Convert.ToString(f["fsuitcombnumber"]) &&
                    Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("0")
                    );
                    if (!Isexist)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"对不起，当前订单存在套件商品未选择子件，禁止提交总部！",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        } 
    }
}