using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.PropSelectionSingle
{
    /// <summary>
    /// 加载与当前属性相关的其他属性信息
    /// </summary>
    [InjectService]
    [FormId("sel_propselectionsingle")]
    [OperationNo("LoadRelationProp")]
    public class LoadRelationProp : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var selCategoryId = this.GetQueryOrSimpleParam<string>("selCategoryId", "");
            var propNumber = this.GetQueryOrSimpleParam<string>("propNumber", "");
            if (selCategoryId.IsNullOrEmptyOrWhiteSpace()
                || propNumber.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException("参数值 selCategoryId 或 propNumber 为空，请检查！");
            }

            //加载选配约束条件
            var propSelService = this.Container.GetService<IPropSelectionService>();
            var constraints = propSelService.LoadSelConstraint(this.Context, selCategoryId);
            if (constraints == null || !constraints.Any()) return;

            var formulaService = this.Container.GetService<IFormulaService>();
            var relationPropNumbers = new List<string>();

            //对约束条件和约束值公式进行解析
            foreach (var dynObj in constraints)
            {
                var conditionFormula = Convert.ToString(dynObj?["fconstraintcondition"]);
                var valueFormula = Convert.ToString(dynObj?["fconstraintval"]);
                if (conditionFormula.IsNullOrEmptyOrWhiteSpace()
                    || valueFormula.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                
                var propNumberKv1 = formulaService.ParseMusiSapFormulaPropNumbers(conditionFormula);
                var propNumberKv2 = formulaService.ParseMusiSapFormulaPropNumbers(valueFormula);

                //找出【约束条件】公式中和【约束值】公式中与当前属性相关的其他属性编码
                if (propNumberKv1.ContainsKey(propNumber) || propNumberKv2.ContainsKey(propNumber))
                {
                    relationPropNumbers.AddRange(propNumberKv1.Keys);
                    relationPropNumbers.AddRange(propNumberKv2.Keys);
                }
            }

            relationPropNumbers = relationPropNumbers.Distinct().ToList();

            this.Result.SrvData = new { relationPropNumbers = relationPropNumbers };
            this.Result.IsSuccess = true;
        }
    }
}