using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Reward
{
    /// <summary>
    /// 奖惩处理单保存
    /// </summary>
    [InjectService]
    [FormId("ser_reward")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["frewardpunish"]).NotEmpty().WithMessage("奖惩类型不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fteamid"]).NotEmpty().WithMessage("所属团队不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fdutyid"]).NotEmpty().WithMessage("责任人不能为空！"));
          
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string punishType = Convert.ToString(newData["frewardpunish"]);
                if (Convert.ToString(newData["frewardtype"]).IsNullOrEmptyOrWhiteSpace() && (punishType.EqualsIgnoreCase("reward01") || punishType.EqualsIgnoreCase("reward03")))
                    return false;
                return true;
            }).WithMessage("奖励方式不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string punishType = Convert.ToString(newData["frewardpunish"]);
                if (Convert.ToString(newData["fpunishtype"]).IsNullOrEmptyOrWhiteSpace() && punishType.EqualsIgnoreCase("reward02"))
                    return false;
                return true;
            }).WithMessage("处罚方式不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string punishType = Convert.ToString(newData["frewardpunish"]);
                string rewardMoney = Convert.ToString(newData["frewardmoney"]);
                decimal reMoney = 0.0M;
                if (punishType.EqualsIgnoreCase("reward01") || punishType.EqualsIgnoreCase("reward03"))
                {
                    if (!rewardMoney.IsNullOrEmptyOrWhiteSpace() && (!decimal.TryParse(rewardMoney, out reMoney) || (reMoney < 0.01M || reMoney > 999999999)))
                        return false;
                }
                return true;
            }).WithMessage("并奖励现金填写不合理！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string punishType = Convert.ToString(newData["fpunishtype"]);
                string punishMoney = Convert.ToString(newData["fpunishmoney"]);
                decimal punMoney = 0.0M;
                if (Convert.ToString(newData["frewardpunish"]).EqualsIgnoreCase("reward02"))
                {
                    if (punishMoney.IsNullOrEmptyOrWhiteSpace() && (punishType.EqualsIgnoreCase("punish_type03") || punishType.EqualsIgnoreCase("punish_type04")))
                        return false;
                    if (!punishMoney.IsNullOrEmptyOrWhiteSpace() && (!decimal.TryParse(punishMoney, out punMoney) || (punMoney < 0.01M || punMoney > 999999999)))
                        return false;
                }
                return true;
            }).WithMessage("并处罚现金填写不合理！"));

        }
    }
}
