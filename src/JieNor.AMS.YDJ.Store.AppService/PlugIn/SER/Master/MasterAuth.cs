using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Master
{
    /// <summary>
    /// 师傅认证
    /// </summary>
    [InjectService]
    [FormId("ydj_master")]
    [OperationNo("auth")]
    public class MasterAuth : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            //姓名、出生年月、性别、身份证号、身份证(正)、身份证(反)、身份证(手持)
            base.PrepareValidationRules(e);

            string auditStatus = this.GetQueryOrSimpleParam<string>("setstatus");
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (auditStatus.EqualsIgnoreCase("auth4") || auditStatus.EqualsIgnoreCase("auth1")) return true;
                return !n["fname"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("姓名不能为空"));
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (auditStatus.EqualsIgnoreCase("auth4") || auditStatus.EqualsIgnoreCase("auth1")) return true;
                return !n["fbirth"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("出生年月不能为空"));
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (auditStatus.EqualsIgnoreCase("auth4") || auditStatus.EqualsIgnoreCase("auth1")) return true;
                return !n["fsex"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("性别不能为空"));
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (auditStatus.EqualsIgnoreCase("auth4") || auditStatus.EqualsIgnoreCase("auth1")) return true;
                if (n["fidcard"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                var idcard = Convert.ToString(n["fidcard"]).Trim();
                return idcard.Length == 18 ? CheckIDCard18(idcard) : CheckIDCard15(idcard);

            }).WithMessage("身份证格式错误,不符合GB11643-1999标准"));
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (auditStatus.EqualsIgnoreCase("auth4") || auditStatus.EqualsIgnoreCase("auth1")) return true;
                return !n["fidcar1"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("身份证(正)不能为空"));
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (auditStatus.EqualsIgnoreCase("auth4") || auditStatus.EqualsIgnoreCase("auth1")) return true;
                return !n["fidcar2"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("身份证(反)不能为空"));
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (auditStatus.EqualsIgnoreCase("auth4") || auditStatus.EqualsIgnoreCase("auth1")) return true;
                return !n["fidcar3"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("身份证(手持)不能为空"));
        }
        private bool CheckIDCard18(string idNumber)
        {
            long n = 0;
            if (long.TryParse(idNumber.Remove(17), out n) == false
                || n < Math.Pow(10, 16) || long.TryParse(idNumber.Replace('x', '0').Replace('X', '0'), out n) == false)
            {
                return false;//数字验证  
            }
            string address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
            if (address.IndexOf(idNumber.Remove(2)) == -1)
            {
                return false;//省份验证  
            }
            string birth = idNumber.Substring(6, 8).Insert(6, "-").Insert(4, "-");
            DateTime time = new DateTime();
            if (DateTime.TryParse(birth, out time) == false)
            {
                return false;//生日验证  
            }
            string[] arrVarifyCode = ("1,0,x,9,8,7,6,5,4,3,2").Split(',');
            string[] Wi = ("7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2").Split(',');
            char[] Ai = idNumber.Remove(17).ToCharArray();
            int sum = 0;
            for (int i = 0; i < 17; i++)
            {
                sum += int.Parse(Wi[i]) * int.Parse(Ai[i].ToString());
            }
            int y = -1;
            Math.DivRem(sum, 11, out y);
            if (arrVarifyCode[y] != idNumber.Substring(17, 1).ToLower())
            {
                return false;//校验码验证  
            }
            return true;//符合GB11643-1999标准  
        }
        /// <summary>  
        /// 15位身份证号码验证  
        /// </summary>  
        private bool CheckIDCard15(string idNumber)
        {
            long n = 0;
            if (long.TryParse(idNumber, out n) == false || n < Math.Pow(10, 14))
            {
                return false;//数字验证  
            }
            string address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
            if (address.IndexOf(idNumber.Remove(2)) == -1)
            {
                return false;//省份验证  
            }
            string birth = idNumber.Substring(6, 6).Insert(4, "-").Insert(2, "-");
            DateTime time = new DateTime();
            if (DateTime.TryParse(birth, out time) == false)
            {
                return false;//生日验证  
            }
            return true;
        }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0)
            {
                return;
            }
            DynamicObject getData = e.DataEntitys[0];
            string status = this.GetQueryOrSimpleParam<string>("setstatus");
            string ystatus = Convert.ToString(getData["fapprovestatus"]);
            switch (status)
            {
                case "auth1"://未认证
                    if (ystatus != "auth2")
                    {
                        throw new BusinessException("该操作要求状态必须是【已认证】！");
                    }
                    break;
                case "auth2"://已认证
                    if (ystatus != "auth3")
                    {
                        throw new BusinessException("该操作要求状态必须是【等待审核】！");
                    }
                    break;
                case "auth3"://等待审核
                    if (ystatus != "auth4" && ystatus != "auth1")
                    {
                        throw new BusinessException("该操作要求状态必须是【未认证】或【认证不通过】！");
                    }

                    //如果是提交认证，则自动更新本地用户名
                    this.AutoUpdateLocalUserName(getData);
                    break;
                case "auth4"://认证不通过
                    if (ystatus != "auth3")
                    {
                        throw new BusinessException("该操作要求状态必须是【等待审核】！");
                    }
                    break;
                default:
                    break;
            }
            getData["fapprovestatus"] = status;
            if (status == "auth2")
            {
                //认证成功发送提醒消息
                try
                {
                    var installMessageService = this.Container.GetService<IInstallServiceMessageProxy>();
                    installMessageService.InitHubContext(this.Context, this);
                    installMessageService.SendAuthSucessMessage(this.Context, getData["Id"].ToString());
                }
                catch (Exception ex) { }
            }
            if (status == "auth4")
            {
                //认证失败发送提醒消息
                try
                {
                    var installMessageService = this.Container.GetService<IInstallServiceMessageProxy>();
                    installMessageService.InitHubContext(this.Context, this);
                    installMessageService.SendAuthErrorMessage(this.Context, getData["Id"].ToString());

                }
                catch (Exception ex) { }
            }
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_master");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            dm.Save(getData);
        }

        /// <summary>
        /// 本地用户名自动同步更新
        /// </summary>
        /// <param name="userName"></param>
        private void AutoUpdateLocalUserName(DynamicObject masterObj)
        {
            if (masterObj.IsNullOrEmpty()) return;
            var phone = masterObj["fphone"] as string;
            var userName = masterObj["fname"] as string;

            var userMeta = this.MetaModelService.LoadFormModel(this.Context, "sec_user");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, userMeta.GetDynamicObjectType(this.Context));
            var localUserObj = dm.SelectBy($"fnumber='{phone}' and fmainorgid='{this.Context.Company}'")
                .OfType<DynamicObject>()
                .FirstOrDefault();
            if (localUserObj != null)
            {
                localUserObj["fname"] = userName;
                dm.Save(localUserObj);
            }
        }
    }
}