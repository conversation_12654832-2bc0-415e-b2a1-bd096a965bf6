//using JieNor.Framework;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Service
//{
//    [InjectService]
//    [FormId("ydj_service")]
//    public class Writeback : AbstractWritebackServicePlugIn
//    {
//        /// <summary>
//        /// 反写后，完成商户订单的金额与总金额计算
//        /// </summary>
//        /// <param name="e"></param>
//        public override void AfterWriteback(AfterWritebackEventArgs e)
//        {
//            //反写完成后处理商户订单的金额与总金额
//            if (this.WritebackRule.WritebackFieldKey.EqualsIgnoreCase("fqty"))
//            {
//                List<string> lstSqlItems = new List<string>();
//                StringBuilder sbWritebackSql = new StringBuilder();
//                sbWritebackSql.Append($@"
//update t_ydj_serviceproduct as u1 set (famount) = (
//    select u2.fqty*u2.fprice
//    from {e.TempTableName} tmp
//    inner join t_ydj_serviceproduct u2 on tmp.fsourcelinkid=u2.fentryid
//    where 1=1 and tmp.fsourcelinkid=u1.fentryid
//)
//");
//                lstSqlItems.Add(sbWritebackSql.ToString());

//                sbWritebackSql.Clear();
//                sbWritebackSql.Append($@"
//update t_ydj_service as u1 set (fexpectamount) = (
//    select ftotalamount
//    from (
//    select u3.fid,sum(u3.famount) ftotalamount
//    from ( select distinct u2.fid from t_ydj_serviceproduct u2
//            where exists(select 1 from {e.TempTableName} where fsourcelinkid = u2.fentryid)
//    ) u4
//    inner join t_ydj_serviceproduct u3 on u4.fid=u3.fid    
//    group by u3.fid
//    ) m1
//    where 1=1 and m1.fid=u1.fid
//)
//");
//                lstSqlItems.Add(sbWritebackSql.ToString());
//                this.DBServiceEx.ExecuteBatch(this.Context, lstSqlItems);
//            }
//        }
//    }
//}
