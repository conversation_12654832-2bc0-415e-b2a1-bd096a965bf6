using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Collections.Generic;

using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Service
{
    /// <summary>
    /// 实现服务状态变化时获取发送短信信息的逻辑
    /// </summary>
    [InjectService]
    [FormId("ydj_service")]
    [OperationNo("setstatus07")]
    public class SendSms : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "onloadsmsphone":
                    this.OnLoadSmsPhone(e);
                    break;
                case "onloadsmsparam":
                    this.OnLoadSmsParam(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 加载短信手机号
        /// </summary>
        /// <param name="e"></param>
        private void OnLoadSmsPhone(OnCustomServiceEventArgs e)
        {
            ////示例代码：
            //var eventData = e.EventData as Tuple<string, string, DynamicObject>;
            //if (eventData == null) return;
            //var formModel = this.MetaModelService.LoadFormModel(this.Context, eventData.Item1);
            //if (formModel == null) return;
            //var bizField = this.HtmlForm.GetField(eventData.Item2);
            //if (bizField == null) return;
            ////手机号字段
            //switch (eventData.Item2.ToLower())
            //{
            //    case "fmasterid":
            //        //获取手机号的逻辑代码......
            //        e.Result = "15817373967";
            //        e.Cancel = true;
            //        break;
            //    default:
            //        break;
            //}
        }

        /// <summary>
        /// 加载短信模板中的占位符参数
        /// </summary>
        /// <param name="e"></param>
        private void OnLoadSmsParam(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, string, DynamicObject, List<string>>;
            if (eventData == null) return;
            //操作ID
            switch (eventData.Item2.ToLower())
            {
                //预约确认
                case "setstatus07":
                    var msgParmas = new Dictionary<string, string>();
                    foreach (var fieldId in eventData.Item4)
                    {
                        switch (fieldId.ToLower())
                        {
                            case "fserdate":
                                //获取预约时间
                                string serDate = "";
                                DynamicObjectCollection entrys = eventData.Item3["fhistoryentry"] as DynamicObjectCollection;
                                if (entrys != null)
                                {
                                    DynamicObject entry = entrys.Where(t => Convert.ToBoolean(t["fissuccess"])).FirstOrDefault();
                                    if (entry != null && !entry["fappointdate"].IsNullOrEmptyOrWhiteSpace())
                                    {
                                        serDate = Convert.ToDateTime(entry["fappointdate"]).ToString("MM月dd日 HH:mm");
                                    }
                                }
                                msgParmas.Add("fserdate", serDate);
                                break;
                            case "fmstphone":
                                //获取师傅手机号码
                                string mstPhone = this.GetMasterPhone(eventData.Item3["fmasterid"] as string);
                                msgParmas.Add("fmstphone", mstPhone);
                                break;
                            case "fderbrand":
                                //根据商户ID获取商户经营品牌
                                string derBrand = this.GetDealerBrand(eventData.Item3["fdealerid"] as string);
                                msgParmas.Add("fderbrand", derBrand);
                                break;
                            default:
                                break;
                        }
                    }
                    e.Cancel = true;
                    e.Result = msgParmas;
                    break;

                default:
                    break;
            }
        }

        /// <summary>
        /// 根据商户ID获取商品的经营品牌
        /// </summary>
        /// <param name="dealerId">商户ID</param>
        /// <returns>经营品牌</returns>
        private string GetDealerBrand(string dealerId)
        {
            if (!dealerId.IsNullOrEmptyOrWhiteSpace())
            {
                var meta = this.MetaModelService.LoadFormModel(this.Context, "ydj_dealer");
                var dm = this.Context.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, meta.GetDynamicObjectType(this.Context));
                var obj = dm.Select(dealerId) as DynamicObject;
                if (obj != null)
                {
                    return obj["foperatebrand"] as string;
                }
            }
            return string.Empty;
        }

        /// <summary>
        /// 获取师傅手机号码
        /// </summary>
        /// <param name="masterId">师傅ID</param>
        /// <returns>手机号码</returns>
        private string GetMasterPhone(string masterId)
        {
            if (!masterId.IsNullOrEmptyOrWhiteSpace())
            {
                var masterMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_master");
                var dm = this.Context.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, masterMeta.GetDynamicObjectType(this.Context));
                var master = dm.Select(masterId) as DynamicObject;
                if (master != null)
                {
                    return master["fphone"] as string;
                }
            }
            return string.Empty;
        }
    }
}