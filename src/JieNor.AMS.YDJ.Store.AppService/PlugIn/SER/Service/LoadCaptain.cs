using System;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Service
{
    /// <summary>
    /// 服务单：根据团队ID获取队长信息
    /// </summary>
    [InjectService]
    [FormId("ydj_service")]
    [OperationNo("getcaptain")]
    public class LoadCaptain : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            //根据团队ID取到团队成员
            string teamId = this.GetQueryOrSimpleParam<string>("teamId");
            if (!teamId.IsNullOrEmptyOrWhiteSpace())
            {
                var teamMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_team");
                var teamDm = this.Context.Container.GetService<IDataManager>();
                teamDm.InitDbContext(this.Context, teamMeta.GetDynamicObjectType(this.Context));
                DynamicObject teamData = teamDm.Select(teamId) as DynamicObject;
                if (teamData != null)
                {
                    DynamicObjectCollection entrys = teamData["fstaffentry"] as DynamicObjectCollection;
                    if (entrys != null)
                    {
                        //获取队长名称
                        DynamicObject entry = entrys.Where(t => Convert.ToBoolean(t["fiscaptain"])).FirstOrDefault();
                        if (entry != null)
                        {
                            string masterId = entry["fstaffid"] as string;
                            var masterMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_master");
                            var masterDm = this.Context.Container.GetService<IDataManager>();
                            masterDm.InitDbContext(this.Context, masterMeta.GetDynamicObjectType(this.Context));
                            DynamicObject masterData = masterDm.Select(masterId) as DynamicObject;
                            if (masterData != null)
                            {
                                this.Result.SrvData = new
                                {
                                    id = masterId,
                                    fnumber = masterData["fnumber"] as string,
                                    fname = masterData["fname"] as string,
                                };
                            }
                        }
                    }
                }
            }
        }
    }
}