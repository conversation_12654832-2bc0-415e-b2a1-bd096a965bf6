using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using System.Text.RegularExpressions;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Team
{
    /// <summary>
    /// 根据师傅id获取该团队信息
    /// </summary>
    [InjectService]
    [FormId("ydj_team")]
    [OperationNo("getdataByTid")]
    public class GetTeamDetail : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            string Id = this.GetQueryOrSimpleParam<string>("Id");


            if (Id.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_team");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            DynamicObject dyObj = dm.Select(Id) as DynamicObject;
            if (dyObj != null)
            {
                this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, formMeta.GetDynamicObjectType(this.Context), dyObj, false);
                IUiDataConverter u = this.Container.GetService<IUiDataConverter>();
                this.Result.SrvData = u.CreateUIDataObject(this.Context, formMeta, dyObj);
            }

        }
    }
}
