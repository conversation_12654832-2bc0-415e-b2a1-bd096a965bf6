using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
	/// 运维问题：数据提供
	/// </summary>
	[InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("dataprovide")]
    public class DataProvide : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var isDataRepair = Convert.ToString(newData["fisdatarepair"]);
                if ("false".Equals(isDataRepair) || "False".Equals(isDataRepair) || "0".Equals(isDataRepair))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起,数据提供操作失败！只有勾选是否修复数据，才能操作！"));
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            foreach (var item in e.DataEntitys)
            {
                item["fdataprovidedate"] = currentTime;
            }

            var res = this.Gateway.InvokeBillOperation(this.Context, "ydj_maintenance", e.DataEntitys, "save", new Dictionary<string, object>() { });

            res.ThrowIfHasError();

            //刷新页面
            this.AddRefreshPageAction();
        }
    }
}
