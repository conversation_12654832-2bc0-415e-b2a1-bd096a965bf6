using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.AMS.YDJ.Core.Interface.StockPick;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Senparc.Weixin.Helpers.Extensions;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 销售合同下推销售出库单
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("ydj_order2stk_sostockout")]
    public class SalOrder2OutStockConvertPlugIn : AbstractConvertServicePlugIn
    {
        /// <summary>
        /// 获取源单数据查询对象接口，允许添加额外需要获取的源单数据字段或提供额外过滤条件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareQueryBuilderParameter(OnPrepareQueryBuilderParameterEventArgs e)
        {
            if (e.ActiveQueryEntity == null) return;
            switch (e.ActiveQueryEntity.Id.ToLower())
            {
                case "fbillhead":

                    break;
            }
        }

        /// <summary>
        /// 单据转换执行完成后生成关联数据行接口
        /// </summary>
        /// <param name="e"></param>
        public override void OnCreateLinkData(OnCreateLinkDataEventArgs e)
        {
            if (e.LinkEntryData == null) return;
            if (e.LinkRelation == null) return;

        }

        public override void BeforeGetSourceBillData(BeforeGetSourceBillDataEventArgs e)
        {
            base.BeforeGetSourceBillData(e);
        }

        /// <summary>
        /// 单据转换执行完成后接口（目标单数据包将会生成）
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            StockPickSetting setting = new StockPickSetting
            {
                ActiveEntityKey = "fentity",
                QtyFieldKey = "fqty",
                PlanQtyFieldKey = "fplanqty",
                StockQtyFieldKey = "fstockqty",
                PriceFieldKey = "",
                AmountFieldKey = ""
            };

            var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
            var targetForm = metaModelService.LoadFormModel(this.UserContext, e.TargetFormId);

            //过滤套件头商品
            RemoveKitHeadProduct(e.TargetDataEntities);

            SetStoreHouseInfoByDept(e.TargetDataEntities);
            //更新出货时间
            ModifyFdata(e.TargetDataEntities);

            var stockPickService = this.UserContext.Container.GetService<IStockPickService>();
            var result = stockPickService.Picking(this.UserContext, setting, targetForm, e.TargetDataEntities, this.Option);
            result.ThrowIfHasError(true, "库存拣货出现意外错误！");

            FilterNotOverSalQty(e.TargetDataEntities);
            DealTailDifference(e.TargetDataEntities);
            var inventoryService = this.UserContext.Container.GetService<IInventoryService>();
            inventoryService.FillReserveStockInfos(this.UserContext, this.TargetHtmlForm, e.TargetDataEntities);
            RevertSaleOrderStoreLocation(e.TargetDataEntities);
            inventoryService.FillFIFOStockInfos(this.UserContext, this.TargetHtmlForm, e.TargetDataEntities, false);

            if (e.ConvertMode == Enu_BillConvertMode.Default)
            {
                //仓库推荐完成后，按照商品明细仓库拆单
                List<DynamicObject> saveTargetDatas = new List<DynamicObject>();
                foreach (var targetData in e.TargetDataEntities)
                {
                    var currEntrys = targetData["fentity"] as DynamicObjectCollection;
                    var storeGroup = currEntrys.GroupBy(t => Convert.ToString(t["fstorehouseid"]).Trim());
                    if (storeGroup.Count() > 1)
                    {
                        var nullData = targetData.Clone() as DynamicObject;
                        foreach (var group in storeGroup)
                        {
                            var newData = targetData.Clone() as DynamicObject;
                            var newEntrys = newData["fentity"] as DynamicObjectCollection;
                            newEntrys.Clear();
                            foreach (var ent in group)
                            {
                                newEntrys.Add(ent);
                            }

                            if (group.Key.IsNullOrEmptyOrWhiteSpace())
                            {
                                nullData = newData;
                                continue;
                            }
                            saveTargetDatas.Add(newData);
                        }

                        if (storeGroup.Where(x => x.Key.IsNullOrEmptyOrWhiteSpace()).Any())
                        {
                            saveTargetDatas.Add(nullData);
                        }
                    }
                    else
                    {
                        saveTargetDatas.Add(targetData);
                    }
                }
                e.SetTargetDataEntities(saveTargetDatas);
            }
            foreach (var targetData in e.TargetDataEntities)
            {
                var entry = targetData["fentity"] as DynamicObjectCollection;
                var hasPartialSign = entry.Any(a => Convert.ToString(a["fdeliverytype"]) == "delivery_type_01");
                //一件代发，并且存在总部直发签收的商品时，自动填充仓库、部门、员工信息
                //经销商模式
                if (Convert.ToBoolean(targetData["fpiecesendtag"]) && hasPartialSign && Convert.ToString(targetData["fmanagemodel"]).Equals("0"))
                {
                    var topContext = this.UserContext.CreateTopOrgDBContext();
                    var defaultStaff = topContext.LoadBizDataByNo("ydj_staff", "fnumber", new List<string>() { "YJDFXNYG" });
                    var defaultDept = topContext.LoadBizDataByNo("ydj_dept", "fnumber", new List<string>() { "YJDFXNBM" });
                    var defaultStore = this.UserContext.LoadBizDataByNo("ydj_storehouse", "fnumber", new List<string>() { "YJDFZBCK" });
                    if (defaultStaff.Any() && defaultDept.Any() && defaultStore.Any())
                    {
                        targetData["fisautopush"] = "1";
                        targetData["fstockstaffid"] = defaultStaff.FirstOrDefault()["id"];
                        targetData["fstockdeptid"] = defaultDept.FirstOrDefault()["id"];
                        entry.ForEach(a => a["fstorehouseid"] = defaultStore.FirstOrDefault()["id"]);
                    }
                }
                //if (Convert.ToString(targetData["fmanagemodel"])=="1")
                //{
                //    var defaultStore = this.UserContext.LoadBizDataByNo("ydj_storehouse", "fnumber", new List<string>() { "ZFCK" });
                //    var defaultStore = this.UserContext.LoadBizDataByNo("ydj_storehouse", "fnumber", new List<string>() { "ZFCK" });
                //    if ( defaultStore.Any())
                //    {
                //        targetData["fisautopush"] = "1";
                //        targetData["fstockstaffid"] = "";
                //        targetData["fstockdeptid"] = "";
                //        var entry = targetData["fentity"] as DynamicObjectCollection;
                //        entry.ForEach(a => a["fstorehouseid"] = defaultStore.FirstOrDefault()["id"]);
                //    }
                //}
            }
            RevertZYInfo(e.TargetDataEntities);
        }

        public override void BeforeMapFieldValue(BeforeMapFieldValueEventArgs e)
        {
            base.BeforeMapFieldValue(e);

            if (e.SourceDataEntities.Any() == false)
            {
                return;
            }

            var targetFldKey = e.FieldMapObject?.Id;
            var targetField = this.TargetHtmlForm?.GetField(targetFldKey);
            if (targetField == null)
            {
                return;
            }

            var sourceDataEntity = e.SourceDataEntities.First();
            if (sourceDataEntity == null)
            {
                return;
            }

            bool isCancel = false;
            object targetValue = null;
            switch (targetField.Id.ToLower())
            {
                case "fhistorystaffer":
                    {
                        var currStaffer = sourceDataEntity.GetValue("fstaffid_fname");
                        var sourceNumber = sourceDataEntity.GetValue("fbillno");
                        if (!sourceNumber.IsNullOrEmptyOrWhiteSpace())
                        {
                            var historyStaffer = GetHistorySalesperson(sourceNumber.ToString(), currStaffer.ToString());
                            if (!historyStaffer.IsNullOrEmptyOrWhiteSpace())
                            {
                                currStaffer = historyStaffer;
                            }
                        }
                        isCancel = true;
                        targetValue = currStaffer;
                    }
                    break;
                case "fconsignee":
                    {
                        isCancel = true;
                        targetValue = "";
                        var customercontactid = sourceDataEntity.GetValue("fcustomercontactid");
                        if (!customercontactid.IsNullOrEmptyOrWhiteSpace())
                        {
                            var sqlText = $@"select fcuscontacttryid,fcontacter from t_ydj_fcuscontacttry with(nolock) 
                                        where fcuscontacttryid='{customercontactid}'";
                            var dbService = this.UserContext.Container.GetService<IDBService>();
                            var dynObj = dbService.ExecuteDynamicObject(this.UserContext, sqlText)?.FirstOrDefault();
                            if (dynObj != null) targetValue = dynObj["fcontacter"];
                        }
                    }
                    break;
            }
            if (isCancel)
            {
                e.Cancel = true;
                targetField.DynamicProperty.SetValue(e.TargetEntryDataEntity, targetValue);
            }
        }

        /// <summary>
        /// 过滤出库数量不超过销售数量
        /// </summary>
        private void FilterNotOverSalQty(IEnumerable<DynamicObject> dataEntities)
        {
            var sourceEntryIds = dataEntities.SelectMany(s => s["fentity"] as DynamicObjectCollection)
                .Select(s => Convert.ToString(s["fsourceentryid"])).Where(s => !s.IsNullOrEmptyOrWhiteSpace()).ToList();

            if (!sourceEntryIds.Any())
            {
                return;
            }

            var dbService = this.UserContext.Container.GetService<IDBService>();

            string strSql = $@"
select se.fentryid, se.fqty, se.fsourceentryid
FROM t_stk_sostockoutentry se with(nolock)
INNER JOIN t_stk_sostockout s with(nolock) ON s.fid = se.fid 
WHERE (s.fcancelstatus = 0 AND se.fsourceentryid IN ({sourceEntryIds.JoinEx(",", true)}))
 ";

            var allOutEntrys = dbService.ExecuteDynamicObject(this.UserContext, strSql);

            strSql = $@"
select oe.fentryid, oe.fproductid, m.fname as fproductname, oe.fseq, oe.fqty, oe.freturnqty
FROM t_ydj_orderentry oe with(nolock) 
inner join t_bd_material m with(nolock) on oe.fproductid = m.fid
WHERE oe.fentryid IN ({sourceEntryIds.JoinEx(",", true)})
 ";

            var orderEntrys = dbService.ExecuteDynamicObject(this.UserContext, strSql);


            List<string> errMsgs = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                var entries = dataEntity["fentity"] as DynamicObjectCollection;
                List<DynamicObject> beRemove = new List<DynamicObject>();
                foreach (var entry in entries)
                {
                    var fsourceentryid = Convert.ToString(entry["fsourceentryid"]);
                    if (fsourceentryid.IsNullOrEmptyOrWhiteSpace())
                    {
                        beRemove.Add(entry);
                        continue;
                    }

                    var orderEntry = orderEntrys.FirstOrDefault(s =>
                        Convert.ToString(s["fentryid"]).EqualsIgnoreCase(fsourceentryid));
                    if (orderEntry == null)
                    {
                        continue;
                    }

                    // 关联销售合同的基本单位销售数量
                    var salQty = Convert.ToDecimal(orderEntry["fqty"]);
                    // 关联销售合同的基本单位退换数量
                    var returnQty = Convert.ToDecimal(orderEntry["freturnqty"]);
                    // 可下推数量
                    var canOutQty = salQty + returnQty;

                    var matchOutEntrys = allOutEntrys.Where(s => Convert.ToString(s["fsourceentryid"]).EqualsIgnoreCase(fsourceentryid));
                    if (!matchOutEntrys.Any())
                    {
                        // 第一次下推
                        entry["fplanqty"] = canOutQty;
                        //entry["fbizplanqty"] = canOutQty;
                        entry["fqty"] = canOutQty;
                        //entry["fbizqty"] = canOutQty;
                        entry["forderqty"] = canOutQty;
                        //entry["fbizorderqty"] = canOutQty;
                        entry["famount"] = Convert.ToDecimal(entry["fprice"]) * canOutQty;

                        continue;
                    }

                    var entryId = Convert.ToString(entry["id"]);
                    // 排除当前行的汇总基本单位出库数量
                    var ignoreCurrSumOutQty = matchOutEntrys
                        .Where(s => !Convert.ToString(s["fentryid"]).EqualsIgnoreCase(entryId))
                        .Sum(s => Convert.ToDecimal(s["fqty"]));
                    // 当前行基本单位出库数量
                    var currOutQty = Convert.ToDecimal(entry["fqty"]);

                    // 关联销售合同的基本单位销售数量 + 关联销售合同的基本单位退换数量 <= 排除当前行的汇总基本单位出库数量
                    if (salQty + returnQty < ignoreCurrSumOutQty)
                    {
                        var seq = Convert.ToInt32(orderEntry["fseq"]);
                        var productName = Convert.ToString(orderEntry["fproductname"]);
                        errMsgs.Add($"第{seq}行商品【{productName}】的销售数量[{salQty}]+销售已退换数量[{returnQty}]应大于已下推的销售出库单的累计实发数量[{ignoreCurrSumOutQty}]，才允许出库！");

                        continue;
                    }

                    // 29396 销售合同下推生成的《销售出库单.出库明细》的【应发数量】应等于源单销售合同.商品明细的【销售数量】- 销售出库单.出库明细的【实发数量】汇总
                    canOutQty = Math.Max((salQty + returnQty - ignoreCurrSumOutQty), currOutQty);
                    if (canOutQty <= 0)
                    {
                        beRemove.Add(entry);
                        continue;
                    }

                    entry["fplanqty"] = canOutQty;
                    //entry["fbizplanqty"] = canOutQty;
                    entry["fqty"] = canOutQty;
                    //entry["fbizqty"] = canOutQty;
                    entry["forderqty"] = canOutQty;
                    //entry["fbizorderqty"] = canOutQty;
                    entry["famount"] = Convert.ToDecimal(entry["fprice"]) * canOutQty;
                }

                foreach (var entry in beRemove)
                {
                    entries.Remove(entry);
                }
            }

            if (errMsgs.Any())
            {
                throw new BusinessException(errMsgs.JoinEx("\r\n", false));
            }

            //根据基本单位数量自动反算关联业务单位数量字段（如库存单位，业务单位对应的数量）
            var unitService = this.UserContext.Container.GetService<IUnitConvertService>();
            unitService.ConvertByBasQty(this.UserContext, this.TargetHtmlForm, dataEntities, this.Option);
        }

        /// <summary>
        /// 更新时间
        /// </summary>
        /// <param name="dataEntities"></param>
        private void ModifyFdata(IEnumerable<DynamicObject> dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() <= 0)
            {
                return;
            }

            var billTypeService = this.UserContext.Container.GetService<IBillTypeService>();
            var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.UserContext, "stk_sostockout");

            foreach (var item in dataEntities)
            {
                var billTypeId = Convert.ToString(item["fbilltype"]);
                var paramSetObj = billTypeService.GetBillTypeParamSet(this.UserContext, htmlForm, billTypeId);
                if (paramSetObj != null)
                {
                    if (int.TryParse(Convert.ToString(paramSetObj["fday"]), out var deliveryDays))
                    {
                        if (!DateTime.TryParse(Convert.ToString(item["fcreatedate"]), out var fcreatedate))
                        {
                            fcreatedate = DateTime.Now;
                        }

                        item["fdate"] = fcreatedate.Date.AddDays(deliveryDays);
                    }
                }
            }
        }

        /// <summary>
        /// 按部门配置的仓库进行填充
        /// </summary>
        /// <param name="dataEntities"></param>
        private void SetStoreHouseInfoByDept(IEnumerable<DynamicObject> dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() <= 0)
            {
                return;
            }

            string orderFormId = "ydj_order";

            var billnos = dataEntities.Where(x => Convert.ToString(x["fsourcetype"]) == orderFormId)
                                      .Select(x => Convert.ToString(x["fsourcenumber"])).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();

            if (billnos == null || billnos.Count <= 0)
            {
                return;
            }

            IMetaModelService metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
            var orderHtmlForm = metaModelService.LoadFormModel(this.UserContext, orderFormId);
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, orderHtmlForm.GetDynamicObjectType(this.UserContext));

            StringBuilder where = new StringBuilder("fmainorgid=@fmainorgid and fbillno ");
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,this.UserContext.Company)
            };

            if (billnos.Count == 1)
            {
                where.Append(" = @fbillno");
                sqlParams.Add(new SqlParam("@fbillno", System.Data.DbType.String, billnos[0]));
            }
            else
            {
                where.Append(" in (");
                where.Append(string.Join(",", billnos.Select((x, i) => string.Format("@fbillno{0}", i))));
                where.Append(")");
                sqlParams.AddRange(billnos.Select((x, i) => new SqlParam(string.Format("@fbillno{0}", i), System.Data.DbType.String, x)));
            }

            var dataReader = this.UserContext.GetPkIdDataReader(orderHtmlForm, where.ToString(), sqlParams);
            var dbEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

            if (dbEntities == null || dbEntities.Count <= 0)
            {
                return;
            }

            var depInfos = dbEntities.SelectMany(x =>
              {
                  var fentries = x["fentry"] as DynamicObjectCollection;
                  if (fentries == null || fentries.Count <= 0)
                  {
                      return new List<Dictionary<string, string>>();
                  }
                  return fentries.Select(y => new Dictionary<string, string>//.Where(y => Convert.ToBoolean(y["fisoutspot"]))
                  {
                    { "billno",Convert.ToString(x["fbillno"])},
                    { "entryid",Convert.ToString(y["id"])},
                    { "deptid",Convert.ToString(y["fdeptid"])}
                  });
              }).ToList();

            if (depInfos == null || depInfos.Count <= 0)
            {
                return;
            }

            var depIds = depInfos.Select(x => x["deptid"]).Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();

            if (depIds == null || depIds.Count <= 0)
            {
                return;
            }

            StringBuilder sql = new StringBuilder("select fid,fstorehouseid,fleaderid from T_BD_department where fmainorgid=@fmainorgid and fid ");
            sqlParams.Clear();
            sqlParams.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, this.UserContext.Company));

            if (depIds.Count == 1)
            {
                sql.Append(" = @fid");
                sqlParams.Add(new SqlParam("@fid", System.Data.DbType.String, depIds[0]));
            }
            else
            {
                sql.Append(" in (");
                sql.Append(string.Join(",", depIds.Select((x, i) => string.Format("@fid{0}", i))));
                sql.Append(")");
                sqlParams.AddRange(depIds.Select((x, i) => new SqlParam(string.Format("@fid{0}", i), System.Data.DbType.String, x)));
            }

            var datas = new List<Dictionary<string, string>>();
            var dbServer = this.UserContext.Container.GetService<IDBService>();
            using (var reader = dbServer.ExecuteReader(this.UserContext, sql.ToString(), sqlParams))
            {
                while (reader.Read())
                {
                    datas.Add(new Dictionary<string, string>
                    {
                        { "deptid",reader.GetValueToString("fid") },
                        { "storehoseid",reader.GetValueToString("fstorehouseid") },
                        { "leaderid",reader.GetValueToString("fleaderid")}
                    });
                }
            }

            if (datas == null || datas.Count <= 0)
            {
                return;
            }

            foreach (var dataEntity in dataEntities)
            {
                var fentities = dataEntity["fentity"] as DynamicObjectCollection;
                if (fentities == null || fentities.Count <= 0)
                {
                    continue;
                }
                bool isFirst = true;
                foreach (var fentity in fentities)
                {
                    var fsoorderentryid = Convert.ToString(fentity["fsoorderentryid"]);
                    var depInfo = depInfos.FirstOrDefault(x => x["entryid"] == fsoorderentryid);
                    if (depInfo == null)
                    {
                        continue;
                    }

                    var data = datas.FirstOrDefault(x => x["deptid"] == depInfo["deptid"]);
                    if (data == null || string.IsNullOrWhiteSpace(data["storehoseid"]))
                    {
                        continue;
                    }

                    if (isFirst && !string.IsNullOrWhiteSpace(data["leaderid"]))
                    {
                        isFirst = false;
                        dataEntity["fstockdeptid"] = data["deptid"];
                        dataEntity["fstockstaffid"] = data["leaderid"];
                    }

                    fentity["fstorehouseid"] = data["storehoseid"];
                }
            }
        }

        /// <summary>
        /// 获取历史销售员
        /// </summary>
        /// <param name="sourceNumber"></param>
        /// <returns></returns>
        private string GetHistorySalesperson(string sourceNumber, string currSales)
        {
            string strSql = @"SELECT DISTINCT fstaffid,ST.fname,OD.fcreatedate FROM T_YDJ_ORDER_CHG AS OD WITH(NOLOCK) 
                                INNER JOIN T_BD_STAFF AS ST WITH(NOLOCK)  ON OD.fstaffid=ST.fid 
                                WHERE fsourcenumber=@fsourcenumber ORDER BY OD.fcreatedate DESC";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fsourcenumber", System.Data.DbType.String, sourceNumber)
            };

            //查询销售合同变更记录中的历史销售员
            List<string> lstHistorySalesperson = new List<string>() { currSales };
            using (var dr = this.UserContext.ExecuteReader(strSql, sqlParam))
            {
                while (dr.Read())
                {
                    var staffName = dr["fname"]?.ToString();
                    if (!staffName.IsNullOrEmptyOrWhiteSpace() && !lstHistorySalesperson.Contains(staffName))
                    {
                        lstHistorySalesperson.Add(staffName);
                    }
                }
            }
            return string.Join("，", lstHistorySalesperson);
        }

        /// <summary>
        /// 还原销售合同上的仓位到出库单
        /// </summary>
        /// <param name="dataEntities"></param>
        private void RevertSaleOrderStoreLocation(IEnumerable<DynamicObject> dataEntities)
        {
            //查询所有的上游单据
            var allEntrys = dataEntities.SelectMany(t => t["fentity"] as DynamicObjectCollection);
            var allOrderId = allEntrys.Select(t => Convert.ToString(t["fsoorderinterid"])).Distinct();
            var allOrderObjs = this.UserContext.LoadBizDataById("ydj_order", allOrderId);
            //获取上游销售合同的所有明细行
            var allOrderEntrys = allOrderObjs.SelectMany(t => t["fentry"] as DynamicObjectCollection);

            //判断是否有明细行与源单明细行的仓库相同的
            var matchEntrys = allEntrys.Where(t => allOrderEntrys.Any(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(t["fsoorderentryid"]))
                                                                        && Convert.ToString(o["fstorehouseid"]).EqualsIgnoreCase(Convert.ToString(t["fstorehouseid"]))));
            if (matchEntrys != null && matchEntrys.Any())
            {
                foreach (var entry in matchEntrys)
                {
                    var sourceEntry = allOrderEntrys.Where(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(entry["fsoorderentryid"]))
                                                             && Convert.ToString(o["fstorehouseid"]).EqualsIgnoreCase(Convert.ToString(entry["fstorehouseid"]))).FirstOrDefault();
                    //如果仓库相同，则直接取源单的仓位
                    entry["fstorelocationid"] = sourceEntry["fstorelocationid"];
                }
            }
        }

        /// <summary>
        /// 过滤套件头商品（解决整单下推的时候pull不会过滤）
        /// </summary>
        /// <param name="dataEntities"></param>
        private void RemoveKitHeadProduct(IEnumerable<DynamicObject> dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() <= 0) return;

            var allEntrys = dataEntities?.SelectMany(t => t["fentity"] as DynamicObjectCollection);
            var materialIds = allEntrys.Select(entry => Convert.ToString(entry["fmaterialid"])).Where(materialid => !materialid.IsNullOrEmptyOrWhiteSpace())?.Distinct()?.ToList();
            var suiteHeadEntryIds = new List<string>();
            if (materialIds != null && materialIds.Any())
            {
                var productObjs = this.UserContext.LoadBizBillHeadDataById("ydj_product", materialIds, "fsuiteflag");
                var suiteHeadIds = productObjs.Where(t => Convert.ToString(t["fsuiteflag"]).ToLower() == "true" || Convert.ToString(t["fsuiteflag"]) == "1")
                                              .Select(m => Convert.ToString(m["id"])).ToList();//套件头商品ID

                foreach (var dataEntity in dataEntities)
                {
                    var entries = dataEntity["fentity"] as DynamicObjectCollection;
                    List<DynamicObject> beRemove = new List<DynamicObject>();
                    foreach (var entry in entries)
                    {
                        if (suiteHeadIds.Contains(Convert.ToString(entry["fmaterialid"])))
                        {
                            beRemove.Add(entry);
                        }
                    }
                    foreach (var entry in beRemove)
                    {
                        entries.Remove(entry);
                    }
                }
            }
        }

        private void DealTailDifference(IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntityItem in dataEntities)
            {
                var sourceEntryIds = (dataEntityItem["fentity"] as DynamicObjectCollection)
                    .Select(s => Convert.ToString(s["fsourceentryid"])).Where(s => !s.IsNullOrEmptyOrWhiteSpace()).ToList();


                var sql = $@"select t1.fentryid fsoorderentryid, max(t1.fbizqty) orderQty,max(t1.fdealamount) orderAmount,SUM(t2.fbizqty) outQty,SUM(t2.famount) outAmount from t_ydj_orderentry t1
                        left join t_stk_sostockoutentry t2 on t1.fentryid=t2.fsoorderentryid
                        where t1.fentryid in ({sourceEntryIds.JoinEx(",", true)}) ";
                if (!string.IsNullOrWhiteSpace(Convert.ToString(dataEntityItem["id"])))
                {
                    sql += $" and t2.fid<>'{Convert.ToString(dataEntityItem["id"])}' ";
                }
                sql += " group by t1.fentryid ";
                var dbService = this.UserContext.Container.GetService<IDBService>();
                var result = dbService.ExecuteDynamicObject(this.UserContext, sql);
                if (result != null && result.Count() > 0)
                {
                    foreach (var item in result)
                    {
                        var dataItem = (dataEntityItem["fentity"] as DynamicObjectCollection)
                            .Where(a => Convert.ToString(a["fsourceentryid"]).Equals(Convert.ToString(item["fsoorderentryid"]))).FirstOrDefault();
                        if (dataItem != null)
                        {
                            //实发数量=合同数量
                            if (Convert.ToDecimal(dataItem["fbizqty"]) == Convert.ToDecimal(item["orderQty"]))
                            {
                                dataItem["famount"] = Convert.ToDecimal(item["orderAmount"]);
                            }
                        }


                    }
                }
            }
        }

        /// <summary>
        /// 还原直营管理模式下的仓库、销售员、销售部门信息
        /// </summary>
        private void RevertZYInfo(IEnumerable<DynamicObject> dataEntities)
        {
            if (dataEntities == null) return;

            // 只处理fmanagemodel=1的单据
            var zyEntities = dataEntities.Where(d => Convert.ToString(d["fmanagemodel"]) == "1" && Convert.ToBoolean(d["fpiecesendtag"])).ToList();
            if (!zyEntities.Any()) return;
            var billData = this.GetBilltype();

            var billent = billData.Where(x => x["fname"].ToString() == "直发销售出库").FirstOrDefault();

            var defaultStore = this.UserContext.LoadBizDataByNo("ydj_storehouse", "fnumber", new List<string>() { "ZFCK" });
            // 收集所有上游合同ID
            var allEntrys = zyEntities.SelectMany(t => t["fentity"] as DynamicObjectCollection);
            var allOrderId = allEntrys.Select(t => Convert.ToString(t["fsoorderinterid"])).Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();
            if (!allOrderId.Any()) return;

            // 查询上游销售合同
            var allOrderObjs = this.UserContext.LoadBizDataById("ydj_order", allOrderId);
            if (allOrderObjs == null || !allOrderObjs.Any()) return;

            // 获取所有上游销售合同明细
            var allOrderEntrys = allOrderObjs.SelectMany(t => t["fentry"] as DynamicObjectCollection);

            foreach (var dataEntity in zyEntities)
            {
                var entryList = dataEntity["fentity"] as DynamicObjectCollection;
                if (entryList == null || entryList.Count == 0) continue;

                // 取第一个明细的上游合同ID，作为表头销售员、部门的来源
                var firstEntry = entryList.FirstOrDefault();
                string soOrderId = firstEntry != null ? Convert.ToString(firstEntry["fsoorderinterid"]) : null;
                var soOrder = allOrderObjs.FirstOrDefault(o => Convert.ToString(o["id"]) == soOrderId);
                if (soOrder != null)
                {
                    dataEntity["fstockstaffid"] = soOrder["fstaffid"];
                    dataEntity["fstockdeptid"] = soOrder["fdeptid"];
                }
                if (billent != null)
                {
                    dataEntity["fbilltype"] = billent["id"];
                }

                // 明细行赋值仓库
                foreach (var entry in entryList)
                {
                    entry["fstorehouseid"] = defaultStore.FirstOrDefault()?["id"];

                }
            }
        }

        /// <summary>
        /// 返回对应单据类型
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetBilltype()
        {
            //原逻辑：GetPkIdDataReader不传条件的话查的单据类型是当前组织自建的和预置的（fmainorgid=0）单据类型
            //现逻辑：如果是在总部对预置的单据类型做了保存，生成了总部自建的单据类型副本数据（fmainorgid=TopCompanyId）就查不到了，这里需要补充这个场景
            var where = $"(fmainorgid='{this.UserContext.Company}' or fmainorgid='0' or fmainorgid ='{this.UserContext.TopCompanyId}' )";
            Dictionary<string, string> dic = new Dictionary<string, string>();
            var purForm = this.MetaModelService.LoadFormModel(this.UserContext, "bd_billtype");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, purForm.GetDynamicObjectType(this.UserContext));
            var reader = this.UserContext.GetPkIdDataReader(purForm, where, new List<SqlParam>());
            var purOrder = dm.SelectBy(reader).OfType<DynamicObject>();
            return purOrder;
        }
    }
}