using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("pur_receiptnotice2stk_postockin")]
    public class PurReceiptNotice2PoStockInConvertPlugIn: BaseScanTaskConvertPlugIn
    {
        
    }
}
