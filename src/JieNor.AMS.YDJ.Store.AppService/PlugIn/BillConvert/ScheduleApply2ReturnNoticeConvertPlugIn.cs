using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 排单申请单->销售退货通知单的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("sal_returnnotice")]
    [OperationNo("stk_scheduleapply2sal_returnnotice")]
    public class ScheduleApply2ReturnNoticeConvertPlugIn: BaseSchedulePlatformConvertPlugIn
    {
        protected override string storeHouseIdMapKey
        {
            get
            {
                return "fstorehouseidto";
            }
        }

        protected override string dateMapKey
        {
            get
            {
                return "fstockdateto";
            }
        }

        protected override string stockDeptIdMapKey
        {
            get
            {
                return "fstockdeptidto";
            }
        }

        protected override string stockStaffIdMapKey
        {
            get
            {
                return "fstockstaffidto";
            }
        }

        protected override object BeforeMapFieldValue(Dictionary<string, object> existReturnObj, string targetFieldId, out bool isCancel)
        {
            isCancel = false;
            object targetValue = null;
            object fscheduleqty = 0m;

            switch (targetFieldId)
            {
                case "fplanqty":
                    if (existReturnObj.TryGetValue("fscheduleqty", out fscheduleqty))
                    {
                        isCancel = true;
                        targetValue = fscheduleqty;
                    }
                    break;
                case "freturntype":
                    object fapplytype = string.Empty;
                    if (existReturnObj.TryGetValue("fapplytype", out fapplytype))
                    {
                        isCancel = true;
                        targetValue = Convert.ToString(fapplytype) == "0" ? "sostockreturn_biztype_01" : "sostockreturn_biztype_02";
                    }
                    break;
            }

            return targetValue;
        }

        /// <summary>
        /// 计算汇总数据
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities?.Any() != true)
            {
                throw new BusinessException("转换失败：未能成功生成销售退货通知单！");
            }

            var applyIds = e.TargetDataEntities.SelectMany(x => x["fentity"] as DynamicObjectCollection).Select(x => Convert.ToString(x["fsourceinterid"])).Where(x => false == string.IsNullOrWhiteSpace(x)).ToList();


            var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
            var stockOutForm = metaModelService.LoadFormModel(this.UserContext, "stk_sostockout");
            List<DynamicObject> applyEntities = null;
            List<DynamicObject> stockOutEntities = null;
            List<DynamicObject> orderEntities = null;


            if (applyIds != null && applyIds.Count > 0)
            {
                var dm = this.UserContext.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserContext, this.SourceHtmlForm.GetDynamicObjectType(this.UserContext));
                applyEntities = dm.Select(applyIds).OfType<DynamicObject>().ToList();
            }

            if (applyEntities != null && applyEntities.Count > 0)
            {
                var stockOutIds = applyEntities.SelectMany(x => x["fentity"] as DynamicObjectCollection)
                                               .Where(x => Convert.ToString(x["fsourceformid"]) == stockOutForm.Id)
                                               .Select(x => Convert.ToString(x["fsourceinterid"]))
                                               .Where(x => false == string.IsNullOrWhiteSpace(x))
                                               .ToList();
                var dm = this.UserContext.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserContext, stockOutForm.GetDynamicObjectType(this.UserContext));
                stockOutEntities = dm.Select(stockOutIds).OfType<DynamicObject>().ToList();
            }

            if (stockOutEntities != null && stockOutEntities.Count > 0)
            {
                var orderIds = stockOutEntities.SelectMany(x => x["fentity"] as DynamicObjectCollection)
                                               .Select(x => Convert.ToString(x["fsoorderinterid"]))
                                               .Where(x => false == string.IsNullOrWhiteSpace(x))
                                               .ToList();
                var orderForm = metaModelService.LoadFormModel(this.UserContext, "ydj_order");
                var dm = this.UserContext.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserContext, orderForm.GetDynamicObjectType(this.UserContext));
                orderEntities = dm.Select(orderIds).OfType<DynamicObject>().ToList();
            }

            foreach (var dataEntity in e.TargetDataEntities)
            {
                var factualreturnamount = 0m;

                var fentities = dataEntity["fentity"] as DynamicObjectCollection;
                if (fentities == null || fentities.Count <= 0)
                {
                    continue;
                }

                foreach (var fentity in fentities)
                {
                    factualreturnamount += Convert.ToDecimal(fentity["famount"]);

                    var applyEntry = findSourceEntry(fentity, applyEntities);

                    if (applyEntry == null)
                    {
                        continue;
                    }

                    if (Convert.ToString(applyEntry["fsourceformid"]) != stockOutForm.Id)
                    {
                        continue;
                    }

                    var stockOutEntry = findSourceEntry(applyEntry, stockOutEntities);

                    if (stockOutEntry == null)
                    {
                        continue;
                    }

                    var orderEntity = findSourceEntry(stockOutEntry, orderEntities, "fsoorderinterid", "fsoorderentryid", "fentry");
                    if (orderEntity == null)
                    {
                        continue;
                    }

                    var stockOutEntity = stockOutEntry.Parent as DynamicObject;
                    fentity["fsooutstockno"] = stockOutEntity[stockOutForm.NumberFldKey];
                    fentity["fsooutstockinterid"] = stockOutEntity["id"];
                    fentity["fsooutstockentryid"] = stockOutEntry["id"];
                    fentity["fsoorderno"] = stockOutEntry["fsoorderno"];
                    fentity["fsoorderinterid"] = stockOutEntry["fsoorderinterid"];
                    fentity["fsoorderentryid"] = stockOutEntry["fsoorderentryid"];
                    fentity["forderqty"] = orderEntity["fqty"];
                    fentity["forderdate"] = (orderEntity.Parent as DynamicObject)["forderdate"];
                }

                if (Convert.ToString(dataEntity["freturntype"]) == "sostockreturn_biztype_02")
                {
                    dataEntity["factualreturnamount"] = factualreturnamount;
                    dataEntity["fplanreturnamount"] = factualreturnamount;
                }
            }

            var unitConvertService = this.UserContext.Container.GetService<IUnitConvertService>();
            unitConvertService.ConvertByBasQty(this.UserContext, this.TargetHtmlForm, e.TargetDataEntities, Option);
        }

        private DynamicObject findSourceEntry(DynamicObject fentity,
                                              List<DynamicObject> sourceEntities,
                                              string sourceFieldId= "fsourceinterid",
                                              string sourceEntryFieldId= "fsourceentryid",
                                              string sourceEntryKey= "fentity")
        {
            var id = Convert.ToString(fentity[sourceFieldId]);
            var entryId = Convert.ToString(fentity[sourceEntryFieldId]);
            if (string.IsNullOrWhiteSpace(id) || string.IsNullOrWhiteSpace(entryId))
            {
                return null;
            }

            var entity = sourceEntities?.FirstOrDefault(x => Convert.ToString(x["id"]) == id);
            if (entity == null)
            {
                return null;
            }

            return (entity[sourceEntryKey] as DynamicObjectCollection)?.FirstOrDefault(x => Convert.ToString(x["id"]) == entryId);
        }
    }
}
