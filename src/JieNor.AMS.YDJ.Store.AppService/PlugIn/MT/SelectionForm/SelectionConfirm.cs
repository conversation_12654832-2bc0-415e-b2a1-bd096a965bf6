using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;
using System;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MT.SelectionForm
{
    /// <summary>
    /// 选配：确认
    /// </summary>
    [InjectService]
    [FormId("mt_selectionform")]
    [OperationNo("selectionconfirm")]
    public class SelectionConfirm : SelectionBase
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var entity = e.DataEntitys[0];
            AssignmentUniqueIdentifier(entity);
            var product = GetProduct(entity["fproductid"].ToString());
            var issuit = Convert.ToBoolean(product["fissuit"]);//是否属于套件

            var selection = GetOrCreateSelection(entity, issuit);
            if (selection != null)
            {
                var refManager = this.Container.GetService<LoadReferenceObjectManager>();
                refManager.Load(this.Context, selection.DynamicObjectType, selection, true);

                JObject so = new JObject();
                if (issuit)
                {
                    so["fid"] = selection["id"].ToString();
                    so["fproductid"] = selection["fproductid"].ToString();
                    so["fnumber"] = selection["fnumber"].ToString();
                    so["fdescription"] = selection["fdescription"].ToString();
                    var ja = new JArray();
                    var es = selection["fentity"] as DynamicObjectCollection;
                    foreach (var item in es)
                    {
                        JObject po = new JObject();
                        po["fpartproductid"] = item["fpartproductid"].ToString();
                        var fpartsselectionid_ref = item["fpartsselectionid_ref"] as DynamicObject;
                        po["fpartsselectionnumber"] = fpartsselectionid_ref["fnumber"].ToString();
                        po["fdescription"] = fpartsselectionid_ref["fdescription"].ToString();
                        po["fqty"] = item["fqty"].ToString();
                        ja.Add(po);
                    }
                    so["fentity"] = ja;
                }
                else
                {
                    so["fproductid"] = selection["fproductid"].ToString();
                    so["fnumber"] = selection["fnumber"].ToString();
                    so["fdescription"] = selection["fdescription"].ToString();
                    so["fqty"] = (entity["fentity"] as DynamicObjectCollection)[0]["fqty"].ToString();
                }

                this.Result.SimpleData["data"] = so.ToJson();
            }
        }

        private DynamicObject GetProduct(string productId)
        {
            var dm = this.Context.Container.GetService<IDataManager>();
            var meta = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            dm.InitDbContext(this.Context, meta.GetDynamicObjectType(this.Context));
            return dm.Select(productId) as DynamicObject;
        }
        public DynamicObject GetOrCreateSelection(DynamicObject entity, bool issuit)
        {
            var partsSelectionEntity = entity["fentity"] as DynamicObjectCollection;

            #region 获取子件选配的信息
            //获取选配维度信息
            var dimensions = this.SimpleData["dimensions"];
            List<DynamicObject> partsselectionObjs = new List<DynamicObject>();
            var meta = this.MetaModelService.LoadFormModel(this.Context, "mt_partsselection");//子件选配信息
            var dcSerializer = this.Context.Container.GetService<IDynamicSerializer>();
            var arr = JArray.Parse(dimensions);
            dcSerializer.Sync(meta.GetDynamicObjectType(this.Context), partsselectionObjs, arr);

            //必录校验
            if (partsselectionObjs != null && partsselectionObjs.Count > 0)
            {
                var isChecked = true;
                partsselectionObjs.ForEach(x =>
                {
                    if (!CheckMustDimension(x))
                    {
                        isChecked = false;
                    }
                });
                if (!isChecked)
                {
                    return null;
                }
            }

            SortedDictionary<string, string> partsIdentities = new SortedDictionary<string, string>();
            List<DynamicObject> forSavePartsSelection = new List<DynamicObject>();
            //以子件选配为准
            //Dictionary<string, int> itemNo = new Dictionary<string, int>();
            //var num = 0;
            foreach (var ps in partsSelectionEntity)
            {
                //num++;
                var partsProductId = ps["fpartproductid"].ToString();
                var item = partsselectionObjs.FirstOrDefault(x => x["fproductid"].ToString() == partsProductId);
                if (item == null)
                {
                    //没有选配维度，但有常用方案，使用常用方案
                    if (!ps["fpartsselectionid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var dmBase = this.Context.Container.GetService<IDataManager>();
                        var metaBase = this.MetaModelService.LoadFormModel(this.Context, "mt_partsselection");
                        dmBase.InitDbContext(this.Context, metaBase.GetDynamicObjectType(this.Context));
                        var commonSelection = dmBase.Select(ps["fpartsselectionid"]) as DynamicObject;
                        partsIdentities.Add(partsProductId, commonSelection["fidentity"].ToString());
                    }
                    else
                    {
                        var product = this.Context.LoadBizDataById("ydj_product", partsProductId);
                        var productName = product["fname"];
                        var msg = $"子件产品{productName}的维度信息不能为空！";
                        //没有选配维度，也没有默认方案
                        this.Result.IsSuccess = false;
                        this.Result.ComplexMessage.ErrorMessages.Add(msg);
                        return null;
                    }
                }
                else
                {
                    //有选配维度，查找识别码，存在直接使用，不存在则创建
                    var partsIdentity = GetPartsSelectionIdentity(partsProductId, item["fentity"] as DynamicObjectCollection);
                    var partsSelection = GetPartsSelectionByIdentity(partsProductId, partsIdentity);
                    if (partsSelection == null)
                    {
                        forSavePartsSelection.Add(item);
                        //itemNo.Add(partsProductId, num);
                    }
                    else
                    {
                        //存在相同的识别码，直接使用
                        ps["fpartsselectionid"] = partsSelection["fid"];
                    }
                    partsIdentities.Add(partsProductId, partsIdentity);
                }
            }

            //不存在相同的识别码的子件选配，则创建
            if (forSavePartsSelection.Count > 0)
            {
                var result = this.Gateway.InvokeBillOperation(
                    this.Context,
                    "mt_partsselection",
                    forSavePartsSelection,
                    "save",
                    new Dictionary<string, object>()
                //{
                //    { "ItemNo", itemNo }
                //}
                );
                if (!result.IsSuccess)
                {
                    this.Result.IsSuccess = false;
                    this.Result.ComplexMessage.ErrorMessages.AddRange(result.ComplexMessage.ErrorMessages);
                    return null;
                }

                forSavePartsSelection.ForEach(x =>
                {
                    var ss = partsSelectionEntity.FirstOrDefault(y => x["fproductid"].ToString() == y["fpartproductid"].ToString());
                    ss["fpartsselectionid"] = x["id"];
                });

            }
            #endregion

            if (!issuit)
            {
                //产品是子件则直接返回子件选配信息
                var partsSelection = partsSelectionEntity.FirstOrDefault();
                if (partsSelection != null)
                {
                    return GetPartsSelection(new[] { partsSelection["fpartsselectionid"].ToString() }).FirstOrDefault();
                }
                return null;
            }

            var suiteProductId = entity["fproductid"].ToString();
            var fploidy = partsSelectionEntity.Sum(x => Convert.ToDecimal(x["fqty"]));

            var suiteIdentity = GetSuiteSelectionIdentity(suiteProductId, fploidy, partsSelectionEntity, partsIdentities);
            var suiteSelection = GetSuiteSelectionByIdentity(suiteProductId, suiteIdentity);
            if (suiteSelection == null)
            {
                var suiteSelectionForm = this.MetaModelService.LoadFormModel(this.Context, "mt_suiteselection");
                suiteSelection = new DynamicObject(suiteSelectionForm.GetDynamicObjectType(this.Context));
                suiteSelection["fproductid"] = entity["fproductid"];
                suiteSelection["fploidy"] = fploidy;
                var fentity = entity["fentity"] as DynamicObjectCollection;
                var ssentity = suiteSelection["fentity"] as DynamicObjectCollection;
                fentity.ToList().ForEach(x =>
                {
                    var eitem = new DynamicObject(ssentity.DynamicCollectionItemPropertyType);
                    eitem["fpartproductid"] = x["fpartproductid"];
                    eitem["fqty"] = x["fqty"];
                    eitem["fpartsselectionid"] = x["fpartsselectionid"];
                    ssentity.Add(eitem);
                });

                var result = this.Gateway.InvokeBillOperation(
                    this.Context,
                    "mt_suiteselection",
                    new[] { suiteSelection },
                    "save",
                    new Dictionary<string, object>()
                );
                if (!result.IsSuccess)
                {
                    this.Result.IsSuccess = false;
                    this.Result.ComplexMessage.ErrorMessages.AddRange(result.ComplexMessage.ErrorMessages);
                    return null;
                }
            }
            return suiteSelection;
        }


        /// <summary>
        /// 给套件选配信息，子件选配信息，赋值唯一标识
        /// </summary>
        public void AssignmentUniqueIdentifier(DynamicObject entity)
        {
            var fsuiteselectionid = Convert.ToString(entity["fsuiteselectionid"]);//套件ID
            var suiteselectionList = this.Context.LoadBizDataByFilter("mt_suiteselection", "fid = '{0}'".Fmt(fsuiteselectionid)).ToList();  //套件

            //判断套件是否存在识别码
            if (suiteselectionList.Where(p => Convert.ToString(p["fidentity"]).IsNullOrEmptyOrWhiteSpace()).Any())
            {
                var gateway = this.Context.Container.GetService<IHttpServiceInvoker>();
                SortedDictionary<string, string> partsIdentities = new SortedDictionary<string, string>();
                var partsSelectionEntity = suiteselectionList[0]["fentity"] as DynamicObjectCollection;
                var partsselectionId = partsSelectionEntity.Select(p => Convert.ToString(p["fpartsselectionid"])).ToList();//子件Id
                var partsselectionlist = this.Context.LoadBizDataByFilter("mt_partsselection", "fid in('{0}')".Fmt(string.Join("','", partsselectionId)));//子件

                foreach (var ps in partsSelectionEntity)
                {
                    var partsProductId = ps["fpartproductid"].ToString();//子件产品Id

                    var fpartsselectionid = ps["fpartsselectionid"].ToString();//子件ID

                    var item = partsselectionlist.FirstOrDefault(x => x["fproductid"].ToString() == partsProductId);
                    var partsIdentity = GetPartsSelectionIdentity(partsProductId, item["fentity"] as DynamicObjectCollection);
                    partsIdentities.Add(partsProductId, partsIdentity);
                    item["fidentity"] = partsIdentity;//赋值识别码
                }
                //子件保存
                gateway.InvokeBillOperation(this.Context, "mt_partsselection", partsselectionlist, "save", new Dictionary<string, object>());
                var fploidy = partsSelectionEntity.Sum(x => Convert.ToDecimal(x["fqty"]));
                var suiteIdentity = GetSuiteSelectionIdentity(fsuiteselectionid, fploidy, partsSelectionEntity, partsIdentities);
                suiteselectionList[0]["fidentity"] = suiteIdentity;//赋值识别码
                //套件保存
                gateway.InvokeBillOperation(this.Context, "mt_suiteselection", suiteselectionList, "save", new Dictionary<string, object>());
            }
        }
    }
}
