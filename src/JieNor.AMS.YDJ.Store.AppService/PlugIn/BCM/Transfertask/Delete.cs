using JieNor.AMS.YDJ.Core.Interface.BarcodeMgr;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.Transfertask
{
    [InjectService]
    [FormId("bcm_transfertask|bcm_transferintask")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 增加删除校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            var billNoField = this.HtmlForm.GetNumberField();
            e.Rules.Add(this.RuleFor("fbillhead", dataObj => dataObj["ftaskstatus"] as string)
                .IsTrue((dataObj, status) =>
                {
                    return status.EqualsIgnoreCase("ftaskstatus_01");
                })
                .WithMessage("扫描任务{0}的任务状态不等于待作业, 不允许删除!", (dataObj, bizStatus) => billNoField.DynamicProperty.GetValue<string>(dataObj)));
        }

    }
}
