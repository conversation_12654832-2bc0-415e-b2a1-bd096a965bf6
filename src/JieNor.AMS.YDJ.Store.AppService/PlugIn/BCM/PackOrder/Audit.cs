using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Utils;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PackOrder
{
    /// <summary>
    /// 包装清单：审核
    /// </summary>
    [InjectService]
    [FormId("bcm_packorder|bcm_packorderinit")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            // 如果已经生成过则不继续生成《收货扫描任务》要报错提示
            string errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string id = Convert.ToString(newData["Id"]);
                string fsourcenumber = Convert.ToString(newData["fsourcenumber"]);
                var fsourcetype = Convert.ToString(newData["fsourcetype"]);
                var fpackbiztype = Convert.ToString(newData["fpackbiztype"]);
                bool isCreate = false;
                string sql = "";
                if (fsourcetype.EqualsIgnoreCase("ydj_purchaseorder"))
                {
                    sql = $@"/*dialect*/SELECT a.fid,a.fbillno
                FROM T_BCM_RECEPTIONSCANTASK a WITH(NOLOCK)
                LEFT JOIN T_BCM_RESCANTASKENTITY b WITH(NOLOCK) ON b.fid=a.fid
                WHERE 
                    --a.ftaskstatus <>'ftaskstatus_04' AND 
                    a.fmainorgid = '{this.Context.Company}'
	                AND b.fsourceformid='ydj_purchaseorder' AND b.fsourcebillno = '{fsourcenumber}'";
                }
                else
                {
                    sql = $"select 1 from t_bcm_receptionscantask where fsourcetype = '{fsourcetype}' and fsourcenumber  = '{fsourcenumber}'";
                }
                using (var reader = this.Container.GetService<IDBService>().ExecuteReader(this.Context,
                    sql)
                )
                {
                    if (reader.Read() && fpackbiztype.EqualsIgnoreCase("1"))
                    {
                        isCreate = true;
                    }
                }

                if (isCreate)
                {
                    errorMsg = $"编码为【{newData["fbillno"]}】的包装清单对应的的来源单据【{newData["fsourcenumber"]}】已生成《收货扫描任务》，不允许进行包装审核 !请直接进行收货任务 ! ";
                    return false;
                }

                return true;

            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            //审核时, 如果《包装清单》的【业务类型】=”调拨包装”时, 要判断否已经生成过《调入扫描任务》, 如果已经生成过则不继续生成《调入扫描任务》要报错提示”来源单据 XXXXX 已生成调入扫描任务, 不允许进行包装审核 ! 请直接进行调入任务 !”;  因为目前设计是一张库存调拨单据只会关联一个调入任务; 而校验逻辑是用《包装清单》表头【来源单据】+【来源单据编号】匹配《调入扫描任务》单据体-扫描任务明细【来源单据】+【来源单据编号】, 如果匹配上时表示已经生成了《调入扫描任务》, 没有匹配上时表示没有生成《调入扫描任务》
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((n, o) =>
            {
                return ValiDation(n);
            }).WithMessage("来源单据【{0}】已生成调入扫描任务, 不允许进行包装审核 ! 请直接进行调入任务!", (dy, dyObj) => dy["fsourcenumber"]));

            e.Rules.Add(this.RuleFor("fentity", data => data).IsTrue((n, o) =>
            {
                var bizremainqty = Convert.ToDecimal(n["fbizremainqty"]);
                if (bizremainqty > 0)
                {
                    return false;
                }
                return true;
            }).WithMessage("第【{0}】行明细还有可包装的数量，请检查！", (dy, dyObj) => dy["fseq"]));
        }

        private bool ValiDation(DynamicObject n)
        {
            bool flag = true;
            string fpackbiztype = n["fpackbiztype"]?.ToString();//调拨包装
            if (fpackbiztype == "3")
            {
                string fsourcenumber = Convert.ToString(n["fsourcenumber"]);
                var sql = $"select 1 from t_bcm_transfertask where ftask_type = 'transferin' and fsourcenumber = '{fsourcenumber}'";
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                if (data.Any())
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            return flag;
        }


        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            var data = e.DataEntitys;
            this.Context.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, data.FirstOrDefault().DynamicObjectType, data, false);
            process(data);
            CreateRecTask(data);
            if (this.Result.ComplexMessage.ErrorMessages.Count > 0)
            {
                throw new BusinessException("包装清单审核失败!");
            }
        }

        private void process(DynamicObject[] dataEntities)
        {
            var packEntityAndBarCodeMasterMapping = new Dictionary<DynamicObject, DynamicObject>();
            var barCodeMasterAndPackOrderMapping = new Dictionary<DynamicObject, DynamicObject>();
            var dataEntityAndBarCodeMastersMapping = new Dictionary<DynamicObject, List<DynamicObject>>();
            var metaModelService = this.Container.GetService<IMetaModelService>();

            //获取源单
            Dictionary<string, List<DynamicObject>> sourceDatas = getSourceEntities(dataEntities, metaModelService);

            if (sourceDatas == null || sourceDatas.Count <= 0)
            {
                return;
            }

            //生成条码主档，并根据源单收货后是否同步入库设置条码的状态
            var barCodeMasters = createBarCodeMaster(dataEntities,
                                                     sourceDatas,
                                                     packEntityAndBarCodeMasterMapping,
                                                     barCodeMasterAndPackOrderMapping,
                                                     dataEntityAndBarCodeMastersMapping,
                                                     metaModelService);

            if (barCodeMasters == null || barCodeMasters.Count <= 0)
            {
                return;
            }

            //生成扫描记录，并根据源单收货后是否同步入库设置记录的状态
            var scanResults = createScanResult(barCodeMasters, barCodeMasterAndPackOrderMapping, metaModelService);

            if (scanResults == null || scanResults.Count <= 0)
            {
                return;
            }

            //反写条形码回包装清单
            writeBack(packEntityAndBarCodeMasterMapping, dataEntityAndBarCodeMastersMapping);

            //反写源单数量
            var sourceInfos = writeBack(dataEntities, sourceDatas, metaModelService);

            if (sourceInfos == null || sourceInfos.Count <= 0)
            {
                return;
            }

            //下推源单
            push(sourceInfos, scanResults);
        }

        /// <summary>
        /// 下推源单信息到下游单据
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <param name="id"></param>
        /// <param name="packEntities"></param>
        private void push(HtmlForm htmlForm, string id, DynamicObjectCollection packEntities)
        {
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var dataEntity = dm.Select(id) as DynamicObject;

            if (dataEntity == null)
            {
                return;
            }

            var fautobcinstock = Convert.ToBoolean(dataEntity["fautobcinstock"]);
            if (fautobcinstock == false)
            {
                return;
            }

            var response = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, new[] { dataEntity }, "audit", new Dictionary<string, object>());
            if (response == null || response.IsSuccess == false || response.ComplexMessage.ErrorMessages.Count > 0)
            {
                if (response != null && response.ComplexMessage.ErrorMessages.Count > 0)
                {
                    this.Result.ComplexMessage.ErrorMessages.AddRange(response.ComplexMessage.ErrorMessages);
                }
                return;
            }

            var convertService = this.Container.GetService<IConvertService>();
            var targetFormId = string.Empty;
            var ruleId = string.Empty;
            var entityKey = "fentity";

            switch (htmlForm.Id)
            {
                case "pur_receiptnotice":
                    ruleId = "pur_receiptnotice2stk_postockin";
                    targetFormId = "stk_postockin";
                    break;
                case "stk_otherstockin":
                    ruleId = "stk_otherstockin2stk_otherstockin";
                    targetFormId = "stk_otherstockin";
                    break;
                case "sal_returnnotice":
                    ruleId = "sal_returnnotice2stk_sostockreturn";
                    targetFormId = "stk_sostockreturn";
                    break;
            }

            var option = OperateOption.Create();
            var result = convertService.Push(this.Context, new BillConvertContext()
            {
                RuleId = ruleId,
                SourceFormId = htmlForm.Id,
                TargetFormId = targetFormId,
                SelectedRows = packEntities.Distinct(x => Convert.ToString(x["fsourceentryid"])).Select(x => new SelectedRow
                {
                    PkValue = Convert.ToString(x["fsourceinterid"]),
                    EntityKey = entityKey,
                    EntryPkValue = Convert.ToString(x["fsourceentryid"])
                }).ToConvertSelectedRows(),
                Option = option
            });
            var convertResult = result.SrvData as ConvertResult;
            var targetDatas = convertResult.TargetDataObjects?.ToList();
            if (targetDatas == null || targetDatas.Count <= 0)
            {
                this.Result.ComplexMessage.ErrorMessages.Add($"编号为{Convert.ToString(dataEntity["fnumber"])}的{htmlForm.Caption}下推失败!");
                return;
            }
            response = this.Gateway.InvokeBillOperation(this.Context, targetFormId, targetDatas, "save", new Dictionary<string, object>());
            if (response == null || response.IsSuccess == false || response.ComplexMessage.ErrorMessages.Count > 0)
            {
                if (response != null && response.ComplexMessage.ErrorMessages.Count > 0)
                {
                    this.Result.ComplexMessage.ErrorMessages.AddRange(response.ComplexMessage.ErrorMessages);
                }
                return;
            }
        }

        private void push(List<KeyValuePair<string, string>> sourceInfos, List<DynamicObject> scanResults)
        {
            scanResults = scanResults.Where(x => sourceInfos.Any(y => y.Key == Convert.ToString(x["fsourceformid"]) &&
                                                                      y.Value == Convert.ToString(x["fsourceinterid"])))
                                     .ToList();
            scanResults = scanResults.Where(w => Convert.ToString(w["fsourceformid"]).Equals("pur_receiptnotice") || Convert.ToString(w["fsourceformid"]).Equals("stk_otherstockin") || Convert.ToString(w["fsourceformid"]).Equals("stk_inventorytransferreq")).ToList();
            if (scanResults == null || scanResults.Count <= 0)
            {
                return;
            }

            var packOrderService = this.Container.GetService<IPackOrderService>();
            packOrderService.PushBySanResult(this.Context, scanResults, this.Result.ComplexMessage.ErrorMessages);
        }

        /// <summary>
        /// 反写源单数量
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="metaModelService"></param>
        /// <returns></returns>
        private List<KeyValuePair<string, string>> writeBack(DynamicObject[] dataEntities,
                                                            Dictionary<string, List<DynamicObject>> sourceDatas,
                                                            IMetaModelService metaModelService)
        {
            string sqlFormat = @"
select sum(fqty) as fqty,fsourceinterid,fsourceentryid from (
select fsourceentryid,isnull(sum(fqty),0) as fqty,fsourceformid,fsourceinterid from t_bcm_packorderentry
where fpackgroup like '2%' and fid='{0}'
group by fsourceformid,fsourceinterid,fsourceentryid
union all
select fsourceentryid,count(distinct fpackgroup) as fqty,fsourceformid,fsourceinterid from t_bcm_packorderentry 
where fpackgroup like '1%' and fid='{0}'
group by fsourceformid,fsourceinterid,fsourceentryid
) it
group by fsourceformid,fsourceinterid,fsourceentryid
";
            var dbService = this.Container.GetService<IDBService>();
            var unitConvertService = this.Container.GetService<IUnitConvertService>();
            var results = new List<KeyValuePair<string, string>>();
            var isModifySourcebill = false;//是否修改源单单据
            foreach (var dataEntity in dataEntities)
            {
                var fpackentities = dataEntity["fpackentity"] as DynamicObjectCollection;
                if (fpackentities == null || fpackentities.Count <= 0)
                {
                    continue;
                }

                var fpackentity = fpackentities.FirstOrDefault();
                var fsourceformid = Convert.ToString(fpackentity["fsourceformid"]);
                var fsourceinterid = Convert.ToString(fpackentity["fsourceinterid"]);
                var sourceEntity = sourceDatas[fsourceformid]?.FirstOrDefault(x => Convert.ToString(x["id"]) == fsourceinterid);

                if (fsourceformid.EqualsIgnoreCase("stk_inventoryverify") || fsourceformid.EqualsIgnoreCase("stk_otherstockin"))
                {
                    continue;
                }
                if (sourceEntity == null)
                {
                    //this.Result.ComplexMessage.ErrorMessages.Add($"编号为{Convert.ToString(dataEntity["fbillno"])}包装清单没有找到源单单据!");
                    continue;
                }

                //var fid = dataEntity["id"];
                //var sql = string.Format(sqlFormat, fid);
                //var dbDatas = new List<Dictionary<string, object>>();
                //using (var dataReader = dbService.ExecuteReader(this.Context, sql))
                //{
                //    while (dataReader.Read())
                //    {
                //        dbDatas.Add(new Dictionary<string, object>
                //        {
                //            { "fqty",dataReader.GetValueToString("fqty")},
                //            { "fsourceinterid",dataReader.GetValueToString("fsourceinterid")},
                //            { "fsourceentryid",dataReader.GetValueToString("fsourceentryid")}
                //        });
                //    }
                //}

                //var fentities = sourceEntity["fentity"] as DynamicObjectCollection;
                //var updateResult = false;
                //var htmlForm = metaModelService.LoadFormModel(this.Context, fsourceformid);
                //if (!fsourceformid.Equals("stk_sostockreturn"))
                //{
                //    foreach (var fentity in fentities)
                //    {
                //        var fentryid = Convert.ToString(fentity["id"]);
                //        fentity["fqty"] = 0;
                //        var dbData = dbDatas.FirstOrDefault(x => Convert.ToString(x["fsourceentryid"]) == fentryid);
                //        if (dbData == null)
                //        {
                //            continue;
                //        }
                //        if (htmlForm != null && htmlForm.GetFieldList().Where(w => w.PropertyName.Equals("fplanqty")).FirstOrDefault() != null)
                //        {
                //            var fqty = Convert.ToDecimal(dbData["fqty"]);
                //            var fplanqty = Convert.ToDecimal(fentity["fplanqty"]);
                //            if (fplanqty >= fqty)
                //            {
                //                fentity["fqty"] = fqty;
                //                updateResult = true;
                //            }

                //        }
                //        else
                //        {
                //            return null;
                //        }
                //    }

                //}
                //else
                //{
                //    updateResult = true;
                //}

                //if (updateResult == false)
                //{
                //    var qtyName = fsourceformid == "sal_returnnotice" || fsourceformid == "stk_sostockreturn" ? "实退数量" : "实收数量";
                //    var planName= fsourceformid == "sal_returnnotice" || fsourceformid == "stk_sostockreturn" ? "应退数量" : "应收数量";
                //    this.Result.ComplexMessage.ErrorMessages.Add($"反写失败,编号为{sourceEntity["fbillno"]}的{htmlForm.Caption}至少有一行{planName}大于等于{qtyName}!");
                //    continue;
                //}

                //var unitResult = unitConvertService.ConvertByBasQty(this.Context, htmlForm, new[] { sourceEntity }, OperateOption.Create());
                //if (unitResult == null || unitResult.IsSuccess == false)
                //{
                //    this.Result.ComplexMessage.ErrorMessages.Add($"反写失败,编号为{sourceEntity["fbillno"]}的{htmlForm.Caption}单位换算失败!");
                //    continue;
                //}
                var htmlform = this.Context.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, fsourceformid);
                if (htmlform != null && htmlform.GetFieldList().Where(w => w.PropertyName.Equals("fautobcinstock")).FirstOrDefault() != null)
                {
                    var fautobcinstock = Convert.ToBoolean(sourceEntity["fautobcinstock"]);
                    if (fautobcinstock)
                    {
                        if (results.Count <= 0 || results.All(x => x.Key != fsourceformid || x.Value != fsourceinterid))
                        {
                            results.Add(new KeyValuePair<string, string>(fsourceformid, fsourceinterid));
                        }
                    }
                    else
                    {
                        if (htmlform != null && htmlform.GetFieldList().Where(w => w.PropertyName.Equals("fscanstatus")).FirstOrDefault() != null)
                        {
                            sourceEntity["fscanstatus"] = "2";
                            isModifySourcebill = true;
                        }
                    }
                }
            }

            if (isModifySourcebill)
            {
                //判断是否修改了源单单据内容，没修改的情况下无需进行暂存操作
                foreach (var sourceData in sourceDatas)
                {
                    var dm = this.Container.GetService<IDataManager>();
                    var htmlForm = metaModelService.LoadFormModel(this.Context, sourceData.Key);
                    //dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                    //dm.Save(sourceData.Value);
                    //BUG#31172：调整保存方法
                    var result = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, sourceData.Value, "Draft", new Dictionary<string, object>());
                    result?.ThrowIfHasError(true, $"保存{htmlForm.Caption}失败!");
                }
            }

            return results;
        }

        /// <summary>
        /// 反写条形码回包装清单
        /// </summary>
        /// <param name="packEntityAndBarCodeMasterMapping"></param>
        /// <param name="dataEntityAndBarCodeMastersMapping"></param>
        private void writeBack(Dictionary<DynamicObject, DynamicObject> packEntityAndBarCodeMasterMapping,
                               Dictionary<DynamicObject, List<DynamicObject>> dataEntityAndBarCodeMastersMapping)
        {
            foreach (var kv in packEntityAndBarCodeMasterMapping)
            {
                kv.Key["fbarcode"] = kv.Value["fnumber"];
            }

            foreach (var kv in dataEntityAndBarCodeMastersMapping)
            {
                var fbarcodeentity = kv.Key["fbarcodeentity"] as DynamicObjectCollection;
                foreach (var barCodeEntity in kv.Value)
                {
                    var barCodeEntry = new DynamicObject(fbarcodeentity.DynamicCollectionItemPropertyType);
                    barCodeEntry["fbarcode"] = barCodeEntity["fnumber"];
                    fbarcodeentity.Add(barCodeEntry);
                }
            }
        }

        /// <summary>
        /// 创建扫描记录
        /// </summary>
        /// <param name="barCodeMasters"></param>
        /// <param name="barCodeMasterAndPackOrderMapping"></param>
        /// <param name="metaModelService"></param>
        /// <returns></returns>
        private List<DynamicObject> createScanResult(List<DynamicObject> barCodeMasters,
                                                     Dictionary<DynamicObject, DynamicObject> barCodeMasterAndPackOrderMapping,
                                                     IMetaModelService metaModelService)
        {
            var scanResults = new List<DynamicObject>();
            var scanResultForm = metaModelService.LoadFormModel(this.Context, "bcm_scanresult");

            foreach (var barCoadeMaster in barCodeMasters)
            {
                var scanResult = new DynamicObject(scanResultForm.GetDynamicObjectType(this.Context));
                scanResults.Add(scanResult);
                scanResult["fbarcode"] = barCoadeMaster["id"];
                scanResult["fbarcodetext"] = barCoadeMaster["fnumber"];
                scanResult["fstorehouseid"] = barCoadeMaster["fstorehouseid"];
                scanResult["fstorelocationid"] = barCoadeMaster["fstorelocationid"];
                //如果条码主档是可用状态，则表明源单是自动入库，则设置扫描状态为已扫描，否则设置为待扫描
                if (Convert.ToString(barCoadeMaster["fbizstatus"]) == "1")
                {
                    scanResult["fbizstatus"] = "2";
                    //var currentTime = DateTime.Now;
                    //scanResult["fdate"] = currentTime;
                    //scanResult["foperatorid"] = this.Context.UserId;
                    //scanResult["fopdatetime"] = currentTime;
                    //scanResult["fdeviceid"] = string.Empty;
                }
                else
                {
                    scanResult["fbizstatus"] = "1";
                }
                var currentTime = DateTime.Now;
                scanResult["fbizstatus"] = "2";
                scanResult["fdate"] = currentTime;
                scanResult["foperatorid"] = this.Context.UserId;
                scanResult["fopdatetime"] = currentTime;
                scanResult["fdeviceid"] = string.Empty;

                //var ftraceentity = (barCoadeMaster["ftraceentity"] as DynamicObjectCollection)?.FirstOrDefault();
                //if (ftraceentity != null)
                //{
                //    scanResult["fsourceformid"] = ftraceentity["fsourceformid"];
                //    scanResult["fsourcebillno"] = ftraceentity["fsourcebillno"];
                //    scanResult["fsourceinterid"] = ftraceentity["fsourceinterid"];
                //}
                scanResult["fsourceformid"] = barCoadeMaster["fsourcetype"];
                scanResult["fsourcebillno"] = barCoadeMaster["fsourcenumber"];
                scanResult["fsourceinterid"] = barCoadeMaster["fsourcebillid"];
                var packOrder = barCodeMasterAndPackOrderMapping[barCoadeMaster];
                scanResult["fpackbillno"] = packOrder["fbillno"];
                scanResult["fpackinterid"] = packOrder["id"];
            }
            //this.Context.SaveBizData(scanResultForm.Id, scanResults);
            //BUG#31172：调整保存方法
            var result = this.Gateway.InvokeBillOperation(this.Context, scanResultForm.Id, scanResults, "Draft", new Dictionary<string, object>());
            result?.ThrowIfHasError(true, $"保存{scanResultForm.Caption}失败!");
            return scanResults;
        }

        /// <summary>
        /// 创建条码主档
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="sourceDatas"></param>
        /// <param name="metaModelService"></param>
        private List<DynamicObject> createBarCodeMaster(DynamicObject[] dataEntities,
                                                        Dictionary<string, List<DynamicObject>> sourceDatas,
                                                        Dictionary<DynamicObject, DynamicObject> packEntityAndBarCodeMasterMapping,
                                                        Dictionary<DynamicObject, DynamicObject> barCodeMasterAndPackOrderMapping,
                                                        Dictionary<DynamicObject, List<DynamicObject>> dataEntityAndBarCodeMastersMapping,
                                                        IMetaModelService metaModelService)
        {
            var barCodeMasterForm = metaModelService.LoadFormModel(this.Context, "bcm_barcodemaster");
            var barCodeMasters = new List<DynamicObject>();
            int i = 1;
            foreach (var dataEntity in dataEntities)
            {
                var fpackentity = dataEntity["fpackentity"] as DynamicObjectCollection;
                if (fpackentity == null || fpackentity.Count <= 0)
                {
                    continue;
                }

                var barCodeMasterMappingValues = new List<DynamicObject>();
                dataEntityAndBarCodeMastersMapping.Add(dataEntity, barCodeMasterMappingValues);
                var groupDatas = fpackentity.GroupBy(x => Convert.ToString(x["fpackgroup"]));

                foreach (var groupData in groupDatas)
                {
                    //1包多件
                    if (groupData.Key.StartsWith("3"))
                    {
                        var firstItem = groupData.FirstOrDefault();
                        DynamicObject barCodeMaster = createBarCodeMaster(barCodeMasterForm, firstItem, sourceDatas, 1, 1, dataEntity, Convert.ToString(i));
                        i++;
                        var fentities = barCodeMaster["fentity"] as DynamicObjectCollection;
                        var ftraceentities = barCodeMaster["ftraceentity"] as DynamicObjectCollection;
                        var nowDate = DateTime.Now;

                        barCodeMasters.Add(barCodeMaster);
                        barCodeMasterMappingValues.Add(barCodeMaster);
                        barCodeMasterAndPackOrderMapping.Add(barCodeMaster, dataEntity);

                        foreach (var groupItem in groupData)
                        {
                            appendEntities(fentities, nowDate, groupItem);
                            appendTraceEntities(ftraceentities, nowDate, groupItem, dataEntity);
                            packEntityAndBarCodeMasterMapping.Add(groupItem, barCodeMaster);
                        }
                        continue;

                    }

                    //1件多包
                    if (groupData.Key.StartsWith("2"))
                    {
                        var packCount = groupData.Count();
                        var packIndex = 1;
                        var dd = DateTime.Now.ToString("yyMMddHHmmss") + DateTime.Now.Millisecond.ToString() + i.ToString();
                        foreach (var groupItem in groupData)
                        {
                            DynamicObject barCodeMaster = createBarCodeMaster(barCodeMasterForm, groupItem, sourceDatas, packIndex, packCount, dataEntity, dd, true);
                            var fentities = barCodeMaster["fentity"] as DynamicObjectCollection;
                            var ftraceentities = barCodeMaster["ftraceentity"] as DynamicObjectCollection;
                            var nowDate = DateTime.Now;
                            barCodeMasters.Add(barCodeMaster);
                            barCodeMasterMappingValues.Add(barCodeMaster);
                            barCodeMasterAndPackOrderMapping.Add(barCodeMaster, dataEntity);
                            appendEntities(fentities, nowDate, groupItem);
                            appendTraceEntities(ftraceentities, nowDate, groupItem, dataEntity);
                            packEntityAndBarCodeMasterMapping.Add(groupItem, barCodeMaster);
                            packIndex++;
                        }
                        i++;
                    }
                }
            }

            if (barCodeMasters != null && barCodeMasters.Count > 0)
            {
                var result = this.Gateway.InvokeBillOperation(this.Context, barCodeMasterForm.Id, barCodeMasters, "save", new Dictionary<string, object>());
                if (result.IsSuccess)
                {
                    foreach (var bb in barCodeMasters)
                    {
                        var fsuffix = Convert.ToString(bb["fsuffix"]);
                        if (!string.IsNullOrWhiteSpace(fsuffix))
                        {
                            bb["fnumber"] = Convert.ToString(bb["fsuffix"]);
                            bb["fname"] = Convert.ToString(bb["fsuffix"]);
                            bb["fsuffix"] = "";
                        }
                    }
                    //this.Context.SaveBizData(barCodeMasterForm.Id, barCodeMasters);
                    //BUG#31172：调整保存方法
                    result = this.Gateway.InvokeBillOperation(this.Context, barCodeMasterForm.Id, barCodeMasters, "Draft", new Dictionary<string, object>());
                }
                result?.ThrowIfHasError(true, $"保存{barCodeMasterForm.Caption}失败!");
            }

            return barCodeMasters;
        }

        /// <summary>
        /// 追加条码追溯明细
        /// </summary>
        /// <param name="ftraceentities"></param>
        /// <param name="nowDate"></param>
        /// <param name="groupItem"></param>
        /// <param name="packOrder"></param>
        private void appendTraceEntities(DynamicObjectCollection ftraceentities, DateTime nowDate, DynamicObject groupItem, DynamicObject packOrder)
        {
            //var fsourceformid = Convert.ToString(groupItem["fsourceformid"]);
            //var fsourceinterid = Convert.ToString(groupItem["fsourceinterid"]);
            //if (ftraceentities.Count <= 0 || ftraceentities.All(x => Convert.ToString(x["fsourceformid"]) != fsourceformid || Convert.ToString(x["fsourceinterid"]) != fsourceinterid))
            //{
            //    var ftraceentity = new DynamicObject(ftraceentities.DynamicCollectionItemPropertyType);
            //    ftraceentities.Add(ftraceentity);
            //    ftraceentity["fsourcebizdate"] = packOrder["fdate"];
            //    ftraceentity["fsourceformid"] = fsourceformid;
            //    ftraceentity["fsourcebillno"] = groupItem["fsourcebillno"];
            //    ftraceentity["fsourceinterid"] = fsourceinterid;
            //    ftraceentity["fstorehouseid"] = groupItem["fstorehouseid"];
            //    ftraceentity["fstorelocationid"] = groupItem["fstorelocationid"];
            //    //ftraceentity["fstockstatus"] = groupItem["fstockstatus"];
            //    //ftraceentity["fownertype"] = groupItem["fownertype"];
            //    //ftraceentity["fownerid"] = groupItem["fownerid"];
            //    ftraceentity["foperatorid"] = this.Context.UserId;
            //    ftraceentity["fopdatetime"] = nowDate;
            //}
        }

        /// <summary>
        /// 追加条码主档商品明细
        /// </summary>
        /// <param name="fentities"></param>
        /// <param name="nowDate"></param>
        /// <param name="groupItem"></param>
        private void appendEntities(DynamicObjectCollection fentities, DateTime nowDate, DynamicObject groupItem)
        {
            var fentity = new DynamicObject(fentities.DynamicCollectionItemPropertyType);
            fentities.Add(fentity);
            fentity["fserialno"] = groupItem["fserialno"];
            fentity["fmaterialid"] = groupItem["fmaterialid"];
            fentity["fattrinfo"] = groupItem["fattrinfo"];
            fentity["fcustomdesc"] = groupItem["fcustomdesc"];
            fentity["fstockunitid"] = groupItem["fstockunitid"];
            fentity["fstockqty"] = groupItem["fstockqty"];
            fentity["funitid"] = groupItem["funitid"];
            fentity["fqty"] = groupItem["fqty"];
            fentity["flotno"] = groupItem["flotno"];
            fentity["fmtono"] = groupItem["fmtono"];
            fentity["finstockdate"] = nowDate;
            fentity["fstockunitid"] = groupItem["fstockunitid"];
            fentity["fstockqty"] = groupItem["fstockqty"];
            fentity["fstockstatus"] = groupItem["fstockstatus"];
            fentity["fownertype"] = groupItem["fownertype"];
            fentity["fownerid"] = groupItem["fownerid"];
            fentity["fsuitcombnumber"] = groupItem["ftjno"];
            fentity["fpartscombnumber"] = groupItem["fpjno"];
            fentity["fsofacombnumber"] = groupItem["fsfno"];
            fentity["fentrynote"] = groupItem["fentrynote"];//备注
        }

        /// <summary>
        /// 生成条码主档头部信息
        /// </summary>
        /// <param name="barCodeMasterForm"></param>
        /// <param name="groupItem"></param>
        /// <param name="sourceDatas"></param>
        /// <param name="packIndex"></param>
        /// <param name="packCount"></param>
        /// <returns></returns>
        private DynamicObject createBarCodeMaster(HtmlForm barCodeMasterForm,
                                                  DynamicObject groupItem,
                                                  Dictionary<string, List<DynamicObject>> sourceDatas,
                                                  int packIndex,
                                                  int packCount,
                                                  DynamicObject dataEntity, string i,
                                                  bool suffix = false)
        {
            var barCodeMaster = new DynamicObject(barCodeMasterForm.GetDynamicObjectType(this.Context));
            barCodeMaster["fpackindex"] = packIndex;
            barCodeMaster["fpackcount"] = packCount;
            barCodeMaster["fstorehouseid"] = "";
            barCodeMaster["fstorelocationid"] = "";
            //barCodeMaster["fstockstatus"] = groupItem["fstockstatus"];
            //barCodeMaster["fownertype"] = groupItem["fownertype"];
            //barCodeMaster["fownerid"] = groupItem["fownerid"];
            barCodeMaster["fpackgroup"] = groupItem["fpackgroup"];
            //var mat = groupItem["fmaterialid_ref"] as DynamicObject;
            barCodeMaster["fpackagtype"] = groupItem["fpacktype"];
            barCodeMaster["fpackcount"] = groupItem["fallpacknum"];
            barCodeMaster["fpackindex"] = groupItem["fpackno"];
            barCodeMaster["fsourcelinenumber"] = groupItem["fsourcelinenumber"];
            barCodeMaster["fstorehouseid"] = groupItem["fstorehouseid"];
            barCodeMaster["fstorelocationid"] = groupItem["fstorelocationid"];
            //22456 【WMS】打码生成条码主档未判断来源单据，业务类型未对应
            /*
             采购订单，其他入库单，销售退货单，打码生成时【业务类型】=待入库
                盘点单，【业务类型】=可用
             */
            var fsourcetype = Convert.ToString(dataEntity["fsourcetype"]);
            switch (fsourcetype.ToLower().Trim())
            {
                case "stk_inventoryverify":
                    barCodeMaster["fbizstatus"] = "1";
                    break;
                default:
                    barCodeMaster["fbizstatus"] = "4";
                    break;
            }

            barCodeMaster["fsourcetype"] = dataEntity["fsourcetype"];
            barCodeMaster["fsourcenumber"] = dataEntity["fsourcenumber"];
            barCodeMaster["fsourcelinenumber"] = groupItem["fsourcelinenumber"];
            barCodeMaster["fsourceentryid"] = groupItem["fsourceentryid"];
            if (suffix)
            {
                if (packCount == 1)
                    barCodeMaster["fsuffix"] = "TM" + i + $"-{Convert.ToString(groupItem["fpackno"])}";
                else
                    barCodeMaster["fsuffix"] = "TM" + i + $"-{Convert.ToString(groupItem["fpackno"])}/{Convert.ToString(groupItem["fallpacknum"])}";
            }
            else
                barCodeMaster["fsuffix"] = "TM" + DateTime.Now.ToString("yyMMddHHmmss") + DateTime.Now.Millisecond + i;

            var fsourceformid = Convert.ToString(groupItem["fsourceformid"]);
            var fsourceinterid = Convert.ToString(groupItem["fsourceinterid"]);
            var sourceEntity = sourceDatas[fsourceformid]?.FirstOrDefault(x => Convert.ToString(x["id"]) == fsourceinterid);
            //var fautobcinstock = false;

            //if (sourceEntity != null)
            //{
            //    var htmlform = this.Context.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, fsourceformid);
            //    if (htmlform!=null && htmlform.GetFieldList().Where(w=> w.PropertyName.Equals("fautobcinstock")).FirstOrDefault() != null)
            //        fautobcinstock = Convert.ToBoolean(sourceEntity["fautobcinstock"]);
            //    if (htmlform.Id.EqualsIgnoreCase("stk_inventoryverify"))
            //        fautobcinstock = true;
            //}
            //如果是源单设置自动入库则将状态设置为可用，否则设置为待入库
            //barCodeMaster["fbizstatus"] = fautobcinstock ? "1" : "4";

            barCodeMaster["frecnum"] = groupItem["freceptionno"];
            barCodeMaster["finisrcformid"] = groupItem["finisrcformid"];
            barCodeMaster["finisrcbillno"] = groupItem["finisrcbillno"];
            barCodeMaster["finisrcinterid"] = groupItem["finisrcinterid"];
            barCodeMaster["finisrcentryid"] = groupItem["finisrcentryid"];
            barCodeMaster["finisrcseq"] = groupItem["finisrcseq"];
            return barCodeMaster;
        }

        /// <summary>
        /// 获取源单数据
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="metaModelService"></param>
        /// <returns></returns>
        private Dictionary<string, List<DynamicObject>> getSourceEntities(DynamicObject[] dataEntities, IMetaModelService metaModelService)
        {
            var groupDatas = dataEntities.SelectMany(x => (x["fpackentity"] as DynamicObjectCollection).Select(y => new
            {
                fsourceformid = Convert.ToString(y["fsourceformid"]),
                fsourceinterid = Convert.ToString(y["fsourceinterid"])
            })).GroupBy(x => x.fsourceformid).ToList();

            if (groupDatas == null || groupDatas.Count <= 0)
            {
                return null;
            }

            var results = new Dictionary<string, List<DynamicObject>>();
            foreach (var groupData in groupDatas)
            {
                var htmlForm = metaModelService.LoadFormModel(this.Context, groupData.Key);
                var ids = groupData.Select(x => x.fsourceinterid).Distinct().ToList();
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

                var sourceEntities = dm.Select(ids).OfType<DynamicObject>().ToList();

                foreach (var sourceEntity in sourceEntities)
                {
                    if (Convert.ToString(sourceEntity["fstatus"]) != "D" && !htmlForm.Id.Equals("ydj_purchaseorder") && !htmlForm.Id.Equals("stk_inventoryverify") && !htmlForm.Id.Equals("bcm_receptionscantask"))
                    {
                        throw new BusinessException($"编号为{sourceEntity["fbillno"]}的{htmlForm.Caption}不是已提交状态!");
                    }
                }
                results.Add(htmlForm.Id, sourceEntities);
            }
            return results;
        }

        /// <summary>
        /// 创建收货扫描任务
        /// </summary>
        /// <param name="datas"></param>
        private void CreateRecTask(DynamicObject[] datas)
        {
            var recTaskForm = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "bcm_receptionscantask");
            var newrecTasks = new List<DynamicObject>();
            foreach (var dd in datas)
            {
                var fpackbiztype = Convert.ToString(dd["fpackbiztype"]);
                if (!fpackbiztype.EqualsIgnoreCase("1"))
                {
                    continue;
                }
                var newrecTask = new DynamicObject(recTaskForm.GetDynamicObjectType(this.Context));
                var fsourcetype = Convert.ToString(dd["fsourcetype"]);
                switch (fsourcetype)
                {
                    case "stk_sostockreturn"://销售退货单
                        newrecTask["ftask_type"] = "stk_sostockreturn";
                        newrecTask["freceptionno"] = Convert.ToString(dd["fsourcenumber"]);
                        newrecTask["flogisticsno"] = Convert.ToString(dd["fdeliverybillno"]);
                        break;
                    case "stk_otherstockin"://其他入库单
                        newrecTask["ftask_type"] = "stk_otherstockin";
                        newrecTask["freceptionno"] = Convert.ToString(dd["fsourcenumber"]);
                        newrecTask["flogisticsno"] = Convert.ToString(dd["fdeliverybillno"]);
                        break;
                    case "ydj_purchaseorder"://采购订单
                        newrecTask["ftask_type"] = "stk_postockin";
                        break;
                    case "bcm_receptionscantask"://收货扫描任务
                        continue;
                        break;
                }
                newrecTask["ftaskdate"] = DateTime.Now;
                newrecTask["fsourcetype"] = fsourcetype;
                newrecTask["fsourcenumber"] = Convert.ToString(dd["fsourcenumber"]);
                if (fsourcetype.Equals("stk_sostockreturn") || fsourcetype.Equals("stk_otherstockin"))
                {
                    newrecTask["fexistednetherbill"] = true;
                }
                else
                {
                    newrecTask["fexistednetherbill"] = false;
                }

                newrecTask["ftaskstatus"] = "ftaskstatus_01";
                var fen = dd["fentity"] as DynamicObjectCollection;
                if (fen != null && fen.Any())
                {
                    var recTaskFens = newrecTask["ftaskentity"] as DynamicObjectCollection;
                    foreach (var ff in fen)
                    {
                        var newFen = recTaskFens.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        newFen["fmaterialid"] = ff["fmaterialid"];
                        newFen["fattrinfo"] = ff["fattrinfo"];
                        newFen["fcustomdesc"] = ff["fcustomdesc"];
                        var fpacktype = Convert.ToString(ff["fpacktype"]);
                        newFen["fpackagingtype"] = fpacktype;
                        newFen["fpackagingqty"] = ff["fpackcount"];
                        newFen["fbizunitid"] = ff["fbizunitid"];
                        newFen["fwaitworkqty"] = ff["fbizpackedqty"];
                        newFen["fworkedqty"] = 0;
                        switch (fpacktype)
                        {
                            case "1":
                                //1.	如果【打包类型】=”标准”, 则获取【可包装数量】赋值到【待扫描包数】
                                newFen["fwaitscanqty"] = ff["fbizpackedqty"];
                                break;
                            case "2":
                                //2.	如果【打包类型】=”1件多包”, 则将【可包装数量】*【包数/件数】赋值到【待扫描包数】
                                newFen["fwaitscanqty"] = Convert.ToDecimal(ff["fbizpackedqty"]) * Convert.ToDecimal(ff["fpackcount"]);
                                break;
                            case "3":
                                //3.	如果【打包类型】=”1包多件”, 则将【可包装数量】/【包数/件数】赋值到【待扫描包数】
                                newFen["fwaitscanqty"] = Convert.ToDecimal(ff["fbizpackedqty"]) / Convert.ToDecimal(ff["fpackcount"]);
                                break;
                        }
                        newFen["fscannedqty"] = 0;
                        newFen["flotno"] = ff["flotno"];
                        newFen["fmtono"] = ff["fmtono"];
                        newFen["fownertype"] = ff["fownertype"];

                        newFen["fownerid"] = ff["fownerid"];
                        newFen["fsuitegroupid"] = ff["ftjno"];
                        newFen["fcategorygroupid"] = ff["fpjno"];
                        newFen["fsafagroupid"] = ff["fsfno"];
                        newFen["fqty"] = ff["fpackedqty"];
                        newFen["funitid"] = ff["funitid"];
                        //newFen["fsourceformid"] = ff["fsourceformid"];
                        //newFen["fsourcebillno"] = ff["fsourcebillno"];
                        //newFen["fsourceentryid"] = ff["fsourceentryid"];
                        //newFen["fsourceinterid"] = ff["fsourceseq"];
                        //newFen["fsourcefid"] = ff["fsourceinterid"];
                        //newFen["flinkformid"] = "stk_sostockreturn";
                        //newFen["flinkbillno"] = dd["fbillno"];
                        //newFen["flinkrowinterid"] = ff["id"];
                        newFen["fsourceformid"] = ff["fsourceformid"];
                        newFen["fsourcebillno"] = ff["fsourcebillno"];
                        newFen["fsourceentryid"] = ff["fsourceentryid"];
                        newFen["fsourceinterid"] = ff["fsourceseq"];
                        newFen["fsourcefid"] = ff["fsourceinterid"];
                        newFen["flinkformid"] = ff["fsourceformid"];
                        newFen["flinkbillno"] = ff["fsourcebillno"];
                        newFen["flinkrowinterid"] = ff["fsourceentryid"];
                        //本次作业数量=待作业数量（即：已包装数量）
                        newFen["fcurrworkqty"] = ff["fbizpackedqty"];
                        if (fsourcetype == "stk_sostockreturn" || fsourcetype == "stk_otherstockin")
                        {
                            newFen["fstorehouseid"] = ff["fstorehouseid"];//仓库
                            newFen["fstorelocationid"] = ff["fstorelocationid"];//仓位
                        }
                        else if (fsourcetype == "ydj_purchaseorder")
                        {
                            //采购订单：携带【供货方订单号】
                            var purObj = this.Context.LoadBizDataById("ydj_purchaseorder", Convert.ToString(ff["fsourceinterid"]));
                            if (purObj != null)
                            {
                                newFen["fsupplierorderno"] = purObj["fsupplierorderno"];
                                var purEntrys = purObj["fentity"] as DynamicObjectCollection;
                                if (purEntrys != null && purEntrys.Any())
                                {
                                    var sourceRow = purEntrys.Where(t => Convert.ToString(t["id"]).EqualsIgnoreCase(Convert.ToString(ff["fsourceentryid"]))).FirstOrDefault();
                                    if (sourceRow != null)
                                    {
                                        var nowqty = Convert.ToDecimal(ff["fpackedqty"]);
                                        var dealPrice = Convert.ToDecimal(sourceRow["fdealprice"]);
                                        var nowamount = dealPrice * nowqty;
                                        newFen["fprice"] = dealPrice;//成交单价
                                        newFen["famount"] = nowamount;//成交金额
                                    }
                                }
                            }
                        }
                        recTaskFens.Add(newFen);
                    }
                }
                else
                {
                    continue;
                }
                newrecTasks.Add(newrecTask);
            }
            var getway = this.Context.Container.GetService<IHttpServiceInvoker>();
            if (newrecTasks.Any())
            {
                //走通用保存插件 目的是触发流程状态更新。
                var bizsaveres = getway.InvokeBillOperation(this.Context, "bcm_receptionscantask", newrecTasks, "save", null);
                if (bizsaveres != null && !bizsaveres.IsSuccess)
                {
                    throw new Exception($"保存{this.HtmlForm.Caption}失败:" + string.Join("", bizsaveres.ComplexMessage.ErrorMessages));
                }
            }
            //this.Context.SaveBizData("bcm_receptionscantask", newrecTasks);
        }

        /// <summary>
        /// 业务类型为【调拨包装】的包装清单找出其源单库存调拨单生成调入扫描任务
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (e.DataEntitys == null) return;
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bcm_transferintask");
            var newBill = htmlForm.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
            var fsourcenumbers = e.DataEntitys.Select(o=>o["fsourcenumber"]?.ToString());
            var inventorytransferObjs = this.Context.LoadBizDataByFilter("stk_inventorytransfer", "fbillno in ('{0}')".Fmt(string.Join("','", fsourcenumbers)));
            foreach (var item in e.DataEntitys)
            {
                #region 生成调入扫描任务
                if (item["fpackbiztype"]?.ToString() != "3")//调拨包装
                {
                    continue;
                }
                var inventorytransferObj = inventorytransferObjs.FirstOrDefault(o => o["fbillno"]?.ToString() == item["fsourcenumber"]?.ToString());
                if (inventorytransferObj == null)
                {
                    continue;
                }
                newBill["ftask_type"] = "transferin";//单据头. 任务类型	默认为”调拨入库”
               
                newBill["ftaskdate"] = DateTime.Now;//单据头. 业务日期	默认为当前日期
                newBill["fexistednetherbill"] = "1";//单据头. 已存在下游库存单据	默认勾选
                newBill["ftaskbillno"] = inventorytransferObj["fbillno"];//单据头. 调拨单号
                newBill["fsourcenumber"] = inventorytransferObj["fbillno"];//单据头. 源单编号
                newBill["fsourcetype"] = "stk_inventorytransfer";//单据头. 源单类型
                newBill["fdescription"] = $"库存调拨单【{Convert.ToString(inventorytransferObj["fbillno"])}】生成调入扫描任务";//备注
                var srcEntitys = inventorytransferObj["fentity"] as DynamicObjectCollection;

                ////如果《库存调拨单》单据体-调拨明细商品存在【调入数量】时, 【任务状态】=”已完成”, 否则默认为”待作业”
                //int cnt = srcEntitys.Where(o => Convert.ToDecimal(o["fstockinqty"]) > 0).Count();
                //if (cnt > 0)
                //{
                //    newBill["ftaskstatus"] = "ftaskstatus_04";//单据头. 任务状态	”已完成”
                //}
                //else
                //{
                //    newBill["ftaskstatus"] = "ftaskstatus_01";//单据头. 任务状态	默认为”待作业”
                //}
                newBill["ftaskstatus"] = "ftaskstatus_01";//单据头. 任务状态	默认为”待作业”
                var newTaskEntitys = newBill["ftaskentity"] as DynamicObjectCollection;
                foreach (var secEn in srcEntitys)
                {
                    var newTaskEntity = newTaskEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    newTaskEntity["fmaterialid"] = secEn["fmaterialid"];//单据体. 扫描任务明细. 商品名称	《库存调拨单》单据体-商品明细【商品名称】
                    newTaskEntity["fattrinfo"] = secEn["fattrinfoto"];//单据体. 扫描任务明细. 辅助属性	《库存调拨单》单据体-商品明细【调入辅助属性】
                    newTaskEntity["fcustomdesc"] = secEn["fcallupcustomdescto"];//单据体. 扫描任务明细. 定制说明	《库存调拨单》单据体-商品明细【调入定制说明】
                    newTaskEntity["fbizunitid"] = secEn["fbizunitid"];//单据体. 扫描任务明细. 单位	《库存调拨单》单据体-商品明细【调拨单位】
                    newTaskEntity["fwaitworkqty"] = secEn["fstockoutqty"];//单据体. 扫描任务明细. 待作业数量	《库存调拨单》单据体-商品明细【调出数量】
                    newTaskEntity["fworkedqty"] = 0;//单据体. 扫描任务明细. 已作业数量	默认为0
                    newTaskEntity["fscannedqty"] = 0;//单据体. 扫描任务明细. 已扫描包数	默认为0
                    newTaskEntity["fstorehouseid"] = secEn["fstorehouseidto"];//单据体. 仓库. 《库存调拨单》单据体-商品明细【调入仓库】
                    newTaskEntity["fstorelocationid"] = secEn["fstorelocationidto"];//单据体. 《库存调拨单》单据体-商品明细【调入库位】
                    newTaskEntity["flotno"] = secEn["flotno"];//单据体. 扫描任务明细. 批号	《库存调拨单》单据体-商品明细【批号】
                    newTaskEntity["fmtono"] = secEn["fmtonoto"];//单据体. 扫描任务明细. 物流跟踪号	《库存调拨单》单据体-商品明细【调入物流跟踪号】
                    newTaskEntity["fownertype"] = secEn["fownertypeto"];//单据体. 扫描任务明细. 货主类型	《库存调拨单》单据体-商品明细【调入货主类型】
                    newTaskEntity["fownerid"] = secEn["fowneridto"];//单据体. 扫描任务明细. 货主	《库存调拨单》单据体-商品明细【调入货主】
                    newTaskEntity["fqty"] = secEn["fqty"];//单据体. 扫描任务明细. 基本单位数量	《库存调拨单》单据体-商品明细【基本单位数量】
                    newTaskEntity["funitid"] = secEn["funitid"];//单据体. 扫描任务明细. 基本单位	《库存调拨单》单据体-商品明细【基本单位】
                    newTaskEntity["flinkformid"] = "stk_inventorytransfer";//单据体. 扫描任务明细. 关联单据	默认为”库存调拨单”
                    newTaskEntity["flinkbillno"] = inventorytransferObj["fbillno"];//单据体. 扫描任务明细. 关联单据编号	《库存调拨单》表头【单据编号】
                    newTaskEntity["flinkrownumber"] = secEn["fseq"];//单据体. 扫描任务明细. 关联单行号	《库存调拨单》单据体-商品明细 的 行号
                    newTaskEntity["flinkrowinterid"] = secEn["id"];//单据体. 扫描任务明细. 关联单行内码	《库存调拨单》单据体-商品明细 的 行内码
                    newTaskEntity["fsourceformid"] = "stk_inventorytransfer";//单据体. 扫描任务明细. 来源单据	默认为”库存调拨单”
                    newTaskEntity["fsourcebillno"] = inventorytransferObj["fbillno"]; ;//单据体. 扫描任务明细. 来源单据编号	《库存调拨单》表头【单据编号】
                    newTaskEntity["fsourceinterid"] = secEn["fseq"];//单据体. 扫描任务明细. 来源单行号	《库存调拨单》单据体-商品明细 的 行号
                    newTaskEntity["fsourceentryid"] = secEn["id"];//单据体. 扫描任务明细. 来源单行内码	《库存调拨单》单据体-商品明细 的 行内码
                    newTaskEntitys.Add(newTaskEntity);
                }
                this.Context.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, newBill.DynamicObjectType, newBill, false);
                var taskEntitys = newBill["ftaskentity"] as DynamicObjectCollection;
                foreach (var taskEn in taskEntitys)
                {
                    var mat = taskEn["fmaterialid_ref"] as DynamicObject;
                    if (mat == null)
                        continue;
                    var fpackagingqty = 0;//包数/件数
                    var fpackagtype = Convert.ToString(mat["fpackagtype"]);//'1':'标准','2':'1件多包','3':'1包多件'
                    switch (fpackagtype)
                    {
                        case "1":
                            fpackagingqty = 1;
                            taskEn["fwaitscanqty"] = Convert.ToInt32(taskEn["fwaitworkqty"]) * fpackagingqty;
                            break;
                        case "2":
                            fpackagingqty = Convert.ToInt32(mat["fbag"]);
                            taskEn["fwaitscanqty"] = Convert.ToInt32(taskEn["fwaitworkqty"]) * fpackagingqty;
                            break;
                        case "3":
                            fpackagingqty = Convert.ToInt32(mat["fpiece"]);
                            taskEn["fwaitscanqty"] = Convert.ToInt32(taskEn["fwaitworkqty"]) / fpackagingqty;
                            break;
                    }
                    taskEn["fpackagingtype"] = fpackagtype;//单据体. 扫描任务明细. 打包类型	如果商品对应《商品》基础资料的单据头-包装信息【打包类型】不为空时, 用该字段赋值【打包类型】, 否则默认为空
                    taskEn["fpackagingqty"] = fpackagingqty;//单据体. 扫描任务明细. 包数/件数	1.	如果商品对应《商品》基础资料的单据头-包装信息【打包类型】=”1件多包”时, 用【包】赋值【包数/件数】 2.如果商品对应《商品》基础资料的单据头 - 包装信息【打包类型】=”1包多件”时, 用【件】赋值【包数 / 件数】 3.如果商品对应《商品》基础资料的单据头 - 包装信息【打包类型】=”标准”时, 默认【包数 / 件数】= 1 4.如果商品对应《商品》基础资料的单据头 - 包装信息【打包类型】为空时,则默认【包数 / 件数】为空
                                                            //taskEn["fwaitscanqty"] = Convert.ToInt32(taskEn["fwaitworkqty"]) * fpackagingqty;//单据体. 扫描任务明细. 待扫描包数	根据《调拨入库任务》的【打包类型】+【包数/件数】进行计算 1.如果【打包类型】=”标准”, 则获取《库存调拨单》单据体 - 商品明细【应退数量】赋值到【待扫描包数】 2.如果【打包类型】=”1件多包”, 则将《库存调拨单》单据体 - 商品明细【应退数量】*【包数 / 件数】赋值到【待扫描包数】 3.如果【打包类型】=”1包多件”, 则将《库存调拨单》单据体 - 商品明细【应退数量】/【包数 / 件数】赋值到【待扫描包数】 4.如果【打包类型】为空, 则默认【包数 / 件数】为0
                }
                var saveRet = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, new[] { newBill }, "save", null);
                this.Result.IsSuccess = saveRet.IsSuccess;
                this.Result.ComplexMessage.SuccessMessages.AddRange(saveRet.ComplexMessage.SuccessMessages);
                this.Result.ComplexMessage.ErrorMessages.AddRange(saveRet.ComplexMessage.ErrorMessages);
                #endregion
            }
        }
    }
}
