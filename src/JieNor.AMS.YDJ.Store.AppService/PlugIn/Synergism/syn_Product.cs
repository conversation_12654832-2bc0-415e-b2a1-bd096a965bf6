using JieNor.Framework;
using JieNor.Framework.Interface;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject;
using System.IO;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.CustomException;
using System;
using Newtonsoft.Json;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Synergism
{


    #region 下载平台商品
    [InjectService]
    [FormId("coo_product")]
    [OperationNo("opdownload")]
    public class GetEISProductDownLoad : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            string param = this.GetQueryOrSimpleParam<string>("id");
            if (param.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("请选择您要下载的商品");
            }
            #region 获取eis站点里的数据
            var gateWay = this.Container.GetService<IHttpServiceInvoker>();
            DynamicDTOWrapper dtoBill = new CommonBillDTO()
            {
                FormId = "syn_product",
                OperationNo = "getSynProductDownLoadData",
                SimpleData = new Dictionary<string, string>() { { "id", param }, { "company", this.Context.Company } }
            };
            var resp = gateWay.Invoke(this.Context, TargetSEP.EisService, dtoBill);
            Dictionary<string, Tuple<Dictionary<string, string>, string>> list = new Dictionary<string, Tuple<Dictionary<string, string>, string>>();
            if (resp is DynamicDTOResponse)
            {
                var Srv = (resp as DynamicDTOResponse).OperationResult.SrvData as string;
                list = Srv?.FromJson<Dictionary<string, Tuple<Dictionary<string, string>, string>>>() ?? new Dictionary<string, Tuple<Dictionary<string, string>, string>>();
            }

            if (list.Count == 0)
            {
                return;
            }

            #endregion
            #region 查询已协同过的商品
            var cooProductMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_product");
            var localProductMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_product");

            //远程站点所有产品id
            string[] remoteProductIds = list
                .Where(t => t.Value.Item1 != null && t.Value.Item1.ContainsKey("fproductid"))
                .Select(t => t.Value.Item1["fproductid"])
                .OfType<string>()
                .ToArray();
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, cooProductMeta.GetDynamicObjectType(this.Context));
            var cooPkIdReader = this.Context.GetPkIdDataReader(cooProductMeta, $"fremoteproductid in('{string.Join("','", remoteProductIds)}') ", new SqlParam[] { });
            List<DynamicObject> localCooProductObjs = dm.SelectBy(cooPkIdReader).OfType<DynamicObject>().ToList();

            var dmpro = this.GetDataManager();
            dmpro.InitDbContext(this.Context, localProductMeta.GetDynamicObjectType(this.Context));
            var localPkIdReader = this.Context.GetPkIdDataReader(localProductMeta, $"fsynid in('{string.Join("','", remoteProductIds)}')", new SqlParam[] { });
            List<DynamicObject> localProductObjs = dmpro.SelectBy(localPkIdReader).OfType<DynamicObject>().ToList();
            #endregion

            List<DynamicObject> localCooProductObjList = new List<DynamicObject>();
            List<DynamicObject> localProductObjList = new List<DynamicObject>();
            Dictionary<string, Dictionary<string, object>> dctEISDownLogData = new Dictionary<string, Dictionary<string, object>>();


            var seqSvc = this.Container.GetService<ISequenceService>();
            //商品协同处理结果
            Dictionary<string, Tuple<bool, string>> dctDownResult = new Dictionary<string, Tuple<bool, string>>();
            foreach (var item in list)
            {
                //不适合下载的直接生成结果后继续处理下一个
                if (!item.Value.Item2.IsNullOrEmptyOrWhiteSpace())
                {
                    dctDownResult[item.Key] = Tuple.Create(false, item.Value.Item2);
                    continue;
                }
                string remoteProductId = item.Value.Item1["fproductid"];


                #region 打包本地商品数据
                var pro = localProductObjs.FirstOrDefault(t => Convert.ToString(t["fsynid"]) == remoteProductId);
                if (pro == null)
                {
                    pro = new DynamicObject(localProductMeta.GetDynamicObjectType(this.Context));
                    pro["id"] = seqSvc.GetSequence<string>();
                    pro["fsynid"] = remoteProductId;
                    pro["fdataorigin"] = "协同生成";
                }
                pro["fname"] = item.Value.Item1["fname"];
                pro["fimage"] = item.Value.Item1["fimageid"];
                pro["fsendstatus"] = "未发布";
                pro["fcompany"] = item.Value.Item1["fpublishcompany"];
                pro["fcontent"] = item.Value.Item1["fcontent"];
                pro["fiscontent"] = item.Value.Item1["fcontent"]?.Length > 0;

                #endregion
                localProductObjList.Add(pro);

                #region 打包本地协同商品数据
                var synpro = localCooProductObjs.FirstOrDefault(t => Convert.ToString(t["fremoteproductid"]) == remoteProductId);
                if (synpro == null)
                {
                    synpro = new DynamicObject(cooProductMeta.GetDynamicObjectType(this.Context));
                    synpro["id"] = seqSvc.GetSequence<string>();
                    synpro["fremoteproductid"] = remoteProductId;
                }
                synpro["fimage"] = item.Value.Item1["fimage"];
                synpro["fname"] = item.Value.Item1["fname"];
                synpro["fcategory"] = item.Value.Item1["fcategory"];
                synpro["funit"] = item.Value.Item1["funit"];
                synpro["fbrand"] = item.Value.Item1["fbrand"];
                synpro["fpublishcompany"] = item.Value.Item1["fpublishcompany"];
                synpro["fpublishcompanyid"] = item.Value.Item1["fpublishcompanyid"];
                synpro["fpublishstatus"] = item.Value.Item1["fpublishstatus"];
                synpro["fpublishdate"] = Convert.ToDateTime(item.Value.Item1["fpublishdate"]);
                synpro["fcontent"] = item.Value.Item1["fcontent"];
                synpro["flocalproductid"] = pro["id"];
                synpro["flocalproduct"] = pro["fname"];

                #endregion
                localCooProductObjList.Add(synpro);

                #region 打包EIS协同商品的下载记录
                Dictionary<string, object> entrydata = new Dictionary<string, object>();
                if (item.Value.Item1["fentryid"].IsNullOrEmptyOrWhiteSpace() || item.Value.Item1["fentryid"] == "null")
                {

                }
                else
                {
                    entrydata.Add("id", item.Value.Item1["fentryid"]);
                }
                entrydata.Add("fcustomerid", this.Context.Company);
                entrydata.Add("flocalproname", pro["fname"]);
                entrydata.Add("flocalproid", pro["id"]);
                entrydata.Add("fcustomername", this.Context.Companys.Where(t => t.CompanyId == this.Context.Company).FirstOrDefault().CompanyName);
                entrydata.Add("fgetdate", DateTime.Now);
                entrydata.Add("freceivestatus", "已下载");

                Dictionary<string, object> data = new Dictionary<string, object>();
                if (!dctEISDownLogData.TryGetValue(item.Value.Item1["fid"], out data))
                {
                    data = new Dictionary<string, object>();
                    dctEISDownLogData[item.Value.Item1["fid"]] = data;
                }
                data.Add("id", item.Value.Item1["fid"]);
                object downEntryData = null;
                if (!data.TryGetValue("fproductentry", out downEntryData))
                {
                    downEntryData = new List<Dictionary<string, object>>();
                    data["fproductentry"] = downEntryData;
                }
                (downEntryData as List<Dictionary<string, object>>).Add(entrydata);
                #endregion

                dctDownResult[item.Key] = Tuple.Create(true, "下载成功");
            }
            //保存本地商品与协同商品
            var pre = this.Container.GetService<IPrepareSaveDataService>();
            pre.PrepareDataEntity(this.Context, cooProductMeta, localCooProductObjList.ToArray(), this.Option);
            dm.Save(localCooProductObjList);
            pre.PrepareDataEntity(this.Context, localProductMeta, localProductObjList.ToArray(), this.Option);
            dmpro.Save(localProductObjList);

            //回传商品下载结果给前端
            this.Result.SrvData = dctDownResult;

            var ToEIS = new CommonBillDTO()
                .SetBillData(dctEISDownLogData.Values)
                .SetSyncDataMode((int)(Enu_SyncDataMode.AddEntry | Enu_SyncDataMode.EditEntry))
                .SetFormId("syn_product")
                .SetOperationNo("save")
                //.SetSimpleData("downlist", dctEISDownLogData.Values.ToJson())
                //.SetOptionFlag((long)Enu_OpFlags.RequestToReply)
                .Runbackground();
            var synResult = gateWay.Invoke(this.Context, TargetSEP.EisService, ToEIS);
        }
    }

    #endregion

    #region 获取未协同的协同商品
    [InjectService]
    [FormId("coo_product")]
    [OperationNo("getSynProduct")]
    public class getsynProductBy : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            List<Dictionary<string, string>> list = new List<Dictionary<string, string>>();
            using (var reader = this.DBService.ExecuteReader(this.Context, $"select fid, fname from t_coo_product where flocalproductid =' ' and fmainorgid='{this.Context.Company}'"))
            {
                while (reader.Read())
                {
                    Dictionary<string, string> model = new Dictionary<string, string>();
                    model.Add("fid", reader["fid"] as string);
                    model.Add("fname", reader["fname"] as string);
                    list.Add(model);
                }
            }
            this.Result.SrvData = list;
        }
    }
    #endregion
}
