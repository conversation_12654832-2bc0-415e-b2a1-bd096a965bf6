using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PromotionScheme
{
    /// <summary>
    /// 促销活动：编辑
    /// </summary>
    [InjectService]
    [FormId("bas_promotionscheme")]
    [OperationNo("refresh")]
    public class Refresh : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }
            if (this.Context.BizOrgId == this.Context.TopCompanyId)
            {
                return;
            }
            var pros = this.Context.LoadBizDataByFilter("bas_deliver", string.Format(" fforbidstatus = 0 and fagentid='{0}'", this.Context.BizOrgId));
            List<DynamicObject> removeitem1 = new List<DynamicObject>();
            var rangeentry = e.DataEntitys[0]["frangeentry"] as DynamicObjectCollection;

            foreach (var item in rangeentry)
            {
                if (string.IsNullOrWhiteSpace(Convert.ToString(item["fdeliverid"]))) continue;
                if (!pros.Any(a => a["id"].Equals(item["fdeliverid"])))
                {//不存在，移除行
                    removeitem1.Add(item);
                }
            }
            removeitem1.ForEach(a => rangeentry.Remove(a));
        }
    }
}

