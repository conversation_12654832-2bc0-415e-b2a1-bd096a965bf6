using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity; 

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：审核
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);            
            e.Rules.Add(new ValidationOrgOperation("审核"));
        }
         
    }
}