using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using Newtonsoft.Json.Linq;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity;
using Senparc.Weixin.Work.Entities;
using System.Threading;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fnumber"]).NotEmpty().WithMessage("编码不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fname"]).NotEmpty().WithMessage("名称不能为空！"));

            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    // 慕思同步跳过此判断
            //    this.Option.TryGetVariableValue("IsMuSiSync", out bool isMuSiSync);
            //    if (isMuSiSync) return true;

            //    if (newData["fimage"].IsNullOrEmptyOrWhiteSpace())
            //    {
            //        return false;
            //    }
            //    return true;
            //}).WithMessage("至少上传一张商品主图！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToBoolean(newData["fispresetprop"])
                    && newData["fselcategoryid"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("【允许选配】勾选时，选配类别不能为空！"));

            //检查商品是否是套件商品，如果是套件商品，则【选配套件】字段必须勾选
            var suiteRule = this.Container.GetValidRuleService(YDJHtmlElementType.HtmlValidator_ProductSuiteValidation);
            if (suiteRule != null)
            {
                suiteRule.Initialize(this.Context, "");
                e.Rules.Add(suiteRule);
            }

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entrys = newData["funitentry"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    if (Convert.ToString(entry["fcvttype"]).EqualsIgnoreCase("1") && Convert.ToDecimal(entry["fcvtrate"]) <= 0)
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("单位明细中换算类型为固定时，换算率必须大于0！"));

            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((n, o) =>
            {
                var fisbarcode = Convert.ToBoolean(n["fisbarcode"]);
                if (fisbarcode)
                {
                    var fpackagtype = Convert.ToString(n["fpackagtype"]);
                    var fname = Convert.ToString(n["fname"]);
                    if (string.IsNullOrWhiteSpace(fpackagtype))
                    {
                        errorMessage = $"商品{fname}启用条码, 【打包类型】不允许为空!";
                        return false;
                    }
                    var fpackages = n["fpackage"] as DynamicObjectCollection;
                    var fbag = Convert.ToInt32(n["fbag"]);

                    //var fpacknumbers = fpackages.Select(x => (string)x["fpacknumber"]).ToList();
                    //var repeat = fpacknumbers.GroupBy(x => x).Where(x => x.Count() > 1).Select(x => x.Key).ToList();
                    //if (repeat.Count > 0)
                    //{
                    //    errorMessage = $"商品{fname}包装明细的【包件序号】{repeat[0]}出现重复, 不允许保存!";
                    //    return false;
                    //}

                    switch (fpackagtype)
                    {
                        case "2":
                            if (fbag <= 1)
                            {
                                errorMessage = $"商品{fname}启用条码且打包类型为1件多包, 包数不允许为空或填写数量为1 !";
                                return false;
                            }
                            //if (fbag != fpackages.Count)
                            //{
                            //    errorMessage = $"商品{fname}为1件多包, 设置的包数与包装明细的行数不相等, 不允许保存!";
                            //    return false;
                            //}
                            break;
                        case "3":
                            var fpiece = Convert.ToInt32(n["fpiece"]);
                            if (fpiece <= 1)
                            {
                                errorMessage = $"商品{fname}启用条码且打包类型为1包多件, 件数不允许为空或填写数量为1 !";
                                return false;
                            }
                            break;
                        default:
                            break;
                    }

                    //foreach (var item in fpacknumbers)
                    //{
                    //    if (item.IsNullOrEmpty()) continue;
                    //    if (item.ToCharArray().Count(x => x == '/') != 1 || item.Length < 3)
                    //    {
                    //        errorMessage = $"商品{fname}包装明细的【包件序号】未按照”流水号”+”斜杠”+”总包数”进行填写, 不允许保存!";
                    //        return false;
                    //    }
                    //    var startNum = item.Substring(0, item.IndexOf("/"));
                    //    var endNum = item.Substring(item.IndexOf("/") + 1);
                    //    if (endNum != fbag.ToString() || !Int32.TryParse(startNum, out int result))
                    //    {
                    //        errorMessage = $"商品{fname}包装明细的【包件序号】未按照”流水号”+”斜杠”+”总包数”进行填写, 不允许保存!";
                    //        return false;
                    //    }
                    //}
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            string errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData.DataEntityState.FromDatabase)
                {
                    var pkId = Convert.ToString(newData["id"]);
                    var dbObj = this.Context.LoadBizDataById(this.HtmlForm.Id, pkId);
                    if (!Convert.ToString(newData["funitid"]).EqualsIgnoreCase(Convert.ToString(dbObj["funitid"])))
                    {
                        errorMsg = $"商品【{Convert.ToString(newData["fnumber"])}】的【基本单位】被改变，不允许保存！";
                        return false;
                    }
                    if (!Convert.ToString(newData["fstockunitid"]).EqualsIgnoreCase(Convert.ToString(dbObj["fstockunitid"])))
                    {
                        errorMsg = $"商品【{Convert.ToString(newData["fnumber"])}】的【库存单位】被改变，不允许保存！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            bool isImport = false;
            var excelImport = this.GetQueryOrSimpleParam<string>("TopOrperationNo", "");//是否从excel导入客户 
            if (excelImport.EqualsIgnoreCase("ExcelImport"))
            {
                isImport = true;
            }
            e.Rules.Add(new Validation_Save(isImport)); //因性能优化导入批量处理类 
        }

        /// <summary>
        /// 调用操作事务前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //是否是协同下载调用的保存 SyncDownloadConfirm
            //var originalOpNo = this.Option.GetVariableValue<string>("originalOpNo", string.Empty);
            bool isImport = false;
            var originalOpNo = this.GetQueryOrSimpleParam<string>("TopOrperationNo", "");//是否从excel导入客户 
            if (originalOpNo.EqualsIgnoreCase("ExcelImport"))
            {
                isImport = true;
            }

            // 填充单位明细
            ProductUnitHelper.FillUnitEntry(this.Context, this.HtmlForm, e.DataEntitys);

            e.DataEntitys = FilterBaseUnitAndStockUnitNotCVT(e);
            if (e.DataEntitys.IsNullOrEmpty()) return;

            //如果是导入新增，则根据品牌系列查询
            var branddata = new List<DynamicObject>();
            var seriesdata = new List<DynamicObject>();
            //if (isImport)
            //{
                var brandids = e.DataEntitys.Select(x => Convert.ToString(x["fbrandid"]));
                branddata = this.Context.LoadBizBillHeadDataByACLFilter("ydj_brand", $@"fid in ('{brandids.JoinEx("','", false)}')", "fmusibrand");

                var seriesids = e.DataEntitys.Select(x => Convert.ToString(x["fseriesid"]));
                seriesdata = this.Context.LoadBizBillHeadDataByACLFilter("ydj_series", $@"fid in ('{seriesids.JoinEx("','", false)}')", "fmusiseries");
            //}
            var profileService = this.Context.Container.GetService<ISystemProfile>();
            var matbarcoderulecheck = profileService.GetSystemParameter<int>(this.Context, "stk_stockparam", "fmatbarcoderulecheck", 0);//参数【商品条码包装规则维护校验】

            foreach (var dataEntity in e.DataEntitys)
            {
                dataEntity["fismusiproduct"] = "0";
                //如果是手动新增
                if (!dataEntity.DataEntityState.FromDatabase)
                {
                    if (dataEntity["fpublishcid_txt"].IsNullOrEmptyOrWhiteSpace())
                    {
                        dataEntity["fpublishcid_txt"] = "本企业";
                    }
                    if (dataEntity["fpublishcid_pid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        dataEntity["fpublishcid_pid"] = this.Context.Product;
                    }
                    if (dataEntity["fpublishcid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        dataEntity["fpublishcid"] = this.Context.Company;
                    }
                }

                //如果是导入新增，则根据品牌系列赋值是否慕思商品
                //if (isImport)
                //{
                    if (this.Context.Company == this.Context.TopCompanyId)
                    {
                        //慕思总部商品默认是
                        dataEntity["fismusiproduct"] = "1";
                    }
                    else
                    {
                        if (branddata != null)
                        {
                            var musibrand = branddata.Where(x => Convert.ToString(x["id"]) == Convert.ToString(dataEntity["fbrandid"])).Select(x => Convert.ToString(x["fmusibrand"])).FirstOrDefault();
                            if (musibrand == "1")
                            {
                                dataEntity["fismusiproduct"] = musibrand;
                            }
                        }
                        if (seriesdata != null)
                        {
                            var musiseries = seriesdata.Where(x => Convert.ToString(x["id"]) == Convert.ToString(dataEntity["fseriesid"])).Select(x => Convert.ToString(x["fmusiseries"])).FirstOrDefault();
                            if (musiseries == "1")
                            {
                                dataEntity["fismusiproduct"] = musiseries;
                            }
                        }
                    }
                    if (matbarcoderulecheck == 2)
                    {
                    }
                //}
            }

            //填充单位明细流水号
            var preSerivce = this.Container.GetService<IPrepareSaveDataService>();
            preSerivce.PrepareDataEntity(this.Context, HtmlForm, e.DataEntitys, OperateOption.Create());
            //在新起线程之前先
            var ids = e.DataEntitys.Select(s => Convert.ToString(s["id"])).ToList();
            var product_olds = this.Context.LoadBizBillHeadDataById("ydj_product", ids, "fspecialflag");
            //另起一个线程 处理专供标记 商品授权清单更新超时的问题
            Task task = new Task(() =>
            {
                HandleSpecialFlag(e.DataEntitys, product_olds);
                ClearCache(e.DataEntitys);
            });
            ThreadWorker.QuequeTask(task, result => { });


            ChangWriteLog(e.DataEntitys);
        }

        /// <summary>
        /// 清除商品授权缓存
        /// </summary>
        /// <param name="products"></param>
        private void ClearCache(DynamicObject[] products)
        {
            if (products == null || !products.Any()) return;

            var isClearCache = false;

            // 判断是否存在总部商品
            if (!products.Any(s => Convert.ToString(s["fmainorgid"]).EqualsIgnoreCase(this.Context.TopCompanyId)))
            {
                return;
            }

            // 是否新商品
            var isNewPrd = products.Any(s => !s.DataEntityState.FromDatabase);
            if (isNewPrd)
            {
                isClearCache = true;
            }
            else
            {
                // 检查是否有影响缓存的字段更新
                var dbProducts = this.Context.LoadBizDataById(this.HtmlForm.Id, products.Select(s => s["id"].ToString()), false);

                foreach (var dbProduct in dbProducts)
                {
                    var id = Convert.ToString(dbProduct["id"]);
                    var product = products.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(id));
                    if (product == null) continue;

                    // 附属品牌、
                    var headFldIds = new string[] { "fseriesid", "fauxseriesid", "fspecialflag", "fbrandid" };

                    foreach (var fldId in headFldIds)
                    {
                        var fld = this.HtmlForm.GetField(fldId);
                        if (fld == null) continue;

                        if (!Convert.ToString(product[fld.PropertyName]).EqualsIgnoreCase(Convert.ToString(dbProduct[fld.PropertyName])))
                        {
                            isClearCache = true;
                            break;
                        }
                    }

                    if (isClearCache) break;

                    var fsaleorgentry = ((DynamicObjectCollection)product["fsaleorgentry"]).OrderBy(s => Convert.ToString(s["fsaleorgid"])).ToList();
                    var fsaleorgentry_db = ((DynamicObjectCollection)dbProduct["fsaleorgentry"]).OrderBy(s => Convert.ToString(s["fsaleorgid"])).ToList();

                    if (fsaleorgentry.Count != fsaleorgentry_db.Count)
                    {
                        isClearCache = true;
                        break;
                    }

                    var entryFldIds = new string[] { "fsaleorgid", "fdisablestatus", "fdisabledate", "fverifypricestatus" };

                    for (int i = 0; i < fsaleorgentry.Count; i++)
                    {
                        var saleOrg = fsaleorgentry[i];
                        var saleOrg_db = fsaleorgentry_db[i];

                        foreach (var fldId in entryFldIds)
                        {
                            var fld = this.HtmlForm.GetField(fldId);
                            if (fld == null) continue;

                            if (!Convert.ToString(saleOrg[fld.PropertyName]).EqualsIgnoreCase(Convert.ToString(saleOrg_db[fld.PropertyName])))
                            {
                                isClearCache = true;
                                break;
                            }
                        }

                        if (isClearCache) break;
                    }
                }
            }

            if (isClearCache)
            {
                //清除缓存
                ProductDataIsolateHelper.ClearCacheByBiz(this.Context, new PrdDataIsolateChannelMessage
                {
                    Message = $"{this.HtmlForm?.Caption}-{this.OperationNo}",
                    TopCompanyId = this.Context.TopCompanyId
                });
            }
        }

        /// <summary>
        /// 过滤基本单位与库存单位没有固定换算
        /// </summary>
        /// <param name="e"></param>
        /// <returns></returns>
        private DynamicObject[] FilterBaseUnitAndStockUnitNotCVT(BeginOperationTransactionArgs e)
        {
            List<DynamicObject> saved = new List<DynamicObject>();
            foreach (var dataEntity in e.DataEntitys)
            {
                var unitEntrys = (DynamicObjectCollection)dataEntity["funitentry"];

                string funitid = Convert.ToString(dataEntity["funitid"]);
                string fstockunitid = Convert.ToString(dataEntity["fstockunitid"]);

                bool isValid = true;

                var unitIds = new[] { funitid, fstockunitid };
                foreach (var unitId in unitIds)
                {
                    if (!unitEntrys.Any(s => Convert.ToString(s["funitid"]).EqualsIgnoreCase(unitId)
                                             && Convert.ToString(s["fcvttype"]).EqualsIgnoreCase("1")))
                    {
                        isValid = false;
                        break;
                    }
                }

                if (isValid)
                {
                    saved.Add(dataEntity);
                }
                else
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"商品{dataEntity["fnumber"]}的基本单位与库存单位必须存在固定换算！");
                }
            }

            return saved.ToArray();
        }

        /// <summary>
        /// 处理【特供标记】逻辑（必须在保存前执行）
        /// </summary>
        /// <param name="dataEntities"></param>
        private void HandleSpecialFlag(IEnumerable<DynamicObject> dataEntities, DynamicObjectCollection product_olds)
        {
            if (dataEntities.IsNullOrEmpty()) return;

            //3.当《商品》表头的【专供标记】勾选上并保存时, 更新所有【授权组织类型】= "经销商"的《商品授权清单》, 将该《商品》赋值到《商品授权清单》单据体 - 例外商品(如果保存时原本就是已经勾上【专供标记】就不需要重复触发, 否则就会导致原本清除掉的例外商品又被重新赋值)

            //4.反之如果《商品》表头的【专供标记】取消勾选上并保存时, 更新所有【授权组织类型】= "经销商"的《商品授权清单》, 将该《商品》从对应《商品授权清单》单据体 - 例外商品 中删除(如果保存时原本就是取消勾选【专供标记】就不需要重复触发, 可以减轻性能的影响)

            // 先从数据库获取【特供标记】
            //var ids = dataEntities.Select(s => Convert.ToString(s["id"])).ToList();
            //var products = this.Context.LoadBizBillHeadDataById("ydj_product", ids, "fspecialflag");

            // 判断需要处理的商品
            var specialTrueIds = new List<string>();
            var specialFalseIds = new List<string>();

            foreach (var dataEntity in dataEntities)
            {
                string id = Convert.ToString(dataEntity["id"]);
                bool fspecialflagNew = Convert.ToBoolean(dataEntity["fspecialflag"]);
                var product = product_olds.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(id));

                // 新增时，只处理【专供标记】勾选
                if (product == null && fspecialflagNew)
                {
                    specialTrueIds.Add(id);
                    continue;
                }

                // 修改时，只处理【专供标记】有变化
                if (product != null)
                {
                    var value = Convert.ToString(product["fspecialflag"]);
                    var fspecialflagOld = value.EqualsIgnoreCase("1") || value.EqualsIgnoreCase("true");

                    if (fspecialflagOld != fspecialflagNew)
                    {
                        if (fspecialflagNew)
                        {
                            specialTrueIds.Add(id);
                        }
                        else
                        {
                            specialFalseIds.Add(id);
                        }
                    }
                }
            }

            var productAuthService = this.Container.GetService<IProductAuthService>();

            // 更新所有【授权组织类型】= "经销商"的《商品授权清单》, 将该《商品》赋值到《商品授权清单》单据体 - 例外商品
            if (specialTrueIds.Any())
            {
                // 找到所有【授权组织类型】= "经销商"的《商品授权清单》
                //var filter = $@" forgid in (select fid from t_bas_organization with(nolock) where forgtype='4') ";
                var filter = $@"  exists( select 1 from t_bas_organization with(nolock) where forgid = t_bas_organization.fid and  forgtype='4' )  ";
                var productAuths = this.Context.LoadBizDataByFilter("ydj_productauth", filter);

                productAuthService.AddSpecialBySql(this.Context, specialTrueIds);
            }

            // 更新所有【授权组织类型】= "经销商"的《商品授权清单》, 将该《商品》从对应《商品授权清单》单据体 - 例外商品 中删除 
            if (specialFalseIds.Any())
            {
                productAuthService.RemoveSpecial(this.Context, specialFalseIds);
            }
        }

        /// <summary>
        /// 商品修改记录日志
        /// 操作日志-操作描述记录品牌和系列，是否慕思原值和新值。
        /// </summary>
        /// <param name="dataEntities"></param>
        private void ChangWriteLog(DynamicObject[] dataEntities)
        {
            var billSnapshotObjs = this.Option.GetBillSaveSnapshot();

            //查询品牌
            var brandids = dataEntities.Select(o => Convert.ToString(o["fbrandid"])).ToList();
            brandids.AddRange(billSnapshotObjs.Select(o => Convert.ToString(o["fbrandid"])).ToList());
            var BrandObjs = this.Context.LoadBizBillHeadDataByACLFilter("ydj_brand", $"fid in ('{brandids.JoinEx("','", false)}')", "fid,fnumber,fname").ToList();
            //查询系列
            var seriesids = dataEntities.Select(o => Convert.ToString(o["fseriesid"])).ToList();
            seriesids.AddRange(billSnapshotObjs.Select(o => Convert.ToString(o["fseriesid"])).ToList());
            var SeriesObjs = this.Context.LoadBizBillHeadDataByACLFilter("ydj_series", $"fid in ('{seriesids.JoinEx("','", false)}')", "fid,fnumber,fname").ToList();
            foreach (var item in dataEntities)
            {
                var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(item["id"] as string));
                if (existSnapshot != null)
                {
                    if (Convert.ToBoolean(existSnapshot["fismusiproduct"]) != Convert.ToBoolean(item["fismusiproduct"]))
                    {
                        var oldValue = "品牌-{0}，系列-{1}，慕思商品-{2}".Fmt(BrandObjs.Where(x => Convert.ToString(x["fid"]) == Convert.ToString(existSnapshot["fbrandid"])).Select(x => x["fname"]).FirstOrDefault(),
                            SeriesObjs.Where(x => Convert.ToString(x["fid"]) == Convert.ToString(existSnapshot["fseriesid"])).Select(x => x["fname"]).FirstOrDefault(), GetName(existSnapshot["fismusiproduct"]));
                        var newValue = "品牌-{0}，系列-{1}，慕思商品-{2}".Fmt(BrandObjs.Where(x => Convert.ToString(x["fid"]) == Convert.ToString(item["fbrandid"])).Select(x => x["fname"]).FirstOrDefault(),
                            SeriesObjs.Where(x => Convert.ToString(x["fid"]) == Convert.ToString(item["fseriesid"])).Select(x => x["fname"]).FirstOrDefault(), GetName(item["fismusiproduct"]));

                        this.Logger.WriteLog(this.Context, new LogEntry
                        {
                            BillIds = item["id"] as string,
                            BillNos = item["fnumber"] as string,
                            BillFormId = this.HtmlForm.Id,
                            OpName = "商品保存",
                            OpCode = this.OperationNo,
                            Content = "执行了【保存】操作，更新了【慕思商品】，原值：{0}；新值：{1}。".Fmt(oldValue, newValue),
                            DebugData = "执行了【保存】操作，更新了【慕思商品】，原值：{0}；新值：{1}。".Fmt(oldValue, newValue),
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03
                        });
                    }
                }
            }
        }

        private string GetName(object id)
        {
            var name = string.Empty;
            switch (id)
            {
                case false:
                    name = "否";
                    break;
                case true:
                    name = "是";
                    break;
                default:
                    break;
            }
            return name;
        }
    }
}