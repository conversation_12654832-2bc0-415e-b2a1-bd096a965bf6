using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("ydj_goodstopricing")]
    public class GoodsToPricing : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 商品基础资料--去定价操作
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            string formid = this.GetQueryOrSimpleParam<string>("formid");
            string fmaterialid = this.GetQueryOrSimpleParam<string>("fmaterialid");
            if (formid.IsNullOrEmptyOrWhiteSpace() || fmaterialid.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SrvData = new
                {
                    result=false,
                    error="请先选择物料，再进行此操作！"
                };
                return;
            }
            ///有对应物料的定价，则打开编辑界面，无则新增。
            var metaModel = Context.Container?.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, formid);//加载模型
            var dm = Context.Container?.TryGetService<IDataManager>();
            dm.InitDbContext(this.Context, metaModel.GetDynamicObjectType(this.Context));
            DynamicObject data = null;
            try
            {
                data = dm.SelectBy("fmaterial='{0}'".Fmt(fmaterialid)).OfType<DynamicObject>().FirstOrDefault();
            }
            catch (Exception ex)
            {
                this.Result.SrvData = new
                {
                    result = false,
                    error = "操作失败：" + ex.Message
                };
                return;
            }
            if (data == null) { return; }
            string id = data["id"].ToString();
            this.Result.SrvData = new
            {
                result=true,
                id=id
            };

        }
    }
}
