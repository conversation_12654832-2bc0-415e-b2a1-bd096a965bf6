using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：根据主键ID获取数据
    /// </summary>
    //[InjectService]
    //[FormId("ydj_product")]
    //[OperationNo("copy")]
    //public class Copy : AbstractOperationServicePlugIn
    //{
    //    /// <summary>
    //    /// 开始事务前事件
    //    /// </summary>
    //    /// <param name="e"></param>
    //    public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
    //    {
    //        if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
    //        {
    //            return;
    //        }

    //        foreach(var item in e.DataEntitys)
    //        {
    //            item["fnumber"] = null;
    //        }

    //    }
    //}
}
