using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PositionAppJobMap
{
    /// <summary>
    /// 岗位保存
    /// </summary>
    [InjectService]
    [FormId("ydj_position_appjob_map")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
           
                var fmarkingassistant = newData["fmarkingassistant"] as string;
                if (fmarkingassistant.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage(@"营销助手APP职位必填！"));
            e.Rules.Add(new Validation());
        }

    }
}
