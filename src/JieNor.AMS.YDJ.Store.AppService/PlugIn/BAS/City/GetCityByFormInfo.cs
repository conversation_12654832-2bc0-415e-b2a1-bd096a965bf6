using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System.Linq;
using System.Collections.Generic;
using System.Data;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.City
{
    /// <summary>
    /// 根据对应表单和主键id得到门店对应城市
    /// </summary>
    [InjectService]
    [FormId("ydj_city")]
    [OperationNo("getcitybyforminfo")]
    public class GetCityByFormInfo : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var formid = this.GetQueryOrSimpleParam<string>("hformid", "");
            var fid = this.GetQueryOrSimpleParam<string>("id", "");
            bool success = false;
            DynamicObject result = null;
            string sql = string.Empty;
            List<SqlParam> param = new List<SqlParam>();
            if (!formid.IsNullOrEmptyOrWhiteSpace() && !fid.IsNullOrEmptyOrWhiteSpace())
            {
                switch (formid)
                {
                    case "ydj_order":
                        sql = @"select c.fid,c.fnumber,c.fname from t_ydj_order o
                        inner join t_bd_department d on o.fdeptid = d.fid  and d.fforbidstatus='0'
                        inner join t_bas_store s on d.fstore = s.fid and s.fforbidstatus='0'
                        inner join t_ydj_city c on s.fmycity = c.fid and c.fforbidstatus='0'
                        where o.fid = @fid";
                        param.Add(new SqlParam("@fid", DbType.String, fid));
                        result = this.Context.ExecuteDynamicObject(sql, param).FirstOrDefault();
                        success = true;
                        this.Result.SrvData = new Dictionary<string, string>
                        {
                            { "fid",result?["fid"].ToString()},
                            { "fnumber",result?["fnumber"].ToString()},
                            { "fname",result?["fname"].ToString()},
                        };
                        this.Result.OptionData.Add("returnType", "product");
                        break;
                    case "bas_agent":
                        sql = @"/*dialect*/select distinct c.fid,c.fnumber,c.fname from t_bas_agent a
                        inner join t_ydj_city c on a.fcity like '%'+c.fid+'%' and c.fforbidstatus='0'
                        where a.fid in(
                                select @fid
                                union all
                                select me.fsubagentid from t_bas_mac m with(nolock) 
                                inner join t_bas_macentry me with(nolock) on m.fid=me.fid 
                                where m.fmainagentid=@fid and m.fforbidstatus='0'
                                )";
                        param.Add(new SqlParam("@fid", DbType.String, fid));
                        List<BaseDataSimpleModel> list = new List<BaseDataSimpleModel>();
                        var data= this.Context.ExecuteDynamicObject(sql, param);
                        foreach (var item in data)
                        {
                            list.Add((new BaseDataSimpleModel
                            {
                                Id = Convert.ToString(item["fid"]),
                                Number = Convert.ToString(item["fnumber"]),
                                Name = Convert.ToString(item["fname"]),
                            }));
                        }
                        this.Result.SrvData = list;
                        this.Result.OptionData.Add("returnType", "agent");
                        success = true;
                        break;
                    default:
                        break;
                }
            }
            else
            {
                this.Result.SrvData = null;
            }
            this.Result.IsSuccess = success;
        }
    }
}
