using JieNor.AMS.YDJ.Core;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Price
{
    /// <summary>
    /// 商品价目：获取停产商品价格
    /// </summary>
    [InjectService]
    [FormId("ydj_price")]
    [OperationNo("getinventoryproductprice")]
    public class GetInventoryProductPrice : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            //获取请求JSON数据包
            string requestJsonStr = this.GetQueryOrSimpleParam<string>("productInfos");
            var productInfos = JArray.Parse(requestJsonStr);

            var productIds = productInfos.Select(o => Convert.ToString(o["productId"]));
            if (productIds != null && productIds.Count() > 0)
            {
                //查找综合价目
                var sql = $@"select funifysaleprice,fmaterialid,fattrinfo,fcustomdesc,funitid from t_ydj_pricesynthesize with(nolock)
                         where fmaterialid in({productIds.JoinEx(",", true)}) and fmainorgid='{this.Context.Company}'";
                var ProductPrice = this.DBService.ExecuteDynamicObject(this.Context, sql);

                var srvData = new List<JToken>();
                foreach (var item in productInfos)
                {
                    item["salPrice"] = 0;
                    var productId = item.GetJsonValue<string>("productId");
                    if (IsTC(productId))
                    {
                        item["salPrice"] = ProductPrice.Where(x => (string)x["fmaterialid"] == productId
                                    && Convert.ToString(x["fattrinfo"]).Trim() == Convert.ToString(item["auxPropValId"]).Trim()
                                    && Convert.ToString(x["fcustomdesc"]).Trim() == Convert.ToString(item["customdesc"]).Trim()
                                    && (string)x["funitid"] == (string)item["unitId"]).Select(x => Convert.ToDecimal(x["funifysaleprice"])).FirstOrDefault();
                    }
                }

                var priceInfos = productInfos.Select(x => new JObject
                    {
                        { "success", true },
                        { "clientId", x["clientId"] },
                        { "productId", x["productId"] },
                        { "purPrice", 0 },
                        { "salPrice", x["salPrice"] },
                        { "isfrominventory",true },
                        { "definedPrice", 0 },
                    }).ToList();
                srvData.AddRange(priceInfos);

                var fetchSpecialPrice = this.GetQueryOrSimpleParam<bool>("fetchSpecialPrice");
                var specialPriceDateStr = this.GetQueryOrSimpleParam<string>("specialPriceDate");
                DateTime.TryParse(specialPriceDateStr, out var specialPriceDate);

                if (fetchSpecialPrice)
                {
                    IPriceService priceService = this.Container.GetService<IPriceService>();
                    priceService.SetSpecialPrice(this.Context, productInfos, srvData, specialPriceDate);
                }

                this.Result.SrvData = srvData;
                this.Result.IsSuccess = true;
            }
        }

        /// <summary>
        /// 判断商品是否停产
        /// </summary>
        /// <param name="product"></param>
        /// <returns></returns>
        private bool IsTC(string productId)
        {
            //停产定义:
            //1)  首先根据 商品 对应的【系列】, 找到《销售组织与业绩品牌关系》单据体 - 销售组织与业绩品牌关系 的【系列】
            //2)  如果在《销售组织与业绩品牌关系》里匹配不到【系列】时, 就认为没有停产, 因为有可能就是通配品牌或慕思助眠
            //3)  如果匹配到【系列】时, 获取《销售组织与业绩品牌关系》单据体 - 销售组织与业绩品牌关系, 对应行的【销售组织】, 将获取到的【销售组织】匹配《商品》基础资料里 单据体-产品销售组织【销售组织】
            //4) 如果匹配上的【销售组织】对应行的【禁用状态】=”已启用”时, 则不为停产
            //5) 如果匹配上的【销售组织】对应行的【禁用状态】=”已禁用”时, 则认为这商品在总部已经停产了
            //6) 如果匹配不到【销售组织】, 也要走停购的逻辑判断(已有停购的逻辑), 因为我认为这商品在总部已经停产了
            bool IsTc = false;
            string sql = $@" select 1 FROM t_bd_material t1
                              INNER JOIN t_ydj_orgresultbrandentry t3 on t1.fseriesid = t3.fseriesid
                              where t1.fid in('{productId}') ";
            //如果销售组织与业绩品牌关系匹配不到 则直接返回未停产
            if (this.DBService.ExecuteDynamicObject(this.Context, sql).Count == 0) return false;

            sql = $@"select distinct t2.fsaleorgid,t2.fdisablestatus FROM t_bd_material t1
                            join t_bd_materialsaleorg t2 on t1.fid=t2.fid
                            join t_ydj_orgresultbrandentry t3 on t2.fsaleorgid = t3.forganizationid
                            join t_ydj_series t4 on t3.fseriesid = t4.fid
                            join t_bas_organization t5 on t3.forganizationid = t5.fid
                            where t1.fid in('{productId}')";
            var saleorg = this.DBService.ExecuteDynamicObject(this.Context, sql);
            if (saleorg.Count == 0)
            {
                //如果没有匹配的销售组织则为停产
                IsTc = true;
            }
            else
            {
                //fdisablestatus 1：已启用，2：已禁用 存在一个已启用的即不为停产
                if (saleorg.Any(o => Convert.ToString(o["fdisablestatus"]).EqualsIgnoreCase("1")))
                {
                    IsTc = false;
                }
                else
                {
                    IsTc = true;
                }
            }
            return IsTc;
        }
    }
}
