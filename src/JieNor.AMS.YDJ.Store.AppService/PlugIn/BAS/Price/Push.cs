using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Price
{
    /// <summary>
    /// 商品价目：改价
    /// </summary>
    [InjectService]
    [FormId("ydj_price|ydj_selfprice|ydj_reprice|ydj_dealprice")]
    [OperationNo("Push")]
    public class Push : AbstractOperationServicePlugIn
    {

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if(e.DataEntitys==null || e.DataEntitys.Length ==0)
            {
                return;
            }

            foreach (var bill in e.DataEntitys)
            { 
                var fbizorgid = bill["fbizorgid"]?.ToString();
                var fnumber = bill["fnumber"]?.ToString();
                if (bill.DataEntityState.FromDatabase && !fbizorgid.EqualsIgnoreCase(this.Context.Company))
                {
                    throw new Exception(@"所选价目表【{0}】是总部授权的价目表，只能列表查看，不能进行编辑！".Fmt(fnumber));
                }
            }
            
        }


    }



}
 
