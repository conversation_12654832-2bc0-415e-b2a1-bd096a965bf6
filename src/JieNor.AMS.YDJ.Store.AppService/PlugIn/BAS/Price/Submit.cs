using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity; 

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Price
{
    /// <summary>
    /// 商品：提交
    /// </summary>
    [InjectService]
    [FormId("ydj_price|ydj_selfprice|ydj_reprice|ydj_dealprice")]
    [OperationNo("Submit")]
    public class Submit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
             
            e.Rules.Add(new ValidationOrgOperation("提交"));
        }
         
    }
}