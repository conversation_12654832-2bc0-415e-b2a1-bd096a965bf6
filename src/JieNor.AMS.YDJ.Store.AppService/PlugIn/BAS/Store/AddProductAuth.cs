using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Store
{
    /// <summary>
    /// 门店：生成或更新商品授权清单
    /// </summary>
    [InjectService]
    [FormId("bas_store")]
    [OperationNo("AddProductAuth")]
    public class AddProductAuth : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    var fstatus = Convert.ToString(newData["fstatus"]);
            //    var fforbidstatus = Convert.ToBoolean(newData["fforbidstatus"]);

            //    return fstatus.EqualsIgnoreCase("E") && !fforbidstatus;
            //}).WithMessage("{0}", (billObj, propObj) => $"{this.HtmlForm.Caption}【{billObj["fnumber"]}】的【数据状态】!=已审核 或 【禁用状态】=是，无法生成商品授权清单！"));
        }

        /// <summary>
        /// 执行操作事务前事件，通知插件对要处理的数据进行排序等预处理
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            this.Container.GetService<IStoreService>()
                .AddOrUpdateProductAuth(this.Context, e.DataEntitys);
        }
    }
}
