using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Deliver
{

     
        /// <summary>
        /// 审核的校验
        /// </summary>
    public    class AuditValidation : AbstractBaseValidation
    {
         

        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }
         
        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities==null || dataEntities.Length ==0)
            {
                return result;
            }

            var agents = dataEntities.Where(f => f["fagentid"].IsNullOrEmptyOrWhiteSpace()).ToList();
            if (agents.Any())
            {
                foreach (var item in agents)
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = @"送达方【{0} {1}】未设置对应的经销商，无法进行审核！".Fmt(item["fnumber"], item["fname"]),
                        DataEntity = item,
                    });
                }
            }

            var orgs = dataEntities.Where(f => f["fsaleorgid"].IsNullOrEmptyOrWhiteSpace()).ToList();
            if (orgs.Any())
            {
                foreach (var item in orgs)
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = @"送达方【{0} {1}】未设置对应的销售组织，无法进行审核！".Fmt(item["fnumber"], item["fname"]),
                        DataEntity = item,
                    });
                }
            }

            var checks = dataEntities.Except(agents).Except(orgs).ToList();
            if(checks.Count >0)
            {
                var agentIds = checks.Select(f => Convert.ToString(f["fagentid"])).ToList();
                var existAgents = this.Context.LoadBizBillHeadDataById("bas_agent", agentIds);
                foreach (var item in checks)
                {
                    var ex = existAgents.Any(f => Convert.ToString(item["fagentid"]) == Convert.ToString(f["id"]));
                    if(!ex )
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = @"送达方【{0} {1}】设置的经销商不存在，无法进行审核！".Fmt(item["fnumber"], item["fname"]),
                            DataEntity = item,
                        });
                    }
                }

                var saleOrgIds = checks.Select(f => Convert.ToString(f["fsaleorgid"])).ToList();
                var existOrgs = this.Context.LoadBizBillHeadDataById("bas_organization", saleOrgIds);
                foreach (var item in checks)
                {
                    var ex = existOrgs.Any(f => Convert.ToString(item["fsaleorgid"]) == Convert.ToString(f["id"]));
                    if (!ex)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = @"送达方【{0} {1}】设置的销售组织不存在，无法进行审核！".Fmt(item["fnumber"], item["fname"]),
                            DataEntity = item,
                        });
                    }
                }

                foreach (var item in checks)
                {
                    var agentId = item["fagentid"].ToString();
                    var agentCtx = this.Context.CreateAgentDBContext(agentId);
                    if (agentCtx==null || !agentCtx.Companys.Any ())
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = @"送达方【{0} {1}】设置的经销商还未审核或还未生成对应的经销商组织，无法进行审核！".Fmt(item["fnumber"], item["fname"]),
                            DataEntity = item,
                        });
                    }
                }
            }
             
            return result;
        }
    }


}
