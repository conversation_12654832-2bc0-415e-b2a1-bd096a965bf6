using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Helper
{
    /// <summary>
    /// 总部直营协同模式帮助类
    /// </summary>
    public class DirectSynergyHelper
    {
        /// <summary>
        /// 将协同客户到K3Cloud
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formMeta">客户表单模型</param>
        /// <param name="dataEntity">客户数据包</param>
        public static void SyncCustomerToK3Cloud(UserContext userCtx, HtmlForm formMeta, DynamicObject dataEntity)
        {
            ////如果不是成交客户，则无需协同
            //var cusNature = Convert.ToString(dataEntity["fcusnature"]);
            //if (!cusNature.EqualsIgnoreCase("cusnature_02")) return;

            //如果没有建立协同关系，则无需协同
            var target = GetSyncTargetSEP(userCtx);
            if (target == null) return;

            //加载引用数据
            var refObjMgr = userCtx.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(userCtx, formMeta.GetDynamicObjectType(userCtx), dataEntity, false);

            var billData = GetCustomerSyncBillData(userCtx, dataEntity);

            //发起协同请求
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var responseResult = gateway.Invoke(
                userCtx,
                target,
                new CommonBillDTO()
                {
                    FormId = "ydj_customer",
                    OperationNo = "SaveSynergy",
                    BillData = billData,
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string> { }
                }.SetOptionFlag((long)Enu_OpFlags.RequestToReply)) as CommonBillDTOResponse;

            responseResult?.OperationResult?.ThrowIfHasError(true, $"客户协同失败，对方系统未返回任何响应！");
        }

        /// <summary>
        /// 获取客户协同数据包
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="dataEntity">客户数据包</param>
        /// <returns>协同数据包</returns>
        private static string GetCustomerSyncBillData(UserContext userCtx, DynamicObject dataEntity)
        {
            var billDatas = new List<Dictionary<string, object>>();

            var country = Convert.ToString((dataEntity["fcountry_ref"] as DynamicObject)?["fenumitem"]);
            var province = Convert.ToString((dataEntity["fprovince_ref"] as DynamicObject)?["fenumitem"]);
            var city = Convert.ToString((dataEntity["fcity_ref"] as DynamicObject)?["fenumitem"]);
            var customerType = Convert.ToString((dataEntity["fcustomertype_ref"] as DynamicObject)?["fenumitem"]);

            //获取客户负责人所属部门关联的K3组织Id
            var dutyEntrys = dataEntity["fdutyentry"] as DynamicObjectCollection;
            var deptIds = dutyEntrys
                ?.Where(o => !o["fdeptid"].IsNullOrEmptyOrWhiteSpace())
                ?.Select(o => Convert.ToString(o["fdeptid"]))
                ?.Distinct()
                ?.ToList();
            if (!deptIds.Any())
            {
                throw new BusinessException($"客户负责人【所属部门】为空，无法协同！");
            }

            var deptOrgMapKv = GetK3OrgIdsByDeptIds(userCtx, deptIds);

            //可用组织名称串 取【客户负责人】明细所有行的【所属部门】关联的K3组织名称，多个名称之间用逗号“,”分隔
            var avbOrgList = "";

            //创建组织、使用组织 取【客户负责人】明细第一行的【所属部门】关联的K3组织ID
            var orgId = "";
            if (deptOrgMapKv.Any())
            {
                var orgNames = deptOrgMapKv.Select(o => o.Value["orgName"]);
                avbOrgList = string.Join(",", orgNames);
                orgId = deptOrgMapKv.FirstOrDefault().Value["orgId"];
            }

            //客户联系人信息
            var contactList = new List<Dictionary<string, string>>();
            var contactEntrys = dataEntity["fcuscontacttry"] as DynamicObjectCollection;
            foreach (var item in contactEntrys)
            {
                contactList.Add(new Dictionary<string, string>
                {
                    { "fcontacter", Convert.ToString(item["fcontacter"]) },
                    { "fphone", Convert.ToString(item["fphone"]) },
                    { "faddress", Convert.ToString(item["faddress"]) }
                });
            }

            var billData = new Dictionary<string, object>();
            billData["ftranid"] = dataEntity["ftranid"]; //交易流水号
            billData["fcreateorgid"] = orgId; //创建组织
            billData["fuseorgid"] = orgId; //使用组织
            billData["fname"] = dataEntity["fname"]; //客户名称
            billData["fnumber"] = dataEntity["fnumber"]; //客户编码
            billData["fwechat"] = dataEntity["fwechat"]; //微信号
            billData["fphone"] = dataEntity["fphone"]; //联系电话
            billData["fcustomertype"] = customerType; //客户分类
            billData["fcountry"] = country; //国家
            billData["fprovince"] = province; //省份
            billData["fcity"] = city; //城市
            billData["faddress"] = dataEntity["faddress"]; //收货地址
            billData["favborglist"] = avbOrgList; //可用组织
            billData["fcontactentry"] = contactList; //联系人列表
            billDatas.Add(billData);
            return billDatas.ToJson();
        }

        /// <summary>
        /// 获取唯一的总部直营模式的供应商协同目标
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <returns>协同目标</returns>
        public static TargetSEP GetSyncTargetSEP(UserContext userCtx)
        {
            var supplierForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "ydj_supplier");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, supplierForm.GetDynamicObjectType(userCtx));

            var where = "fmainorgid=@fmainorgid and foperationmode='1'";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, userCtx.Company),
            };
            var dataReader = userCtx.GetPkIdDataReader(supplierForm, where, sqlParam);
            var suppliers = dm.SelectBy(dataReader).OfType<DynamicObject>();
            if (suppliers == null || !suppliers.Any()) return null;

            if (suppliers.Count() > 1)
            {
                throw new BusinessException("当前存在多个总部直营的供应商，无法确定协同目标！");
            }

            var supplier = suppliers.FirstOrDefault();
            var cooCompanyId = Convert.ToString(supplier["fcoocompanyid"]);
            var cooProductId = Convert.ToString(supplier["fcooproductid"]);
            if (cooCompanyId.IsNullOrEmptyOrWhiteSpace() || cooProductId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"总部直营供应商【{supplier["fname"]}】的 fcoocompanyid 或 fcooproductid 为空，无法协同！");
            }

            return new TargetSEP(cooCompanyId, cooProductId);
        }

        /// <summary>
        /// 从【资料值映射】中加载指定部门对应的K3组织
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="deptIds">部门ID集合</param>
        /// <returns>部门ID与K3组织ID的键值对字典，以部门ID为键，以K3组织ID为值</returns>
        public static Dictionary<string, Dictionary<string, string>> GetK3OrgIdsByDeptIds(UserContext userCtx, List<string> deptIds)
        {
            var deptOrgMapKv = new Dictionary<string, Dictionary<string, string>>();

            deptIds = deptIds
                ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                ?.Distinct()
                ?.ToList();
            if (deptIds == null || !deptIds.Any()) return deptOrgMapKv;

            var dataService = userCtx.Container.GetService<IBillDataService>();
            var deptRefDataMapObjs = dataService.LoadBySql(userCtx,
                "si_bizobjectfieldvaluemap",
                "fmyobjecttype='2' and fmybaseformid=@fmybaseformid",
                new SqlParam("fmybaseformid", System.Data.DbType.String, "ydj_dept"));

            foreach (var refDataObj in deptRefDataMapObjs)
            {
                var entryValueObjs = refDataObj["fentity"] as DynamicObjectCollection;
                if (entryValueObjs == null || !entryValueObjs.Any()) continue;

                foreach (var deptId in deptIds)
                {
                    foreach (var entryObj in entryValueObjs)
                    {
                        var myValueId = entryObj["fmyvalueid"] as string; //我方资料值
                        var extValueId = entryObj["fextvalueid"] as string; //对方资料值
                        var extValueName = entryObj["fextvaluename"] as string; //对方资料值名称
                        if (myValueId.IsNullOrEmptyOrWhiteSpace()) continue;

                        if (myValueId.EqualsIgnoreCase(deptId))
                        {
                            deptOrgMapKv[deptId] = new Dictionary<string, string> 
                            {
                                { "orgId", extValueId },
                                { "orgName", extValueName }
                            };
                            break;
                        }
                    }
                }
            }

            return deptOrgMapKv;
        }

        /// <summary>
        /// 从【资料值映射】中加载指定K3组织对应的部门
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="orgIds">部门ID集合</param>
        /// <returns>部门ID与K3组织ID的键值对字典，以K3组织ID为键，以部门ID为值</returns>
        public static Dictionary<string, Dictionary<string, string>> GetDeptIdsByK3OrgIds(UserContext userCtx, List<string> orgIds)
        {
            var deptOrgMapKv = new Dictionary<string, Dictionary<string, string>>();

            orgIds = orgIds
                ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                ?.Distinct()
                ?.ToList();
            if (orgIds == null || !orgIds.Any()) return deptOrgMapKv;

            var dataService = userCtx.Container.GetService<IBillDataService>();
            var deptRefDataMapObjs = dataService.LoadBySql(userCtx,
                "si_bizobjectfieldvaluemap",
                "fmyobjecttype='2' and fmybaseformid=@fmybaseformid",
                new SqlParam("fmybaseformid", System.Data.DbType.String, "ydj_dept"));

            foreach (var refDataObj in deptRefDataMapObjs)
            {
                var entryValueObjs = refDataObj["fentity"] as DynamicObjectCollection;
                if (entryValueObjs == null || !entryValueObjs.Any()) continue;

                foreach (var orgId in orgIds)
                {
                    foreach (var entryObj in entryValueObjs)
                    {
                        var myValueId = entryObj["fmyvalueid"] as string; //我方资料值
                        var myValueName = entryObj["fmyvalueid_txt"] as string; //我方资料值名称
                        var extValueId = entryObj["fextvalueid"] as string; //对方资料值
                        if (extValueId.IsNullOrEmptyOrWhiteSpace()) continue;

                        if (extValueId.EqualsIgnoreCase(orgId))
                        {
                            deptOrgMapKv[orgId] = new Dictionary<string, string>
                            {
                                { "deptId", myValueId },
                                { "deptName", myValueName }
                            };
                            break;
                        }
                    }
                }
            }

            return deptOrgMapKv;
        }
    }
}
