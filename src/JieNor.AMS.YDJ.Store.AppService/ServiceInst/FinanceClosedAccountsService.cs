using JieNor.AMS.YDJ.Core.Interface.Finance;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.ServiceInst
{
    /// <summary>
    /// 财务关账逻辑校验服务
    /// 作者：zpf
    /// 日期：2022-07-28
    /// </summary>    
    [InjectService]
    [ServiceMetaAttribute("name", "财务关账逻辑校验服务")]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlBizService_FinanceClosedAccounts)]
    public class FinanceClosedAccountsService : AbstractBaseValidation
    {
        /// <summary>
        /// 财务关账逻辑校验
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            //数据包是否有数据
            if (dataEntities == null || !dataEntities.Any()) return result;
            var billNoField = formInfo.GetNumberField();

            /*
             * 关联任务：34093 子 【慕思现场】系统实现财务关帐功能 / 增加财务关账/反关账功能（参数设置，执行关账/反关账，表单校验）
                编号为XXXXXX的收支记录保存 或 提交 或 撤销 或 审核 或 反审核 或 作废 或 反作废 操作失败：支付日期不允许小于当前系统最近财务关账日期！
                编号为XXXXXX的费用应付单保存 或 提交 或 撤销 或 审核 或 反审核 或 作废 或 反作废 操作失败：业务日期不允许小于当前系统最近财务关账日期！
                编号为XXXXXX的其他应收单保存 或 提交 或 撤销 或 审核 或 反审核 或 作废 或 反作废 操作失败：业务日期不允许小于当前系统最近财务关账日期！
                编号为XXXXXX的其他应付单保存 或 提交 或 撤销 或 审核 或 反审核 或 作废 或 反作废 操作失败：业务日期不允许小于当前系统最近财务关账日期！
             */
            //获取最后一次关账时间
            var baseStockService = userCtx.Container.GetService<IFinanceBaseService>();
            var lastclosuredate = baseStockService.GetLatestFinanceCloseDate(this.Context);
            var fdatenames = new List<string>() { "ydj_collectreceipt", "ydj_payreceipt", "ste_registfee" };
            var fdatename = fdatenames.Contains(formInfo.Id) ? "fregistdate" : "fdate";
            var dateName = formInfo.Id == "coo_incomedisburse" ? "支付日期" : "业务日期";

            //获取财务管理参数中关账校验配置
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            var checkoutentrys = profileService.GetSystemParameter<DynamicObjectCollection>(userCtx, "sal_dealersetup", "fcheckoutentry");

            if (checkoutentrys.IsNullOrEmptyOrWhiteSpace())
            {
                return result;
            }

            var financeBillFormIds = new List<string>();
            try
            {
                financeBillFormIds = checkoutentrys.Select(t => Convert.ToString(t["ferrortypedescription"]).Split('|')[1]).Distinct().ToList();
            }
            catch (Exception ex)
            {
                return result;
            }
            if (financeBillFormIds.Contains(formInfo.Id))
            {
                var checkoutentry = checkoutentrys.FirstOrDefault(t => Convert.ToString(t["ferrortypedescription"]).Contains(formInfo.Id));
                if (!checkoutentry.IsNullOrEmptyOrWhiteSpace() && !checkoutentry.DynamicObjectType.Properties.ContainsKey("fenable"))
                {
                    return result;
                }
                if (!checkoutentry.IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(checkoutentry["fenable"]))
                {
                    //获得最近关账日期
                    foreach (var entity in dataEntities)
                    {
                        var timeDifference = DateTime.Compare(Convert.ToDateTime(entity[fdatename]), Convert.ToDateTime(lastclosuredate));
                        if (timeDifference <= 0)
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"编号为{billNoField?.DynamicProperty.GetValue(entity)}的{formInfo.Caption}{this.OperationName}操作失败：{dateName}不允许小于等于当前系统最近关账日期！",
                                DataEntity = entity
                            });
                        }
                    }
                }
            }
            return result;
        }
    }
}
