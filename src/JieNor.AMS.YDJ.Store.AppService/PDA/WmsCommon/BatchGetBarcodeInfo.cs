using JieNor.AMS.YDJ.Store.AppService.Model;
using JieNor.AMS.YDJ.Store.AppService.Model.DeliveryScanTask;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon
{
    /// <summary>
    /// 批量获取扫描条码信息
    /// </summary>
    [InjectService]
    [FormId("bcm_deliveryscantask|bcm_transfertask")]
    [OperationNo("batchgetscanbarcodeinfo")]
    public class BatchGetBarcodeInfo : AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var barCode = this.GetQueryOrSimpleParam<string>("barCode");//条码信息
            var taskNos = this.GetQueryOrSimpleParam<string>("taskNos");//任务单号
            var agentId = this.GetQueryOrSimpleParam<string>("agentId");//经销商ID
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);
            if (string.IsNullOrWhiteSpace(barCode))
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "参数【商品条码】不能为空!";
                return;
            }
            if (string.IsNullOrWhiteSpace(taskNos))
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "参数【任务单号】不能为空!";
                return;
            }
            var filter = $" fforbidstatus='0' and fmainorgid='{this.AgentContext.Company}' and fnumber ='{barCode}' ";
            var barCodeInfo = this.AgentContext.LoadBizDataByFilter("bcm_barcodemaster", filter, true).FirstOrDefault();
            if (barCodeInfo == null)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = $"扫描失败, 当前扫描的条码【{barCode}】不存在！";
                return;
            }

            DynamicObject task = null;
            var barCodes = new List<string>();
            //获取包含条码本身的所有下层条码
            BarCodeMasterHelper.GetSubBarCode(this.AgentContext, new List<string>() { barCode }, ref barCodes);
            var allBarCodeDatas = this.AgentContext.LoadBizDataByFilter("bcm_barcodemaster", $" fnumber in ('{string.Join("','", barCodes)}')", true);
            if (allBarCodeDatas == null || allBarCodeDatas.Count <= 0)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = $"根据扫描的条码【{barCode}】找不到对应的条码主档。";
                return;
            }
            //获取源单数据
            List<DynamicObject> tasks = this.AgentContext.LoadBizDataByFilter(this.HtmlForm.Id, " fbillno in ('{0}')".Fmt(string.Join("','", taskNos.Split(','))));

            //记录错误信息
            List<string> errMsgs = new List<string>();

            foreach (var item in taskNos.Split(','))
            {
                task = tasks.FirstOrDefault(o => o["fbillno"]?.ToString() == item);
                if (tasks == null || tasks.Count <= 0)
                {
                    errMsgs.Add($"未找到单号为【{item}】的{this.HtmlForm.Caption}!");
                }
                task = tasks.FirstOrDefault(f => f["ftaskstatus"]?.ToString() != "ftaskstatus_04");
                if (task == null)
                {
                    errMsgs.Add($"扫描失败, 单号为【{item}】的{this.HtmlForm.Caption}状态为已完成！");
                }
            }

            bool flag = false;
            Func<DynamicObject, bool> func = (f) => { return Convert.ToString(f["fbizstatus"]) != "1"; };
            //校验条码
            flag = CheckBarCode(func, allBarCodeDatas, barCode, "【可用】", ref errMsgs);
            if (flag)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = string.Join("", errMsgs);
                return;
            }

            var barCodeList = new List<string>();
            //递归获取最外层条码
            BarCodeMasterHelper.GetLastBarCode(this.AgentContext, new List<string>() { barCode }, ref barCodeList);

            var barCodeData = new BCReturnDataModel();
            var subBarCodeInfos = new List<BCMaterial>();


            //扫码出库启用仓位校验
            var profileService = this.Container.GetService<ISystemProfile>();
            var fischeckstorehouseloc = profileService.GetSystemParameter(this.Context, "stk_stockparam", "fischeckstorehouseloc", false);

            foreach (var item in taskNos.Split(','))
            {
                task = tasks.FirstOrDefault(o => o["fbillno"]?.ToString() == item);
                bool isValidAndPass = false;//标识进行了校验且通过
                foreach (var bar in barCodeList)
                {
                    var barData = allBarCodeDatas.FirstOrDefault(f => Convert.ToString(f["fnumber"]) == bar);
                    var Entitys = barData["fentity"] as DynamicObjectCollection;
                    foreach (var entity in Entitys)
                    {
                        var taskEntitys = task?["ftaskentity"] as DynamicObjectCollection;
                        if (taskEntitys == null || !taskEntitys.Any()) continue;
                        
                        //匹配物料
                        var taskEntity = MatchMat(taskEntitys, entity);
                        if (taskEntity == null && !taskEntity.Any())
                        {
                            errMsgs.Add($"当前扫描商品与{this.HtmlForm.Caption}待作业商品不一致, 不允许操作 !");
                        }
                        if (this.HtmlForm.Id.EqualsIgnoreCase("bcm_transfertask"))
                        {
                            var taskEn = taskEntity.Where(w => Convert.ToString(w["fstorehouseid"]).Equals(Convert.ToString(barData["fstorehouseid"])) && Convert.ToString(w["fstorelocationid"]).Equals(Convert.ToString(barData["fstorelocationid"]))).FirstOrDefault();
                            if (taskEn == null)
                            {
                                var storeHouseObj = barData["fstorehouseid_ref"] as DynamicObject;
                                string storeHousName = storeHouseObj == null ? "" : Convert.ToString(storeHouseObj["fname"]);
                                var storeLocationObj = barData["fstorelocationid_ref"] as DynamicObject;
                                string storeLocationName = storeLocationObj == null ? "" : Convert.ToString(storeLocationObj["fname"]);
                                var fentity = barData["fentity"] as DynamicObjectCollection;
                                var fmaterialid_ref = fentity.FirstOrDefault()?["fmaterialid_ref"] as DynamicObject;
                                errMsgs.Add($"当前扫描条码【{Convert.ToString(barData["fnumber"])}】【{Convert.ToString(fmaterialid_ref["fnumber"])}】【{Convert.ToString(fmaterialid_ref["fname"])}】的仓库位于【{storeHousName}】,仓位位于【{storeLocationName}】,在该【{item}】{this.HtmlForm.Caption}没有匹配的, 不允许操作 !");
                            }
                            else
                            {
                                isValidAndPass = true;
                                errMsgs.Clear();//清除之前记录的错误信息
                                break;
                            }
                        }
                    }
                    //PC端库存管理参数增加【扫码出库启用仓位校验】
                    if (fischeckstorehouseloc)
                    {
                        if (this.HtmlForm.Id == "bcm_deliveryscantask")//发货扫描任务
                        {
                            string ftask_type = task["ftask_type"]?.ToString();
                            if (ftask_type == "stk_postockreturn" || ftask_type == "stk_sostockout" || ftask_type == "stk_otherstockout")//采购退货，销售出库，其他出库
                            {
                                string fstorelocationid = barData["fstorelocationid"]?.ToString();//条码【仓位】
                                if (!string.IsNullOrWhiteSpace(fstorelocationid))
                                {
                                    var taskEntitys = task?["ftaskentity"] as DynamicObjectCollection;
                                    var taskEntity = taskEntitys.Where(w => Convert.ToString(w["fstorehouseid"]).Equals(Convert.ToString(barData["fstorehouseid"])) && Convert.ToString(w["fstorelocationid"]).Equals(Convert.ToString(barData["fstorelocationid"]))).FirstOrDefault();
                                    if (taskEntity == null)
                                    {
                                        var fstorehouseid_ref = barData["fstorehouseid_ref"] as DynamicObject;
                                        var fstorelocationid_ref = barData["fstorelocationid_ref"] as DynamicObject;
                                        var fentity = barData["fentity"] as DynamicObjectCollection;
                                        var fmaterialid_ref = fentity.FirstOrDefault()?["fmaterialid_ref"] as DynamicObject;

                                        errMsgs.Add($"当前条码【{Convert.ToString(barData["fnumber"])}】商品【{Convert.ToString(fmaterialid_ref["fname"])}】所在仓库【{Convert.ToString(fstorehouseid_ref?["fname"])}】，仓位【{Convert.ToString(fstorelocationid_ref?["fname"])}】与待发货商品的仓库仓位不匹配，请您检查，谢谢！");
                                    }
                                    else
                                    {
                                        isValidAndPass = true;
                                        errMsgs.Clear();//清除之前记录的错误信息
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                if (isValidAndPass) break;
            }

            if (errMsgs.Any())
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = string.Join("", errMsgs);
                return;
            }

            //List<string> attrInfoIds = allBarCodeDatas.SelectMany(o => o["fentity"] as DynamicObjectCollection).Select(o => o["fattrinfo"]?.ToString()).ToList();
            ////获取辅助属性
            //List<DynamicObject> AuxPropValues = GetAuxPropValues(attrInfoIds, this.Context);

            foreach (var bar in barCodeList)
            {
                var barData = allBarCodeDatas.FirstOrDefault(f => Convert.ToString(f["fnumber"]) == bar);
                var Entitys = barData["fentity"] as DynamicObjectCollection;
                foreach (var entity in Entitys)
                {
                    var subBarCodeInfo = new BCMaterial();
                    var materialid_ref = entity["fmaterialid_ref"] as DynamicObject;
                    subBarCodeInfo.scanbarcode = barCode;
                    subBarCodeInfo.subbarcode = bar;
                    subBarCodeInfo.barcode = Convert.ToString(entity["fbarcode"]);
                    subBarCodeInfo.material = new BaseDataModel(materialid_ref);
                    //获取辅助属性
                    //var AuxPropValue = AuxPropValues.FirstOrDefault(o => o["fid"]?.ToString() == entity["fattrinfo"]?.ToString());
                    //subBarCodeInfo.attrinfo = new
                    //{
                    //    id = AuxPropValue?["fid"],
                    //    fnumber = AuxPropValue?["fnumber"],
                    //    fname = AuxPropValue?["fname"]
                    //};
                    subBarCodeInfo.attrinfo = Convert.ToString(entity["fattrinfo"]);
                    subBarCodeInfo.attrinfo_e = Convert.ToString(entity?["fattrinfo_e"] ?? "");
                    subBarCodeInfo.customdesc = Convert.ToString(entity["fcustomdesc"]);
                    subBarCodeInfo.mtono = Convert.ToString(entity["fmtono"]);
                    subBarCodeInfo.lotno = Convert.ToString(entity["flotno"]);
                    subBarCodeInfo.ownertype = Convert.ToString(entity["fownertype"]);
                    subBarCodeInfo.ownerid = Convert.ToString(entity["fownerid"]);
                    subBarCodeInfo.packcount = Convert.ToInt32(barData["fpackcount"]);
                    subBarCodeInfo.packindex = Convert.ToInt32(barData["fpackindex"]);
                    subBarCodeInfo.store = new BaseDataModel(barData["fstorehouseid_ref"] as DynamicObject);
                    subBarCodeInfo.storelocation = new BaseDataModel(barData["fstorelocationid_ref"] as DynamicObject);
                    subBarCodeInfo.sourcetype = Convert.ToString(barData["fsourcetype"]);
                    subBarCodeInfo.sourcebillno = Convert.ToString(barData["fsourcenumber"]);
                    subBarCodeInfo.sourceentryid = Convert.ToString(barData["fsourceentryid"]);
                    decimal qty = Convert.ToDecimal(entity["fstockqty"]);
                    var packTypeName = "";
                    switch (barData["fpackagtype"]?.ToString())
                    {
                        case "1":
                            packTypeName = "标准";
                            break;
                        case "2":
                            qty = 1;
                            packTypeName = "1件多包";
                            break;
                        case "3":
                            packTypeName = "1包多件";
                            break;
                        default:
                            packTypeName = "标准";
                            break;
                    }
                    subBarCodeInfo.packtype = new { id = Convert.ToString(barData["fpackagtype"]), name = packTypeName };
                    subBarCodeInfo.qty = qty;
                    var unitid_ref = materialid_ref["funitid_ref"] as DynamicObject;
                    subBarCodeInfo.unit = new BaseDataModel(unitid_ref);
                    var stockunitid_ref = materialid_ref["fstockunitid_ref"] as DynamicObject;
                    subBarCodeInfo.stockunit = new BaseDataModel(stockunitid_ref);
                    subBarCodeInfo.specifica = Convert.ToString(materialid_ref["fspecifica"]);
                    var brandid_ref = materialid_ref["fbrandid_ref"] as DynamicObject;
                    subBarCodeInfo.brandid = new BaseDataModel(brandid_ref);
                    var seriesid_ref = materialid_ref["fseriesid_ref"] as DynamicObject;
                    subBarCodeInfo.seriesid = new BaseDataModel(seriesid_ref);
                    subBarCodeInfos.Add(subBarCodeInfo);
                }
            }

            barCodeData.subbarcodes = subBarCodeInfos;
            //获取父级条码
            GetParentBarcode(barCodeData);
            this.Result.SrvData = barCodeData;
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = $"条码【{barCode}】扫描成功!";
        }

        /// <summary>
        /// 校验条码
        /// </summary>
        /// <param name="func"></param>
        /// <param name="allBarCodeDatas"></param>
        /// <param name="barCode"></param>
        /// <param name="status"></param>
        /// <param name="errMsgs"></param>
        /// <returns></returns>
        private bool CheckBarCode(Func<DynamicObject, bool> func, List<DynamicObject> allBarCodeDatas, string barCode, string status, ref List<string> errMsgs)
        {
            var barCodeData = allBarCodeDatas.FirstOrDefault(func);
            bool flag = false;
            if (barCodeData != null)
            {
                if (barCodeData["fnumber"]?.ToString() == barCode)
                {
                    errMsgs.Add($"当前条码【{barCode}】状态不为{status}, 不允许扫描！");
                    flag = true;
                }
                else
                {
                    errMsgs.Add($"当前条码【{barCode}】的下层条码【{Convert.ToString(barCodeData["fnumber"])}】状态不为{status}, 不允许扫描！");
                    flag = true;
                }
            }
            return flag;
        }

        /// <summary>
        /// 匹配物料
        /// </summary>
        /// <param name="taskEntitys"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        private List<DynamicObject> MatchMat(DynamicObjectCollection taskEntitys, DynamicObject entity)
        {
            return taskEntitys.Where(f =>
                           Convert.ToDecimal(f["fwaitworkqty"]) > 0
                           && Convert.ToString(f["fmaterialid"]).Trim() == Convert.ToString(entity["fmaterialid"]).Trim()
                           && Convert.ToString(f["fattrinfo_e"]).Trim() == Convert.ToString(entity["fattrinfo_e"]).Trim()
                           && Convert.ToString(f["fcustomdesc"]).Trim() == Convert.ToString(entity["fcustomdesc"]).Trim()
                           && Convert.ToString(f["fmtono"]).Trim() == Convert.ToString(entity["fmtono"]).Trim()
                           && Convert.ToString(f["flotno"]).Trim() == Convert.ToString(entity["flotno"]).Trim()
                           && Convert.ToString(f["fownertype"]).Trim() == Convert.ToString(entity["fownertype"]).Trim()
                           && Convert.ToString(f["fownerid"]).Trim() == Convert.ToString(entity["fownerid"]).Trim()
                            ).ToList();
        }

        /// <summary>
        /// 获取父级条码
        /// </summary>
        /// <param name="barCodeData"></param>
        private void GetParentBarcode(BCReturnDataModel barCodeData)
        {
            var subBarCodes = barCodeData.subbarcodes;
            var scanBarCodes = subBarCodes.Select(s => Convert.ToString(s.scanbarcode));
            if (scanBarCodes == null || !scanBarCodes.Any())
                return;
            var sql = $@"select  a.fnumber,b.fbarcode 
                                from t_bcm_barcodemaster a
                                left join t_bcm_mastermtrlentry b on a.fid = b.fid
                                where b.fbarcode in ('{string.Join("','", scanBarCodes)}') and fmainorgid='{this.AgentContext.Company}'";
            var datas = this.Context.Container.GetService<IDBService>().ExecuteDynamicObject(this.Context, sql);
            if (datas == null || !datas.Any())
                return;
            foreach (var subBar in subBarCodes)
            {
                var data = datas.FirstOrDefault(w => Convert.ToString(w["fbarcode"]).Equals(subBar.scanbarcode));
                if (data != null)
                {
                    subBar.daddybar = Convert.ToString(data["fnumber"]);
                }
            }
        }

        /// <summary>
        /// 获取物料对应辅助属性
        /// </summary>
        /// <param name="attrInfoIds"></param>
        /// <returns></returns>
        private List<DynamicObject> GetAuxPropValues(List<string> attrInfoIds, UserContext userCtx)
        {
            var sql = $@"select  fid,fnumber,fname from 
                                T_BD_AUXPROPVALUE
                                where fid in ('{string.Join("','", attrInfoIds)}') ";
            var datas = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql).ToList();
            return datas;
        }
    }
}
