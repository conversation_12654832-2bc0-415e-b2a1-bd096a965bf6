using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon
{
    /// <summary>
    /// PDA提交辅助类
    /// </summary>
    public class SubmitHelper
    {
        /// <summary>
        /// 检查任务作业完成状态
        /// </summary>
        /// <param name="taskObj"></param>
        /// <returns></returns>
        public static bool CheckTaskComplete(string formId, DynamicObject taskObj)
        {
            var checkResult = false;
            if (taskObj == null) return checkResult;

            var entityKey = "";
            switch (formId)
            {
                case "bcm_deliveryscantask"://发货扫描任务
                case "bcm_transfertask"://调出扫描任务
                case "bcm_transferintask"://调入扫描任务
                    entityKey = "ftaskentity";
                    break;
            }

            if (string.IsNullOrWhiteSpace(entityKey)) return checkResult;//不支持的表单直接返回

            var taskEntitys = taskObj["ftaskentity"] as DynamicObjectCollection;
            if (taskEntitys != null && taskEntitys.Any())
            {
                //检查任务明细是否存在【待作业数量】大于0的行
                checkResult = !taskEntitys.Any(t => Convert.ToDecimal(t["fwaitworkqty"]) > 0);
            }
            return checkResult;
        }



        /// <summary>
        /// 标记明细行提交状态
        /// 说明：此标记是为了避免同一行明细，被多个PDA同时提交时出现的作业数量反写问题
        /// </summary>
        /// <param name="userContext">用户上下文</param>
        /// <param name="entryIds">收货扫描任务.扫描任务明细 行内码集合</param>
        /// <param name="mark">是否标记（true:标记，false:取消标记）</param>
        public static void MarkEntrysSubmitStatus(UserContext userContext, List<string> entryIds, bool mark)
        {
            if (entryIds == null || !entryIds.Any())
            {
                return;
            }
            var markSubmitDate = "1900-01-01 00:00:00";
            if (mark)
            {
                markSubmitDate = DateTime.Now.ToString("G");
            }
            var dbService = userContext.Container.GetService<IDBService>();
            var tempTable = "";
            string markSql = "";
            using (var tran = userContext.CreateTransaction())
            {
                if (entryIds.IsGreaterThan(50))
                {
                    tempTable = dbService.CreateTempTableWithDataList(userContext, entryIds,false);
                    markSql = $@"/*dialect*/UPDATE A SET A.fmarksubmitdate='{markSubmitDate}'  
                           FROM T_BCM_RESCANTASKENTITY AS A WITH(NOLOCK)
                           INNER JOIN {tempTable} AS B ON A.fentryid = B.fid ";
                }
                else if (entryIds.Count == 1)
                {
                    markSql = $@"/*dialect*/UPDATE T_BCM_RESCANTASKENTITY SET fmarksubmitdate='{markSubmitDate}' 
                             WHERE fentryid='{entryIds.FirstOrDefault()}'";
                }
                else
                {
                    markSql = $@"/*dialect*/UPDATE T_BCM_RESCANTASKENTITY SET fmarksubmitdate='{markSubmitDate}' 
                             WHERE fentryid IN ('{string.Join("','", entryIds)}')";
                }
                var dbServiceEx = userContext.Container.GetService<IDBServiceEx>();
                var exeCount = dbServiceEx.Execute(userContext, markSql);
                if (!tempTable.IsNullOrEmptyOrWhiteSpace())
                {
                    dbService.DeleteTempTableByName(userContext, tempTable, true);
                }

                tran.Complete();
            }
        }

        /// <summary>
        /// 检查明细行提交状态
        /// </summary>
        /// <param name="userContext">用户上下文</param>
        /// <param name="entryIds">收货扫描任务.扫描任务明细 行内码集合</param>
        public static bool CheckEntrysSubmitStatus(UserContext userContext, List<string> entryIds)
        {
            if (entryIds == null || !entryIds.Any())
            {
                return true;
            }

            var expireTime = DateTime.Now.AddMinutes(-1).ToString("G");//过期时间 = 当前时间 - 1分钟
            var dbService = userContext.Container.GetService<IDBService>();
            var tempTable = "";
            string checkSql = "";
            using (var tran = userContext.CreateTransaction())
            {
                if (entryIds.IsGreaterThan(50))
                {
                    tempTable = dbService.CreateTempTableWithDataList(userContext, entryIds,false);
                    checkSql = $@"/*dialect*/SELECT C.fentryid   
                             FROM T_BCM_RESCANTASKENTITY AS A WITH(NOLOCK)
                             INNER JOIN {tempTable} AS B ON A.fentryid = B.fid 
                             INNER JOIN T_BCM_RESCANTASKENTITY AS C WITH(NOLOCK) ON A.fid = C.fid 
                             WHERE C.fmarksubmitdate>'{expireTime}'";
                }
                else if (entryIds.Count == 1)
                {
                    checkSql = $@"/*dialect*/SELECT B.fentryid 
                             FROM T_BCM_RESCANTASKENTITY AS A WITH(NOLOCK) 
                             INNER JOIN T_BCM_RESCANTASKENTITY AS B WITH(NOLOCK) ON A.fid = B.fid 
                             WHERE A.fentryid='{entryIds.FirstOrDefault()}' AND B.fmarksubmitdate>'{expireTime}'";
                }
                else
                {
                    checkSql = $@"/*dialect*/SELECT B.fentryid 
                             FROM T_BCM_RESCANTASKENTITY AS A WITH(NOLOCK) 
                             INNER JOIN T_BCM_RESCANTASKENTITY AS B WITH(NOLOCK) ON A.fid = B.fid 
                             WHERE A.fentryid IN ('{string.Join("','", entryIds)}') AND B.fmarksubmitdate>'{expireTime}'";
                }
                var resultObjs = dbService.ExecuteDynamicObject(userContext, checkSql);
                if (!tempTable.IsNullOrEmptyOrWhiteSpace())
                {
                    dbService.DeleteTempTableByName(userContext, tempTable, true);
                }

                tran.Complete();

                return !(resultObjs != null && resultObjs.Any());
            }
        }
    }
}
