using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Model;
using System.Collections.Generic;
using JieNor.Framework;
using System;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.Print
{
    /// <summary>
    /// PDA获取打印模板
    /// </summary>
    [InjectService]
    [FormId("bcm_barcodemaster")]
    [OperationNo("gettemplatelist")]
    public class GetTemplateList : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var formid = this.GetQueryOrSimpleParam<string>("formid","");//来源单类型
            var isExportExcel = this.GetQueryOrSimpleParam<string>("isexportexcel", "");//是否excel导出
            var IsDefault = this.GetQueryOrSimpleParam<string>("isdefault", "");//是否只取默认模板(0否 1是)
            var resdata = GetTemplateData(formid, isExportExcel, IsDefault);
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "执行成功!";
            this.Result.SrvData = resdata;
        }
        private List<BaseDataModel> GetTemplateData(string formid, string isExportExcel, string IsDefault)
        {
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fforbidstatus", System.Data.DbType.String, "0")
            };

            var filter = "";
            if (!string.IsNullOrWhiteSpace(formid))
            {
                filter += $" and fsrcformid='{formid}' ";
            }
            if (isExportExcel=="1")
            {
                filter += $" and ffiletype='xlsx' ";
            }
            if (IsDefault=="1")
            {
                filter += $" and fdefault='1' ";
            }

            var sqlText = $@"select fid,fnumber,fname from t_bas_officetmpl where fmainorgid=@fmainorgid and fforbidstatus=@fforbidstatus {filter} order by fdefault desc";
            
            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParams);

            var models = new List<BaseDataModel>();
            foreach (var item in list)
            {
                var model = new BaseDataModel(item["fid"],item["fnumber"],item["fname"]);

                models.Add(model);
            }
            return models;
        }
    }
}
