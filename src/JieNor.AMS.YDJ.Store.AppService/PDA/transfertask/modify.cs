using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;


namespace JieNor.AMS.YDJ.Store.AppService.PDA.transfertask
{
    [InjectService]
    [FormId("bcm_transferintask|bcm_transfertask")]
    [OperationNo("modify")]
    public class modify : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || !e.DataEntitys.Any())
            {
                return;
            }
            var data = e.DataEntitys.FirstOrDefault();
            DynamicObjectCollection entrys = data["fscannedentity"] as DynamicObjectCollection;
            var scanBill = this.Context.LoadBizDataByFilter("bcm_scanresult", $"fscantaskformid ='{this.HtmlForm.Id}' and fscantaskbillno = '{Convert.ToString(data["fbillno"])}'",true);
            if(scanBill != null && scanBill.Any())
            {
                foreach (var bb in scanBill)
                {
                    var newf = entrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                    var scanBillEntrys = bb["fentity"] as DynamicObjectCollection;
                    
                    if (scanBillEntrys.Count > 0)
                    {
                        foreach (var entry in scanBillEntrys)
                        {
                            newf["fbarcode"] = Convert.ToString(bb["fbarcode"]);
                            newf["fbarcodetext"] = Convert.ToString(bb["fbarcodetext"]);
                            newf["foperatorid"] = Convert.ToString(bb["foperatorid"]);
                            newf["fopdatetime"] = Convert.ToString(bb["fopdatetime"]);
                            newf["fscansceneid"] = Convert.ToString(bb["fscansceneid"]);
                            newf["fstorehouseid_e"] = Convert.ToString(bb["fstorehouseid"]);
                            newf["fstorelocationid_e"] = Convert.ToString(bb["fstorelocationid"]);
                            newf["fdescription_e"] = Convert.ToString(bb["fdescription"]);

                            newf["fmaterialid_fname_b"] = Convert.ToString(entry["fmaterialid"]);

                            var fmaterialRef = entry["fmaterialid_ref"] as DynamicObject;
                            if (!fmaterialRef.IsNullOrEmptyOrWhiteSpace())
                            {
                                var selTypeObj = this.Context.LoadBizDataByFilter("sel_type", $"fid ='{Convert.ToString(fmaterialRef["fseltypeid"])}'", true);
                                if (selTypeObj != null && selTypeObj.Any())
                                {
                                    newf["fseltypeid"] = Convert.ToString(selTypeObj[0]["fname"]);
                                }
                               
                            }

                            entrys.Add(newf);
                        }
                    }
                    else
                    {
                        newf["fbarcode"] = Convert.ToString(bb["fbarcode"]);
                        newf["fbarcodetext"] = Convert.ToString(bb["fbarcodetext"]);
                        newf["foperatorid"] = Convert.ToString(bb["foperatorid"]);
                        newf["fopdatetime"] = Convert.ToString(bb["fopdatetime"]);
                        newf["fscansceneid"] = Convert.ToString(bb["fscansceneid"]);
                        newf["fstorehouseid_e"] = Convert.ToString(bb["fstorehouseid"]);
                        newf["fstorelocationid_e"] = Convert.ToString(bb["fstorelocationid"]);
                        newf["fdescription_e"] = Convert.ToString(bb["fdescription"]);
                        entrys.Add(newf);
                    }
                }

                // 主动加载引用数据
                var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
                refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbarcode", "foperatorid", "fscansceneid", "fstorehouseid_e", "fstorelocationid_e", "fmaterialid_fname_b" });

            }
        }
    }
}
