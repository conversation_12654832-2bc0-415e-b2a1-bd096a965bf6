using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.Store.AppService.Model.DeliveryScanTask;
using JieNor.AMS.YDJ.Store.AppService.Model;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.DeliveryScanTask
{
    /// <summary>
    /// PDA发货任务列表
    /// 取数规则如下：
    /// 1：请求类型：0全部 1待发货 2发货中 3已发货
    /// 根据发货扫描任务中的任务状态判断，1待发货取状态为待作业，2发货中取状态为作业中，3已发货取状态为已作业
    /// 2：数据类型： 0 全部 14销售发货 2采购退货 3其他出库
    /// 根据发货扫描任务中的来源单据判断，1销售发货取来源单据为销售合同，2采购退货取来源单据为采购退货单，3其他出库取来源单据为其它出库单
    /// </summary>
    [InjectService]
    [FormId("bcm_deliveryscantask")]
    [OperationNo("getsendscantasklist")]
    public class GetSendScanTaskList : AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var islist = this.GetQueryOrSimpleParam<string>("islist");//是否请求列表
            var type = this.GetQueryOrSimpleParam<string>("type");//请求类型：0全部 1待发货 2发货中 3已发货 4
            var datatype = this.GetQueryOrSimpleParam<string>("datatype");//数据类型： 0全部 1销售发货 2采购退货 3其他出库
            var scantype = this.GetQueryOrSimpleParam<string>("scantype");//扫描类型：1扫描发货任务 2扫描发货单号 3扫描来源单号
            var scanbarcode = this.GetQueryOrSimpleParam<string>("scanbarcode");//扫描条码
                var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);
            var errMsg = new List<string>();

            if (string.IsNullOrWhiteSpace(datatype))
            {
                errMsg.Add("数据类型不能为空!");
            }

            if (islist == "1")
            {
                if (string.IsNullOrWhiteSpace(type))
                {
                    errMsg.Add("请求类型不能为空!");
                }
            }
            else
            {
                if (string.IsNullOrWhiteSpace(scantype))
                {
                    errMsg.Add("按扫描条码的方式加载数据时，扫描类型不能为空!");
                }
                if (string.IsNullOrWhiteSpace(scanbarcode))
                {
                    errMsg.Add("按扫描条码的方式加载数据时，扫描条码不能为空!");
                }
            }
            if (errMsg.Count > 0)
            {
                this.Result.SimpleMessage = String.Join("", errMsg);
                this.Result.IsSuccess = false;
                return;
            }
            var deliveryTasks = new List<DeliveryTask>();

            try
            {

                var mysql = string.Format(@"
                    select a.fid as Id
                    from t_bcm_deliveryscantask a 
                    left join t_bcm_descantaskentity b on b.fid = a.fid
                    where 1=1 and a.fmainorgid ='{0}'
                ".Fmt(this.AgentContext.BizOrgId));


                //数据类型： 0全部  1销售发货 2采购退货 3其他出库
                switch (datatype)
                {
                    case "0":
                        break;
                    case "1":
                        mysql += " and b.flinkformid='stk_sostockout'";//销售出库单
                        break;
                    case "2":
                        mysql += " and b.flinkformid='stk_postockreturn'";//采购退货单
                        break;
                    case "3":
                        mysql += " and b.flinkformid='stk_otherstockout'";//其它出库单
                        break;
                }

                ////请求类型：0全部 1待发货 2发货中 3已发货
                if (islist == "1")
                {
                    switch (type)
                    {
                        case "0":
                            break;
                        case "1":
                            mysql += " and a.ftaskstatus ='ftaskstatus_01'"; //待作业
                            break;
                        case "2":
                            mysql += " and a.ftaskstatus ='ftaskstatus_03'"; //作业中
                            break;
                        case "3":
                            mysql += " and a.ftaskstatus ='ftaskstatus_04'"; //已作业
                            break;
                    }
                }
                else
                {
                    //扫描类型：1扫描发货任务 2扫描发货单号 3扫描来源单号
                    switch (scantype)
                    {
                        case "1"://扫描发货任务
                            mysql += $" and a.fbillno='{scanbarcode}'";
                            break;
                        case "2"://扫描发货单号
                            mysql += $" and a.fdeliveryno='{scanbarcode}'";
                            break;
                        case "3"://扫描来源单号
                            mysql += $" and b.fsourcebillno='{scanbarcode}'";
                            break;
                    }
                }

                var datas = this.AgentContext.ExecuteDynamicObject(mysql, null).ToList();

                var loadSer = this.Container.GetService<LoadReferenceObjectManager>();

                if (datas == null || datas.Count <= 0)
                {
                    if (islist != "1")
                    {
                        this.Result.SimpleMessage = "扫描失败, 当前条码未查到对应发货任务！";
                        this.Result.IsSuccess = false;
                        return;
                    }
                    else
                    {
                        this.Result.SimpleMessage = "数据为空";
                        this.Result.IsSuccess = false;
                        this.Result.SrvData = deliveryTasks;
                        return;
                    }
                }

                var ids = datas.Select(f => f["Id"]?.ToString()).Distinct().ToList();
                var bizdatas = this.AgentContext.LoadBizDataById(this.HtmlForm.Id, ids, true);
                if (bizdatas == null || bizdatas.Count() <= 0)
                {
                    this.Result.SimpleMessage = "扫描失败, 当前条码未查到对应发货任务！";
                    this.Result.IsSuccess = false;
                    return;
                }
                if (islist != "1")
                {
                    var completedatas = bizdatas.Where(f => f["ftaskstatus"]?.ToString() != "ftaskstatus_04").ToList();
                    if (completedatas == null || completedatas.Count <= 0)
                    {
                        this.Result.SimpleMessage = "当前发货任务已完成, 无需进行发货！";
                        this.Result.IsSuccess = false;
                        return;
                    }
                }

                //根据发货扫描任务，获取已提交的所有条码扫描记录,再获取所有的条码主档数据
                var billnos = bizdatas.Select(f => f["fbillno"]?.ToString()).Distinct().ToList();
                var allscanbarcodes = this.AgentContext.LoadBizDataByFilter("bcm_scanresult", $"fisparentbarcode='1' and fscantaskformid='bcm_deliveryscantask' and fscantaskbillno in ('{string.Join("','", billnos)}')", true);
                var barmasterids = allscanbarcodes.Select(f => f["fbarcode"]?.ToString()).Distinct().ToList();

                var sql = string.Format(@"
            select a.fid as Id,a.fnumber,b.fmaterialid
            from T_BCM_BARCODEMASTER a with(nolock)
            left join T_BCM_MASTERMTRLENTRY b with(nolock) on b.fid=a.fid
            where a.fmainorgid ='{0}' and a.fid in ('{1}')".Fmt(this.AgentContext.Company,string.Join("','", barmasterids)));
                var allbcmasters = this.AgentContext.ExecuteDynamicObject(sql, null).ToList();

                //获取系统参数默认仓库和默认仓位
                var profileService = this.AgentContext.Container.GetService<ISystemProfile>();
                var paradata = profileService.GetSystemParameter(this.AgentContext, "stk_stockparam");
                var storeid = Convert.ToString(paradata["fdefaultstoreid"]);
                var storelocationid = Convert.ToString(paradata["fdefaultlocationid"]);
                var store = this.AgentContext.LoadBizDataById("ydj_storehouse", storeid);

                var allMaterials = GetMaterialExtraInfo(bizdatas);

                var ordernos = new List<string>();
                var sostockoutNos = new List<string>();//销售出库单单号
                foreach (var data in bizdatas)
                {
                    var entitys = data["ftaskentity"] as DynamicObjectCollection;
                    foreach(var entity in entitys)
                    {
                        var fsourceformidng = entity["fsourceformid"]?.ToString();
                        if (fsourceformidng == "ydj_order")
                        {
                            var fsourcebillnong = entity["fsourcebillno"]?.ToString();
                            if (!ordernos.Contains(fsourcebillnong))
                            {
                                ordernos.Add(fsourcebillnong);
                            }
                        }
                        var flinkformid = entity["flinkformid"]?.ToString();//关联单据
                        if (flinkformid == "stk_sostockout")
                        {
                            var flinkbillno = entity["flinkbillno"]?.ToString();//关联单据编号
                            if (!sostockoutNos.Contains(flinkbillno))
                            {
                                sostockoutNos.Add(flinkbillno);
                            }
                        }
                    }
                }
                var allSourceBills = this.AgentContext.LoadBizDataByFilter("ydj_order", $" fbillno in ('{string.Join("','", ordernos)}')",true);

                var allLinkBills = this.AgentContext.LoadBizDataByFilter("stk_sostockout", $" fbillno in ('{string.Join("','", sostockoutNos)}')", true);//销售出库单

                //获取仓位
                List<DynamicObject> storelocationObjs = new List<DynamicObject>();
                var fstorelocationids = bizdatas.SelectMany(o => o["ftaskentity"] as DynamicObjectCollection).Select(o => o["fstorelocationid"]?.ToString()).ToList().Distinct();
                var locationsql = string.Format(@"select * from t_ydj_storehouselocation where 
                                    fentryid in ('{0}')".Fmt(string.Join("','", fstorelocationids)));
                storelocationObjs = this.DBService.ExecuteDynamicObject(this.AgentContext, locationsql).ToList();

                foreach (var data in bizdatas)
                {
                    if (islist != "1" && data["ftaskstatus"]?.ToString() == "ftaskstatus_04")
                    {
                        continue;
                    }
                    var deliveryTask = new DeliveryTask();

                    deliveryTask.mainid = Convert.ToString(data["Id"]);
                    deliveryTask.deliverytaskno = Convert.ToString(data["fbillno"]);
                    deliveryTask.receptionno = Convert.ToString(data["fdeliveryno"]);
                    deliveryTask.logisticsno = Convert.ToString(data["flogisticsno"]);
                    var ftaskstatus = "待发货";
                    switch (data["ftaskstatus"])
                    {
                        case "ftaskstatus_01":
                            ftaskstatus = "待发货";
                            break;
                        case "ftaskstatus_03":
                            ftaskstatus = "发货中";
                            break;
                        case "ftaskstatus_04":
                            ftaskstatus = "已发货";
                            break;
                    }
                    deliveryTask.taskstatus = ftaskstatus;

                    if (store != null)
                    {
                        deliveryTask.warehouse = new
                        {
                            id = store["Id"],
                            fnumber = store["fnumber"],
                            fname = store["fname"]
                        };

                        var storeentitys = store["fentity"] as DynamicObjectCollection;
                        var storelocation = storeentitys.FirstOrDefault(f => f["Id"]?.ToString() == storelocationid);
                        deliveryTask.warehouselocation = new
                        {
                            id = storelocation == null ? "" : storelocation["Id"],
                            fnumber = storelocation == null ? "" : storelocation["flocnumber"],
                            fname = storelocation == null ? "" : storelocation["flocname"]
                        };
                    }
                    else
                    {
                        deliveryTask.warehouse = new
                        {
                            id = "",
                            fnumber = "",
                            fname = ""
                        };
                        deliveryTask.warehouselocation = new
                        {
                            id = "",
                            fnumber = "",
                            fname = ""
                        };
                    }

                    #region 获取商品信息
                    var materials = new List<MaterialInfo>();
                    var entitys = data["ftaskentity"] as DynamicObjectCollection;

                    var sourcetypeName = "";
                    var sourcebillno = "";
                    var customer = new List<string>();
                    var customerphone = new List<string>();
                    var deliveryuser = new List<string>();
                    var deliveryuserphone = new List<string>();
                    var deliverydate = new List<string>();
                    var stockoutdate = new List<string>();//出库日期
                    foreach (var entity in entitys)
                    {
                        var materialinfo = new MaterialInfo();
                        var materialiObj = allMaterials.FirstOrDefault(d => Convert.ToString(d["id"]) == Convert.ToString(entity["fmaterialid"]));
                        if (materialiObj == null) continue;

                        var nowsourcebillno = entity["fsourcebillno"]?.ToString();
                        var sourcebill = allSourceBills.FirstOrDefault(f => (f["fbillno"]?.ToString()).EqualsIgnoreCase(nowsourcebillno));
                        if (sourcebill != null)
                        {
                            var customerref = sourcebill["fcustomerid_ref"] as DynamicObject;
                            if (customerref != null)
                            {
                                var customername = customerref["fname"]?.ToString();
                                if (!customer.Contains(customername) && !string.IsNullOrWhiteSpace(customername))
                                {
                                    customer.Add(customername);
                                }
                                var custphone = customerref["fphone"]?.ToString();
                                if (!customerphone.Contains(custphone) && !string.IsNullOrWhiteSpace(custphone))
                                {
                                    customerphone.Add(custphone);
                                }
                            }
                            var linkstaffid = (sourcebill["fcustomercontactid_ref"] as DynamicObject)?["fcontacter"]?.ToString();
                            //if(!deliveryuser.Contains(linkstaffid) && !string.IsNullOrWhiteSpace(linkstaffid))
                            //{
                            //    deliveryuser.Add(linkstaffid);
                            //}
                            var dphone = sourcebill["fphone"]?.ToString();
                            if (!deliveryuserphone.Contains(dphone) && !string.IsNullOrWhiteSpace(dphone))
                            {
                                deliveryuserphone.Add(dphone);
                            }
                            var fdeliverydate = Convert.ToDateTime(sourcebill["fdeliverydate"]).ToString("yyyy-MM-dd");
                            if(!deliverydate.Contains(fdeliverydate) && !string.IsNullOrWhiteSpace(fdeliverydate))
                            {
                                deliverydate.Add(fdeliverydate);
                            }
                        }
                        if (!deliveryuser.Contains(entity["fconsignee"].ToString()) && !string.IsNullOrWhiteSpace(entity["fconsignee"].ToString()))
                        {
                            deliveryuser.Add(entity["fconsignee"].ToString());
                        }
                        var nowlinkbillno = entity["flinkbillno"]?.ToString();//关联单据编号
                        var linkBill = allLinkBills.FirstOrDefault(f => (f["fbillno"]?.ToString()).EqualsIgnoreCase(nowlinkbillno));//销售出库单
                        if (linkBill != null)
                        {
                            var fdate = Convert.ToDateTime(linkBill["fdate"]).ToString("yyyy-MM-dd");
                            if (!stockoutdate.Contains(fdate) && !string.IsNullOrWhiteSpace(fdate))
                            {
                                stockoutdate.Add(fdate);
                            }
                        }
                        materialinfo.mainid = Convert.ToString(data["Id"]);
                        materialinfo.entryid = Convert.ToString(entity["Id"]);
                        materialinfo.seq = Convert.ToInt32(entity["fseq"]);
                        materialinfo.material = new BaseDataModel(materialiObj["id"], materialiObj["fnumber"], materialiObj["fname"]);
                        materialinfo.baseunit = new BaseDataModel(materialiObj["funitid"], materialiObj["funitnumber"], materialiObj["funitname"]);
                        materialinfo.stockunit = new BaseDataModel(materialiObj["fstockunitid"], materialiObj["fstockunitnumber"], materialiObj["fstockunitname"]);
                        materialinfo.needqty = Convert.ToDecimal(entity["fwaitworkqty"]) + Convert.ToDecimal(entity["fworkedqty"]);
                        materialinfo.waitrecqty = Convert.ToDecimal(entity["fwaitworkqty"]);
                        materialinfo.recqty = Convert.ToDecimal(entity["fworkedqty"]);
                        materialinfo.waitpackqty = Convert.ToDecimal(entity["fwaitscanqty"]);
                        materialinfo.recpackqty = Convert.ToDecimal(entity["fscannedqty"]);
                        materialinfo.attrinfo = new BaseDataModel(entity["fattrinfo_ref"] as DynamicObject);
                        materialinfo.attrinfo_e =  Convert.ToString(entity?["fattrinfo_e"]??"");
                        materialinfo.customdesc = Convert.ToString(entity["fcustomdesc"]);
                        materialinfo.mtono = Convert.ToString(entity["fmtono"]);
                        materialinfo.ownertype = Convert.ToString(entity["fownertype"]);
                        materialinfo.ownerid = Convert.ToString(entity["fownerid"]);
                        materialinfo.lotno = Convert.ToString(entity["flotno"]);
                        materialinfo.sourcetype = Convert.ToString(entity["fsourceformid"]);
                        materialinfo.sourcebillno = Convert.ToString(entity["fsourcebillno"]);
                        materialinfo.sourceentryid = Convert.ToString(entity["fsourceentryid"]);

                        //仓库
                        var fstorehouseid_ref = entity["fstorehouseid_ref"] as DynamicObject;
                        materialinfo.storehouseid = new
                        {
                            id = fstorehouseid_ref?["Id"],
                            fnumber = fstorehouseid_ref?["fnumber"],
                            fname = fstorehouseid_ref?["fname"],
                            priority = Convert.ToString(fstorehouseid_ref?["fstorehousepriority"]) == "0" ? "999999" : fstorehouseid_ref?["fstorehousepriority"],//仓库优先级
                        };
                        //仓位
                        //var fstorelocationid_ref = entity["fstorelocationid_ref"] as DynamicObject;
                        var storelocationObj = storelocationObjs.FirstOrDefault(o=>o["fentryid"]?.ToString()== entity["fstorelocationid"]?.ToString());
                        //考虑仓库没有仓位的情况
                        var locationPriority = "999999";
                        if (storelocationObj != null && storelocationObj.DynamicObjectType.Properties.ContainsKey("flocationpriority"))
                        {
                            var priority = Convert.ToString(storelocationObj["flocationpriority"]);
                            if (priority != "0")
                            {
                                locationPriority = priority;
                            }
                        }
                        materialinfo.storelocationid = new
                        {
                            id = storelocationObj?["fentryid"],
                            fnumber = storelocationObj?["flocnumber"],
                            fname = storelocationObj?["flocname"],
                            priority = locationPriority,//库位优先级
                        };
                        //客户《PDA-采购退货》不显示【客户】
                        var fcustomerid_ref = entity["fcustomerid_ref"] as DynamicObject;
                        materialinfo.customerid = new
                        {
                            id = fcustomerid_ref?["Id"],
                            fnumber = fcustomerid_ref?["fnumber"],
                            fname = fcustomerid_ref?["fname"]
                        };

                        //根据打包类型和包数计算包装规则
                        var packagerule = "1件1包";
                        var fpackagingqty = Convert.ToString(Convert.ToInt16(entity["fpackagingqty"])); //包数/件数
                        var fpackagetype = Convert.ToString(entity["fpackagingtype"]);//打包类型
                        switch (fpackagetype)
                        {
                            case "1":
                                break;
                            case "3":
                                packagerule = "1件" + fpackagingqty + "包";
                                break;
                            case "2":
                                packagerule = "1包" + fpackagingqty + "件";
                                break;
                        }
                        materialinfo.packagerule = packagerule;

                        //判断是否包装完成:待收货数量>0且状态不为已收货
                        materialinfo.iscomplete = (Convert.ToDecimal(entity["fwaitworkqty"]) > 0 && data["ftaskstatus"]?.ToString() != "ftaskstatus_04") ? "0" : "1";

                        var barCodeInfos = new List<BCReturnData>();
                        var bcscanresults = allscanbarcodes.Where(f => f["fscantaskentryid"]?.ToString() == Convert.ToString(entity["Id"])).ToList();
                        foreach (var scanbc in bcscanresults)
                        {
                            var bcmaster = allbcmasters.FirstOrDefault(f => f["Id"]?.ToString() == Convert.ToString(scanbc["fbarcode"]));
                            var barCodeInfo = new BCReturnData();
                            barCodeInfo.mainid = Convert.ToString(data["Id"]);
                            barCodeInfo.entryid = Convert.ToString(entity["Id"]);
                            barCodeInfo.subentryid = Convert.ToString(scanbc["fbarcode"]);
                            barCodeInfo.barcode = bcmaster == null ? "" : Convert.ToString(bcmaster["fnumber"]);
                            barCodeInfo.warehouse = new BaseDataModel(scanbc["fstorehouseid_ref"] as DynamicObject);
                            barCodeInfo.warehouselocation = new BaseDataModel(scanbc["fstorelocationid_ref"] as DynamicObject);
                            barCodeInfo.scanuser = new BaseDataModel(scanbc["foperatorid_ref"] as DynamicObject);
                            barCodeInfo.scantime = Convert.ToString(scanbc["fopdatetime"]);
                            barCodeInfo.issubmit = "1";
                            barCodeInfos.Add(barCodeInfo);
                        }
                        if (string.IsNullOrWhiteSpace(sourcetypeName))
                        {
                            var ddd = entity["fsourceformid"]?.ToString().ToLower();
                            switch (ddd)
                            {
                                case "ydj_order":
                                    sourcetypeName = "销售合同";
                                    break;
                                case "ydj_purchaseorder":
                                    sourcetypeName = "采购订单";
                                    break;
                                case "stk_otherstockout":
                                    sourcetypeName = "其它出库单";
                                    break;
                            }
                        }
                        if (!sourcebillno.Contains(entity["fsourcebillno"]?.ToString()))
                        {
                            sourcebillno = string.IsNullOrWhiteSpace(sourcebillno) ? entity["fsourcebillno"]?.ToString() : sourcebillno + "," + entity["fsourcebillno"]?.ToString();
                        }
                        materialinfo.barcodeinfos = barCodeInfos;

                        materialinfo.barcodeinfos = barCodeInfos;
                        materials.Add(materialinfo);
                    }

                    deliveryTask.sourcetype = sourcetypeName;
                    deliveryTask.sourcebillno = sourcebillno;
                    deliveryTask.customer = String.Join(",", customer);
                    deliveryTask.customerphone = String.Join(",", customerphone);
                    deliveryTask.deliveryuser = String.Join(",",deliveryuser);
                    deliveryTask.deliveryuserphone = String.Join(",", deliveryuserphone);
                    deliveryTask.deliverydate = String.Join(",", deliverydate);
                    deliveryTask.stockoutdate = String.Join(",", stockoutdate);//出库日期

                    deliveryTask.materialinfos = materials;
                    deliveryTask.materialcount = Convert.ToString(materials.Count);

                    //扫码枪商品排序规则
                    var stockparam = profileService.GetSystemParameter(this.AgentContext, "stk_stockparam");
                    deliveryTask.sortrule = stockparam["fsortrule"]?.ToString();

                    #endregion
                    deliveryTasks.Add(deliveryTask);
                }
            }
            catch (Exception ex)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = ex.Message;
                return;
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = deliveryTasks;
            this.Result.SimpleMessage = "加载数据成功!";
        }

        /// <summary>
        /// 获取商品的单位、库存单位、品牌和系列
        /// </summary>
        /// <param name="bizdatas"></param>
        /// <returns></returns>
        private List<DynamicObject> GetMaterialExtraInfo(List<DynamicObject> bizdatas)
        {
            if (bizdatas == null && !bizdatas.Any())
            {
                return null;
            }
            var materialids = new List<string>();
            foreach (var data in bizdatas)
            {
                var dentitys = data["ftaskentity"] as DynamicObjectCollection;
                var dids = dentitys.Select(f => Convert.ToString(f["fmaterialid"])).Distinct().ToList();
                foreach (var did in dids)
                {
                    if (!materialids.Contains(did))
                    {
                        materialids.Add(did);
                    }
                }
            }
            var tempTableName = "";
            var allMaterials = new List<DynamicObject>();
            if (materialids != null && materialids.Any())
            {
                using (var tran = AgentContext.CreateTransaction())
                {
                    tempTableName = this.DBService.CreateTempTableWithDataList(this.AgentContext, materialids,false);

                    var matSql = string.Format($@"select 
                                        a.fid as id,a.fnumber,a.fname,a.fspecifica,
	                                    c.fid as funitid,c.fnumber as funitnumber,c.fname as funitname,
	                                    d.fid as fstockunitid,d.fnumber as fstockunitnumber,d.fname as fstockunitname,
	                                    e.fid as fbrandid,e.fnumber as fbrandnumber,e.fname as fbrandname,
	                                    f.fid as fseriesid,f.fnumber as fseriesnumber,f.fname as fseriesname 
                                    from T_BD_MATERIAL a with(nolock)
                                    inner join {tempTableName} temp on temp.fid = a.fid
                                    left join T_YDJ_UNIT c with(nolock) on c.fid=a.funitid
                                    left join T_YDJ_UNIT d with(nolock) on d.fid=a.fstockunitid
                                    left join t_ydj_brand e with(nolock) on e.fid=a.fbrandid
                                    left join t_ydj_series f with(nolock) on f.fid=a.fseriesid");

                    allMaterials = this.DBService.ExecuteDynamicObject(this.AgentContext, matSql).ToList();

                    if (!tempTableName.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.DBService.DeleteTempTableByName(this.AgentContext, tempTableName, true);
                    }

                    tran.Complete();
                }
            }
            return allMaterials;
        }
    }

}
