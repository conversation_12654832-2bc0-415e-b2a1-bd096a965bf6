using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Utils
{
    /// <summary>
    /// 套件帮助类
    /// </summary>
    public static class SuiteUtil
    {
        /// <summary>
        /// 获取套件总数
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static BaseDataModel GetPropSuiteCount(this UserContext userCtx)
        {
            var htmlForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "sel_prop");

            string filter = "fname = '套件总件数'";

            var orgFld = htmlForm.GetField("fmainorgid");
            if (htmlForm.Isolate == "1" && orgFld != null)//按组织隔离
            {
                filter = "(fmainorgid='' or fmainorgid='0' or fmainorgid='{1}') and ({0})".Fmt(filter, userCtx.Company);
            }
            else if (orgFld != null)//不按组织隔离，则取总部数据及自己组织的数据
            {
                // 父级企业过滤条件
                var parentCompanyIdFilter = "";
                if (!userCtx.ParentCompanyId.IsNullOrEmptyOrWhiteSpace())
                {
                    parentCompanyIdFilter = $" or fmainorgid='{userCtx.ParentCompanyId}'";
                }
                filter = "(fmainorgid='' or fmainorgid='0' or fmainorgid='{1}' or fmainorgid='{2}' {3}) and ({0})".Fmt(filter, userCtx.Company, userCtx.TopCompanyId, parentCompanyIdFilter);
            }

            var sql = @"select top 1 fid, fnumber, fname from {0} with(nolock) where {1} ".Fmt(htmlForm.HeadEntity.TableName, filter);

            var dynObj = userCtx.ExecuteDynamicObject(sql, new List<SqlParam>()).FirstOrDefault();

            var result = new BaseDataModel();

            if (dynObj != null)
            {
                result.Id = Convert.ToString(dynObj["fid"]);
                result.Name = Convert.ToString(dynObj["fname"]);
                result.Number = Convert.ToString(dynObj["fnumber"]);
            }

            return result;
        }
    }
}
