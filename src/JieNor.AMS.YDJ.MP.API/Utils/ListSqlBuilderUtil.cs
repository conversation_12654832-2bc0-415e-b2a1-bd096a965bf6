using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Utils
{
    /// <summary>
    /// 列表sql构建器扩展类
    /// </summary>
    public static class ListSqlBuilderUtil
    {
        /// <summary>
        /// 构建列表数据和列表描述信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="listBuilder"></param>
        /// <param name="param"></param>
        /// <param name="ignoreSystemDataScope"></param>
        /// <param name="listData"></param>
        /// <param name="listDesc"></param>
        /// <returns></returns>
        public static void BuildListDataAndListDesc(UserContext userCtx, IListSqlBuilder listBuilder,
            SqlBuilderParameter param, bool ignoreSystemDataScope, out List<Dictionary<string, object>> listData, out ListDesc listDesc)
        {

            //// 忽略系统数据范围
            //List<string> oldRoles = null;
            if (ignoreSystemDataScope)
            {
                //userCtx.IgnoreSystemDataScope(out oldRoles);
                param.EnableDataRowACL = false;
            }

            //查询对象
            var queryObj = listBuilder.GetQueryObject(userCtx, param);

            //获取分页数据
            listData = listBuilder.GetQueryData(userCtx, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            listDesc = listBuilder.GetListDesc(userCtx, param, queryObj);

            //// 还原系统数据范围
            //if (ignoreSystemDataScope)
            //{
            //    userCtx.RevertSystemDataScope(oldRoles);
            //}
        }


        /// <summary>
        /// 构建列表数据和列表描述信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="listBuilder"></param>
        /// <param name="param"></param>
        /// <param name="ignoreSystemDataScope"></param> 
        /// <param name="listDesc"></param>
        /// <returns></returns>
        public static void BuildListDesc(UserContext userCtx, IListSqlBuilder listBuilder,
            SqlBuilderParameter param, bool ignoreSystemDataScope, out ListDesc listDesc)
        {

            //// 忽略系统数据范围
            //List<string> oldRoles = null;
            if (ignoreSystemDataScope)
            {
                //userCtx.IgnoreSystemDataScope(out oldRoles);
                param.EnableDataRowACL = false;
            }

            //查询对象
            var queryObj = listBuilder.GetQueryObject(userCtx, param);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            listDesc = listBuilder.GetListDesc(userCtx, param, queryObj);

            //// 还原系统数据范围
            //if (ignoreSystemDataScope)
            //{
            //    userCtx.RevertSystemDataScope(oldRoles);
            //}
        }

    }
}