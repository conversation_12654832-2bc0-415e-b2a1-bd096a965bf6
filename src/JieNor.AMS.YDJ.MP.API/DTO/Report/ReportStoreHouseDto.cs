using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 统计仓库取数接口
    /// </summary>
    [Api("统计仓库取数接口")]
    [Route("/mpapi/report/storehouse")]
    [Authenticate]
    public class ReportStoreHouseDto : BaseListPageDTO
    {
        /// <summary>
        /// 仓库类型Id
        /// </summary>
        public string WarehouseType { get; set; }
    }
}
