using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.DTO.STE.Order
{
    /// <summary>
    /// 合同单检查部门关联门店接口
    /// </summary>
    [Api("合同单检查部门关联门店接口")]
    [Route("/mpapi/order/checkstore")]
    [Authenticate]
    public class OrderCheckStoreDTO 
    {
        /// <summary>
        /// 部门id
        /// </summary>
        public string DeptId { get; set; }
    }
}
