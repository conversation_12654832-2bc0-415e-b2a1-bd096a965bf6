using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO.STE.Order
{
    /// <summary>
    /// 合同单变更接口
    /// </summary>
    [Api("合同单变更接口")]
    [Route("/mpapi/order/change")]
    [Authenticate]
    [CheckBillLockFilter("ydj_order", "change")]
    public class OrderChangeDTO: CheckBillLockDTO
    {
    }
}
