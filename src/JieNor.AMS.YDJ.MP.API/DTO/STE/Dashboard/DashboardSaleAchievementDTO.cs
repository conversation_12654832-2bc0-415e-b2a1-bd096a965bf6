using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 首页销售业绩接口
    /// </summary>
    [Api("首页销售业绩接口")]
    [Route("/mpapi/dashboard/saleachievement")]
    [Authenticate]
    public class DashboardSaleAchievementDTO : BaseDTO
    {
        /// <summary>
        /// 时间维度（本月、上月、今天、昨天）
        /// </summary>
        public string[] DateTypes { get; set; }

        /// <summary>
        /// 数据范围权限
        /// myself：本人
        /// mydepartment：本部门
        /// mysubordinates：本部门及下属部门
        /// mycompany：本企业
        /// </summary>
        public string DataPerm { get; set; }
    }
}
