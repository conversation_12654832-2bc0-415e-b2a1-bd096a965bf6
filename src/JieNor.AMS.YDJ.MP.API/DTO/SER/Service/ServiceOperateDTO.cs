using JieNor.AMS.YDJ.MP.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 服务单操作接口
    /// </summary>
    [Api("服务单操作接口")]
    [Route("/mpapi/service/operate")]
    [Authenticate]
    [CheckBillLockFilter("ydj_service", null)]
    public class ServiceOperateDTO : CheckBillLockDTO
    {
        ///// <summary>
        ///// 服务单id
        ///// </summary>
        //public string Id { get; set; }
        /// <summary>
        /// 操作编码
        /// </summary>
        public string OpId { get; set; }
        /// <summary>
        /// 视操作而定传入json字符串，约定好数据格式
        /// </summary>
        public string JsonObj { get; set; }

        /// <summary>
        /// 所在区域 - 省份
        /// </summary>
        public string Province { get; set; }

        /// <summary>
        /// 所在区域 - 城市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 所在区域 - 区县
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string CollectAdd { get; set; }

        public override string GetOperationNo()
        {
            return OpId;
        }
    }
}
