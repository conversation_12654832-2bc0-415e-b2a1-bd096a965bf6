using JieNor.AMS.YDJ.MP.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 服务单详情取数接口
    /// </summary>
    [Api("服务单详情取数接口")]
    [Route("/mpapi/service/detail")]
    [Authenticate]
    public class ServiceDetailDTO : BaseDetailDTO
    {
    }
}
