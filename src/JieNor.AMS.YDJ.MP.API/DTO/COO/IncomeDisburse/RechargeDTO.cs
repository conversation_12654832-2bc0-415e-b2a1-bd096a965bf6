using JieNor.AMS.YDJ.MP.API.Model;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.COO.IncomeDisburse
{
    /// <summary>
    /// 立即充值
    /// </summary>
    [Api("立即充值")]
    [Route("/mpapi/incomedisburse/recharge")]
    [Authenticate]
    public class RechargeDTO
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 源单id    fsourceid
        /// </summary>
        public string fsourceid { get; set; }

        /// <summary>
        /// 源单类型
        /// </summary>
        public string fsourcetype { get; set; }

        /// <summary>
        /// 源单单号    fsourcenumber
        /// </summary>
        public string fsourceNumber { get; set; }
        /// <summary>
        /// 凭证数组 fimage
        /// </summary>
        public List<BaseDataSaveDTO> certificates { get; set; }
        //充值账户
        public string fusagetype { get; set; }
        /// <summary>
        /// 银行卡号ID
        /// </summary>
        public string fsynbankid { get; set; }
        //支付方式
        public string fway { get; set; }
        //充值金额 必须大于0
        public string fmoney { get; set; }
        //代收单位
        public string fcontactunitid { get; set; }
        //充值日期
        public string fdate { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string fdescription { get; set; }

        /// <summary>
        /// 款项说明
        /// </summary>
        public string paymentdesc { get; set; }
        /// <summary>
        /// 销售部门
        /// </summary>
        public string fdeptid { get; set; }

        /// <summary>
        /// 销售员
        /// </summary>
        public string fstaffid { get; set; }

        /// <summary>
        /// 收款小票号
        /// </summary>
        public string ReceiptNo { get; set; }

        /// <summary>
        /// 联合开单
        /// </summary>
        public List<OrderJoinStaffModel> JoinStaffs { get; set; } = new List<OrderJoinStaffModel>();
    }
}
