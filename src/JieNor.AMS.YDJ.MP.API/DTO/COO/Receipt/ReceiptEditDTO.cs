using JieNor.AMS.YDJ.MP.API.Model;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO.COO.Receipt
{
    [Api("收款单编辑取数接口")]
    [Route("/mpapi/receipt/edit")]
    [Authenticate]
    [ApplyBillLockFilter("coo_incomedisburse")]
    public class ReceiptEditDTO : ReceiptModel, IApplyBillLockDTO
    {

    }
}
