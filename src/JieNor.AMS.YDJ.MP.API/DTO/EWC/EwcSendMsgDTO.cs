using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 企业微信：发送消息接口请求对象
    /// </summary>
    [Route("/work/message/send")]
    public class EwcSendMsgDTO : EwcBaseDTO
    {
        /// <summary>
        /// 发送用户（以 | 隔开，touser、toparty、totag三者必须有一项）
        /// </summary>
        public string Touser { get; set; }

        /// <summary>
        /// 发送部门（以 | 隔开，touser、toparty、totag三者必须有一项）
        /// </summary>
        public string Toparty { get; set; }

        /// <summary>
        /// 发送标签（以 | 隔开，touser、toparty、totag三者必须有一项）
        /// </summary>
        public string Totag { get; set; }

        /// <summary>
        /// 消息标题，长度限制4-12个汉字（支持id转译）
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 消息描述，长度限制4-12个汉字（支持id转译）
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 正文项
        /// </summary>
        public Dictionary<string, string> Items { get; set; }

        /// <summary>
        /// 小程序页面路径，例如 /pages/business/detail
        /// </summary>
        public string Page { get; set; }
    }
}