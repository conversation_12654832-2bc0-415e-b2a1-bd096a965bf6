
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.DTO.MS.AIBedSelectAdvise
{
    [Api("选配意见接口")]
    [Route("/mpapi/aiadvise/advise")]
    [Authenticate]
    public class AiSelectAdviseDTO
    {
        /// <summary>
        /// 回答的选项
        /// </summary>
        public string Answer { set; get; }

        /// <summary>
        /// 回答的选项ID
        /// </summary>
        public string ChoiceId { set; get; }

        /// <summary>
        /// 选项的位置
        /// </summary>
        public string Score { set; get; }
    }
}