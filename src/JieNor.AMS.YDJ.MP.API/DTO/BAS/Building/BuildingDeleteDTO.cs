using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 楼盘删除接口
    /// </summary>
    [Api("楼盘删除接口")]
    [Route("/mpapi/building/delete")]
    [Authenticate]
    public class BuildingDeleteDTO : BaseDTO
    {
        public string Id { get; set; }
    }
}