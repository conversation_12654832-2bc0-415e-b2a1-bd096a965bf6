using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 经销商列表取数接口
    /// </summary>
    [Api("经销商列表取数接口")]
    [Route("/mpapi/agent/list")]
    [Authenticate]
    public class AgentListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 请求的formid，如：ydj_service
        /// </summary>
        public string formId { get; set; }
    }
}