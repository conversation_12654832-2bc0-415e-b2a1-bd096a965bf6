using JieNor.AMS.YDJ.Core;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商品属性检查接口
    /// </summary>
    [Api("商品属性检查接口")]
    [Route("/mpapi/product/propcheck")]
    [Authenticate]
    public class ProductPropCheckDTO : BaseDetailDTO
    {
        /// <summary>
        /// 是否非标选配
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 属性列表
        /// </summary>
        public List<PropEntity> PropList { get; set; }
    }
}