using JieNor.AMS.YDJ.Core;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商品属性取数接口（销售价、可用库存、图片 等等）
    /// </summary>
    [Api("商品属性取数接口")]
    [Route("/mpapi/product/profile")]
    [Authenticate]
    public class ProductProfileDTO : BaseDetailDTO
    {
        /// <summary>
        /// 属性列表
        /// </summary>
        public List<PropEntity> PropList { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; }

        /// <summary>
        /// 是否是非标选配
        /// </summary>
        public bool IsNonStandard { get; set; }
        /// <summary>
        /// 业务日期（取价根据业务日期去取而非当前如期）
        /// </summary>
        public DateTime OrderDate { get; set; }

        /// <summary>
        /// 是否库存选择
        /// </summary>
        public bool IsFromInventory { get; set; } = false;
        /// <summary>
        /// 出现货标识
        /// </summary>
        public bool Isoutspot { get; set; } = false;
        /// <summary>
        /// 是否二级分销
        /// </summary>
        public bool isresellorder { get; set; } = false;
        /// <summary>
        /// 合同所选客户id
        /// </summary>
        public string customerid { get; set; } = "";
        /// <summary>
        /// 销售合同来源单号，二级分销用到
        /// </summary>
        public string sourcenumber { get; set; } = "";
    }
}