using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.Common.Duty
{
    /// <summary>
    /// 新增负责人接口
    /// </summary>
    [Api("新增负责人接口")]
    [Route("/mpapi/common/dutyadd")]
    [Authenticate]
    public class DutyAddDTO
    {
        /// <summary>
        /// id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 部门id
        /// </summary>
        public string DeptId { get; set; }
        /// <summary>
        /// 负责人id
        /// </summary>
        public string DutyId { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        ///  表单识别id
        /// </summary>
        public string FormId { get; set; }
    }
}
