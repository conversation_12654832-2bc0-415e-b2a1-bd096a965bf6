using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.MP.API.Config;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 商机阶段数据模型
    /// </summary>
    public class CustomerRecordGetPhaseModel 
    {  
        public List<CustomerRecordPhaseInfo> Phases { get; set; }
    }
}