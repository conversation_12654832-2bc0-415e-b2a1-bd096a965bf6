using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model.SAL.DataAnalysis
{
    public class RankingEmployeesModel
    {
        /// <summary>
        /// 员工
        /// </summary>
        public string fname { get; set; }

        /// <summary>
        /// 销售额
        /// </summary>
        public decimal fdealamount { get; set; }

        public RankingEmployeesModel() { }

        /// <summary>
        /// 构造数据
        /// </summary>
        /// <param name="fname"></param>
        /// <param name="fdealamount"></param>
        public RankingEmployeesModel(string fname, decimal fdealamount)
        {
            this.fname = fname;
            this.fdealamount = fdealamount;
        }
    }

    public class RankingEmployeesListModel
    {
        public string fstaffid { get; set; }
        /// <summary>
        /// 员工
        /// </summary>
        public string fname { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public string deptname { get; set; }

        /// <summary>
        /// 已审核金额
        /// </summary>
        public decimal amount_audit { get; set; }

        /// <summary>
        /// 未审核金额
        /// </summary>
        public decimal amount_noaudit { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public string AvatarUrl { get; set; }

        public RankingEmployeesListModel() { }

        /// <summary>
        /// 构造数据
        /// </summary>
        /// <param name="fname"></param>
        /// <param name="fdealamount"></param>
        public RankingEmployeesListModel(
            string fstaffid,
            string fname,
            string deptname,
            decimal amount_audit,
            decimal amount_noaudit,
            string AvatarUrl
            )
        {
            this.fstaffid = fstaffid;
            this.fname = fname;
            this.deptname = deptname;
            this.amount_audit = amount_audit;
            this.amount_noaudit = amount_noaudit;
            this.AvatarUrl = AvatarUrl;
        }
    }

    /// <summary>
    /// 我的排名
    /// </summary>
    public class MyRankingMdl : RankingEmployeesListModel
    {
        public int ranking { get; set; }
        public MyRankingMdl() { }
        public MyRankingMdl(
             string fstaffid,
            int ranking,
           string fname,
           string deptname,
           decimal amount_audit,
           decimal amount_noaudit,
           string AvatarUrl
           )
        {
            this.fstaffid = fstaffid;
            this.ranking = ranking;
            this.fname = fname;
            this.deptname = deptname;
            this.amount_audit = amount_audit;
            this.amount_noaudit = amount_noaudit;
            this.AvatarUrl = AvatarUrl;
        }
    }
}
