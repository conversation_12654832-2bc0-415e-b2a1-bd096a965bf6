using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class ServiceReportDetailModel
    {

        //完工时间
        public string Reportdate { get; set; }

        //完工说明
        public string Discription { get; set; }

        /// <summary>
        /// 签字确认图片集
        /// </summary>
        public List<BaseImageModel> SignConfirmImg { get; set; }
        /// <summary>
        /// 现场图片集
        /// </summary>
        public List<BaseImageModel> DoneImg { get; set; }

        /// <summary>
        /// 是否有复购需求
        /// </summary>
        public bool Isrepurchase { get; set; }

    }

}
