using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class CaseListModel : BaseDataModel
    {
        [IgnoreDataMember]
        public new string Name { get; set; }

        /// <summary>
        /// 案例名字
        /// </summary>
        public string Fname { get; set; }

        /// <summary>
        /// 案例首页图片
        /// </summary>
        public string ImageUrl { get; set; }
        /// <summary>
        /// 案例首页图片 缩略图
        /// </summary>
        public string ImageThumbUrl { get; set; }
        /// <summary>
        /// 收藏数
        /// </summary>
        public int LikeCount { get; set; }
    }
}
