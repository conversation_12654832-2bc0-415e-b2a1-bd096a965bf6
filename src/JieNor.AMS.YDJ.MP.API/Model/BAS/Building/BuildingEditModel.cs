using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 商机编辑数据模型
    /// </summary>
    public class BuildingEditModel : BaseDataModel
    {
        [IgnoreDataMember]
        public new ComboDataModel Status { get; set; }

        /// <summary>
        /// 图片
        /// </summary>
        public BaseImageModel Image { get; set; }

        /// <summary>
        /// 总户数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 参考价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 开盘日期
        /// </summary>
        public DateTime? OpenDate { get; set; }

        /// <summary>
        /// 交楼日期
        /// </summary>
        public DateTime? GiveDate { get; set; }

        /// <summary>
        /// 开发商
        /// </summary>
        public string Developer { get; set; }

        /// <summary>
        /// 楼盘户型
        /// </summary>
        public ComboDataModel BuildingType { get; set; } = new ComboDataModel();

        /// <summary>
        /// 装修状态
        /// </summary>
        public ComboDataModel Renovation { get; set; } = new ComboDataModel();

        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel Province { get; set; } = new ComboDataModel();

        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; } = new ComboDataModel();

        /// <summary>
        /// 区域
        /// </summary>
        public ComboDataModel Region { get; set; } = new ComboDataModel();

        /// <summary>
        /// 所在区域
        /// </summary>
        public string District { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 已入住数
        /// </summary>
        public string Inseveral { get; set; }

        /// <summary>
        /// 装修数
        /// </summary>
        public string Decoratenum { get; set; }

        /// <summary>
        /// 未装修数
        /// </summary>
        public string Notdecoratenum { get; set; }
        /// <summary>
        /// 对接人
        /// </summary>
        public string Abutment { get; set; }
        /// <summary>
        /// 对接人电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 楼盘户型
        /// </summary>
        public List<BuildingTypeEntry> BuildingTypeEntries { get; set; } = new List<BuildingTypeEntry>();

        /// <summary>
        /// 辅助资料、简单枚举、单据类型 下拉框数据源
        /// </summary>
        public Dictionary<string, List<Dictionary<string, object>>> ComboData { get; set; }
    }
}