using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Product
{
    /// <summary>
    /// 微信小程序：商品清库存辅助属性取数接口
    /// </summary>
    public class ProductClearStockAuxPropController : BaseController
    {
        /// <summary>
        /// 商品表单模型
        /// </summary>
        protected HtmlForm ProductForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductClearStockAuxPropDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ProductClearStockAuxPropModel>
            {
                Data = new ProductClearStockAuxPropModel
                {
                    AuxPropInfo = new JArray()
                }
            };

            if (!ValidateParam(dto, resp)) return resp;

            //根据唯一标识获取数据
            this.ProductForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var productObj = this.ProductForm.GetBizDataById(this.Context, dto.Id, true);

            //设置响应数据包
            this.SetResponseData(dto, productObj, resp);

            return resp;
        }

        /// <summary>
        /// 校验参数
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool ValidateParam(ProductClearStockAuxPropDTO dto, BaseResponse<ProductClearStockAuxPropModel> resp)
        {
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return false;
            }

            if (dto.SpecValueName.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 specValueName 不能为空！";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 设置响应数据包
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="productObj"></param>
        /// <param name="resp"></param>
        private void SetResponseData(ProductClearStockAuxPropDTO dto, DynamicObject productObj, BaseResponse<ProductClearStockAuxPropModel> resp)
        {
            if (productObj == null)
            {
                resp.Message = "商品不存在或已被删除！";
                resp.Success = false;
                return;
            }

            resp.Message = "取数成功！";
            resp.Success = true;

            var productInfoService = this.Container.GetService<IProductInfoService>();
            productInfoService.InitClearStockInventory(this.Context);

            var clearStockInventoryData = productInfoService.GetClearStockInventoryData(this.Context);

            string productId = dto.Id;
            string productNumber = Convert.ToString(productObj["fnumber"]);
            string specValue = dto.SpecValueName;

            var groups =
                clearStockInventoryData.Where(s => s.GetJsonValue("number", "").EqualsIgnoreCase(productNumber) && s.GetJsonValue("spec", "").EqualsIgnoreCase(specValue)).GroupBy(s => s.GetJsonValue("material", ""));

            var auxPropInfo = ProductUtil.GetAuxPropInfo(this.Context, productId);

            var materialAuxProp = auxPropInfo.FirstOrDefault(s => s.PropName.EqualsIgnoreCase("材质"));
            var colorAuxProp = auxPropInfo.FirstOrDefault(s => s.PropName.EqualsIgnoreCase("颜色"));

            var mainAuxPropForm = this.MetaModelService.LoadFormModel(this.Context, "bas_mainauxprop");
            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();

            foreach (var group in groups)
            {
                var item = new JObject();
                item["auxPropId"] = materialAuxProp?.PropId ?? string.Empty;
                item["auxPropName"] = materialAuxProp?.PropName ?? string.Empty;
                item["valueId"] = group.Key;
                item["valueName"] = group.Key;

                // 获取主辅属性约束
                var mainAuxProp = this.Context.LoadBizDataByFilter("bas_mainauxprop",
                    $" fmainorgid='{this.Context.Company}' and fauxpropid='{item["auxPropId"]}' and frefenumvalue in (select fentryid from t_bd_enumdataentry with(nolock) where fenumitem='{item["valueId"]}')").FirstOrDefault();
                refMgr.Load(this.Context, mainAuxPropForm.GetDynamicObjectType(this.Context), mainAuxProp, false);
                var entitys = mainAuxProp?["fentity"] as DynamicObjectCollection;
                //if (entitys == null) continue;

                var auxProps = new JObject();
                string auxPropId = colorAuxProp?.PropId ?? string.Empty;
                auxProps["auxPropId"] = auxPropId;
                auxProps["auxPropName"] = colorAuxProp?.PropName ?? string.Empty;

                var valueList = new JArray();
                foreach (var g in group)
                {
                    var value = new JObject();
                    string valueId = g.GetJsonValue("color", "");
                    value["valueId"] = valueId;
                    value["valueName"] = valueId;

                    var entity = entitys?.FirstOrDefault(s =>
                        Convert.ToString(s["fauxpropid"]).EqualsIgnoreCase(auxPropId) &&
                        Convert.ToString((s["frefenumvalue_ref"] as DynamicObject)?["fenumitem"]).EqualsIgnoreCase(valueId)
                        );

                    if (entity != null)
                    {
                        //// 排除禁用
                        //if (Convert.ToBoolean(entity["fisdisable"])) continue;

                        var image = ImageFieldUtil.ParseImage(Convert.ToString(entity["fimage"]),
                            Convert.ToString(entity["fimage_txt"]), true);
                        value["image"] = JToken.FromObject(new
                        {
                            id = image?.Id ?? string.Empty,
                            name = image?.Name ?? string.Empty,
                            url = image?.Url ?? string.Empty,
                            thumbUrl = image?.ThumbUrl ?? string.Empty
                        });
                    }
                    else
                    {
                        value["image"] = JToken.FromObject(new
                        {
                            id = "",
                            name = "",
                            url = "",
                            thumbUrl = ""
                        });
                    }

                    valueList.Add(value);
                }

                auxProps["valueList"] = valueList;
                item["auxProps"] = auxProps;

                resp.Data.AuxPropInfo.Add(item);
            }
        }
    }
}