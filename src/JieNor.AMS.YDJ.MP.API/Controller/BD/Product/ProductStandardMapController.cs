using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.MP.API.DTO.SEL.Suite;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.AMS.YDJ.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Product
{
    /// <summary>
    /// 微信小程序：商品标准品映射匹配接口
    /// </summary>
    public class ProductStandardMapController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductStandardMapDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            //验证参数
            if (!this.Validate(dto, resp)) return resp;

            //属性选配服务
            var propSelService = this.Container.GetService<IPropSelectionService>();

            //匹配标准品
            var standardProductId = propSelService.LoadStandardProductId(this.Context, dto.Id, dto.PropList);

            //数据隔离规则SQL
            var authPara = new DataQueryRuleParaInfo()
            {
                SrcFormId = dto.SrcFormId,
            };
            authPara.SrcPara.Add("billtypeName", dto.BillTypeName);
            authPara.SrcPara.Add("billtypeNo", dto.BillTypeNo);
            var allProductView = Context.GetAuthProductDataPKID(authPara);

            //匹配加载配件列表
            var partsList = this.LoadPartsList(dto, standardProductId, resp, allProductView);

            //设置响应数据包
            resp.Data = new
            {
                standardProductId = standardProductId,
                partsList = partsList
            };

            if (!resp.Success) return resp;

            resp.Message = "标准品和配件取数成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 加载配件列表
        /// </summary>
        private List<PartsBaseDTO> LoadPartsList(ProductStandardMapDTO dto, string standardProductId, BaseResponse<object> resp, string allProductView)
        {
            var partsList = new List<PartsBaseDTO>();

            //如果有匹配到标准品，则不用处理
            //2024-09-23 禅道：65042 【转内部需求】【240775】 【慕思现场-8.8-8.12需求】小程序铁架床料号定制转标准品后，没有自动带出配件
            //if (!standardProductId.IsNullOrEmptyOrWhiteSpace())
            //{
            //    resp.Success = true;
            //    return partsList;
            //}

            //选配配件服务
            var selPartsService = this.Container.GetService<ISelectionPartsService>();
            var partsMapingEntrys = selPartsService.LoadPartsMapingEntryList(this.Context, dto.Id, dto.PropList, allProductView);

            //如果不是非标 并且 是自动匹配配件时，检查匹配到的配件映射明细
            if (!dto.IsNonStandard && !dto.IsMuiltiParts)
            {
                var checkResult = selPartsService.CheckPartsMapingEntrys(this.Context, dto.PropList, partsMapingEntrys);
                if (!checkResult.IsSuccess)
                {
                    resp.Message = checkResult.SimpleMessage;
                    resp.Success = false;
                    return partsList;
                }
            }
            resp.Success = true;
            var productId = dto.Id;
            if (!standardProductId.IsNullOrEmptyOrWhiteSpace()) 
            {
                productId = standardProductId;
            }
            var Mainproduct = this.Context.LoadBizDataById("ydj_product", productId);
            var fispresetprop = Convert.ToBoolean(Mainproduct["fispresetprop"]);
            var fispartflag = Convert.ToBoolean(Mainproduct["fispartflag"]);
            //如果是勾选了配件标记但是 未勾选允许选配（即标准品）带出铁架床配件逻辑
            if (fispartflag && !fispresetprop)
            {
                // 向麦浩系统发送请求
                var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                    this.Request,
                    new CommonBillDTO()
                    {
                        FormId = "ydj_order",
                        OperationNo = "tgcpartbystandard",
                        SimpleData = new Dictionary<string, string>
                        {
                            { "fproductid", productId }
                        }
                    });
                var result = response?.OperationResult;

                var re = result.ToResponseModel<object>(false);
                if (result.IsSuccess) {
                    var data = result.SrvData as string;
                    var parts = JArray.Parse(data);
                    if (parts.Count > 0) {
                        //前端要求 铁架床配件只返回一个
                        var part = parts.FirstOrDefault();
                        var partproductid = part.GetJsonValue<string>("fmaterialid");
                        var partproduct = this.Context.LoadBizDataById("ydj_product", partproductid);

                        var po = new PartsBaseDTO
                        {
                            Product = new BaseDataDTO
                            {
                                Id = Convert.ToString(partproduct["Id"]),
                                Name = Convert.ToString(partproduct["fname"]),
                                Number = Convert.ToString(partproduct["fnumber"])
                            },
                            ProductCategoryId = Convert.ToString(partproduct["fcategoryid"]),
                            AuxPropVals = ProductUtil.LoadPropEntityList(this.Context, Convert.ToString(part?["attrinfoid"])),
                            ImageList = ProductUtil.GetImages(this.Context, partproduct, ""),
                            ProductCategory = new BaseDataDTO
                            {
                                Id = Convert.ToString(partproduct["fcategoryid"]),
                                Name = Convert.ToString(part?["parttype"]),
                                Number = ""
                            },
                            Qty = Convert.ToDecimal(part?["fqty"]),
                            IsNonStandard = Convert.ToBoolean(partproduct["funstdtype"]),
                            IsSofaCategory = ProductUtil.HaveAnyCategory(this.Context, "沙发类", Convert.ToString(partproduct["Id"]))
                        };
                        ProductProfileDTO pp = new ProductProfileDTO
                        {
                            Id = partproductid,
                            PropList = po.AuxPropVals,
                            CustomDesc = ""
                        };
                        var profile = this.Gateway.Send<BaseResponse<ProductProfileModel>>(pp);
                        if (profile.Success)
                        {
                            po.ImageList = profile.Data.ImageList;
                            po.SalPrice = profile.Data.SalPrice;
                            po.HqSalPrice = profile.Data.HqSalPrice;
                            po.GuidePrice = profile.Data.GuidePrice;
                        }
                        partsList.Add(po);
                    }
                    return partsList;
                }
            }

            //按配件类别分组
            var groups = partsMapingEntrys.GroupBy(o => Convert.ToString(o["fcategoryid"])).ToList();
            //var TGC = partsMapingEntrys.Where(o => Convert.ToString(o["fcategoryname"]).EqualsIgnoreCase("铁架床")).GroupBy(o => Convert.ToString(o["fmaterialid"])).ToList();
            //groups.AddRange(TGC);
            foreach (var group in groups)
            {
                List<DynamicObject> list = new List<DynamicObject>();
                if (!dto.IsMuiltiParts)
                {
                    //铁架床匹配到多个就返回多个
                    //if (group.Any(o=>Convert.ToString(o["fcategoryname"]).EqualsIgnoreCase("铁架床")))
                    //{
                    //    list.AddRange(group);
                    //}
                    //else {
                    //    //相同的配件类型只需要返回一个配件即可
                    //    var firstParts = group.FirstOrDefault();
                    //    if (firstParts == null) continue;

                    //    list.Add(firstParts);
                    //}
                    //相同的配件类型只需要返回一个配件即可
                    var firstParts = group.FirstOrDefault();
                    if (firstParts == null) continue;

                    list.Add(firstParts);
                }
                else
                {
                    list = group.ToList();
                }
                foreach (var item in list)
                {
                    var propList = ProductUtil.LoadPropEntityList(this.Context, Convert.ToString(item["fattrinfo"]));

                    var partsproductid = Convert.ToString(item["fmaterialid"]);
                    ProductProfileDTO pp = new ProductProfileDTO
                    {
                        Id = partsproductid,
                        PropList = propList,
                        CustomDesc = ""
                    };

                    var partproduct = this.Context.LoadBizDataById("ydj_product", partsproductid);

                    var po = new PartsBaseDTO
                    {
                        Product = new BaseDataDTO
                        {
                            Id = Convert.ToString(partproduct["Id"]),
                            Name = Convert.ToString(partproduct["fname"]),
                            Number = Convert.ToString(partproduct["fnumber"])
                        },
                        ProductCategoryId = Convert.ToString(partproduct["fcategoryid"]),
                        AuxPropVals = propList,
                        ImageList = ProductUtil.GetImages(this.Context, partproduct, ""),
                        ProductCategory = new BaseDataDTO
                        {
                            Id = Convert.ToString(item["fcategoryid"]),
                            Name = Convert.ToString(item["fcategoryname"]),
                            Number = ""
                        },
                        Qty = Convert.ToDecimal(item["fqty"]),
                        IsNonStandard = Convert.ToBoolean(partproduct["funstdtype"]),
                        IsSofaCategory = ProductUtil.HaveAnyCategory(this.Context, "沙发类", Convert.ToString(partproduct["Id"]))
                };
                    //判断当前配件是否允许定制
                    var Iscustom = Convert.ToBoolean(partproduct["fcustom"]);
                    //如果是床箱底板配件且允许定制，则需要在此明细记录定制说明：
                    if (Convert.ToString(item["fcategoryname"]).EqualsIgnoreCase("床箱底板") && Iscustom) 
                    {
                       var Has= dto.PropList.Where(o => o.PropName == "床架其它定制").FirstOrDefault();
                        if (Has!=null)
                        {
                            var seltype =Convert.ToString(partproduct["fseltypeid"]);
                            var seltypename = this.Context.LoadBizDataById("sel_type", seltype);
                            if (seltypename != null) {
                                var str = "床架型号:" + Convert.ToString(seltypename?["fname"]) + ";床架其它定制：" + Convert.ToString(Has.ValueName);
                                po.customdes = str;
                            }

                        }
                    }

                    var profile = this.Gateway.Send<BaseResponse<ProductProfileModel>>(pp);
                    if (profile.Success)
                    {
                        po.ImageList = profile.Data.ImageList;
                        po.SalPrice = profile.Data.SalPrice;
                        po.HqSalPrice = profile.Data.HqSalPrice;
                        po.GuidePrice = profile.Data.GuidePrice;
                    }

                    partsList.Add(po);
                }
            }

            //var Isunstdtype = Mainproduct["funstdtype"];
            var Isunstdtype = dto.IsNonStandard;
            //如果非标定制 找不到满足条件的配件的情况 将通过主商品型号 找到型号档案中配置的床箱底板商品 返回到前台

            //如果属性中不存在配件属性，则不用处理
            var PropName = dto.PropList
            .Where(o => (o.ValueName ?? "").Trim().EqualsIgnoreCase("有"))
            .Select(o => o.PropName)
            .ToList();
            if (Convert.ToBoolean(Isunstdtype) && !partsList.Any(o => Convert.ToString(o.ProductCategory.Name) == "床箱底板"))
            {
                if (PropName.Any(o => Convert.ToString(o).EqualsIgnoreCase("床箱底板"))) 
                {
                    //额外增加判断只有当前在做非标的商品对应的【商品类别】为 "床架" (编码为1001) 的才可以带出来该床箱底板
                    var categoryid = Convert.ToString(Mainproduct?["fcategoryid"]);
                    //上级类别是否包含床架
                    if (CheckCategory(categoryid)) 
                    {

                        var Category = this.Context.LoadBizDataByACLFilter("ydj_category", "fname = '床箱底板'").FirstOrDefault();
                        //获取主商品型号
                        var fseltypeid = Mainproduct["fseltypeid"];
                        if (!fseltypeid.IsNullOrEmptyOrWhiteSpace())
                        {
                            //根据主商品型号 获取型号档案中配置的床箱底板商品
                            var sel_type = this.Context.LoadBizDataById("sel_type", Convert.ToString(fseltypeid));
                            var unstdtypeproduct =Convert.ToString(sel_type["fproductid"]);
                            if (!unstdtypeproduct.IsNullOrEmptyOrWhiteSpace())
                            {
                                //var product = this.Context.LoadBizDataById("ydj_product", Convert.ToString(unstdtypeproduct));
                                var product = this.Context.LoadBizDataByFilter("ydj_product", $"fnumber = '{unstdtypeproduct.Trim()}'").FirstOrDefault();
                                if (product != null)
                                {
                                    var Iscustom = Convert.ToBoolean(product["fcustom"]);
                                    var po = new PartsBaseDTO
                                    {
                                        Product = new BaseDataDTO
                                        {
                                            Id = Convert.ToString(product["Id"]),
                                            Name = Convert.ToString(product["fname"]),
                                            Number = Convert.ToString(product["fnumber"])
                                        },
                                        ProductCategoryId = Convert.ToString(product["fcategoryid"]),
                                        AuxPropVals = GetUnsdAuxPorpVals(dto.PropList),
                                        ImageList = ProductUtil.GetImages(this.Context, product, ""),
                                        ProductCategory = new BaseDataDTO
                                        {
                                            Id = Convert.ToString(Category["Id"]),
                                            Name = Convert.ToString(Category["fname"]),
                                            Number = ""
                                        },
                                        Qty = 1,
                                        IsNonStandard = true
                                    };
                                    //如果是床箱底板配件且允许定制，则需要在此明细记录定制说明：
                                    if (Iscustom)
                                    {
                                        var Has = dto.PropList.Where(o => o.PropName == "床架其它定制").FirstOrDefault();
                                        if (Has != null)
                                        {
                                            var seltype = Convert.ToString(product["fseltypeid"]);
                                            var seltypename = this.Context.LoadBizDataById("sel_type", seltype);
                                            if (seltypename != null)
                                            {
                                                var str = "床架型号:" + Convert.ToString(seltypename?["fname"]) + ";床架其它定制：" + Convert.ToString(Has.ValueName);
                                                po.customdes = str;
                                            }

                                        }
                                    }
                                    ProductProfileDTO pp = new ProductProfileDTO
                                    {
                                        Id = po.Product.Id,
                                        PropList = po.AuxPropVals,
                                        CustomDesc = ""
                                    };
                                    var profile = this.Gateway.Send<BaseResponse<ProductProfileModel>>(pp);
                                    if (profile.Success)
                                    {
                                        po.ImageList = profile.Data.ImageList;
                                        po.SalPrice = profile.Data.SalPrice;
                                        po.HqSalPrice = profile.Data.HqSalPrice;
                                        po.GuidePrice = profile.Data.GuidePrice;
                                    }
                                    partsList.Add(po);
                                }
                            }
                        }
                    }
                }
            }

            return partsList;
        }

        private List<PropEntity> GetUnsdAuxPorpVals(List<PropEntity> propLists)
        {
            List<PropEntity> propList = new List<PropEntity>();
            List<Dictionary<string, object>> propValueList = new List<Dictionary<string, object>>();
            //处理前端录入的非标值
            var noStdValueList = propLists
                .Where(o => o.PropName.EqualsIgnoreCase("床架长")
                || o.PropName.EqualsIgnoreCase("床架宽")
                || o.PropName.EqualsIgnoreCase("床脚高度")
                || o.PropName.EqualsIgnoreCase("床架其它定制")
                )
                .ToList();
            List<PropEntity> newPropList = new List<PropEntity>();
            //组装新的属性 床架长--> 床箱底板长 ;床架宽--> 床箱底板宽 ;床脚高度--> 床脚高度
            foreach (var a in noStdValueList)
            {
                var PropName = "";
                switch (a.PropName)
                {
                    case "床架长":
                        PropName = "床箱底板长";
                        break;
                    case "床架宽":
                        PropName = "床箱底板宽";
                        break;
                    case "床脚高度":
                        PropName = "床脚高度";
                        break;
                    case "床架其它定制":
                        PropName = "床箱底板其它定制";
                        break;
                }
                if (!PropName.IsNullOrEmptyOrWhiteSpace())
                {
                    var sel_propobj = this.Context.LoadBizDataByFilter("sel_prop", $"fname ='{PropName}'").FirstOrDefault();
                    newPropList.Add(new PropEntity
                    {
                        PropId = Convert.ToString(sel_propobj?["id"]),
                        PropName = PropName,
                        PropNumber = Convert.ToString(sel_propobj?["fnumber"]),
                        PropValueDataType = a.PropValueDataType,
                        ValueId = Convert.ToString(a.ValueId),
                        ValueName = Convert.ToString(a.ValueName),
                        ValueNumber = Convert.ToString(a.ValueNumber)
                    });
                }
            }

            //属性选配服务
            var propSelService = this.Container.GetService<IPropSelectionService>();
            var propValueObjs = propSelService.LoadOrCreatePropValue(this.Context, newPropList);
            //主要处理将 组装新的属性 床架长--> 床箱底板长 ;床架宽--> 床箱底板宽 ;床脚高度--> 床脚高度 如果系统没有 录入的非标值则创建此非标值
            foreach (var item in propValueObjs)
            {
                var propId = Convert.ToString(item["fpropid"]);
                var valueId = Convert.ToString(item["id"]);
                var valueName = Convert.ToString(item["fname"]);
                var valueNumber = Convert.ToString(item["fnumber"]);

                var ProPObj = newPropList.Where(o => o.PropId.EqualsIgnoreCase(propId)).FirstOrDefault();

                propValueList.Add(new Dictionary<string, object>
                {
                    { "fauxpropid" , Convert.ToString(ProPObj.PropId)},
                    { "fname" , Convert.ToString(ProPObj.PropName)},
                    { "fnumber",Convert.ToString(ProPObj.PropNumber)},
                    { "fvalueid",Convert.ToString(item["id"])},
                    { "fvaluenumber",Convert.ToString(item["fnumber"])},
                    { "fvaluename",Convert.ToString(item["fname"])}
                });

                propList.Add(new PropEntity
                {
                    PropId = Convert.ToString(ProPObj.PropId),
                    PropName = Convert.ToString(ProPObj.PropName),
                    PropNumber = Convert.ToString(ProPObj.PropNumber),
                    ValueId = valueId,
                    ValueName = valueName,
                    ValueNumber = valueNumber
                });
            }
            return propList;
        }

        /// <summary>
        /// 验证参数
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Validate(ProductStandardMapDTO dto, BaseResponse<object> resp)
        {
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return false;
            }

            //if (dto.PropList == null || !dto.PropList.Any())
            //{
            //    resp.Success = false;
            //    resp.Message = $"属性列表 propList 不能为空！";
            //    return false;
            //}

            return true;
        }

        /// <summary>
        /// 校验商品类别以及上级商品类别 是否等于 “床架”
        /// </summary>
        /// <param name="fcategoryid"></param>
        /// <returns></returns>
        private bool CheckCategory(string fcategoryid)
        {
            string sql = $@" select c1.fid fid1,c1.fname fname1,
                            c2.fparentid fparentid2,c2.fid fid2,c2.fname fname2,
                            c3.fparentid fparentid3,c3.fid fid3,c3.fname fname3 
                            from ser_ydj_category c1 with(nolock) 
                            left join ser_ydj_category c2 with(nolock) on c2.fid=c1.fparentid
                            left join ser_ydj_category c3 with(nolock) on c3.fid=c2.fparentid 
                            where c1.fid='{fcategoryid}' AND (c1.fnumber='1001' OR c2.fnumber='1001' OR c3.fnumber='1001')";
            return this.DBService.ExecuteDynamicObject(this.Context, sql).Count > 0;
        }
    }
}