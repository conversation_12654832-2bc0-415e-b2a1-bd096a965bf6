using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.Assist
{
    /// <summary>
    /// 微信小程序：所有下拉框（辅助资料、简单枚举下拉框、单据类型）列表取数接口
    /// </summary>
    public class AllComboListController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(AllComboDataListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            if (dto.FormId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 formId 不能为空！";
                return resp;
            }

            var fieldKeys = dto.FieldKey?.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fieldKeys == null || fieldKeys.Length < 1)
            {
                resp.Success = false;
                resp.Message = $"参数 fieldKey 不能为空！";
                return resp;
            }

            var metaService = this.Container?.GetService<IMetaModelService>();
            var bizForm = metaService?.LoadFormModel(this.Context, dto.FormId);
            var comboDs = bizForm.GetComboDataSource(this.Context, dto.FieldKey);

            resp.Data = comboDs;
            resp.Message = "取数成功！";
            resp.Success = true;
            return resp;
        }
    }
}