using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.Common.Print
{
    /// <summary>
    /// 微信小程序：打印模板列表接口
    /// </summary>
    public class PrintTemplateListController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(PrintTemplateListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<List<BaseDataModel>>();

            if (dto.SourceType.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 sourceType 不能为空！";
                resp.Success = false;
                return resp;
            }

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data = MapTo(dto);

            return resp;
        }

        private List<BaseDataModel> MapTo(PrintTemplateListDTO dto)
        {
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fsrcformid", System.Data.DbType.String, dto.SourceType)
            };


            var sqlText = $@"select fid,fnumber,fname from t_bas_officetmpl with(nolock) where fmainorgid=@fmainorgid and fsrcformid=@fsrcformid order by fdefault desc";

            if (dto.ExportExcel)//如果是导出excel文件
            {
                sqlText= $@"select fid,fnumber,fname from t_bas_officetmpl with(nolock) where fmainorgid=@fmainorgid and fsrcformid=@fsrcformid and ffiletype='xlsx' order by fdefault desc";
            }
            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParams);

            var models = new List<BaseDataModel>();
            foreach (var item in list)
            {
                var model = new BaseDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fid"]),
                    Number = JNConvert.ToStringAndTrim(item["fnumber"]),
                    Name = JNConvert.ToStringAndTrim(item["fname"])
                };

                models.Add(model);
            }

            return models;
        }
    }
}