using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.Interface;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.Controller.Common
{
    /// <summary>
    /// 微信小程序：销售管理参数取数接口
    /// </summary>
    public class DownloadFileController : ServiceStack.Service
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(DownloadFileDTO dto)
        {
            if (dto.ExportUrl.IsNullOrEmptyOrWhiteSpace()) return "参数 exportUrl 不能为空！";

            if (!dto.ExportUrl.StartsWith("/prints/")) return "只允许下载导出文件！";

            //当前下载文件的文件名
            string downloadFileName = "", downloadFilePath = "";
            bool isDeleteTempFile = false;

            //导入文件的路径，比如：/export/1493988312899.xlsx
            string exportUrl = dto.ExportUrl;

            //文件的相对路径，比如：export/1493988312899.xlsx
            var relativePath = exportUrl.TrimStart('/');

            //文件名，比如：1493988312899.xlsx
            downloadFileName = Path.GetFileName(relativePath);

            //文件的物理路径，比如：E:\SVN\jienorcode\fw\branches\v1.0\src2\JieNor.Framework.Web\export\1493988312899.xlsx
            downloadFilePath = HttpContext.Current.Server.MapPath("~/" + relativePath);

            //isDeleteTempFile = true; 

            string extName = Path.GetExtension(downloadFilePath);

            if (extName.ToLower() == ".xlsx")
            {
                //以文件路径的形式下载文件 
                this.Response.AddHeader("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode(downloadFileName, Encoding.UTF8));
                this.Response.AddHeader("Set-Cookie", "fileDownload=true; path=/");
                this.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                this.Response.WriteFile(downloadFilePath);
                this.Response.Close();
            }
            else
            {
                //以字符流的形式下载文件 
                FileStream fs = new FileStream(downloadFilePath, FileMode.Open);
                byte[] bytes = new byte[(int)fs.Length];
                fs.Read(bytes, 0, bytes.Length);
                fs.Close();
                this.Response.AddHeader("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode(downloadFileName, Encoding.UTF8));
                this.Response.AddHeader("Content-Disposition", "attachment;filename=" + downloadFileName);
                this.Response.AddHeader("Set-Cookie", "fileDownload=true; path=/");
                this.Response.WriteBytesToResponse(bytes, "application/octet-stream");
                this.Response.Close();
            }

            return null;
        }
    }
}