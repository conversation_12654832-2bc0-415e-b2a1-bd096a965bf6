using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.Report
{
    /// 微信小程序：统计获取仓库取数接口
    /// </summary>
    public class ReportStoreHouseController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ReportStoreHouseDto dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<BaseDataSimpleModel>>();
            resp.Data.List = new List<BaseDataSimpleModel>();


            //获取仓库数据列表
            DynamicObjectCollection StoreHouseList = GetStoreHouseList(dto);

            //获取总记录数
            int total = GetTotal(dto);

            if (StoreHouseList == null || !StoreHouseList.Any())
            {
                resp.Success = true;
                resp.Message = "暂无仓库数据！";
                return resp;
            }

            var storeHouses = new List<BaseDataSimpleModel>();
            foreach (var storeHouse in StoreHouseList)
            {
                //仓库
                storeHouses.Add(new BaseDataSimpleModel
                {
                    Id = Convert.ToString(storeHouse["fid"]),
                    Number = Convert.ToString(storeHouse["fnumber"]),
                    Name = Convert.ToString(storeHouse["fname"]),
                });
            }

            resp.Message = "取数成功！";
            resp.Success = true;
            resp.Data.TotalRecord = total;
            resp.Data.PageSize = dto.PageSize;
            resp.Data.List = storeHouses;
            return resp;
        }

        /// <summary>
        /// 获取总记录数
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private int GetTotal(ReportStoreHouseDto dto)
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
            };
            var wheresql = "";
            if (!dto.WarehouseType.IsNullOrEmptyOrWhiteSpace())
            {
                wheresql += $@" and fwarehousetype=@fwarehousetype";
                sqlParam.Add(new SqlParam("@fwarehousetype", System.Data.DbType.String, dto.WarehouseType));
            }
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                wheresql += $@" and (fname like @key or fnumber like @key)";
                sqlParam.Add(new SqlParam("@key", System.Data.DbType.String, $@"%{dto.Keyword}%"));
            }
            var sql = $@"select count(1) total from T_YDJ_STOREHOUSE with(nolock) where fmainorgid=@fmainorgid and fforbidstatus='0' {wheresql} ";

            var res = this.DBService.ExecuteDynamicObject(Context, sql, sqlParam);
            if (res != null && res.Any())
            {
                return Convert.ToInt32(res[0]["total"]);
            }
            else
            {
                return 0;
            }
        }

        //获取仓库列表
        private DynamicObjectCollection GetStoreHouseList(ReportStoreHouseDto dto)
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
            };
            var wheresql = "";
            if (!dto.WarehouseType.IsNullOrEmptyOrWhiteSpace())
            {
                wheresql += $@" and fwarehousetype=@fwarehousetype";
                sqlParam.Add(new SqlParam("@fwarehousetype", System.Data.DbType.String, dto.WarehouseType));
            }
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                wheresql += $@" and (fname like @key or fnumber like @key)";
                sqlParam.Add(new SqlParam("@key", System.Data.DbType.String, $@"%{dto.Keyword}%"));
            }
            var sql = $@" select  * from (select  ROW_NUMBER() over(order by fid) rownum,fid,fnumber,fname,fstockid from T_YDJ_STOREHOUSE with(nolock)  where fmainorgid=@fmainorgid and fforbidstatus='0' {wheresql})t where t.rownum between {(dto.PageIndex - 1) * dto.PageSize + 1} and {dto.PageIndex * dto.PageSize}";

            var res = this.DBService.ExecuteDynamicObject(Context, sql, sqlParam);
            return res;
        }


        /// <summary>
        /// 获取用户表单数据范围权限
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        private List<DataRowAuthInfo> GetDataRowACLPermDataByUser(UserContext ctx, string formId)
        {
            List<DataRowAuthInfo> drAuth = new List<DataRowAuthInfo>();
            string strSql = $@" select distinct t0.froleid, fbizobjid,ffiltertype,fexpress,fdesc ,t0.fbdfldfilter
                                from t_sec_roledatarowacl t0  with(nolock) 
                                inner join t_sec_roleuser t1  with(nolock) on t0.froleId=t1.froleId
                                where t1.fuserid=@fuserid 
                                    and (t0.fcompanyid=@fcompanyid or t0.fcompanyid=@ftoporgid)
                                    and t0.fbizobjid=@fbizobjid ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@fuserid", System.Data.DbType.String,ctx.UserId),
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),//经销商管理员角色在总部组织下
                new SqlParam("@fbizobjid", System.Data.DbType.String,formId),
            };

            var svc = ctx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    var authInfo = new DataRowAuthInfo()
                    {
                        RoleId = reader.GetValue<string>("froleid"),
                        FormId = reader.GetValue<string>("fbizobjid"),
                        FilterType = reader.GetValue<string>("ffiltertype"),
                        Express = reader.GetValue<string>("fexpress"),
                        Desc = reader.GetValue<string>("fdesc"),
                    };
                    var bdFilter = reader.GetValue<string>("fbdfldfilter");
                    if (!bdFilter.IsNullOrEmptyOrWhiteSpace())
                    {
                        authInfo.BDFldsAuthInfo = bdFilter.FromJson<List<BDFldDataRowAuthInfo>>();
                    }

                    drAuth.Add(authInfo);
                }
            }
            return drAuth;
        }
    }
}
