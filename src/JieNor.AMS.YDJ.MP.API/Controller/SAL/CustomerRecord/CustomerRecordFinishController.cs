using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.CustomerRecord
{
    /// <summary>
    /// 微信小程序：商机成单关闭接口
    /// </summary>
    public class CustomerRecordFinishController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerRecordFinishDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"id不能为空！";
                return resp;
            }

            if (dto.Phone.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"手机号不能为空！";
                return resp;
            }

            var customerRecordObj = this.Context.GetBizDataById("ydj_customerrecord", dto.Id);
            if (customerRecordObj == null)
            {
                resp.Message = "商机不存在或已被删除！";
                return resp;
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_customerrecord",
                    OperationNo = "finish",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "id", dto.Id },
                        { "phone", dto.Phone }
                    }
                });
            var result = response?.OperationResult;

            return result.ToResponseModel<BaseDataModel>();
        }
    }
}