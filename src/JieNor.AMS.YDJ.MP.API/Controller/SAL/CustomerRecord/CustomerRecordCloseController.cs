using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.CustomerRecord
{
    /// <summary>
    /// 微信小程序：商机关闭接口
    /// </summary>
    public class CustomerRecordCloseController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerRecordCloseDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            if (dto.CloseReasonId.IsNullOrEmptyOrWhiteSpace()||
                dto.CloseReasonName.IsNullOrEmptyOrWhiteSpace()
                )
            {
                resp.Success = false;
                resp.Message = $"参数 原因 不能为空！";
                return resp;
            }

            //直接根据唯一标识获取数据
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
            var data = htmlForm.GetBizDataById(this.Context, dto.Id);

            if (data == null)
            {
                resp.Message = "商机不存在或已被删除！";
                resp.Success = false;
                return resp;
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_customerrecord",
                    OperationNo = "customerrecordclose",
                    BillData = BuildBillData(data),
                    Id = dto.Id,
                    SimpleData = new Dictionary<string, string>
                    {
                        { "closeReason", dto.Reason },
                        { "closeEnum", dto.CloseReasonId },
                        { "closeEnumName", dto.CloseReasonName },
                    }
                });
            var result = response?.OperationResult;

            resp = result.ToResponseModel<BaseDataModel>();

            return resp;
        }


        private string BuildBillData(DynamicObject data)
        {
            string billData = new List<DynamicObject> { data }.ToJson();

            return billData;
        }

    }
}