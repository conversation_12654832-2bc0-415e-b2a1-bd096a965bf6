using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;

namespace JieNor.AMS.YDJ.MP.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：检查企业微信用户是否已绑定麦浩账号的接口
    /// </summary>
    public class CheckUserController : ServiceStack.Service
    {
        /// <summary>
        /// 服务容器
        /// </summary>
        private IServiceContainer Container { get; set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        private ILogServiceEx LogService { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CheckUserDTO dto)
        {
            this.Container = this.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            this.LogService = this.Container.GetService<ILogServiceEx>();

            var resp = new BaseResponse<object>();

            if (dto.Code.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"绑定失败：微信用户登录凭证 {nameof(dto.Code)} 不能为空！";
                return resp;
            }

            //调用企业微信临时登录凭证校验接口
            var ewcRes = EwcJsonClient.Invoke<EwcCode2SessionData, EwcCode2SessionDTO>(
                new EwcCode2SessionDTO()
                {
                    JS_Code = dto.Code
                });
            var ewcData = ewcRes.Data;
            if (!ewcRes.Success)
            {
                var errMsg = $"调用企业微信API失败：{ewcRes.Code}-{ewcRes.Msg}";
                this.LogService.Error(errMsg);
                resp.Message = errMsg;
                return resp;
            }

            if (ewcData.CorpId.IsNullOrEmptyOrWhiteSpace()
                || ewcData.Open_UserId.IsNullOrEmptyOrWhiteSpace()
                || ewcData.Session_Key.IsNullOrEmptyOrWhiteSpace())
            {
                this.LogService.Error($"调用企业微信API失败：{ewcData.ToJson()}");
                resp.Message = $"调用企业微信API失败：返回的企业微信用户信息为空！";
                return resp;
            }

            //此处根据企业微信用户的 openId 查询AC用户信息
            //向认证站点发送请求
            var gateway = this.Container.GetService<IHttpServiceInvoker>();
            var acResponse = gateway.Invoke(null, TargetSEP.AuthService,
                new CommonFormDTO()
                {
                    FormId = "auth_user",
                    OperationNo = "GetUserByQyWxOpenId",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "qyWxCorpId", ewcData.CorpId },
                        { "qyWxOpenUserId", ewcData.Open_UserId }
                    }
                }) as DynamicDTOResponse;
            var result = acResponse?.OperationResult;
            result?.ThrowIfHasError(true, "根据企业微信OpenId获取用户信息出错！");

            var acUser = acResponse
                ?.OperationResult
                ?.SrvData
                ?.ToString()
                ?.FromJson<Dictionary<string, string>>()
                ?? new Dictionary<string, string>();

            var userName = "";
            acUser?.TryGetValue("userName", out userName);

            //当前企业微信用户是否已绑定过业务用户，如果还未绑定业务用户，则要求在微信小程序中使用业务用户名和密码进行登录绑定微信用户
            var isBind = false;
            AuthenticateResponse response = null;
            if (!userName.IsNullOrEmptyOrWhiteSpace())
            {
                //已经绑定过微信用户，则需要自动登陆业务系统
                isBind = true;

                //则自动登录用户绑定的企业
                var companyId = acUser?.GetValue("qyWxCompanyId") ?? "";

                try
                {
                    //根据 userName 和 openId 实现自动登陆逻辑，并且返回用户登陆信息
                    //此处把密码参数用来传递 openId，以便在现有登陆体系下，通过在参数尾部追加微信登陆标记来快速实现微信登陆
                    //向麦浩系统发送登录请求
                    response = JsonClient.Invoke<Authenticate, AuthenticateResponse>(
                        this.Request,
                        new Authenticate
                        {
                            UserName = userName,
                            Password = $"{WeiXinConsts.QyWxLoginSign}{ewcData.UserId}|{ewcData.CorpId}|{ewcData.Open_UserId}",
                            Meta = new Dictionary<string, string>
                            {
                                { "X-AppId", "qywxminiprogram" }, //企业微信小程序登录
                                { "X-Bind-QyWxCompanyId", companyId } //小程序登录时如果有选择麦皓企业，则登录选择的企业
                            }
                        });
                }
                catch (Exception ex)
                {
                    var stackTrace = ex.StackTrace;
                    var message = ex.Message;
                    var innerEx = ex.InnerException;
                    if (innerEx != null)
                    {
                        stackTrace = innerEx.StackTrace;
                        message = innerEx.Message;
                        if (innerEx is WebServiceException)
                        {
                            var wsEx = (innerEx as WebServiceException);
                            stackTrace = wsEx.StackTrace;
                            message = wsEx.ErrorMessage;
                        }
                    }
                    this.LogService.Error($"自动登录失败：{stackTrace}");
                    resp.Message = $"自动登录失败：{message}";
                    return resp;
                }

                resp.Message = "自动登录成功！";
            }
            else
            {
                resp.Message = "当前企业微信未绑定系统账号！";
            }

            resp.Success = true;
            resp.Data = new
            {
                isBind = isBind,
                userId = response?.UserId ?? "",
                userName = response?.UserName ?? "",
                displayName = response?.DisplayName ?? "",
                bearerToken = response?.BearerToken ?? "",
                sessionId = response?.SessionId ?? "",
                meta = response?.Meta ?? new Dictionary<string, string>(),
                ssoToken = JwtUtil.CreateSSOTOKEN(new UserContext(), response?.UserName)
            };
            return resp;
        }
    }
}