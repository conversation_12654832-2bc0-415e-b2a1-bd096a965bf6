using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore.PermData;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：表单数据范围权限
    /// </summary>
    public class DataRangPermissionsController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(DataRangPermissionsDto dto)
        {
            base.InitializeOperationContext(dto);

            var ctx = this.Context;
            var resp = new BaseResponse<List<DataRangPermissionsModel>>();

            if (dto.FormIds == null || dto.FormIds.Count == 0)
            {
                resp.Success = false;
                resp.Message = "表单查询参数不能为空！";
                return resp;
            }
            foreach (var formId in dto.FormIds)
            {
                if (!formId.IsNullOrEmptyOrWhiteSpace())
                    resp.Data.Add(new DataRangPermissionsModel { FormId = formId });
            }
            //查询表单数据范围权限
            var permSvc = this.Container.GetService<IPermissionService>();
            //如果是管理员或系统运维，则不设置数据范围权限
            if ((ctx.IsTopOrg && permSvc.IsRoleAdmin(ctx, new PermAuth(ctx))) || permSvc.IsDevOpsUser(ctx, new PermAuth(ctx)))
            {
                foreach (var item in resp.Data)
                {
                    item.PermCaption = "全部";
                    item.PermFilter = "all";
                }
            }
            else
            {
                var userRoles = GetUserRoles(ctx);
                foreach (var result in resp.Data)
                {
                    //该业务对象设置的列表数据范围
                    var drAuths = GetDataRowACLPermDataByUser(ctx, result.FormId);
                    if (drAuths.Any(f => f.FilterType.EqualsIgnoreCase("all")))
                    {
                        //如果有任意一个角色设置为所有
                        result.PermCaption = "全部";
                        result.PermFilter = "all";
                        continue;
                    }
                    else
                    {
                        var allRoles = drAuths.Select(f => f.RoleId).Distinct().ToList();
                        if (userRoles.Any(f => !allRoles.Contains(f)))
                        {
                            //如果有任意一个角色没有设置，则认为为所有
                            result.PermCaption = "全部";
                            result.PermFilter = "all";
                            continue;
                        }
                        else
                        {
                            var dic = new Dictionary<int, string>();
                            //查所有角色，按角色授权取并集权限
                            var myandsubdepartment = drAuths.Any(f => f.FilterType.EqualsIgnoreCase("fmyandsubdepartment"));
                            if (myandsubdepartment)
                            {
                                result.PermCaption = "本部门及下属部门";
                                result.PermFilter = "fmyandsubdepartment";
                                continue;
                            }
                            var mydepartment = drAuths.Any(f => f.FilterType.EqualsIgnoreCase("fmydepartment"));
                            if (mydepartment)
                            {
                                result.PermCaption = "本部门";
                                result.PermFilter = "fmydepartment";
                                continue;
                            }
                            result.PermCaption = "本人";
                            result.PermFilter = "fcurrentuser";
                        }
                    }
                }
            }
            resp.Success = true;
            resp.Message = "操作成功！";
            return resp;
        }

        /// <summary>
        /// 获取用户角色
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        private List<string> GetUserRoles(UserContext ctx)
        {
            var result = new List<string>();
            var svc = ctx.Container.GetService<IDBService>();
            var sql = @"select froleid from t_sec_roleuser with(nolock) where fuserid='{0}' ".Fmt(ctx.UserId);
            var datas = svc.ExecuteDynamicObject(ctx, sql);
            if (datas != null && datas.Count > 0)
            {
                result = datas.Select(f => Convert.ToString(f["froleid"])).Distinct().ToList();
            }

            return result;
        }

        /// <summary>
        /// 获取用户表单数据范围权限
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        private List<DataRowAuthInfo> GetDataRowACLPermDataByUser(UserContext ctx, string formId)
        {
            List<DataRowAuthInfo> drAuth = new List<DataRowAuthInfo>();
            string strSql = $@" select distinct t0.froleid, fbizobjid,ffiltertype,fexpress,fdesc ,t0.fbdfldfilter
                                from t_sec_roledatarowacl t0  with(nolock) 
                                inner join t_sec_roleuser t1  with(nolock) on t0.froleId=t1.froleId
                                where t1.fuserid=@fuserid 
                                    and (t0.fcompanyid=@fcompanyid or t0.fcompanyid=@ftoporgid)
                                    and t0.fbizobjid=@fbizobjid ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@fuserid", System.Data.DbType.String,ctx.UserId),
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),//经销商管理员角色在总部组织下
                new SqlParam("@fbizobjid", System.Data.DbType.String,formId),
            };

            var svc = ctx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    var authInfo = new DataRowAuthInfo()
                    {
                        RoleId = reader.GetValue<string>("froleid"),
                        FormId = reader.GetValue<string>("fbizobjid"),
                        FilterType = reader.GetValue<string>("ffiltertype"),
                        Express = reader.GetValue<string>("fexpress"),
                        Desc = reader.GetValue<string>("fdesc"),
                    };
                    var bdFilter = reader.GetValue<string>("fbdfldfilter");
                    if (!bdFilter.IsNullOrEmptyOrWhiteSpace())
                    {
                        authInfo.BDFldsAuthInfo = bdFilter.FromJson<List<BDFldDataRowAuthInfo>>();
                    }

                    drAuth.Add(authInfo);
                }
            }
            return drAuth;
        }
    }
}
