using System;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.DTO.IM.Notice;
using JieNor.AMS.YDJ.MP.API.Model.IM.Notice;
using JieNor.Framework;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.IM.Notice
{
    public class MyNoticeDetailController : BaseController
    {
        /// <summary>
        /// 企业微信小程序- 我的公告获取详细接口
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(MyNoticeDetailDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<MyNoticeDetailModel>();

            if (dto.Id.IsNullOrEmpty())
            {

                resp.Success = false;
                resp.Message = $"公告Id不能为空！";
                return resp;
            }

            //获取公告通知详情
            var strSql = $@"select t0.fpublishcid_txt,t0.ftitle,t0.fcontent,t0.fpublishtime,t0.ftypename,case when t2.fid is null then '0' ELSE '1' END as readstatus
                            From t_im_noticelist t0 with(nolock)
                            left join T_IM_NOTICELIST_LG as t2 with(nolock) on t2.fbizobjno= t0.fid and t2.fcreatorid='{this.Context.UserId}'
                            where t0.fid ='{dto.Id}'";

            var notice = this.DBService.ExecuteDynamicObject(this.Context, strSql)?.FirstOrDefault();
            if (notice == null)
            {
                resp.Message = "公告不存在或已被删除！";
                resp.Success = false;
                return resp;
            }
            var NoticeDetail = new MyNoticeDetailModel();
            NoticeDetail.Id = dto.Id;
            NoticeDetail.Title = Convert.ToString(notice["ftitle"]);
            NoticeDetail.Content = Convert.ToString(notice["fcontent"]);
            NoticeDetail.PublishTime = Convert.ToDateTime(notice["fpublishtime"]);
            NoticeDetail.ReadStatus = Convert.ToInt32(notice["readstatus"]);
            resp.Data = NoticeDetail;

          

            //返回数据
            resp.Success = true;
            resp.Message = "获取数据成功";
            return resp;
        }
    }
}
