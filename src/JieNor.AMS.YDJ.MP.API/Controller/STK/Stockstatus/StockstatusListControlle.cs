using JieNor.AMS.YDJ.MP.API.DTO.STK.Rpt;
using JieNor.AMS.YDJ.MP.API.Model.STK.Rpt;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.AMS.YDJ.MP.API.Model.STK.Stockstatus;

namespace JieNor.AMS.YDJ.MP.API.Controller.STK.Stockstatus
{
    /// <summary>
    /// 微信小程序：库存状态列表
    /// </summary>
    public class StockstatusListControlle : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(StockstatusListDTO dto)
        {
            var resp = new BaseResponse<object>();
            base.InitializeOperationContext(dto);

            string strSql = "select fid,fname from t_ydj_stockstatus with(nolock) where fstatus='E'";
            List<StockstatusListModel> list = new List<StockstatusListModel>();
            using (var dr = this.DBService.ExecuteReader(this.Context, strSql))
            {
                while (dr.Read())
                {
                    list.Add(new StockstatusListModel(
                        Convert.ToString(dr["fid"]),
                          Convert.ToString(dr["fname"])));
                }
            }
            resp.Data = list;
            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }
    }
}
