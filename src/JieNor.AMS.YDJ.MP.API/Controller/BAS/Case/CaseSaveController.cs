using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS.Case
{
    /// <summary>
    /// 微信小程序： 经典案例保存
    /// </summary>
    public class CaseSaveController : BaseController
    {
        public object Any(CaseSaveDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<object>();
            if (dto.CaseName.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "案例名不能为空";
                resp.Success = false;
                return resp;
            }
            if (dto.Images.Count == 0)
            {
                resp.Message = "至少要上传一张案例图！";
                resp.Success = false;
                return resp;
            }

            List<Dictionary<string, object>> fcaseentry = new List<Dictionary<string, object>>();
            Dictionary<string, object> modelnew = null;
            //上传图片
            foreach (var item in dto.Images)
            {
                modelnew = new Dictionary<string, object>();
                modelnew["ffilestype"] = "";//文件类型
                modelnew["ffilename"] = item.Name;//文件名
                modelnew["fpicdescription"] = "";//图片说明
                modelnew["fuploaderid"] = this.Context.UserId;//上传人id
                modelnew["ffileid"] = item.Id;//文件id
                modelnew["flastupdate"] = DateTime.Now;//文件最后更新时间
                modelnew["ffilesize"] = "";//文件大小
                modelnew["Effect_Drawing"] = "Effect_Drawing";//图片类型
                fcaseentry.Add(modelnew);
            }
            //向麦浩系统发送请求（新增）
            var response = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
               this.Request,
               new CommonBillDTO()
               {
                   FormId = "ydj_case",
                   OperationNo = "save",
                   Id = dto.Id,
                   BillData = BuildNewBillData(dto, fcaseentry)
               });
            var result = response?.OperationResult;
            if (result == null || !result.IsSuccess)
            {
                resp.Message = result?.GetErrorMessage() ?? "操作失败！";
                resp.Success = false;
                return resp;
            }
            //将保存后的主键ID返回给前端
            var jsonPkids = "";
            result?.SimpleData?.TryGetValue("pkids", out jsonPkids);
            var savePkid = jsonPkids?.FromJson<List<string>>()?.FirstOrDefault() ?? "";
            resp.Data = new { id = savePkid };
            resp.Message = "保存成功！";
            resp.Success = true;
            resp.Code = 200;


            return resp;
        }


        private string BuildNewBillData(CaseSaveDTO dto, List<Dictionary<string, object>> fcaseentry = null)
        {
            var data =
                new Dictionary<string, object>
                {
                    {"id",Convert.ToString(dto.Id) },
                    //案例名
                    {"fname", dto.CaseName}, 
                    //发布状态
                    {"fsenstatus", "send_status01"}, 

                    //户型
                    {"froom_enum", Convert.ToString(dto.House.Froom_enum)},
                    {"fhall_enum", Convert.ToString(dto.House.Fhall_enum)},
                    {"ftoilet_enum", Convert.ToString(dto.House.Ftoilet_enum)},
                    {"fbalcony_enum", Convert.ToString(dto.House.Fbalcony_enum)},

                    //风格
                    {"fstyle", Convert.ToString(dto.Fstyle)},
                    //空间
                    {"fspace", Convert.ToString(dto.Fspace)},
                    //面积
                    {"farea", Convert.ToString(dto.Farea)},
                    //楼盘
                    {"fbuildingid", Convert.ToString(dto.Fbuildingid)},
                    //合作渠道
                    {"fchannel", Convert.ToString(dto.Fchannel)},
                    //设计师
                    {"fstylistid", Convert.ToString(dto.Fstylistid)}, 
                    //所属门店
                    {"fdeptid", Convert.ToString(dto.Fdeptid)}, 
                    //VR链接
                    {"fvrlink", Convert.ToString(dto.VRlink)},
                    //关键字
                    {"fkeywords", Convert.ToString(dto.fkeywords)},
                    //案例说明
                    {"fremark", Convert.ToString(dto.fremark)},

                     //案例类型
                     {"fcasetype", "本地案例"},

                     //来源单据编号
                     {"fsourcenumber", "0"},

                     //案例品类
                     {"fcasecategory", Convert.ToString(dto.Fcasecategory)},

                     //收藏数
                     {"flikenums", 0},

                };
            if (fcaseentry != null && fcaseentry.Any())
            {
                data.Add("fcaseentry", fcaseentry);
            }

            var billData = (new List<Dictionary<string, object>> { data }).ToJson();

            return billData;
        }
    }
}
