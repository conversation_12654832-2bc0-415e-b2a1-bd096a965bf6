using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.MP.API.Controller.BPM.Approval
{
    /// <summary>
    /// 微信小程序：审批驳回接口
    /// </summary>
    public class ApprovalRejectController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ApprovalRejectDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.SourceId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 sourceId 不能为空！";
                return resp;
            }

            if (dto.SourceType.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 sourceType 不能为空！";
                return resp;
            }

            string execOpinion = dto.Reason ?? "驳回";

            //// 向麦浩系统发送请求
            //var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
            //    this.Request,
            //    new CommonBillDTO()
            //    {
            //        FormId = dto.SourceType,
            //        OperationNo = "rejectflow",
            //        SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.SourceId } },
            //        SimpleData = new Dictionary<string, string>
            //        {
            //            { "execOpinion", execOpinion },
            //            { "terminate", dto.Terminate.ToString() }
            //        }
            //    });
            //var result = response?.OperationResult;

            //resp = result.ToResponseModel<BaseDataModel>(false);

            resp = ApprovalHelper.Reject(this.Context, this.Request, dto.SourceType, dto.SourceId, execOpinion, dto.Terminate);

            return resp;
        }
    }
}