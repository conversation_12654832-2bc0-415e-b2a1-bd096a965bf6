using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using System.Data;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    public class ServiceMasterListController : BaseController
    {
        public object Any(ServiceMasterListDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<BaseListPageData<ServiceMasterListModel>>
            {
                Data = new BaseListPageData<ServiceMasterListModel>(dto.PageSize)
            };
            string sqlWhere = "";
            StringBuilder condistion = new StringBuilder();
            condistion.AppendLine(" 1=1 ");
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                condistion.Append($"and master.fphone LIKE '%{dto.Keyword.Trim()}%' OR team.fname LIKE '%{dto.Keyword.Trim()}%' OR master.fname LIKE '%{dto.Keyword.Trim()}%' ");
            }
            //添加企业过滤
            condistion.Append($" and master.fmainorgid = '{this.Context.Company}'");

            sqlWhere = condistion.ToString();
            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data.TotalRecord = GetTotalRecord(sqlWhere);
            resp.Data.List = MapTo(dto, sqlWhere);

            return resp;

        }
        private int GetTotalRecord(string sqlWhere)
        {
            //查询总记录数
            var totalRecord = 0;
            string sql = $@"  SELECT count(1) as totalRecord
                FROM T_YDJ_MASTER master with(nolock) 
                INNER JOIN t_ydj_staffteam staff with(nolock) ON staff.fstaffid = master.fid
                INNER JOIN t_ser_ydj_team team with(nolock) ON staff.fid = team.fid
               Where {sqlWhere}";
            using (var reader = this.DBService.ExecuteReader(this.Context, sql))
            {
                if (reader.Read())
                {
                    totalRecord = Convert.ToInt32(reader["totalRecord"]);
                }
            }
            return totalRecord;
        }

        private List<ServiceMasterListModel> MapTo(ServiceMasterListDTO dto, string sqlWhere)
        {
            //默认按当前订单的服务类型匹配，且服务中少的来排序
            var order = "Desc";
            if (dto.Sortord == "asc") {
                order = "Asc";
            }
            var orderBy = " fserviceitem DESC ,ServicingCount ASC";
            //如果选择的是按服务数排序
            if (dto.OrderType == "2") {
                orderBy = $"CompleteCount {order}";
            }
            int pageSize = dto.PageSize;
            int pageIndex = dto.PageIndex;

            //查询分页数据
            var sqlText = $@"
            select top {pageSize} * from 
            (
	            SELECT DISTINCT row_number() over(order by  {orderBy}) rownum,masterid,fname,fphone,a.teamid,a.teanname,fteamtype,fimage,fimage_txt,ServicingCount,CompleteCount
	             FROM (select DISTINCT  master.fid as masterid,fserviceitem,master.fname,master.fphone,team.fid AS teamid,team.fname as teanname,team.fteamtype,master.fimage,master.fimage_txt 
                 ,(SELECT COUNT(0) FROM T_YDJ_SERVICE with(nolock) WHERE fserstatus IN('sersta02','sersta04','sersta06') AND fmasterid = master.fid) ServicingCount,
                 (SELECT COUNT(0)  FROM T_YDJ_SERVICE with(nolock) WHERE fserstatus IN ('sersta07','sersta11') AND fmasterid = master.fid) CompleteCount
                FROM T_YDJ_MASTER master with(nolock) 
                INNER JOIN t_ydj_staffteam staff with(nolock) ON staff.fstaffid = master.fid
                INNER JOIN t_ser_ydj_team team with(nolock) ON staff.fid = team.fid
                 Where {sqlWhere} ) a
            ) p 
            where rownum > {pageSize} * ({pageIndex} - 1)";

            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText);
            var models = new List<ServiceMasterListModel>();
            foreach (var item in list)
            {
                 var model = new ServiceMasterListModel
                {
                    MasterId = JNConvert.ToStringAndTrim(item["masterid"]),
                    MasterName = JNConvert.ToStringAndTrim(item["fname"]),//师傅名称
                    fimage = JNConvert.ToStringAndTrim(item["fimage"]),//师傅头像
                    fimage_txt = JNConvert.ToStringAndTrim(item["fimage_txt"]),
                    Phone = JNConvert.ToStringAndTrim(item["fphone"]),//师傅手机
                    TeamId = JNConvert.ToStringAndTrim(item["teamid"]), 
                    TeamName = JNConvert.ToStringAndTrim(item["teanname"]),//团队名称
                    TeamType = JNConvert.ToStringAndTrim(item["fteamtype"]),//团队类型
                    ServicingCount = Convert.ToInt32(item["ServicingCount"]),//服务中数量
                    Images = ImageFieldUtil.ParseImages(JNConvert.ToStringAndTrim(item["fimage"]), JNConvert.ToStringAndTrim(item["fimage_txt"]), true),
                    CompleteCount = Convert.ToInt32(item["CompleteCount"])//已完成数量
                };
                models.Add(model);
            }

            return models;
        }
    }
}
