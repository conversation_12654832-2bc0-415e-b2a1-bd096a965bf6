using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using ServiceStack.Web;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    public class BaseServiceController
    {
        /// <summary>
        /// 反写服务单状态
        /// </summary>
        /// <param name="serviceObj"></param>
        public static void RewriteServiceState(string state, string Id, IRequest request)
        {
            var billdata2 = (new List<Dictionary<string, object>>
               {
                    new Dictionary<string, object>
                    {
                        {"id",Id },
                        {"fserstatus",state }
                    }
                }).ToJson();
            var response2 = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                request,
                new CommonBillDTO() { FormId = "ydj_service", OperationNo = "save", BillData = billdata2, Id = Id }
                );
        }
    }
}
