using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.DTO.SER.Service;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    public class StockoutLinkSerController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(StockoutLinkSerDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<List<BaseDataSimpleModel>>();
            if (dto.Mainorgid.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 Mainorgid 不能为空！";
                return resp;
            }
            if (dto.MasteridId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 MasteridId 不能为空！";
                return resp;
            }
            if (dto.SostockoutNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 SostockoutId 不能为空！";
                return resp;
            }

            var masteridId = dto.MasteridId;
            var sostockoutno = dto.SostockoutNo;
            var Mainorgid = dto.Mainorgid;
            var staffinfo = this.Context.LoadBizDataById("ydj_staff", masteridId,true);//员工信息
            //需判断当前员工岗位的【业务类别】是否存在“通用”和“服务”
            var staffentry = staffinfo["fentity"] as DynamicObjectCollection;
            int biztype = 0;
            foreach (var fentry in staffentry)
            {
                int.TryParse(Convert.ToString(fentry["fbiztype"]), out  biztype);
                if (biztype == 0 || biztype == 6)
                    break;
            }
            if(biztype==0 || biztype == 6)
            {
                // 允许扫描此二维码的逻辑
                var sqlParam = new List<SqlParam> {
                 new SqlParam("@fsourcenumber",DbType.String, sostockoutno),
                 new SqlParam("@fmasterid",DbType.String, masteridId)
                };
                string sql = @"select fid,fmainorgid   from t_stk_sostockout where 
                                 fcancelstatus='0'and fbillno=@fsourcenumber
                              ";
                var stksostoutinfo = this.Context.ExecuteDynamicObject(sql, sqlParam).FirstOrDefault();
                if (stksostoutinfo != null)
                {
                    var orgid = Convert.ToString(stksostoutinfo["fmainorgid"]);
                    if (!orgid.Equals(Mainorgid))
                    {
                        resp.Success = false;
                        resp.Message = $"对不起，当前登录用户与销售出库单所在组织不一致，暂不支持!";
                        return resp;
                    }
                }
                sql = @"select fid   from t_ydj_service where 
                                fsourcenumber =@fsourcenumber and fcancelstatus='0'and fservicetype='fres_type_01' 
                              ";
                var servicefid = this.Context.ExecuteDynamicObject(sql, sqlParam).FirstOrDefault();
                 sql = @"select fdeptid   from t_bd_staff where fid=@fmasterid";
                var staffdeptid = this.Context.ExecuteDynamicObject(sql, sqlParam).FirstOrDefault();
                if (servicefid != null)
                {
                    var pkid =Convert.ToString(servicefid["fid"]);
                    var fteamid = Convert.ToString(staffdeptid["fdeptid"]);
                    
                    var serviceinfo = this.Context.LoadBizDataById("ydj_service", pkid, true);
                    serviceinfo["fmasterid"] = masteridId;//【服务人员】设置为当前登录员工
                    this.Context.SaveBizData("ydj_service", serviceinfo);

                    CommonBillDTO data = new CommonBillDTO()
                    {
                        FormId = "ydj_service",
                        OperationNo = "setstatus01",
                        SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = pkid } },
                        Id = pkid
                    };
                    data.SimpleData.Add("callerTerminal", "MPAPI");
                    data.SimpleData.Add("OpId", "setstatus01");
                    data.SimpleData.Add("masterid", masteridId);
                    data.SimpleData.Add("teamid", fteamid);

                    var response2 = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(this.Request, data);
                    IOperationResult result = null;
                    result = response2?.OperationResult;
                    if (!result.IsSuccess)
                    {
                        var errMsg = result.SimpleMessage;
                        var errMsgs = result.ComplexMessage.ErrorMessages;
                        if (errMsgs.Count > 0)
                        {
                            if (!errMsg.IsNullOrEmptyOrWhiteSpace())
                            {
                                errMsg += "，";
                            }
                            errMsg += string.Join("，", errMsgs);
                        }
                        resp.Message = errMsg ?? "操作过程出错！";
                        resp.Success = false;
                        return resp;
                    }
                }
                else
                {
                    resp.Success = false;
                    resp.Message = $"对不起，当前销售出库单不存在下游有效的送装服务单，请仔细检查！";
                    return resp;
                }
            }
            else
            {
                resp.Success = false;
                resp.Message = $"对不起，当前用户关联员工岗位【业务类别】不为“通用”或“服务”，暂不支持扫描此二维码！";
                return resp;
            }

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Code = 200;

            return resp;
        }
    }
}
