using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Response.YDJ.BPM.Approval
{
    public class ApprovalDetailResponse
    {
        public bool isStartFlow { get; set; }
        public string sourceId { get; set; }
        public string sourceType { get; set; }
        public string sourceNumber { get; set; }
        public ApprovalDetailResponse_Flowinstance flowinstance { get; set; }
        public ApprovalDetailResponse_Executionlist[] executionList { get; set; }
        public string content { get; set; }
        public DateTime createdate { get; set; }
        public string creatorid { get; set; }

        public string staffid { get; set; }

        public string deptid { get; set; }

        public string currentNodeFlowModel { get; set; }
    }

    public class ApprovalDetailResponse_Flowinstance
    {
        public string Id { get; set; }
        public string FFormId { get; set; }
        public string fbillno { get; set; }
        public string fdescription { get; set; }
        public string fcreatorid { get; set; }
        public DateTime fcreatedate { get; set; }
        public string fmodifierid { get; set; }
        public string fmodifydate { get; set; }
        public string fstatus { get; set; }
        public string fapproveid { get; set; }
        public object fapprovedate { get; set; }
        public bool fcancelstatus { get; set; }
        public string fcancelid { get; set; }
        public object fcanceldate { get; set; }
        public string fchangestatus { get; set; }
        public string fclosestatus { get; set; }
        public string fcloseid { get; set; }
        public object fclosedate { get; set; }
        public string fnextprocnode { get; set; }
        public string fmainorgid { get; set; }
        public string fmainorgid_txt { get; set; }
        public string fmainorgid_pid { get; set; }
        public string fbizruleid { get; set; }
        public string fflowinstanceid { get; set; }
        public string ftranid { get; set; }
        public string ffromtranid { get; set; }
        public string froottranid { get; set; }
        public string fsourcetype { get; set; }
        public string fsourcenumber { get; set; }
        public string fpublishcid { get; set; }
        public string fpublishcid_txt { get; set; }
        public string fpublishcid_pid { get; set; }
        public string fdataorigin { get; set; }
        public int fprintcount { get; set; }
        public string fprintid { get; set; }
        public object fprintdate { get; set; }
        public object frecordcontent { get; set; }
        public object freminddate { get; set; }
        public string fbizformid { get; set; }
        public string fbizbillno { get; set; }
        public string fbizbillpkid { get; set; }
        public string fflowname { get; set; }
        public string fflowver { get; set; }
        public string finitiator { get; set; }
        public string finitiatornumber { get; set; }
        public string fcurrentnode { get; set; }
        public string foperator { get; set; }
        public string foperatornumber { get; set; }
        public string fflowstatus { get; set; }
        public string fflowid { get; set; }
        public string ferrormsg { get; set; }
        public string fstatemachine { get; set; }
        public string fparenttranid { get; set; }
        public string ftoptranid { get; set; }
        public string __State__ { get; set; }
    }

    public class ApprovalDetailResponse_Executionlist
    {
        public string Id { get; set; }
        public string FFormId { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
        public string fdescription { get; set; }
        public string fcreatorid { get; set; }
        public DateTime fcreatedate { get; set; }
        public string fmodifierid { get; set; }
        public string fmodifydate { get; set; }
        public bool fispreset { get; set; }
        public string fstatus { get; set; }
        public string fapproveid { get; set; }
        public object fapprovedate { get; set; }
        public bool fforbidstatus { get; set; }
        public string fforbidid { get; set; }
        public object fforbiddate { get; set; }
        public string fchangestatus { get; set; }
        public string fnextprocnode { get; set; }
        public string fmainorgid { get; set; }
        public string fmainorgid_txt { get; set; }
        public string fmainorgid_pid { get; set; }
        public string fprimitiveid { get; set; }
        public string fbizruleid { get; set; }
        public string fflowinstanceid { get; set; }
        public string ftranid { get; set; }
        public string ffromtranid { get; set; }
        public string froottranid { get; set; }
        public int fprintcount { get; set; }
        public string fprintid { get; set; }
        public object fprintdate { get; set; }
        public object frecordcontent { get; set; }
        public object freminddate { get; set; }
        public string fsendstatus { get; set; }
        public string fdataorigin { get; set; }
        public string fpublishcid { get; set; }
        public string fpublishcid_txt { get; set; }
        public string fpublishcid_pid { get; set; }
        public object fsenddate { get; set; }
        public object fdownloaddate { get; set; }
        public string fchaindataid { get; set; }
        public string ffromchaindataid { get; set; }
        public string fsectionid { get; set; }
        public string fprevsectionid { get; set; }
        public string fsectionstatus { get; set; }
        public string fopname { get; set; }
        public string finstanceid { get; set; }
        public string fparenttranid { get; set; }
        public string ftoptranid { get; set; }
        public string fname_py { get; set; }
        public string fname_py2 { get; set; }
        public ApprovalDetailResponse_Fentry[] fentry { get; set; }
        public string __State__ { get; set; }
    }

    public class ApprovalDetailResponse_Fentry
    {
        public string Id { get; set; }
        public int FSeq { get; set; }
        public string fexecutors { get; set; }
        public string fexecutors_txt { get; set; }
        public string fexecnumbertemp { get; set; }
        public string ftransfer { get; set; }
        public string ftransfernumber { get; set; }
        public string fexec { get; set; }
        public string fexecnumber { get; set; }
        public string fexecdate { get; set; }
        public string fexecopinion { get; set; }
        public string ftranid { get; set; }
        public string fparenttranid { get; set; }
        public string ftoptranid { get; set; }
        public string fdataorigin { get; set; }
        public string __State__ { get; set; }
    }


}
