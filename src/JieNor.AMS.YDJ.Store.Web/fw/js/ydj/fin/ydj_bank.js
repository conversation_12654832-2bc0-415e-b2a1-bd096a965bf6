///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/fin/ydj_bank.js
*/
; (function () {
    var ydj_bank = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            if (Consts.isdirectsale) {

                that.Model.setVisible({ id: 'fbranchname', value: false })//隐藏
                that.Model.setVisible({ id: 'finterbanknum', value: false })//隐藏
                that.Model.setVisible({ id: 'faddress', value: false })//隐藏

                that.Model.setVisible({ id: 'fopbankid', value: true });//显示
                that.Model.setVisible({ id: 'fjointno', value: true });//显示

            } else {
                that.Model.setVisible({ id: 'fbranchname', value: true })//显示
                that.Model.setVisible({ id: 'finterbanknum', value: true })//显示
                that.Model.setVisible({ id: 'faddress', value: true })//显示

                that.Model.setVisible({ id: 'fopbankid', value: false });//隐藏
                that.Model.setVisible({ id: 'fjointno', value: false });//隐藏

            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fbranchname':
                case 'finterbanknum':
                case 'faddress':
                    if (Consts.isdirectsale) {
                        //直营，不允许编辑
                        e.result.enabled = false;
                    } else {
                        //非直营，允许编辑
                        e.result.enabled = true;
                    }
                    break;
                case 'fopbankid':
                    if (Consts.isdirectsale) {
                        //直营，允许编辑
                        e.result.enabled = true;
                    } else {
                        e.result.enabled = false;
                    }
                    break;
            }
        }

        return _child;
    })(BillPlugIn);
    window.ydj_bank = window.ydj_bank || ydj_bank;
})();