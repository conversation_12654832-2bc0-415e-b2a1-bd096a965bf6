/**
 * 库存可用量校验结果业务插件
 * @ sourceURL=/fw/js/ydj/sys/sys_invusablecheckresult.js
 */
; (function () {
    var sys_invusablecheckresult = (function (_super) {
        var _child = function (args) {
            _super.call(this, args);
        };
        __extends(_child, _super);

        //插件初始化事件
        _child.prototype.onInitialized = function (e) {
            var that = this;

            var cp = that.formContext.cp;
            that.Model.refreshEntry({ id: "fentry", data: cp.fentry });
             
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //取消
                case 'tbpagecancel':
                    e.result = true;
                    that.Model.close();
                    break;
            }
        };
        
        return _child;
    })(BasePlugIn);
    window.sys_invusablecheckresult = window.sys_invusablecheckresult || sys_invusablecheckresult;
})();