/*
 * description:微信菜单业务控制插件
 * author:
 * create date:
 * modify by: 
 * modify date:
 * remark:
 *@ sourceURL=/fw/js/ydj/weixin/wx_menu.js
*/
; (function () {
    var wx_menu = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);

        };
        __extends(_child, _super);

        //自身特有的操作（菜单操作）
        _child.prototype.onMenuItemClick = function (args) {
            //操作类型
            var opcode = args.opcode.toLowerCase();
            if (opcode === 'putmenu') {
                var packet = { }
                args.result = true;
                //将 billData 转换为 json 字符串
                yiAjax.p('/dynamic/wx_menu?operationno=putmenu', packet
                    ,
                function () {
                    if (arguments[0].operationResult.isSuccess) {
                        var _json = arguments[0].operationResult.srvData;
                        yiDialog.a(_json.log);
                    }
                    else {
                        yiDialog.m(arguments[0].operationResult.simpleMessage)
                    }
                }, function () { }, null, null);
                args.result = true;//返回 true 告诉 BasePage 该操作由我自己处理

            }
        };


        return _child;
    })(BillPlugIn);
    window.wx_menu = window.wx_menu || wx_menu;
})();