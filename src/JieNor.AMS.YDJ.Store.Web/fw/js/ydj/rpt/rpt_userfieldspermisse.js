(function () {
    var rpt_userfieldspermisse = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //初始化事件
        _child.prototype.onInitialized = function () {
            debugger;
            var that = this;
        };


        /*_child.prototype.onFieldValueFormat = function (e) {
            debugger;
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fnmenugroupame':
                    if (e.value != '' || e.value != undefined || e.value != ' ') {
                        //#333333
                        e.value = '<span style="color:#333333;cursor:pointer;">{0}</span>'.format(e.value);
                        e.cancel = true;
                    }
                    break;
                case 'fnmenugroupame_fname':
                    if (e.value != '' || e.value != undefined || e.value != ' ') {
                        //#333333
                        e.value = '<span style="color:#333333;cursor:pointer;">{0}</span>'.format(e.value);
                        e.cancel = true;
                    }
                default:
                    break;
            }
        }*/

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;
        }

        //表格单元格点击事件
        /*_child.prototype.onEntryCellClick = function (e) {
            debugger;
            var that = this;
            if (e.id === 'list') {
                switch (e.fieldId.toLowerCase()) {
                    case 'fnmenugroupame':
                        e.result = true;
                        return;
                        break;
                    case 'fnmenugroupame_fname':
                        e.result = true;
                        return;
                        break;
                    default:
                        break;
                }
            }
        }*/

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {

        };

        return _child;
    })(ReportPlugIn);
    window.rpt_userfieldspermisse = window.rpt_userfieldspermisse || rpt_userfieldspermisse;
})();