; (function () {
    var bi_activityobjectivesimport = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }
        __extends(_child, _super);

        _child.prototype.onInitialized = function (e) {
            var that = this;
            var pageid = that.Model.viewModel.pageId;
            that.Model.blockUI({ id: '#page#' });

            yiAjax.p('/bill/bi_activityobjectivesimport?operationno=getmenuinfo', { simpledata: { formId: that.Model.viewModel.formId } },
                function (r) {
                    debugger;
                    that.Model.unblockUI({ id: '#page#' });
                    var encryptData = r.operationResult.srvData;
                    if (encryptData.openNewTabOrIFrame == 0) {
                        that.openIFrame(encryptData, pageid);
                    } else if (encryptData.openNewTabOrIFrame == 1) {
                        that.openNewTab(encryptData, pageid);
                    }
                }
            );
        }

        _child.prototype.openIFrame = function (encryptData, pageid) {
            var that = this;
            that.Model.unblockUI({ id: '#page#' });
            if (encryptData.returnUrl) {
                var url = encryptData.returnUrl + "&page_number=1&fmainorgid=" + Consts.loginCompany.id + "&ssoToken=" + Consts.ssoToken;

                $("#" + pageid).append('<iframe  id="finebiview' + pageid + '" src="' + url + '" width="100%" style="margin:0px" frameborder=0></iframe>');
                var iframe = $("#" + that.Model.viewModel.pageId).find("#finebiview" + pageid);
                setInterval(function () {
                    iframe.attr('height', window.innerHeight - 150)
                }, 100);
            }
        }

        _child.prototype.openNewTab = function (encryptData, pageid) {
            var that = this;
            that.Model.unblockUI({ id: '#page#' });
            if (encryptData.returnUrl) {
                var url = encryptData.returnUrl + "&page_number=1&fmainorgid=" + Consts.loginCompany.id + "&ssoToken=" + Consts.ssoToken;
                window.open(url);
                that.Model.close();
            }
        }

        return _child;
    })(BasePlugIn);
    window.bi_activityobjectivesimport = window.bi_activityobjectivesimport || bi_activityobjectivesimport;
})();