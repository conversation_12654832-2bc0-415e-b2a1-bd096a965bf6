; (function () {
    var si_transferfrommusitaskparam = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };

        //继承 BasePlugIn
        __extends(_child, _super);

        _child.prototype.onInitialized = function (e) {
            var that = this;

            var parentModel = that.Model.getParentModel();
            var paramValString = parentModel.getSimpleValue({ id: "ftaskparameter" });
            if (paramValString) {
                var paramObj = JSON.parse(paramValString);
                if (paramObj && paramObj.fextappid) {
                    that.Model.setValue({ id: "fextappid", value: paramObj.fextappid });
                }
                if (paramObj && paramObj.fbizformids) {
                    that.Model.setValue({ id: "fbizformids", value: paramObj.fbizformids });
                }
            }
        };


        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //退货
                case 'tbconfirm':
                    var data = {};
                    data.fextappid = that.Model.getSimpleValue({ id: 'fextappid' });
                    data.fbizformids = that.Model.getSimpleValue({ id: 'fbizformids' });

                    //校验收集的数据
                    if (!data.fextappid) {
                        e.result = true;
                        yiDialog.mt({ msg: '目标系统应用不可以为空！', skinseq: 2 });
                        return;
                    }

                    if (!data.fbizformids) {
                        e.result = true;
                        yiDialog.mt({ msg: '业务对象不可以为空！', skinseq: 2 });
                        return;
                    }

                    var parentModel = that.Model.getParentModel();
                    if (parentModel) {
                        parentModel.setValue({ id: 'ftaskparameter', value: JSON.stringify(data) });
                    }

                    break;
                //取消
                case 'tbcancel':
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.si_transferfrommusitaskparam = window.si_transferfrommusitaskparam || si_transferfrommusitaskparam;
})();
