///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/dashboard/dashboard_mytask.js
*/
; (function () {
    var dashboard_mytask = (function (_super) {

        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            //查询参数
            that.param = { searchType: 'mytask' };
        };

        //继承 BasePlugIn
        __extends(_child, _super);

        //表单初始化事件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            that.queryMsgs();

            //10分钟刷新一次消息数
            setInterval(function () {
                that.queryMsgs();
            }, 1000 * 60 * 10);

            $(".searchFilter input").bind("keypress", function (e) {
                if (e.which == 13) {
                    var pageOpts = that.pager.getOptions();
                    var searchFilter = $(this).val();
                    that.queryData({ pageIndex: pageOpts.pageNum + 1, pageSize: pageOpts.pageSize, searchFilter: searchFilter });
                }
            });
        };

        //处理表单渲染事件：可以在此方法里，对页面做动态化渲染处理
        _child.prototype.onPageViewRendering = function () {
            var that = this;
            var vm = that.Model.viewModel;
            that.$grid = $('#{0}_mytask_list'.format(vm.pageId)).css({ width: '100%', height: '250px' });
            that.$pager = $('#{0}_mytask_pager'.format(vm.pageId));

            //表格所有列模型
            that.allColumns = [
                { id: 'ftaskname', field: 'ftaskname', name: '任务名称', width: 300, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                { id: 'fnote', field: 'fnote', name: '任务描述', width: 500, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                { id: 'fexcuter', field: 'fexcuter', name: '执行人', width: 180, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                { id: 'fenddate', field: 'fenddate', name: '截止日期', width: 90, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-center' },
                { id: 'ftaskstatustxt', field: 'ftaskstatustxt', name: '任务状态', width: 90, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-center' },
                { id: 'freaddate', field: 'freaddate', name: '已读时间', width: 120, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-center' },
                { id: 'fcreator', field: 'fcreator', name: '创建人', width: 80, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                { id: 'fdeptname', field: 'fdeptname', name: '创建部门', width: 80, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                { id: 'fcreatedate', field: 'fcreatedate', name: '创建时间', width: 120, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-center' },
                {
                    id: 'ftaskop', field: 'ftaskop', name: '操作', width: 230, selectable: true, sortable: true, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left',
                    formatter: function (row, cell, taskOps, column, item, grid) {
                        var opHtml = '';
                        if (taskOps) {
                            for (var i = 0; i < taskOps.length; i++) {
                                opHtml += '<button class="btn btn-sm btn-entry" btnid="{0}" rowid="{2}">{1}</button> '
                                    .format(taskOps[i].id, taskOps[i].caption, item.id);
                            }
                        }
                        return opHtml;
                    }
                }
            ];

            //表格选项
            var options = {
                editorLock: new Slick.EditorLock(),
                enableAddRow: false,
                enableCellNavigation: true,
                asyncEditorLoading: false,
                multiSelect: false,
                multiColumnSort: true,
                numberedMultiColumnSort: true,
                tristateMultiColumnSort: true,
                sortColNumberInSeparateSpan: false,
                explicitInitialization: true,
                autoEdit: false,
                headerRowHeight: 40,
                rowHeight: 41
            };

            //表格列模型
            var columns = that.getShowColumns(['freaddate']);

            //创建数据视图实例
            that.dataView = new Slick.Data.DataView({ inlineFilters: true });

            //创建表格实例
            that.grid = new Slick.Grid(that.$grid, that.dataView, columns, options);

            //创建分页器实例
            that.pager = new Slick.Controls.PagerSrv(that.grid, that.$pager, { pageSize: 50 });

            //按照设置的宽高初始化表格大小
            that.grid.resizeCanvas();

            //订阅表格事件
            that.subscribeEvent();

            //初始化加载表格数据源
            that.refresh();

            //初始化表格
            that.grid.init();
        };

        //获取要显示的列
        _child.prototype.getShowColumns = function (hideCols) {
            var that = this;
            var allColumns = that.allColumns;
            if (hideCols) {
                var columns = [];
                for (var i = 0; i < allColumns.length; i++) {
                    if (hideCols.indexOf(allColumns[i].id) === -1) {
                        columns.push(allColumns[i]);
                    }
                }
                return columns;
            }
            return allColumns;
        };

        //订阅表格事件
        _child.prototype.subscribeEvent = function () {
            var that = this;

            //列头排序时触发
            that.grid.onSort.subscribe(function (e, args) {
                var pageOpts = that.pager.getOptions();
                that.queryData({ pageIndex: pageOpts.pageNum + 1, pageSize: pageOpts.pageSize });
            });

            //单击行时触发
            that.grid.onClick.subscribe(function (e, args) {
                if ($(e.target).hasClass('btn-entry')) return;
            });

            //双击行时触发
            that.grid.onDblClick.subscribe(function (e, args) {
                var data = that.grid.getDataItem(args.row);
                if (data) {
                    var taskProc = $.trim(data.ftaskprocessor);
                    switch (taskProc) {
                        case 'dynamicremind':
                            //如果是“动态提醒任务处理器”则打开关联的业务对象编辑页面
                            that.onOperateButtonClick({ btnId: 'viewbill', data: data });
                            break;
                        default:
                            //没有任务处理器则认为是消息，标记消息为已读
                            if (!taskProc) {
                                that.setMsgStatus({ setType: 'single', readStatus: 'read', taskId: data.ftaskid, msgStatus: data.ftaskstatustxt });
                            }
                            that.showMsgTargetBill(data);
                            break;
                    }
                }
            });

            //在分页时触发（服务端分页）
            that.pager.onPagingInfoChanged.subscribe(function (e, args) {
                var pageIndex = args.pageNum + 1;
                if (args.type === 'any') {
                    if (pageIndex >= args.totalPages) {
                        pageIndex = args.totalPages;
                    } else if (pageIndex <= 0) {
                        pageIndex = 1;
                    }
                }
                that.queryData({ pageIndex: pageIndex, pageSize: args.pageSize });
            });

            //在修改总行数时触发
            that.dataView.onRowCountChanged.subscribe(function (e, args) {
                that.grid.updateRowCount();
                that.grid.render();
            });

            //在修改行数据时触发
            that.dataView.onRowsChanged.subscribe(function (e, args) {
                that.grid.invalidateRows(args.rows);
                that.grid.render();
            });

            //表格行中按钮字段的点击事件
            that.$grid.on('click', '.btn-entry', function (e) {
                e.stopPropagation();
                var $btn = $(this),
                    btnId = $btn.attr('btnid'),
                    rowId = $btn.attr('rowid'),
                    data = that.dataView.getItemById(rowId);
                that.onOperateButtonClick({ btnId: btnId, data: data });
            });
        };

        //查询数据
        _child.prototype.queryData = function (args) {
            var that = this;
            var vm = that.Model.viewModel;

            //字段排序
            var orderBys = [];
            var sorts = that.grid.getSortColumns();
            for (var i = 0; i < sorts.length; i++) {
                var sort = sorts[i];
                var sortStr = (sort.columnId || (sort.sortCol && sort.sortCol.id)) + ' ' + (sort.sortAsc ? 'asc' : 'desc');
                orderBys.push(sortStr);
            }

            var searchFilter = that.Model.getSimpleValue({ id: 'searchfilter' });
            if (args.searchFilter !== undefined) {
                searchFilter = args.searchFilter;
            }

            //参数
            var params = {
                simpleData: $.extend(that.param, {
                    orderByString: orderBys.toString(),
                    pageIndex: args.pageIndex || 1,
                    pageSize: args.pageSize || 50,
                    searchFilter: searchFilter.toString()
                })
            };

            var $container = '#{0}_mytask_container'.format(vm.pageId);
            var queryUrl = '/dynamic/dashboard_mytask?operationno=querymytask&pageid={0}'.format(vm.pageId);
            yiAjax.p(queryUrl, params, function (r) {

                var srvData = r.operationResult.srvData;
                if (!srvData) return;

                //给表格设置数据源并渲染视图
                var data = srvData.dataList || [];
                for (var i = 0; i < data.length; i++) {
                    data[i].id = i;
                }
                that.dataView.setItems([]); //先清空一下，有缓存
                that.dataView.beginUpdate();
                that.dataView.setItems(data);
                that.dataView.endUpdate();

                //设置分页信息
                that.pager.setOptions({
                    totalRows: srvData.records,
                    pageNum: srvData.pageIndex - 1,
                    pageSize: params.simpleData.pageSize,
                    bills: srvData.records
                });

            }, null, null, $(vm.pageSelector));
        };

        //表格操作按钮触发的点击事件
        _child.prototype.onOperateButtonClick = function (e) {
            var that = this;
            if (!e.btnId || !e.data) return;
            var taskProc = $.trim(e.data.ftaskprocessor).toLowerCase();
            switch (taskProc) {
                case 'standard':
                    that.standardTaskProc(e);
                    break;
                case 'approvalflow':
                    that.approvalFlowTaskProc(e);
                    break;
                case 'dynamicremind':
                    that.dynamicRemindTaskProc(e);
                    break;
                default:
                    //没有任务处理器则认为是消息，直接打开所关联的业务表单编辑页面
                    that.setMsgStatus({ setType: 'single', readStatus: 'read', taskId: e.data.ftaskid, msgStatus: e.data.ftaskstatustxt });
                    that.showMsgTargetBill(e.data);
                    break;
            }
        };

        //显示消息对应的业务表单
        _child.prototype.showMsgTargetBill = function (data) {
            var that = this;
            if (data.fbizformid && data.fbizbillpkid) {
                switch (data.fbizformid.toLowerCase()) {
                    //打开收支明细详情对话框
                    case 'coo_incomedisburse':
                        that.Model.invokeFormOperation({
                            id: 'LoadData',
                            opcode: 'LoadData',
                            selectedRows: [{ PKValue: data.fbizbillpkid }],
                            param: {
                                formId: data.fbizformid,
                                domainType: Consts.domainType.bill
                            }
                        });
                        break;
                    default:
                        that.Model.showBill({
                            formId: data.fbizformid,
                            pkids: [data.fbizbillpkid]
                        });
                        break;
                }
            }
        };

        //标准任务处理器
        _child.prototype.standardTaskProc = function (e) {
            var that = this;
            switch (e.btnId) {
                case 'test':

                    break;
            }
        };

        //审批流任务处理器
        _child.prototype.approvalFlowTaskProc = function (e) {
            var that = this;
            switch (e.btnId) {
                case 'view':
                    that.sendTaskprocessor(e, {
                        formId: e.data.fbizformid,
                        billPkId: e.data.fbizbillpkid
                    });
                    break;
                default:
                    //弹窗填写审批意见
                    var title = '请填写审核意见';
                    var area = ['450px', '270px'];
                    var html = '\
                    <div class="form-group row">\
                        <div class="col-md-12">\
                            <textarea class="form-control" placeholder="审核意见" \
                                style="width:410px;max-width:410px;min-width:410px;height:130px;max-height:130px;min-height:130px;">同意</textarea>\
                        </div>\
                    </div>';
                    if (e.btnId === 'reject') {
                        title = '请填写驳回原因';
                        area = ['450px', '300px'];
                        html = '\
                        <div class="form-group row">\
                            <div class="col-md-12">\
                                <textarea class="form-control" placeholder="驳回原因" \
                                    style="width:410px;max-width:410px;min-width:410px;height:123px;max-height:123px;min-height:123px;"></textarea>\
                            </div>\
                        </div>\
                        <div class="form-group row">\
                            <div class="col-md-12">\
                                <div class="checkbox-list">\
                                    <label class="checkbox-inline"><input type="checkbox">终止流程（仅对启用审批流的数据有效）</label>\
                                </div>\
                            </div>\
                        </div>';
                    } else if (e.btnId === 'terminate') {
                        title = '请填写终止原因';
                        html = '\
                        <div class="form-group row">\
                            <div class="col-md-12">\
                                <textarea class="form-control" placeholder="终止原因" \
                                    style="width:410px;max-width:410px;min-width:410px;height:130px;max-height:130px;min-height:130px;"></textarea>\
                            </div>\
                        </div>';
                    }
                    yiDialog.d({
                        id: 'dashboard_mytask_approvalFlow',
                        type: 1,
                        content: html,
                        resize: false,
                        title: title,
                        area: area,
                        btn: ['取消', '确定'],
                        btncls: ['', '0'],
                        yes: function (index, layero) {
                            layer.close(index);
                        },
                        btn2: function (index, layero) {
                            var $layero = $(layero);
                            var execOpinion = $.trim($layero.find('textarea').val());
                            var terminate = false;
                            if (e.btnId === 'reject') {
                                terminate = $layero.find(':checkbox').is(':checked');
                            }
                            that.sendTaskprocessor(e, {
                                formId: e.data.fbizformid,
                                billPkId: e.data.fbizbillpkid,
                                execOpinion: execOpinion,
                                terminate: terminate
                            });
                        },
                        success: function (layero, index) {
                            var $layero = $(layero);
                            $layero.find('textarea').select();
                            if (e.btnId === 'reject') {
                                yiCommon.parserCheckboxRadio($layero);
                            }
                        }
                    });
                    break;
            }
        };

        //动态提醒任务处理器
        _child.prototype.dynamicRemindTaskProc = function (e) {
            var that = this;
            switch (e.btnId) {
                case 'viewbill':
                    that.setMsgStatus({ setType: 'single', readStatus: 'read', taskId: e.data.ftaskid, msgStatus: e.data.ftaskstatustxt });
                    break;
            }
            that.sendTaskprocessor(e, {
                formId: e.data.fbizformid,
                dynRecordId: e.data.fbizbillpkid
            });
        };

        //发送请求处理任务
        _child.prototype.sendTaskprocessor = function (e, taskOpParam) {
            var that = this;
            that.Model.invokeFormOperation({
                id: 'taskprocessor',
                opcode: 'taskprocessor',
                param: {
                    formId: 'bf_task',
                    taskProcessor: e.data.ftaskprocessor,
                    taskOpCode: e.btnId,
                    taskOpParam: JSON.stringify(taskOpParam)
                }
            });
        };

        //加载未读消息数
        _child.prototype.queryMsgs = function () {
            var that = this;
            that.Model.invokeFormOperation({
                id: 'querymsgs',
                opcode: 'querymsgs'
            });
        };

        //标记消息为（已读/未读）
        _child.prototype.setMsgStatus = function (param) {
            var that = this;
            param.msgStatus = $.trim(param.msgStatus);
            if (param.setType === 'all' || param.msgStatus === '未读') {
                that.Model.invokeFormOperation({
                    id: 'setmsgstatus',
                    opcode: 'setmsgstatus',
                    param: param
                });
            }
        };

        //页面元素的点击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id) {
                case 'task':
                    that.param = $.extend(that.param, e.param);
                    var hideCols;
                    var colName;
                    switch (that.param.searchType) {
                        case 'mytask':
                        case 'mytakepartin':
                        case 'myallot':
                            colName = { ftaskname: '任务名称', fnote: '任务描述', ftaskstatustxt: '任务状态' };
                            hideCols = ['freaddate'];
                            break;
                        case 'mymsg':
                            colName = { ftaskname: '消息标题', fnote: '消息正文', ftaskstatustxt: '消息状态' };
                            hideCols = ['fexcuter', 'fenddate'];
                            break;
                    }
                    //隐藏某些列
                    if (hideCols) {
                        var columns = that.getShowColumns(hideCols);
                        for (var key in colName) {
                            for (var i = 0; i < columns.length; i++) {
                                if (columns[i].id === key) {
                                    columns[i].name = colName[key];
                                    break;
                                }
                            }
                        }
                        that.grid.setColumns(columns);
                    }
                    that.refresh({ pageIndex: 1 });
                    break;
                case 'msg':
                    that.setMsgStatus({ setType: 'all', readStatus: e.param.readStatus });
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'taskprocessor':
                    if (isSuccess) {
                        that.refresh();
                    }
                    break;
                case 'querymsgs':
                    if (srvData) {
                        that.Model.setVisible({ id: '.y-myunreads', value: srvData.unReads > 0 });
                        that.Model.setText({ id: '.y-alls', value: srvData.unReads + srvData.reads });
                        that.Model.setText({ id: '.y-unreads', value: srvData.unReads });
                        that.Model.setText({ id: '.y-reads', value: srvData.reads });
                    }
                    break;
                case 'setmsgstatus':
                    if (isSuccess) {
                        that.queryMsgs();
                        that.refresh();
                    }
                    break;
                //加载收支记录信息（用于收支详情对话框的数据展示）
                case 'loaddata':
                    if (isSuccess && srvData) {
                        that.Model.showForm({
                            formId: 'coo_incomedisburserptdetail',
                            param: { openStyle: Consts.openStyle.modal },
                            cp: srvData.uidata
                        });
                    }
                    break;
            }
        };

        //刷新表格数据
        _child.prototype.refresh = function (args) {
            var that = this;
            var pageOpts = that.pager.getOptions();
            var pageIndex = args && args.pageIndex;
            if (!pageIndex) pageIndex = pageOpts.pageNum + 1;
            that.queryData({
                pageIndex: pageIndex,
                pageSize: that.pager.getPageSize()
            });
        };

        return _child;
    })(BasePlugIn);
    window.dashboard_mytask = window.dashboard_mytask || dashboard_mytask;
})();