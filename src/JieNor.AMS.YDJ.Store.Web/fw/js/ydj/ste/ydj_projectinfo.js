/*
*/
; (function () {
    var ydj_projectinfo = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);

        };
        __extends(_child, _super);

        //页面初始化
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
        };


        return _child;
    })(BillPlugIn);
    window.ydj_projectinfo = window.ydj_projectinfo || ydj_projectinfo;
})();