
; (function () {
    var ydj_customerinvoice = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);

        };
        __extends(_child, _super);

        //页面初始化
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {

            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'finvoiceemail':
                    that.validateInvoiceEmails(e);
                    break;
                case 'fbankaccount':
                    that.checkBankAccountIsNumber(e);
                    break;
                    
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'modify':
                    debugger;
                    var row = that.Model.getSelectRows({ id: "fbillhead" });
                    if (row && row.length > 0) {
                        that.param = { openStyle: "inContainer", containerId: 'ydj_customerinvoice' };
                        var url = '/list/' + "ydj_customerinvoice" + '?operationno=modify&pageId=' + that.Model.viewModel.pageId;
                        var params = { selectedRows: [], simpleData: that.param };
                        params.selectedRows.push({ PKValue: row[0].pkValue });
                        yiAjax.p(url, params, null, null, null, $(that.pageSelector));
                    }
                    break;
                case 'save':
                    debugger;
                    e.result = true;
                    var customer = '';
                    var parentModel = that.Model.getParentModel();
                    var parentModelObj = JSON.parse(parentModel.viewModel.dynamicParam);
                    customer = parentModelObj.customer;
                    var cloneData = that.Model.clone();
                    // that.Model.invokeFormOperation({
                    //     id: 'savecustomer',
                    //     opcode: 'savecustomer',
                    //     billData: [cloneData],
                    //     param: {
                    //         formId: 'ydj_customerinvoice',
                    //         customer: customer
                    //     }
                    // });
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: {
                            formId: 'ydj_customerinvoice',
                            customer: customer
                        }
                    });
                    break;
                //更换负责人
            }
        }

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            switch (e.opcode) {
                case 'save':
                case 'savecustomer':
                    if (isSuccess) {
                        //关闭当前弹窗
                        that.Model.close();
                        // setTimeout(function () {
                        //     //刷新父页面
                        //     that.Model.refresh();
                        // },500);
                        that.Model.refresh();
                        // that.Model.refresh();
                    }
                    break;
            }
        }
        _child.prototype.checkBankAccountIsNumber = function (e) {
            debugger;
            var that = this;
            var bankaccount = e.value;
            if (!bankaccount) {
                return;
            }
            var isNumber = /^\d+$/.test(bankaccount);
            if (!isNumber) {
                yiDialog.warn('输入的银行账号格式有误，请检查!');
                that.Model.setValue({ id: 'fbankaccount', value: '', tgChange: false });
                that.Model.setValue({ id: 'fbankaccount', value: '', tgChange: false });
            }
        }

        //检查开票信息中的邮箱是否有效
        _child.prototype.validateInvoiceEmails = function (e) {
            var that = this;

            // 邮箱正则表达式
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            var invoiceemailStr = e.value;

            if (!invoiceemailStr) {
                return;
            }

            // 分割字符串为单个邮箱数组
            var emails = invoiceemailStr.split(',');

            var isTrue = true;

            // 验证每个邮箱
            for (let i = 0; i < emails.length; i++) {
                // 去除前后空格
                const email = emails[i].trim();

                // 跳过空字符串（连续逗号或末尾逗号的情况）
                if (email === '') continue;

                // 验证邮箱格式
                if (!emailRegex.test(email)) {
                    isTrue = false;
                    break;
                }
            }
            if (!isTrue) {
                yiDialog.warn('输入的电子邮箱格式有误，格式类似于***********或者多个邮箱格式************,<EMAIL>');
                that.Model.setValue({ id: 'finvoiceemail', value: '',  tgChange: false });
                that.Model.setValue({ id: 'finvoiceemail', value: '',  tgChange: false });
            }


        }
        return _child;
    })(BillPlugIn);
    window.ydj_customerinvoice = window.ydj_customerinvoice || ydj_customerinvoice;
})();