///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/pur/ydj_purchasequote.js
*/
; (function () {
    var ydj_purchaseorderapply_chg = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.productEntryId = 'fentity';

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;

        };


        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'save':
                case 'savesubmit':
                case 'saveaudit':
                    e.result = true;
                    var param = e.param;
                    param.formId = "ydj_purchaseorderapply_chg";
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: param
                    });
                    break;
            }
        };

        _child.prototype.onGetEditorState = function(e) {
            var that = this;
            var product = that.Model.getEntryRowData({ id: that.productEntryId, row: e.row });
            var rowId = e.row;
            //套件不允许编辑采购数量(新)
            switch (e.id.toLocaleLowerCase()) {
                case 'fnewbizqty':
                    if (product && product.fpartscombnumber != '' && !product.fiscombmain) {
                        e.result.enabled = false;
                    }
                    else if (product && product.fsuitcombnumber != '' && !product.fissuitflag) {
                        e.result.enabled = false;
                    }
                    break;
                
                default:
                    break;
            }
        }

        //表格行创建前事件
        _child.prototype.onEntryRowCreating = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.productEntryId:
                    e.result = true;
                    yiDialog.warn('当前为采购变更申请单,不允许新增行');
                    break;
            }
        };

        //表格行删除前事件：设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.productEntryId:
                    e.result = true;
                    yiDialog.warn('当前为采购变更申请单,不允许删除行');
                    break;
            }
        };

        //字段值变化事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fnewbizqty':
                    debugger;
                    that.calculateqtyBysuit(e);
                    that.calculateqtyBypart(e);
                    that.Model.refreshEntry({ id: that.productEntryId });
                    break;
            }
        };

        //根据套件翻倍子件的销售数量
        _child.prototype.calculateqtyBysuit = function (e) {
            var that = this;
            var suitqty = e.value;
            var product = that.Model.getEntryRowData({ id: that.productEntryId, row: e.row });
            var ds = that.Model.getEntryData({ id: that.productEntryId });
            //如果当前修改明细是套件商品，修改数量后要翻倍子件的销售数量
            if (product && product.fsuitcombnumber) {
                //找到对应的子件明细
                for (var a = 0; a < ds.length; a++) {
                    //有套件组合号 但是不是套件商品则为套件的子件
                    if (ds[a].fsuitcombnumber == product.fsuitcombnumber && !ds[a].fissuitflag) {
                        var fsubqty = yiMath.toNumber(ds[a].fsubqty);
                        var totalQty = suitqty * fsubqty;
                        var currentRowId = ds[a].id;
                        ds[a].fnewbizqty = totalQty;
                        //that.Model.setValue({ id: "fbizqty", value: totalQty, row: currentRowId, tgChange: false });//
                        that.Model.setValue({ id: "fnewbizqty", value: totalQty, row: currentRowId, tgChange: false });//
                    }
                }
            }

        };

        //修改配件主商品时 根据配件组合号 翻倍配件的数量
        _child.prototype.calculateqtyBypart = function (e) {
            var that = this;
            var mainqty = e.value;
            that.calculatePartQty(mainqty, e.row);
        };

        _child.prototype.calculatePartQty = function (mainqty, row) {
            var that = this;
            var product = that.Model.getEntryRowData({ id: that.productEntryId, row: row });
            var ds = that.Model.getEntryData({ id: that.productEntryId });
            //如果当前修改明细是配件商品，修改数量后要翻倍配件的销售数量
            if (product.fpartscombnumber) {
                //找到对应的子件明细
                for (var a = 0; a < ds.length; a++) {
                    //有配件组合号 但是不是配件主商品则为配件商品
                    if (ds[a].fpartscombnumber == product.fpartscombnumber && !ds[a].fiscombmain) {
                        //配件数量（元数量）
                        var fpartqty = yiMath.toNumber(ds[a].fpartqty);
                        that.Model.setValue({ id: "fnewbizqty", row: ds[a].id, value: mainqty * fpartqty, tgChange: false });
                    }
                }
            }

        };

        return _child;
    })(BasePlugIn);
    window.ydj_purchaseorderapply_chg = window.ydj_purchaseorderapply_chg || ydj_purchaseorderapply_chg;
})();