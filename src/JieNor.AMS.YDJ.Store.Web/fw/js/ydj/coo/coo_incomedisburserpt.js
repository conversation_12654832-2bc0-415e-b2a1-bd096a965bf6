///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/coo/coo_incomedisburserpt.js
*/
; (function () {
    var coo_incomedisburserpt = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.entryId = 'freportlist';

        //初始化事件
        _child.prototype.onInitialized = function (args) {
            var that = this;

            //加载日期区间值
            that.getDataRange();
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.entryId:
                    e.result = { multiselect: false };
                    break;
            }
        };

        //表格按钮点击事件
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.entryId:
                    var rowData = that.Model.getEntryRowData({ id: that.entryId, row: e.row });
                    switch (e.btnid.toLowerCase()) {
                        //确认
                        case 'confirm':
                            that.Model.invokeFormOperation({
                                id: 'Confirm',
                                opcode: 'Confirm',
                                selectedRows: [{ PKValue: rowData.fid }],
                                param: {
                                    formId: 'coo_incomedisburse',
                                    domainType: Consts.domainType.bill
                                }
                            });
                            break;
                            //详情
                        case 'detail':
                            that.Model.invokeFormOperation({
                                id: 'LoadData',
                                opcode: 'LoadData',
                                selectedRows: [{ PKValue: rowData.fid }],
                                param: {
                                    formId: 'coo_incomedisburse',
                                    domainType: Consts.domainType.bill
                                }
                            });
                            break;
                    }
                    break;
            }
        };

        //元素点击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            var opcode = e.id.toLowerCase();
            switch (e.id.toLowerCase()) {
                case 'tswk':
                case 'ltwk':
                case 'tsmh':
                case 'ltmh':
                    //点击日期类型，设置选中状态
                    that.Model.setAttr({ id: '.time-area [optype]', random: 'timearea', value: false });
                    that.Model.setAttr({ id: '[optype={0}]'.format(opcode), random: 'timearea', value: true });
                    that.Model.removeClass({ id: '.time-area [timearea=false]', value: 'active' });
                    that.Model.addClass({ id: '.time-area [timearea=true]', value: 'active' });
                    //加载日期区间值
                    that.getDataRange();
                    break;
            };
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                case 'search':
                    e.result = true;
                    that.search();
                    break;
            };
        };

        //分页
        _child.prototype.onElementChange = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fpageindex':
                case 'fpagesize':
                    that.search();
                    break;
            }
        };

        //获取时间范围
        _child.prototype.getDataRange = function (e) {
            var that = this;
            var dtType = that.Model.getText({ id: '[timearea=true]' });
            that.Model.invokeFormOperation({
                id: 'dtSpan',
                opcode: 'dtSpan',
                param: {
                    formId: 'sys_reportshell',
                    domainType: Consts.domainType.dynamic,
                    dtType: dtType
                }
            });
        };

        //业务插件内容写在此
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;
            e.cancel = true;
            e.result = [];
            var bizStatus = $.trim(e.data.fbizstatus).toLowerCase();
            var purpose = $.trim(e.data.fpurpose).toLowerCase();
            var loginCompanyId = $.trim(Consts.loginCompany.id).toLowerCase();
            var createCompanyId = $.trim(e.data.fcreatecompanyid).toLowerCase();
            if (loginCompanyId !== createCompanyId && bizStatus === 'bizstatus_01') {
                e.result.push({ id: 'confirm', text: '确认' });
            }
            e.result.push({ id: 'detail', text: '详情' });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                    //确认
                case 'confirm':
                    if (isSuccess) {
                        //自动查询一次
                        that.search();
                    }
                    break;
                    //加载日期区间值
                case 'dtspan':
                    that.Model.setValue({ id: 'fstartdate', value: srvData[0] });
                    that.Model.setValue({ id: 'fenddate', value: srvData[1].split(' ')[0] });
                    //自动查询一次
                    that.search();
                    break;
                    //加载收支记录信息（用于收支详情对话框的数据展示）
                case 'loaddata':
                    if (isSuccess && srvData) {
                        var cp = srvData.uidata;
                        cp.callback = function (result) {
                            if (result && result.isSuccess) {
                                //刷新账户流水列表
                                that.search();
                            }
                        };
                        that.Model.showForm({
                            formId: 'coo_incomedisburserptdetail',
                            param: { openStyle: Consts.openStyle.modal },
                            cp: cp
                        });
                    }
                    break;
            }
        };

        //查询
        _child.prototype.search = function () {
            var that = this;

            var pageSize = that.Model.getEleMent({ id: '#fpagesize' }).val();
            var pageIndex = that.Model.getEleMent({ id: '#fpageindex' }).val();
            var company = that.Model.getValue({ id: 'fcompany' });
            var bizStatus = that.Model.getSimpleValue({ id: 'fbizstatus_s' });
            var purpose = that.Model.getSimpleValue({ id: 'fpurpose_s' });
            var way = that.Model.getSimpleValue({ id: 'fway_s' });
            var dataType = that.Model.getAttr({ id: '[datetype].active', random: 'datetype' });
            var startDate = that.Model.getValue({ id: 'fstartdate' });
            var endData = that.Model.getValue({ id: 'fenddate' });

            that.Model.refresh({
                pageIndex: pageIndex,
                pageSize: pageSize,
                company: company,
                bizStatus: bizStatus,
                purpose: purpose,
                way: way,
                dataType: dataType,
                startDate: startDate,
                endDate: endData
            });
        };

        return _child;
    })(BillPlugIn);
    window.coo_incomedisburserpt = window.coo_incomedisburserpt || coo_incomedisburserpt;
})();