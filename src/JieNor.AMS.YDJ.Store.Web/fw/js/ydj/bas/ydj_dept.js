// <reference path="/fw/js/basepage.js" />
/* @ sourceURL=/fw/js/ydj/bas/ydj_dept.js */
; (function () {
    var ydj_dept = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);
		
		//初始化事件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            if (that.checkIsAuth(e))
            { 
                that.setIframeUrl(e);
            }
            that.setFieldMustFlagByDirectSale();
        }

        _child.prototype.checkIsAuth = function (e) {
            var that = this;
            var IsAuth = false;
            var param = {
                'formId': 'ms_authcenterparam',
                'domainType': 'parameter'
            };
            yiAjax.p('/bill/ms_authcenterparam?operationno=LoadAuthParam', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                if (!res.isSuccess) {
                    IsAuth = false;
                }
                else {
                    if (res.srvData) {
                        IsAuth = res.srvData["fisuseauth"];
                    }
                }
            }, null, null, null, { async: false });
            return IsAuth;
        };

        //设置认证站点iframe地址
        _child.prototype.setIframeUrl = function (e) {
            var that = this;
            //部门无需跳转认证中台页面。
            return;
            e.result = true;
            Index.openForm({
                formId: 'sec_setiframeurl',
                domainType: 'bill',
                formid: "ydj_dept",
                cp: { formid: "ydj_dept", opcode: "modify", pageid: that.Model.viewModel.pageId}
            });
        };

        _child.prototype.onEntryRowDblClick = function (e) {
            debugger;
            var that = this;
            var page = Index.getPage(that.Model.viewModel.pageId);
            //替换原有的方式 双击不再打开修改页面而是打开iframe
            that.setIframeUrl(e);
        }
        
        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case '':
                    break;
            }
        }

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                case 'new':
                case 'copy':
                case 'delete':
                case 'forbid':
                case 'unforbid':
                    if (that.checkIsAuth(e)) { 
                        //替换原有的方式 双击不再打开修改页面而是打开iframe
                        that.setIframeUrl(e);
                    }
                    break;
            }
        }

        //字段标签点击时触发的事件onFieldLabelClick
        _child.prototype.onFieldLabelClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fstore':
                    //检查是否可查看
                    var deptName = that.Model.getSimpleValue({ id: 'fname' });
                    var storeId = that.Model.getSimpleValue({ id: 'fstore' });
                    var canVist = true;
                    var param = {
                        simpleData: {
                            formId: 'ydj_dept',
                            deptName: deptName,
                            storeId, storeId,
                            domainType: 'dynamic'
                        }
                    };
                    yiAjax.p('/bill/ydj_dept?operationno=checkvisitpermission', param, function (r) {
                        that.Model.unblockUI({ id: '#page#' });
                        var res = r.operationResult;
                        canVist = res.isSuccess;

                    }, null, null, null, { async: false });

                    if (canVist === false) {
                        e.cancel = true;
                        return false;
                    }

                    break;
            }
        };

        //菜单操作执行前方法
        _child.prototype.onBeforeDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            switch (e.opcode) {
                case 'new':
                    if ($.isFunction(that.Model.getSelectedNodes)) {
                        var nodes = that.Model.getSelectedNodes();
                        if (nodes && nodes.length > 0) {
                            var nodePkid = $.trim(nodes[0].pkid);
                            if (nodePkid && nodePkid !== 'all') {
                                e.result = { __defaultData__: JSON.stringify({ fparentid: nodePkid }) }
                            }
                        }
                    }
                    break;
            }
        };

        //设置必录标签
        _child.prototype.setFieldMustFlagByDirectSale = function (e) {
            var that = this;
            if (Consts.isdirectsale) {
                that.Model.setVisible({ id: '.zyclass', value: true }); //显示直营字段
            } else {
                that.Model.setVisible({ id: '.zyclass', value: false }); //显示直营字段
            }
        };
        
        return _child;
    })(BasePlugIn);
    window.ydj_dept = window.ydj_dept || ydj_dept;
})();