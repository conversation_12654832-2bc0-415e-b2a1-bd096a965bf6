///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/bas/ydj_suit.js
*/
; (function () {
    var ydj_suit = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.productEntryId = 'fsuitentry';

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.onBillInitialized = function (args) {
            var that = this;

        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fproductid':
                    var enable = false;
                    if ($.trim(e.value.id)) {
                        enable = true;
                        that.Model.setEnable({ id: 'fattrinfo', row: e.row, value: enable });
                    }
                    
                    that.Model.setValue({ id: 'fqty', row: e.row, value: 0 });
                    that.Model.setValue({ id: "fprice", row: e.row, value: 0 });

                    that.onBillInitProduct('change', { attrinfo: e });
                    break;
                case 'fisgiveaway':
                    if (e.value) {
                        that.Model.setValue({ id: "fprice", row: e.row, value: 0 });
                    } 
                    break;
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fprice':
                    var status = that.Model.getSimpleValue({id:'fstatus'});
                    if (e.value.fisgiveaway || status == 'D' ||　status == 'E') {
                        e.result.enabled = false;
                        return;
                    } else {
                        e.result.enabled = true;
                    }
                    break;
            }
        };

        //初始化商品明细表的经销价插件
        _child.prototype.onBillInitProduct = function (param, params) {
            var that = this;
            var productData = [];

            if (param == 'change') {//改变某一行

                var rowData = that.Model.getEntryRowData({ id: that.productEntryId, row: params.attrinfo.row });
                //如果商品没值，就不用取价，零售价经销价直接设置为0
                if (params.attrinfo.id == 'fproductid' && params.attrinfo.value && !$.trim(params.attrinfo.value.id)) {

                    that.Model.setValue({ id: 'fprice', row: rowData.id, value: 0 });
                    return;
                }
                //如果商品没值，所携带的辅助属性肯定也为空，零售价经销价直接设置为0
                if (params.attrinfo.id == 'fattrinfo' && rowData.fproductid && !$.trim(rowData.fproductid.id)) {
                    that.Model.setValue({ id: 'fprice', row: rowData.id, value: 0 });
                    return;
                }
                //如果是赠品，则直接设置为0
                var fisgiveaway = that.Model.getValue({ id: 'fisgiveaway', row: rowData.id });
                if (fisgiveaway) {
                    that.Model.setValue({ id: 'fprice', row: rowData.id, value: 0 });
                    return;
                }

                var reAttrinofEntry = [];//按照接口，重新组合
                var tempAttr = [];
                if (rowData.fattrinfo && rowData.fattrinfo.fentity && rowData.fattrinfo.fentity.length > 0) {
                    tempAttr = rowData.fattrinfo.fentity;
                }

                for (var n = 0, m = tempAttr.length; n < m; n++) {
                    reAttrinofEntry.push({
                        valueId: tempAttr[n].fvalueid,
                        auxPropId: tempAttr[n].fauxpropid.id
                    })
                }

                productData.push({
                    clientId: rowData.id,
                    productId: rowData.fproductid.id,
                    bizDate: '',
                    length: 0,
                    width: 0,
                    thick: 0,
                    customerId: '',
                    stockStatus: '',
                    attrInfo: {
                        id: '',
                        entities: reAttrinofEntry
                    }
                });



            } else if (param == 'changeall') {
                var reGridData = that.Model.getValue({ id: that.productEntryId });
                for (var i = 0, l = reGridData.length; i < l; i++) {
                    var rowData = reGridData[i];

                    if (!$.trim(rowData.fproductid.id)) {
                        continue;
                    }
                    var reAttrinofEntry = [];//按照接口，重新组合
                    var tempAttr = [];
                    if (rowData.fattrinfo && rowData.fattrinfo.fentity && rowData.fattrinfo.fentity.length > 0) {
                        tempAttr = rowData.fattrinfo.fentity;
                    }

                    for (var n = 0, m = tempAttr.length; n < m; n++) {
                        reAttrinofEntry.push({
                            valueId: tempAttr[n].fvalueid,
                            auxPropId: tempAttr[n].fauxpropid.id
                        })
                    }

                    productData.push({
                        clientId: rowData.id,
                        productId: rowData.fproductid.id,
                        bizDate: '',
                        length: 0,
                        width: 0,
                        thick: 0,
                        customerId: '',
                        stockStatus: '',
                        attrInfo: {
                            id: '',
                            entities: reAttrinofEntry
                        }
                    });
                }
            }
            if (productData.length == 0) {
                return;
            }
            productData = JSON.stringify(productData);

            that.Model.invokeFormOperation({
                id: param,

                opcode: 'getprices',
                param: {
                    productInfos: productData,
                    formId: 'ydj_price',
                    domainType: 'dynamic'
                }
            });
        }

        //报价明细 辅助属性价格查询按钮点击事件
        _child.prototype.onPriceSerch = function (e) {

            var that = this;
            var flag = true;
            that.alertModel = e;
            var productData = [];
            var reAttrinofEntry = [];//按照接口，重新组合 
            var fentry = that.alertModel.Model.uiData.fentity;

            //如果当前选中行是赠品，直接设置为0
            var rowIndex = that.Model.getFocusRowIndex({ id: that.productEntryId });
            var fisgiveaway = that.Model.getValue({ id: 'fisgiveaway', row: rowIndex });
            if (fisgiveaway) {
                that.alertModel.Model.setValue({ id: 'fprice', value: 0 });
                return;
            }

            for (var n = 0, m = fentry.length; n < m; n++) {
                var lm = fentry[n];
                if (lm.fisselect) {//被选中的辅助属性行

                    if (!lm.fvalueid) {//辅助属性行需要填满信息才能查询
                        flag = false;
                    }
                    reAttrinofEntry.push({
                        valueId: lm.fvalueid,
                        auxPropId: lm.fauxpropid.id
                    })
                }

            }

            productData.push({
                clientId: '',
                productId: that.alertModel.Model.uiData.fmaterialid.id,
                bizDate: '',
                length: 0,
                width: 0,
                thick: 0,
                customerId: '',
                stockStatus: '',
                attrInfo: {
                    id: '',
                    entities: reAttrinofEntry
                }
            });

            productData = JSON.stringify(productData);
            if (flag) {
                that.Model.invokeFormOperation({
                    id: 'onPriceSerch',

                    opcode: 'getprices',
                    //option: cvtParams,
                    param: {
                        productInfos: productData,
                        formId: 'ydj_price',
                        domainType: 'dynamic'
                    }
                });
            } else {
                yiDialog.mt({ msg: '辅助属性信息不全，无法查询价格。', skinseq: 2 });
            }
        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':

                    that.onBillInitProduct('change', { attrinfo: e });
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'getprices':
                    if (e.id == 'init' || e.id == 'change' || e.id == 'changeall') {

                        if (!srvData) {
                            return;
                        }

                        var str = '';
                        var gridData = that.Model.getValue({ id: that.productEntryId });

                        for (var i = 0, l = srvData.length; i < l ; i++) {
                            var lm = srvData[i];
                            var rowData = {};
                            var num = 0;
                            for (var n = 0, m = gridData.length; n < m ; n++) {
                                if (gridData[n].id == lm.clientId) {//获取对于行
                                    rowData = gridData[n];
                                    num = n + 1;
                                }
                            }
                            if (lm.success) {//价格匹配成功，则赋值
                                //当count大于1时，提示用户" 第{client}行，{productName},有{count}个项匹配，请谨慎使用匹配的数据" 价目表上获取不到商品名的，前端获取。

                                //	最匹配的销售价(零售价)赋值  ，非自备料，即使查到了零售价数据，也不赋值
                                that.Model.setValue({ id: 'fprice', row: lm.clientId, value: lm.salPrice });

                                if (lm.count > 1) {
                                    var theName = rowData.fproductid.fname;
                                    str += '第{0}行，{1},有{2}个项匹配 <br/>'.format(num, theName, lm.count);
                                }
                            } else {//价格匹配不成功
                                //yiDialog.mt({msg:'价格匹配不成功', skinseq: 2});
                                if (e.id == 'change' || e.id == 'changeall') {//查不到数据，就赋值为0

                                    that.Model.setValue({ id: 'fprice', row: lm.clientId, value: 0 });
                                }
                            }
                        }
                        if (str.length > 0) {
                            yiDialog.mt({ msg: str, skinseq: 2 });

                        }
                    } else if (e.id == 'onPriceSerch') {

                        if (srvData && srvData[0] && that.alertModel.Model) {

                            that.alertModel.Model.setValue({ id: 'fprice', value: srvData[0].salPrice });
                        }

                    }

                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_suit = window.ydj_suit || ydj_suit;
})();