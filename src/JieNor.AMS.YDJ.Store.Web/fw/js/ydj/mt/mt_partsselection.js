/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/ydj/mt/mt_selectiondimension.js
//辅助属性
; (function () {
    var mt_partsselection = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }

        __extends(_child, _super);

        _child.prototype.onBillInitialized = function (e) {
            var that = this;
            //更新维度行
            var isRebuild = that.formContext.status === "new";
            that.buildDimensionRows(isRebuild);
        };

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fproductid':
                    //更新维度行
                    that.buildDimensionRows(true);
                    break;
            }
        };

        _child.prototype.buildDimensionRows = function (isRebuild) {
            var that = this;

            if (isRebuild) that.Model.deleteEntryData({ id: "fentity" });

            var productid = that.Model.getSimpleValue({ id: "fproductid" });
            if (productid) {
                that.Model.invokeFormOperation({
                    id: 'productdimension',
                    opcode: 'productdimension',
                    param: {
                        formId: "mt_selectiondimension",
                        productid: productid
                    }
                });
            }

        }

        //操作后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            if (!isSuccess) return;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'productdimension':
                    debugger
                    if (!e.result.operationResult.simpleData.dimensions) break;
                    var dimensions = eval("(" + e.result.operationResult.simpleData.dimensions + ")");
                    if (dimensions && dimensions.length > 0) {
                        var oriData = that.Model.getEntryData({ id: "fentity" });
                        for (var i = 0; i < dimensions.length; i++) {
                            var d = dimensions[i];
                            var rowData = null;
                            if (oriData && oriData.length > 0) {
                                for (var j = 0; j < oriData.length; j++) {
                                    if (oriData[j]["fmatchingdimensions"] && oriData[j]["fmatchingdimensions"]["id"] === d["fid"]) {
                                        //原来的维度相同时，使用原来的维度数据
                                        rowData = oriData[j];
                                        //维度的来源和类型可能变更了
                                        if (rowData["fbizfieldsrc"]["id"] !== d["fvaluetype"]) {
                                            rowData["fbizfieldsrc"] = {
                                                id: d["fvaluetype"],
                                                fnumber: d["fvaluetype"],
                                                fname: d["fvaluetype"]
                                            };
                                        }
                                        if (rowData["fbizfieldtype"]["id"] !== d["fviewtypenumber"]) {
                                            rowData["fbizfieldtype"] = {
                                                id: d["fviewtypenumber"],
                                                fnumber: d["fviewtypenumber"],
                                                fname: d["fviewtypename"]
                                            };
                                        }
                                        break;
                                    }
                                }
                            }
                            if (rowData === null) {
                                rowData = {
                                    fmatchingdimensions: {
                                        id: d["fid"],
                                        fnumber: d["fnumber"],
                                        fname: d["fname"]
                                    },
                                    fbizfieldsrc: {
                                        id: d["fvaluetype"],
                                        fnumber: d["fvaluetype"],
                                        fname: d["fvaluetype"]
                                    },
                                    fbizfieldtype: {
                                        id: d["fviewtypenumber"],
                                        fnumber: d["fviewtypenumber"],
                                        fname: d["fviewtypename"]
                                    }
                                };
                                that.Model.addRow({ id: "fentity", row: i, data: rowData });
                            }
                        }
                    }
                    var allData = [].concat(that.Model.getEntryData({ id: "fentity" }));
                    for (var j = 0; j < allData.length; j++) {
                        if (allData[j]["fmatchingdimensions"]["id"] == "") that.Model.deleteRow({ id: "fentity", row: allData[j]["id"] });
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.mt_partsselection = window.mt_partsselection || mt_partsselection;
})();