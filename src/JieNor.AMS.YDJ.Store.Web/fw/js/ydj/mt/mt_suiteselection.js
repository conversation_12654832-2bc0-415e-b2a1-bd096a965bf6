/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/ydj/mt/mt_selectiondimension.js
//辅助属性
; (function () {
    var mt_suiteselection = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }

        __extends(_child, _super);

        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fpartsselectionid':
                    var partproductid = that.Model.getSimpleValue({ id: 'fpartproductid', row: e.row });
                    e.result.filterString = "fproductid=@fproductid";
                    e.result.params = [
                        { fieldId: 'fproductid', pValue: partproductid }
                    ];
                    break;
                case 'fpartproductid':
                    var fproductid = that.Model.getSimpleValue({ id: 'fproductid' });
                    //套件下的子件才能选
                    var filter = "fid IN (SELECT fpartproductid FROM T_MT_SUITEENTRY WHERE fid IN (SELECT fid FROM T_MT_SUITE WHERE fproductid = @fproductid))";
                    //排除已选
                    var fentrydata = that.Model.getEntryData({ id: 'fentity' });
                    var usedpids = [];
                    for (var i = 0; i < fentrydata.length; i++) {
                        if (fentrydata[i]["fpartproductid"] && fentrydata[i]["fpartproductid"]["id"]) {
                            usedpids.push(fentrydata[i]["fpartproductid"]["id"]);
                        }
                    }
                    if (usedpids.length > 0) {
                        filter += " AND fid NOT IN ('" + usedpids.join("','") + "')";
                    }
                    e.result.filterString = filter;
                    e.result.params = [
                        { fieldId: 'fproductid', pValue: fproductid }
                    ];
                    break;
            }
        };

        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fpartsselectionid':
                    //已配置子件后才可以选择标准子件方案可
                    var isEnable = e.value.fpartproductid.id != '';
                    e.result.enabled = isEnable;
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.mt_suiteselection = window.mt_suiteselection || mt_suiteselection;
})();