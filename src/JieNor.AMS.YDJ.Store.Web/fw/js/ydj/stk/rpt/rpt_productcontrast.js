///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_productcontrast.js
*/
; (function () {
    var rpt_productcontrast = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        return _child;
    })(ListReportPlugin);
    window.rpt_productcontrast = window.rpt_productcontrast || rpt_productcontrast;
})();