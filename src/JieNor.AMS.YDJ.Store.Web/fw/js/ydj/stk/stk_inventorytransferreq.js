; (function () {
    var stk_inventorytransferreq = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //页面初始化
        _child.prototype.onBillInitialized = function (args) {

        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fmtono':
                    that.fillMtoNoFieldValue(e);
                    break;
                case 'fownerid':
                    that.fillBaseFieldValue(e);
                    break;
                case 'fstorelocationid':
                    that.fillBaseFieldValue(e);
                    break;
            }
        };

        //自定义填充引用字段值
        _child.prototype.fillBaseFieldValue = function (e) {
            var that = this;
            var fieldId = e.id.toLowerCase();
            var fieldValue = e.value;
            var row = e.row;
            if (!fieldValue && $.trim(fieldValue.id) == "") {
                return;
            }
            var targetFiledIdKey = fieldId + "to";
            var targetValue = that.Model.getValue({ id: targetFiledIdKey, row: row });
            if (targetValue && $.trim(targetValue.id) != "") {
                return;
            }
            that.Model.setValue({ id: targetFiledIdKey, value: fieldValue, row: row });
        }

        //辅助属性变化
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    var attrinfoTo = that.Model.getValue({ id: "fattrinfoto", row: e.row });
                    if (!attrinfoTo) {
                        that.Model.getValue({ id: "fattrinfoto", row: e.row, value: e.value });
                    }
                    break;
            }
        };

        //自定义填充物流跟踪号字段值
        _child.prototype.fillMtoNoFieldValue = function (e) {
            var that = this;
            var mtono = $.trim(e.value);
            if (mtono) {
                that.Model.setValue({ id: "fmtonoto", value: mtono, row: e.row });
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcustomdesc':
                case 'fcallupcustomdescto':
                    var type = that.Model.getSimpleValue({ id: 'ftype' });
                    if (type == '1') {
                        e.result.enabled = true;
                        return;
                    }
                    else {
                        e.result.enabled = false;
                        return;
                    }
                    break;
            }
        }
        
        return _child;
    })(BasePlugIn);
    window.stk_inventorytransferreq = window.stk_inventorytransferreq || stk_inventorytransferreq;
})();