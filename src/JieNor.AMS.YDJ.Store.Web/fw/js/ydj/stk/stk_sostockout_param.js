///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/ste/stk_sostockout_param.js
*/
; (function () {
    var stk_sostockout_param = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            _super.prototype.onInitialized.call(that, e);
        };

        return _child;
    })(BasePlugIn);
    window.stk_sostockout_param = window.stk_sostockout_param || stk_sostockout_param;
})();