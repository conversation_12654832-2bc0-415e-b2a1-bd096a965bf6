<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="rpt_safetystockwaterlevel" el="1" basemodel="" cn="安全库存水位线" rac="true" isolate="1">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_safetystockwaterlevel" pn="fbillhead" cn="安全库存水位线表">
        <input group="基本信息" lix="5" el="107" ek="fbillhead" visible="-1" id="fagentnumber" fn="fagentnumber" pn="fagentnumber" cn="售达方编号" width="120" notrace="true" ctlfk="fagent" dispfk="fnumber" />
        <input group="基本信息" lix="10" el="106" ek="fbillhead" visible="-1" id="fagent" fn="fagent" pn="fagent" cn="售达方名称" refid="bas_agent" width="115" defls="true" />
        <input lix="15" group="基本信息" el="107" ek="fbillhead" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="-1" cn="商品编码" aqkf="true" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0" />
        <input lix="20" group="基本信息" el="106" ek="fbillhead" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="-1" cn="商品名称" aqkf="true" lock="-1" copy="0" notrace="true" ts="" refid="ydj_product" filter="" reflvt="0" dfld="fname,fspecifica,fvolume,fgrossload,fpacksize,fsize" />
        <input   group="基本信息" id="fcategorynumber" el="108" ek="fbillhead" fn="fcategorynumber" pn="fcategorynumber" cn="商品类别编码" desc="商品类别编码" width="120" visible="0" copy="0"  />
        <input  lix="21"  group="基本信息"  id="fcategoryname" el="100" ek="fbillhead" fn="fcategoryname" pn="fcategoryname"  cn="商品类别" desc="商品类别" width="120" visible="-1"  />
        <input group="基本信息" id="fstopproduction" el="116" ek="fbillhead" fn="fstopproduction" pn="fstopproduction" cn="是否停产" visible="-1" lix="22" />

        <input lix="25" group="基本信息" el="107" ek="fbillhead" id="fseltypeid" fn="fseltypeid" pn="fseltypeid" visible="-1" cn="型号" aqkf="true" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fseltypeid" refvt="0" />
        <input lix="15" group="基本信息" el="132" ek="fbillhead" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" visible="0" cn="辅助属性" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" pricefk="" />
        <input group="基本信息" el="106" ek="fbillhead" id="fattrinfo_e" fn="fattrinfo_e" pn="fattrinfo_e" refid="bd_auxpropvalue_ext" visible="-1" cn="辅助属性"
               lock="-1" copy="0" lix="30" notrace="true" ts="" />
        <input lix="35" group="基本信息" el="100" ek="fbillhead" id="fcustomdesc" fn="fcustomdesc" pn="fcustomdesc" visible="-1" cn="定制说明" len="2000" lock="-1" copy="0" notrace="true" ts="" />
        <input group="基本信息" el="103" ek="fbillhead" id="ftotalqty" fn="ftotalqty" pn="ftotalqty" cn="总销售数量" visible="-1" format="0,000.00" width="100" lix="40" tips="获取《月均销售数量表》的【总销售数量】" />
        <input group="基本信息" el="103" ek="fbillhead" id="fmonth" fn="fmonth" pn="fmonth" cn="统计月份数" visible="-1" format="0,000.00" width="100" lix="43" tips="获取《月均销售数量表》的【统计月份数】" />
        <input group="基本信息" el="103" ek="fbillhead" id="favgsaleqty" fn="favgsaleqty" pn="favgsaleqty" cn="月均销售数量" visible="-1" format="0,000.00" width="100" lix="45" tips="获取《月均销售数量表》的【月均销售数量】" />
        <input group="基本信息" el="103" ek="fbillhead" id="ftotalstockqty" fn="ftotalstockqty" pn="ftotalstockqty" cn="总库存" visible="-1" format="0,000.00" width="100" lix="46" tips="按【商品+辅助属性+定制说明】维度汇总统计《库存综合查询》里面所有仓库的【库存量】" />
        <input group="基本信息" el="103" ek="fbillhead" id="fsampleqty" fn="fsampleqty" pn="fsampleqty" cn="样品" visible="-1" format="0,000.00" width="100" lix="47" tips="按【商品+辅助属性+定制说明】维度汇总统计《库存综合查询》里面商品【仓库】对应的【仓库类型=门店仓】的商品【库存量】" />
        <input group="基本信息" el="103" ek="fbillhead" id="fexistorderqty" fn="fexistorderqty" pn="fexistorderqty" cn="订单未清数" visible="-1" format="0,000.00" width="100" lix="48" tips="按【商品+辅助属性+定制说明】维度汇总统计销售合同【商品明细.未出库基本数量】。注意：1、以下5类销售合同不在匹配的合同范围：①门店上样②门店下样③v6定制柜合同④V6全屋定制合同⑤上样销售合同2、剔除已作废的销售合同，且商品的【行关闭状态】=手工关闭 或者（【单据头.需转单】=是 并且 【商品明细.出现货】=否）的数据。" />
        <input group="基本信息" el="103" ek="fbillhead" id="funsoldqty" fn="funsoldqty" pn="funsoldqty" cn="滞销库存" visible="-1" format="0,000.00" width="100" lix="49" tips="按【商品+辅助属性+定制说明】维度匹配《库龄分析报表》，商品的【仓库】对应的【仓库类型≠门店仓】并且 【在库天数】>365 的【数量】" />
        <input group="基本信息" el="103" ek="fbillhead" id="fstockintransitqty" fn="fstockintransitqty" pn="fstockintransitqty" cn="采购在途" visible="-1" format="0,000.00" width="100" lix="50" tips="采购订单【总部合同状态=已终审】，【采购在途】=【采购数量】-【采购入库数量】+【采购退换数量】" />
        <input group="基本信息" el="103" ek="fbillhead" id="fstockupqty" fn="fstockupqty" pn="fstockupqty" cn="备货数" visible="-1" format="0,000.00" width="100" lix="53" tips="【备货数】=【总库存】-【样品】-【订单未清数】-【滞销库存】+【采购在途】+【总仓调拨在途】" />
        <input group="基本信息" el="102" ek="fbillhead" id="fstocktosalerate" fn="fstocktosalerate" pn="fstocktosalerate" cn="库销比" visible="-1" format="0,000.00" width="100" lix="55" tips="【库销比】=【备货数】/【月均销售数量】特殊逻辑备注：1、若【月均销售数量】=0，则【库销比】=【备货数】。（相当于【月均销售数量】按1来作为分母计算。）若【月均销售数量】≠0，当【备货数】<=0，最终计算结果为0。" />
        <input group="基本信息" el="103" ek="fbillhead" id="findbqty" fn="findbqty" pn="findbqty" cn="总仓调拨在途" visible="-1" format="0,000.00" width="100" lix="56" tips="汇总统计【单据头.已分步式调出】=是，【数据状态】≠已审核 且 【商品明细.调入仓库对应的仓库类型】≠【门店仓】的《库存调拨单》的【商品明细.调拨数量】。" />
        <input group="基本信息" el="103" ek="fbillhead" id="fsampleindbqty" fn="fsampleindbqty" pn="fsampleindbqty" cn="样品调拨在途" visible="-1" format="0,000.00" width="100" lix="58" tips="汇总统计【单据头.已分步式调出】=是，【数据状态】≠已审核 且 【商品明细.调入仓库对应的仓库类型】=【门店仓】的《库存调拨单》的【商品明细.调拨数量】。" />
        <input group="基本信息" el="152" ek="fbillhead" visible="-1" id="fstockwaterline" fn="fstockwaterline" pn="fstockwaterline" cn="库存水位线状态"
               vals="'0':'','1':'红灯','2':'绿灯','3':'黄灯'" lix="60" align="center" tips="1、当【是否停产商品】=是，则更新状态为“红灯”。2、当【是否停产商品】=否：①.当【库销比】>2：则更新状态为“红灯”。②.当【库销比】<1.5：则更新状态为“绿灯”。③.当1.5≤【库销比】≤2：则更新状态为“黄灯”。" />
        <input lix="70" group="基本信息" el="113" ek="fbillhead" id="fupdatetime" fn="fupdatetime" pn="fupdatetime" visible="-1" cn="更新时间" lock="-1" notrace="true" ts="" />
        <input lix="75" group="基本信息" el="120" ek="fbillhead" id="fmodifierid" fn="fmodifierid" pn="fmodifierid" cn="更新人" refId="Sec_User" dfld="FName" visible="-1" copy="0" xlsin="0" />
        <input group="基本信息" el="103" ek="fbillhead" id="fpurwaitqty" fn="fpurwaitqty" pn="fpurwaitqty" cn="待采数" visible="-1" format="0,000.00" width="100" lix="80" tips="1、若【总库存】-【样品】+【采购在途】>=【订单未清数】：则【待采数】=0。2、若【总库存】-【样品】+【采购在途】<【订单未清数】：则【待采数】=【订单未清数】-（【总库存】-【样品】+【采购在途】）。" />
        <input group="基本信息" el="103" ek="fbillhead" id="fstockwaitqty" fn="fstockwaitqty" pn="fstockwaitqty" cn="库存待发" visible="-1" format="0,000.00" width="100" lix="85" tips="1、当【订单未清数】<(【总库存】-【样品】）：库存待发数=【订单未清数】2、当【订单未清数】>=(【总库存】-【样品】）：库存待发数=【总库存】-【样品】" />

        <input group="基本信息" el="148" ek="fbillhead" id="fmainorgid" fn="fmainorgid" pn="fmainorgid" cn="企业主体" xlsin="0" visible="0" copy="0" />
        <input group="基本信息" el="100" ek="fbillhead" id="ftranid" fn="ftranid" ts="" cn="交易流水号" visible="0" xlsin="0" copy="0" />
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
        <ul el="10" ek="fbillhead" id="print" op="print" opn="打印" data="" permid="fw_print"></ul>
        <ul el="10" ek="fbillhead" id="listdatatopdf" op="listdatatopdf" opn="导出PDF" data="" permid="fw_export"></ul>
        <ul el="10" ek="fbillhead" id="listdatatoexcel" op="listdatatoexcel" opn="导出Excel" data="" permid="fw_export"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_view" cn="查看"></ul>
        <ul el="12" id="fw_print" cn="打印"></ul>
        <ul el="12" id="fw_export" cn="导出"></ul>
        <ul el="12" id="fw_updateStockData" cn="手动更新"></ul>
    </div>

    <!--<div id="ListFuzzyFlds" cn="默认支持快捷过滤的字段列表">
        <ul el="14" id="fw_fuzzyfld" fldkeys="fmaterialid.fname,fmtrlnumber" cn="默认支持快捷过滤的字段列表，多个用逗号或分号隔开"></ul>
    </div>-->
</body>
</html>