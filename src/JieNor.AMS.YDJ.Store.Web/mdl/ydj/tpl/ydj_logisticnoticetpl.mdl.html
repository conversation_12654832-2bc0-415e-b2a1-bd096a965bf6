<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    物流通知单基类模板
-->
<html lang="en">
<head>
</head>
<body id="ydj_logisticnoticetpl" el="1" basemodel="base_stkbilltmpl" cn="物流通知单据模板">
    <div id="fbillhead" el="51" pk="fid" tn="" pn="fbillhead" cn="单据头">

        <!--地址信息-->
        <input group="地址信息" el="100" ek="fbillhead" visible="-1" id="flinkstaffid" fn="flinkstaffid" pn="flinkstaffid" cn="联系人" lix="50" />
        <input group="地址信息" el="100" ek="fbillhead" visible="-1" id="flinkmobile" fn="flinkmobile" pn="flinkmobile" cn="联系人电话" lix="51" />
        <input group="地址信息" el="100" ek="fbillhead" visible="-1" id="flinkaddress" fn="flinkaddress" pn="flinkaddress" cn="联系人地址" lix="52" />

        <!--物流信息-->

        <input group="物流信息" el="117" ek="FBillHead" id="fcarriertype" fn="fcarriertype" pn="fcarriertype" visible="1150" cn="承运类型"
               lock="0" copy="1" lix="60" notrace="true" ts="" refid="bd_enum" cg="承运类型" dfld="fenumitem" />
        <input group="物流信息" el="106" ek="fbillhead" visible="1150" id="fcarrierid" fn="fcarrierid" pn="fcarrierid" cn="承运公司" lix="61" refid="ydj_supplier" filter="ftype='suppliertype_03'" />
        <input group="物流信息" el="100" ek="fbillhead" visible="1150" id="fshippingbillno" fn="fshippingbillno" pn="fshippingbillno" cn="物流单号" lix="62" />
        <input group="物流信息" el="117" ek="FBillHead" id="fdeliverstatus" fn="fdeliverstatus" pn="fdeliverstatus" visible="1150" cn="物流状态"
               lock="0" copy="1" lix="71" notrace="true" ts="" refid="bd_enum" cg="物流状态" dfld="fenumitem" />
        <input group="物流信息" el="100" ek="FBillHead" id="fcardno" fn="fcardno" pn="fcardno" visible="1150" cn="车牌号"
               lock="0" copy="1" lix="70" notrace="true" ts="" />
        <input group="物流信息" el="117" ek="FBillHead" id="fcardtype" fn="fcardtype" pn="fcardtype" visible="1150" cn="承运车型"
               lock="0" copy="1" lix="71" notrace="true" ts="" refid="bd_enum" cg="承运车型" dfld="fenumitem" />
        <input group="物流信息" el="100" ek="FBillHead" id="fdrivername" fn="fdrivername" pn="fdrivername" visible="1150" cn="司机姓名"
               lock="0" copy="1" lix="72" notrace="true" ts="" />
        <!--<input group="物流信息" el="100" ek="FBillHead" id="fchargetype" fn="fchargetype" pn="fchargetype" visible="-1" cn="计费方案"
           lock="0" copy="1" lix="75" notrace="true" ts="" />-->
        <input group="物流信息" el="102" ek="FBillHead" id="fchargeamount" fn="fchargeamount" pn="fchargeamount" visible="1150" cn="运费"
               lock="0" copy="1" lix="76" notrace="true" ts="" roundType="0" format="0,000.00" />

        <input group="物流信息" el="101" ek="fbillhead" id="ftotalpackageqty" fn="ftotalpackageqty" pn="ftotalpackageqty" visible="1150" cn="总件数"
               lock="-1" copy="1" lix="80" notrace="true" ts="" roundType="0" format="0,000.00" />
        <input group="物流信息" el="102" ek="fbillhead" id="ftotalcubeqty" fn="ftotalcubeqty" pn="ftotalcubeqty" visible="1150" cn="总立方数()"
               lock="-1" copy="1" lix="81" notrace="true" ts="" roundType="0" format="0,000.000" />
        <input group="物流信息" el="102" ek="FBillHead" id="ftotalgrossload" fn="ftotalgrossload" pn="ftotalgrossload" visible="1150" cn="总重量(kg)"
               lock="-1" copy="1" lix="82" notrace="true" ts="" roundType="0" format="0,000.00" />

        <!--排程信息-->
        <input group="排程信息" el="100" ek="fbillhead" id="fschedulebillno" fn="fschedulebillno" pn="fschedulebillno" visible="1150" cn="排程编号"
               lock="-1" copy="0" lix="10" notrace="true" ts="" />
         
    </div>

    <!--商品明细-->
    <table id="fentity" el="52" pk="fentryid" tn="" pn="fentity" cn="商品明细" kfks="fmaterialid" must="1">
        <tr>
            <!--修改基类字段属性-->
            <!--以下字段暂时锁定-->
            <th el="100" ek="fentity" id="flotno" cn="批号" lock="-1" must="0"></th>
            <th el="100" ek="fentity" id="fmtono" cn="物流跟踪号" lock="-1" must="0"></th>
            <th el="149" ek="fentity" id="fownertype" cn="货主类型" lock="0" must="0">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th el="150" ek="fentity" id="fownerid" cn="货主" lock="0" must="0"></th>

            <!--以下字段不必录-->
            <th el="106" ek="fentity" id="fstorehouseid" cn="仓库" must="0"></th>
            <th el="106" ek="fentity" id="fstockstatus" cn="库存状态" must="0"></th>

            <!--暂时隐藏以下字段-->
            <th el="109" ek="fentity" id="fstockunitid" cn="库存单位" visible="0" must="0"></th>
            <th el="103" ek="fentity" id="fstockqty" cn="库存单位数量" visible="0" must="0"></th>

            <th el="102" ek="fentity" id="fvolumeqty" fn="fvolumeqty" pn="fvolumeqty" visible="1150" cn="体积(m³)"
                lock="-1" copy="1" lix="116" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.000"></th>
            <th el="102" ek="fentity" id="fvolume" fn="fvolume" pn="fvolume" visible="1150" cn="总体积(m³)"
                lock="0" copy="1" lix="117" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.000"></th>

            <th el="102" ek="fentity" id="fgrossqty" fn="fgrossqty" pn="fgrossqty" visible="1150" cn="毛重(kg)"
                lock="-1" copy="1" lix="118" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="102" ek="fentity" id="fgross" fn="fgross" pn="fgross" visible="1150" cn="总重量(kg)"
                lock="0" copy="1" lix="119" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="101" ek="fentity" id="fpackqty" fn="fpackqty" pn="fpackqty" visible="1150" cn="件数"
                lock="0" copy="1" lix="120" notrace="true" ts="" format="0,000"></th>
            <th el="100" ek="fentity" id="fpacksize" fn="fpacksize" pn="fpacksize" visible="1150" cn="包装尺寸"
                lock="0" copy="1" lix="121" notrace="true" ts=""></th>

        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li id="stocklocationcheck" el="11" remove ></li>
        </ul>

        <ul el="10" ek="fbillhead" id="querybarcode" op="querybarcode" opn="扫码记录" data="{'parameter':{'linkFormIdFieldKey':'','linkBillIdFieldKey':''}}" permid=""></ul>
    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">
    </div>
</body>
</html>