<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="bi_storemonthsaleanalysis" el="1" basemodel="" cn="门店月度销售配套率分析" desc="门店月度销售配套率分析" rac="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bi_storemonthsaleanalysis" pn="fbillhead" cn="门店月度销售配套率分析"></div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="fw_view" cn="查看"></ul>
    </div>
</body>
</html>