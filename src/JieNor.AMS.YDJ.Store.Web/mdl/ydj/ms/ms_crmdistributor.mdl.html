<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ms_crmdistributor" el="3" cn="招商经销商" isolate="0" rac="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ms_crmdistributor" pn="fbillhead" cn="招商经销商">


        <input type="text" id="fnumber" el="108" ek="fbillhead" fn="fnumber" pn="fnumber" apipn="number" cn="招商经销商编号" desc="招商经销商编号" width="120" visible="-1" copy="0" lix="1" />
        <input type="text" id="fname" el="100" ek="fbillhead" fn="fname" pn="fname" apipn="name" cn="招商经销商名称" desc="招商经销商名称" width="120" visible="-1" lix="2" />

        <input el="152" ek="fbillhead" visible="-1" id="fstate" fn="fstate" pn="fstate" cn="招商经销商状态" vals="'0':'启用','1':'禁用'" defval="'0'" len="1" lock="-1" width="115" align="center" lix="6" />

        <input type="text" id="fcreatorid" el="118" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fcreatorid" pn="fcreatorid" cn="创建人" copy="0" visible="-1" lix="250" xlsin="0" />
        <input type="datetime" id="fcreatedate" el="119" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" cn="创建日期" width="130" visible="-1" copy="0" lix="251" xlsin="0" />

        <input type="text" id="fmodifierid" el="120" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fmodifierid" pn="fmodifierid" cn="修改人" visible="1124" copy="0" lix="252" xlsin="0" />
        <input type="datetime" id="fmodifydate" el="121" ek="fbillhead" fn="fmodifydate" pn="fmodifydate" cn="修改日期" visible="1124" copy="0" lix="253" xlsin="0" />


        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="-1" xlsin="0" copy="0" lix="260" align="center"></select>
        <input type="text" id="fapproveid" el="124" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fapproveid" pn="fapproveid" cn="审核人" visible="1124" xlsin="0" copy="0" lix="261" />
        <input type="datetime" id="fapprovedate" el="113" ek="fbillhead" fn="fapprovedate" pn="fapprovedate" cn="审核日期" visible="1124" xlsin="0" copy="0" lix="262" />

        <input type="checkbox" id="fforbidstatus" el="116" ek="fbillhead" fn="fforbidstatus" pn="fforbidstatus" cn="禁用状态" visible="1062" xlsin="0" copy="0" lix="263" />
        <input type="text" id="fforbidid" el="124" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fforbidid" pn="fforbidid" cn="禁用人" visible="1062" xlsin="0" copy="0" lix="264" />
        <input type="datetime" id="fforbiddate" el="113" ek="fbillhead" fn="fforbiddate" pn="fforbiddate" cn="禁用日期" visible="1062" xlsin="0" copy="0" lix="265" />

        <input group="基本信息" type="text" id="foutid" el="100" ek="fbillhead" fn="foutid" pn="foutid" cn="招商经销商ID" width="120" visible="-1" lix="3" />
        <input group="基本信息" el="100" ek="fbillhead" visible="0" id="foutcityid" fn="foutcityid" pn="foutcityid" cn="外部城市ID" />
        <input lix="25" group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcityid" fn="fcityid" pn="fcityid" cn="城市" refid="ydj_city" dfld="flevel,fcountry,fprovince,fstaffid" lix="11" />
        <input lix="10" group="基本信息" el="131" ek="fbillhead" visible="-1" id="fagentid" fn="fagentid" pn="fagentid" refid="bas_agent" cn="售达方" lix="17" />

        <input el="100" ek="fbillhead" visible="0" id="factualownerid" fn="factualownerid" pn="factualownerid" cn="外部实控人ID" desc="记录外部实控人ID，用于匹配实控人" />
        <input el="100" ek="fbillhead" visible="-1" id="factualownernumber" fn="factualownernumber" pn="factualownernumber" cn="实控人ID" desc="实际为实控人编码" width="105" copy="0" />
        <input el="100" ek="fbillhead" visible="-1" id="factualownername" fn="factualownername" pn="factualownername" cn="实控人" width="105" />

        <input type="text" id="fjson" el="127" ek="fbillhead" fn="fjson" pn="fjson" cn="json" width="120" visible="0" lix="12" />

        <!--CDP密码】后台字段-->
        <input group="基本信息" el="100" ek="fbillhead" type="text" id="fcdppassword" fn="fcdppassword" pn="fcdppassword" cn="CDP密码" visiable="0" />
        <!--CDP招商经销商最后登录时间】后台字段-->
        <input group="基本信息" el="100" ek="fbillhead" type="text" id="flastlogintime" fn="flastlogintime" pn="flastlogintime" cn="最后登录时间" visiable="0" defval=""/>
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList"> 
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
        <ul el="10" ek="fbillhead" id="modify" op="modify" opn="修改" data="" permid="fw_modify"></ul>
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
        </ul>

        <ul el="10" ek="fbillhead" id="syncfrommusi" op="syncfrommusi" opn="从慕思拉取数据" permid="fw_syncfrommusi"></ul>
    </div>

    <div id="permList"> 
        <ul el="12" id="fw_view" cn="查看" order="1"></ul>
        <ul el="12" id="fw_viewrecord" cn="查看记录" order="2"></ul>
        <ul el="12" id="fw_modify" cn="修改" order="4"></ul>
        <ul el="12" id="fw_syncfrommusi" cn="从慕思拉取数据"></ul>
    </div>
</body>
</html>