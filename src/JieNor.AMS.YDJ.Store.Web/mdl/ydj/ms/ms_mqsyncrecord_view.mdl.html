<!--
业务拓展视图。针对合同
    -->
<html lang="en">
<head>
</head>
<body id="ms_mqsyncrecord_view" el="3" basemodel="ms_mqsyncrecord" cn="消息队列同步日志" isolate="0" rac="true" nssd="true">
    <div id="fbillhead" el="51" pk="fid" tn="v_ms_mqsyncrecord" pn="fbillhead" cn="消息队列同步日志">

        <input el="100" ek="fbillhead" visible="-1" id="fphone_od" fn="fphone_od" pn="fphone_od" cn="手机号" lix="10" />
        <select el="122" ek="fbillhead" id="fstatus_od" refId="bd_enum" dfld="fenumitem" cg="数据状态" fn="fstatus_od" pn="fstatus_od" cn="数据状态" visible="-1" lix="11"></select>
        <select el="122" ek="fbillhead" visible="-1" id="freceiptstatus_od" fn="freceiptstatus_od" cn="结算状态" refid="bd_enum" cg="收款状态" dfld="fenumitem" defval="'receiptstatus_type_01'" lix="12"></select>
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="mqsyncrecord" op="mqsyncrecord" opn="消息队列" data="" permid="fw_mqsyncrecord" useslave="true"></ul>
        <ul el="10" ek="fbillhead" id="batchaddmqsync" op="batchaddmqsync" opn="批量加入消息队列" data="" permid="fw_batchaddmqsync" useslave="true"></ul>
        <ul el="10" ek="fbillhead" id="tosync" op="tosync" opn="立即同步" data="" permid="fw_tosync" useslave="true"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_tosync" cn="立即同步"></ul>
        <ul el="12" id="fw_mqsyncrecord" cn="消息队列"></ul>
        <ul el="12" id="fw_batchaddmqsync" cn="批量加入消息队列"></ul>
    </div>
</body>
</html>