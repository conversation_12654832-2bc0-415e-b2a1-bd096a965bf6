{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [

  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [

    //客户基础资料值变化时，携带客户属性字段到页面指定的字段上面
    { "expression": "fprovince=fcustomerid__fprovince|fcustomerid=='' or 1==1" },
    { "expression": "fcity=fcustomerid__fcity|fcustomerid=='' or 1==1" },
    { "expression": "fregion=fcustomerid__fregion|fcustomerid=='' or 1==1" },
    { "expression": "faddress=fcustomerid__faddress|fcustomerid=='' or 1==1" },
    { "expression": "fphone=fcustomerid__fphone|fcustomerid=='' or 1==1" }
  ]
}