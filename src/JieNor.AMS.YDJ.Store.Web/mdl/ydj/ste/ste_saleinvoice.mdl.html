<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ste_saleinvoice" basemodel="bill_basetmpl" el="1" cn="销售发票" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ste_saleinvoice" pn="fbillhead" cn="销售发票">

        <!-- 基本信息 -->
        <select group="基本信息" el="123" ek="fbillhead" visible="-1" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="发票类型" refid="bd_billtype" width="90" apipn="billType" lix="5"></select>
        <input group="基本信息" el="112" ek="fbillhead" visible="-1" id="fdate" fn="fdate" pn="fdate" cn="开票日期" defval="@currentshortdate" width="105" format="yyyy-MM-dd" copy="0" lix="10"/>
        <input group="基本信息" el="105" ek="fbillhead" visible="-1" id="famount" fn="famount" pn="famount" cn="不含税金额" width="100" lock="-1" visible="-1" format="0,000.00" lix="15"/>
        <input group="基本信息" el="105" ek="fbillhead" visible="-1" id="fsumtaxamount" fn="fsumtaxamount" pn="fsumtaxamount" cn="价税合计" width="100" lock="-1" visible="-1" format="0,000.00" lix="20"/>
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="finvoicenumber" fn="finvoicenumber" pn="finvoicenumber" cn="开票号" lix="25" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid" fn="fstaffid" pn="fstaffid" cn="开票人" refid="ydj_staff" defVal="@currentStaffId" lix="30" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="开票部门" refid="ydj_dept" defVal="@currentDeptId" lix="35" />
        <input group="基本信息" el="105" ek="fbillhead" visible="-1" id="ftax" fn="ftax" pn="ftax" cn="税额" width="100" lock="-1" visible="-1" format="0,000.00" lix="40"/>
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" id="fisinvoiced" fn="fisinvoiced" pn="fisinvoiced" cn="已开票" copy="0" defval="false" lix="45" />
        <input group="基本信息" el="135" ek="fbillhead" visible="-1" id="fimage" fn="fimage" pn="fimage" cn="附件" lix="50"/>
        <input group="基本信息" el="102" ek="fbillhead" visible="-1" id="ftaxrate" fn="ftaxrate" pn="ftaxrate" cn="税率" width="70" lix="55" format="%"/>
        
        <!-- 购货单位信息 -->
        <input group="购货单位信息" el="106" ek="fbillhead" visible="-1" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" width="90" lix="60" />
        <input group="购货单位信息" el="100" ek="fbillhead" visible="32" id="faddress" fn="faddress" pn="faddress" cn="详细地址" lix="65" />
        <input group="购货单位信息" el="100" ek="fbillhead" visible="32" id="fphone" fn="fphone" pn="fphone" cn="联系电话" width="80" lix="70" />
        <input group="购货单位信息" el="100" ek="fbillhead" visible="32" id="finvoicetitle" fn="finvoicetitle" pn="finvoicetitle" cn="发票抬头" lix="75" />
        <input group="购货单位信息" el="100" ek="fbillhead" visible="32" id="ftaxpayernumber" fn="ftaxpayernumber" pn="ftaxpayernumber" cn="纳税人识别号" lix="80" />
        <input group="购货单位信息" el="100" ek="fbillhead" visible="32" id="fopenbank" fn="fopenbank" pn="fopenbank" cn="开户行" lix="85" />
        <input group="购货单位信息" el="100" ek="fbillhead" visible="32" id="fbanknumber" fn="fbanknumber" pn="fbanknumber" cn="银行账号" lix="90" />

        <!-- 源单信息 -->
        <table id="fentity" el="52" pk="fentryid" tn="t_ste_saleinvoiceentry" pn="fentity" cn="源单信息明细">
            <tr>
                <th el="140" ek="fentity" id="fsourcetype" fn="fsourcetype" pn="fsourcetype" cn="源单类型" visible="1150" copy="0" lock="-1" width="150" lix="95" ></th>
                <th el="141" ek="fentity" id="fsourcenumber" fn="fsourcenumber" pn="fsourcenumber" cn="源单编号" visible="1150" copy="0" lock="-1" width="150" lix="100" ></th>
                <th el="105" ek="fentity" id="ftaxamount" fn="ftaxamount" pn="ftaxamount" cn="含税金额" visible="1150" width="120" copy="0" lock="0" format="0,000.00" lix="105" ></th>
            </tr>
        </table>

        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="17" sid="1002" cn="反写增加开票金额" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'finvoiceamount',
                'sourceLinkFieldKey':'fbillno',
                'linkIdFieldKey':'fsourcenumber',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'finvoiceamount',
                'expression':'ftaxamount',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }">
            </li>
        </ul>

        <ul el="10" ek="fbillhead" id="rejectflow" op="rejectflow" opn="反审核">
            <li el="17" sid="1002" cn="反写增加开票金额" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'finvoiceamount',
                'sourceLinkFieldKey':'fbillno',
                'linkIdFieldKey':'fsourcenumber',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'finvoiceamount',
                'expression':'ftaxamount',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }">
            </li>
        </ul>
        

        </div>
        </body>
</html>