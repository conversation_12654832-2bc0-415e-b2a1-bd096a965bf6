{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "fstatus_",
      "expression": "field:$*;menu:*$tbNew,tbSave,tbVerifyIncomeDisburse|fstatus==''"
    },
    {
      "id": "unlock_tbVerifyIncomeDisburse",
      "expression": "menu:$tbVerifyIncomeDisburse|fstatus!='D' and fstatus!='E'"
    },
    {
      "id": "lock_tbVerifyIncomeDisburse",
      "expression": "menu:tbVerifyIncomeDisburse|fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_fmarketplace",
      "expression": "field:$fmarketplace|fstatus==''"
    },
    {
      "id": "lock_fmarketplace",
      "expression": "field:fmarketplace|fstatus!=''"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [
  ],

  //定义表单计算规则
  "calcRules": [
  ]
}

