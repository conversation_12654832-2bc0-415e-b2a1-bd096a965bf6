<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_team" basemodel="bd_basetmpl" el="3" cn="团队" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ser_ydj_team" pn="fbillhead" cn="团队">

        <!--基本信息-->
        <input group="基本信息" el="100" type="text" id="fnumber" cn="团队编号" />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="faddress" fn="faddress" pn="faddress" cn="详细地址" />
        <input group="基本信息" el="100" type="text" id="fname" cn="团队名称" />
        <input group="基本信息" el="100" type="text" id="fdescription" cn="情况说明" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="关联部门" refid="ydj_dept" reflvt="1" defVal="@currentDeptId" apipn="saleDept" canchange="true" lix="25" />

        <input group="基本信息" type="text" id="fmembercount" el="101" ek="fbillhead" fn="fmembercount" ts="" cn="团队人数" visible="-1" defval="0" lock="-1" />

        <input group="基本信息" el="152" ek="fbillhead" id="fteamtype" fn="fteamtype" pn="fteamtype" visible="-1" cn="团队类型"
               lock="0" copy="1" lix="0" notrace="true" ts="" vals="'1':'外部团队','2':'内部团队'" defval="'2'" />

        <!--人员信息-->
        <table id="fstaffentry" el="52" pk="fentryid" tn="t_ydj_staffteam" pn="fstaffentry" cn="负责人信息" kfks="fstaffid_e">
            <tr>
                <th el="116" ek="fstaffentry" id="fiscaptain_e" fn="fiscaptain" pn="fiscaptain" cn="队长" visible="-1" width="80"></th>
                <th el="106" ek="fstaffentry" id="fstaffid_e" fn="fstaffid" pn="fstaffid" cn="人员" filter="fapprovestatus='auth2'" refid="ydj_master" visible="-1" width="130"></th>
                <th el="100" ek="fstaffentry" id="fphone_e" fn="fphone" pn="fphone" cn="手机号" visible="-1" width="150" lock="-1"></th>
            </tr>
        </table>
    </div>

</body>
</html>