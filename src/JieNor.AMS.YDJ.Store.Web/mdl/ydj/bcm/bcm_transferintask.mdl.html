<html lang="en">
<head>
</head>
<body id="bcm_transferintask" el="1" basemodel="bcm_transfertask" cn="调入扫描任务" isolate="1" nfsa="true" IsAsynLstDesc="true">
    <!--已扫描的条码-->
    <!--<table id="fscannedentity" el="52" pk="fscannedentityid" pn="fscannedentity" cn="已扫描的条码">
        <tr>

            <th el="106" ek="fscannedentity" id="fbarcode" fn="fbarcode" pn="fbarcode" visible="-1" cn="条码"
                lock="-1" copy="0" lix="1" notrace="true" ts="" refid="bcm_barcodemaster" filter="" reflvt="0" dfld=""></th>

            <th el="100" ek="fscannedentity" id="fbarcodetext" fn="fbarcodetext" pn="fbarcodetext" visible="1150" cn="条码(文本)"
                lock="-1" copy="0" lix="11" notrace="true" ts=""></th>

            <th el="124" ek="fscannedentity" id="foperatorid" fn="foperatorid" pn="foperatorid" visible="-1" cn="操作人"
                lock="-1" copy="0" lix="5" notrace="true" ts="" refid="sec_user" filter="" reflvt="0" dfld=""></th>

            <th ek="fscannedentity" el="113" id="fopdatetime" fn="fopdatetime" pn="fopdatetime" visible="-1" cn="操作时间"
                lock="-1" copy="0" lix="6" notrace="true" ts=""></th>

            <th el="152" ek="fscannedentity" id="fscansceneid" fn="fscansceneid" pn="fscansceneid" visible="-1" cn="业务操作"
                lock="-1" copy="0" lix="4" notrace="true" ts="" vals="'0':'包装','1':'收货','2':'发货','3':'调拨','4':'盘点','5':'装箱','6':'拆箱'"></th>

            <th lix="15" el="100" ek="fscannedentity" id="fmaterialid_fname" pn="fmaterialid_fname" visible="1150" cn="商品" width="160" lock="-1"></th>
            <th el="106" ek="fscannedentity" id="fstorehouseid_e" fn="fstorehouseid_e" cn="仓库" lix="7" refid="ydj_storehouse"
                sformid="" visible="-1" lock="-1"></th>-->
    <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fsubmitentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
    <!--<th el="153" ek="fscannedentity" id="fstorelocationid_e" fn="fstorelocationid_e" cn="仓位" lix="8" ctlfk="fstorehouseid_e"
                luek="fentity" lunmfk="flocname" lunbfk="flocnumber" visible="-1" lock="-1"></th>
            <th ek="fscannedentity" el="100" id="fdescription_e" fn="fdescription_e" pn="fdescription_e" apipn="description" cn="备注" visible="-1" len="1000" lix="500" width="280" lock="-1"></th>
            <th ek="fscannedentity" el="100" id="fpda" fn="fpda" pn="fpda" cn="PDA" visible="1150" lix="9" width="100" lock="-1" copy="0"></th>

        </tr>
    </table>-->
</body>
</html>