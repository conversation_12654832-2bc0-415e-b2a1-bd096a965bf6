<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    收货扫描任务
    
    本单据默认由后台计划任务根据系统的其它入库通知单，销售退货通知单，采购收货通知单进行关联生成。
    本单据将作为前端PDA设备作业的任务单据，但围绕PDA进行收货包装的操作过程数据需要记录至另一张包装清单上，以便与PC端包装作业的数据承载单据一致。
    收货确认时，会将条码主档根据包装清单进行生成，并且生成待扫描的收货条码记录（并根据参数决定，是否自动匹配核验），直接入库。这扫描记录将会可以从收货通知单以及入库单上调取查阅
    但是包装后所有商品的包装数量之和应该按关联源单分录行进行汇总后，反写回扫描任务实收数量字段上，并且将扫描任务更新为已完成，并自动审核，以实现反写实际收货数量至来源收货通知单
    后台计划任务进一步轮询生成新的待收货任务（如果之前的收货通知单未完全收货），
    
    另一个计划任务将不断的对已核验的扫描记录进行处理下游关联入库单的生成逻辑，入库单的分录明细由包装清单的分录明细组成
-->


<html lang="en">
<head>
</head>
<body id="bcm_receptionscantask" el="1" basemodel="bill_basetmpl" cn="收货扫描任务" isolate="1" nfsa="true" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bcm_receptionscantask" pn="fbillhead" cn="单据头">

        <input type="text" id="fbillno" el="108" ek="fbillhead" fn="fbillno" pn="fbillno" apipn="billNo" cn="任务单号" desc="任务单号" visible="-1" copy="0" lix="1" lock="-1" width="180" />

        <select group="基本信息" el="123" ek="fbillhead" visible="-1" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="单据类型" refid="bd_billtype" lix="27" width="90" apipn="billType" must="0" notrace="false"
                tips="通过设置不同的单据类型实现字段的必录和锁定效果，以满足不同的业务场景需求"></select>

        <select group="基本信息" el="152" ek="fbillhead" id="ftask_type" fn="ftask_type" pn="ftask_type" visible="-1" cn="任务类型"
                lock="-1" copy="0" lix="10" notrace="true" ts="" vals="'stk_postockin':'采购入库','stk_sostockreturn':'销售退货','stk_otherstockin':'其他入库'"></select>

        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="ftaskstatus" fn="ftaskstatus" pn="ftaskstatus" cn="任务状态" cg="扫描任务状态" refid="bd_enum" dfld="fenumitem" defval="'ftaskstatus_01'" lix="20" width="90" apipn="ftaskstatus" lock="-1"></select>

        <input group="基本信息" el="112" ek="fbillhead" visible="-1" id="ftaskdate" fn="ftaskdate" pn="ftaskdate" cn="业务日期" lix="30" width="105" format="yyyy-MM-dd" copy="0" apipn="ftaskdate" canchange="true" lock="-1" />

        <input type="text" id="freceptionno" el="100" ek="fbillhead" fn="freceptionno" pn="freceptionno" apipn="freceptionno" cn="收货单号" desc="收货单号" visible="-1" copy="0" lix="40" width="180" />

        <input type="text" id="flogisticsno" el="100" ek="fbillhead" fn="flogisticsno" pn="flogisticsno" apipn="flogisticsno" cn="物流单号" desc="物流单号" visible="-1" copy="0" lix="50" width="180" />

        <input type="text" id="fsourcetype" el="140" ek="fbillhead" fn="fsourcetype" pn="fsourcetype" apipn="sourceType" cn="来源单据" visible="0" xlsin="0" copy="0" lix="60" lock="-1">

        <input type="text" id="fsourcenumber" el="141" ek="fbillhead" fn="fsourcenumber" pn="fsourcenumber" apipn="sourceNumber" cn="来源单据编号" ctlfk="fsourcetype" visible="0" xlsin="0" lix="70" copy="0" lock="-1" width="180">
        <input group="基本信息" el="300" ek="fbillhead" visible="150" id="fsourceinterid" fn="fsourceinterid" pn="fsourceinterid" cn="源单Id" lix="60" />
        <input group="基本信息" el="100" ek="fbillHead" id="fdescription" fn="fdescription" pn="fdescription" visible="-1" cn="备注"
               copy="1" lix="80" notrace="true" ts="" canchange="true" sbm="true" />

        <input el="116" ek="fbillhead" visible="1150" id="fexistednetherbill" fn="fexistednetherbill" pn="fexistednetherbill" cn="已存在下游库存单据" lix="90" lock="-1" width="100" />

        <input type="text" id="fcreatorid" el="118" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fcreatorid" pn="fcreatorid" cn="创建人" visible="1150" copy="0" xlsin="0" lix="250" />
        <input type="datetime" id="fcreatedate" el="119" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" cn="创建日期" width="130" visible="1150" copy="0" xlsin="0" lix="251" />

        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="1150" xlsin="0" copy="0" lix="260" align="center"></select>

        <input group="基本信息" el="112" ek="fbillhead" id="fsenddate" fn="fsenddate" pn="fsenddate" cn="工厂发货日期" visible="-1" lix="100" />

        <input group="基本信息" el="116" ek="fbillhead" visible="1150" id="fisdistribute" fn="fisdistribute" pn="fisdistribute" cn="总部下发生成" defval="false" lock="-1" />

        <input group="基本信息" el="106" ek="fbillhead" id="fsupplierid" fn="fsupplierid" pn="fsupplierid" cn="供应商" visible="0" refid="ydj_supplier" lix="10" dfld="fname,fcontacts,fphone,faddress" />

        <!--<input lix="57" group="基本信息" el="100" ek="fbillhead" visible="1086" id="fhqderno_e" fn="fhqderno" pn="fhqderno" lock="-1" copy="0" cn="总部合同号" />-->

        <input group="基本信息" el="116" ek="fbillhead" id="fisbatchwork" fn="fisbatchwork" pn="fisbatchwork" visible="0" cn="是否PDA批量作业（后台字段）" lock="-1" copy="0" lix="70" ts="" />
    </div>

    <!--扫描任务明细-->
    <table id="ftaskentity" el="52" pk="fentryid" tn="t_bcm_rescantaskentity" pn="ftaskentity" cn="扫描任务明细" kfks="fmaterialid">
        <tr>
            <th lix="100" el="107" ek="ftaskentity" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0"></th>
            <th lix="110" el="106" ek="ftaskentity" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="1150" cn="商品名称" width="160"
                lock="-1" copy="1" ts="" refid="ydj_product" multsel="true" filter="" reflvt="0" dfld="fspecifica" sformid=""></th>

            <th lix="115" el="132" ek="ftaskentity" id="fattrinfo" fn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid" pricefk="" width="160" visible="1150" lock="-1"></th>
            <th el="100" ek="ftaskentity" len="2000" id="fcustomdesc" fn="fcustomdesc" cn="定制说明" lix="120" width="160" visible="1150" lock="-1"></th>
            <th el="152" ek="ftaskentity" id="fpackagingtype" fn="fpackagingtype" pn="fpackagingtype" visible="1150" cn="打包类型"
                lock="-1" copy="1" lix="130" notrace="true" ts="" vals="'1':'标准','2':'1件多包','3':'1包多件'" width="120"></th>
            <th lix="135" el="103" ek="ftaskentity" id="fpackagingqty" fn="fpackagingqty" cn="包数/件数" visible="1150" lock="-1"></th>
            <th lix="140" el="109" ek="ftaskentity" id="fbizunitid" fn="fbizunitid" cn="单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" visible="1150" width="90" lock="-1"></th>
            <th lix="145" el="103" ek="ftaskentity" id="fwaitworkqty" fn="fwaitworkqty" cn="待作业数量" visible="1150" lock="-1"></th>
            <th lix="150" el="103" ek="ftaskentity" id="fworkedqty" fn="fworkedqty" cn="已作业数量" visible="1150" lock="-1"></th>
            <th lix="155" el="103" ek="ftaskentity" id="fwaitscanqty" fn="fwaitscanqty" cn="待扫描包数" visible="1150" lock="-1"></th>
            <th lix="160" el="103" ek="ftaskentity" id="fscannedqty" fn="fscannedqty" cn="已扫描包数" visible="1150" lock="-1"></th>
            <th el="100" ek="ftaskentity" id="flotno" fn="flotno" pn="flotno" cn="批号" lix="170" width="100" visible="1150" lock="-1"></th>
            <th el="100" ek="ftaskentity" id="fmtono" fn="fmtono" pn="fmtono" cn="物流跟踪号" lix="180" width="100" visible="1150" lock="-1"></th>
            <th el="149" ek="ftaskentity" id="fownertype" fn="fownertype" pn="fownertype" lix="190" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="1150" lock="-1">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th el="150" ek="ftaskentity" id="fownerid" fn="fownerid" pn="fownerid" lix="200" ctlfk="fownertype" cn="货主" width="100" visible="1150" lock="-1"></th>
            <th lix="210" el="107" ek="ftaskentity" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" cn="规格型号"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0" visible="1150"></th>
            <th lix="220" el="107" ek="ftaskentity" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" dispfk="fbrandid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="230" el="107" ek="ftaskentity" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" dispfk="fseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="231" el="107" ek="ftaskentity" id="fsubseriesid" fn="fsubseriesid" cn="子系列" visible="1086" dispfk="fsubseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="240" el="100" ek="ftaskentity" id="fsuitegroupid" fn="fsuitegroupid" cn="套件组合号" visible="1150" lock="-1"></th>
            <th lix="250" el="100" ek="ftaskentity" id="fcategorygroupid" fn="fcategorygroupid" cn="配件组合号" visible="1150" lock="-1"></th>
            <th lix="260" el="100" ek="ftaskentity" id="fsafagroupid" fn="fsafagroupid" cn="沙发组合号" visible="1150" lock="-1"></th>
            <th el="103" ek="ftaskentity" id="fqty" fn="fqty" cn="基本单位数量" lix="270" visible="1150" ctlfk="funitid" width="120" format="0,000.00" lock="-1"></th>
            <th el="109" ek="ftaskentity" id="funitid" fn="funitid" cn="基本单位" lix="280" ctlfk="fmaterialid" refid="ydj_unit" sformid="" filter="fisbaseunit='1'" visible="1150" width="80" lock="-1"></th>
            <th el="140" ek="ftaskentity" id="flinkformid" fn="flinkformid" ts="" cn="关联单据" visible="1150" copy="0" lix="290" lock="-1"></th>
            <th el="141" ek="ftaskentity" id="flinkbillno" fn="flinkbillno" ts="" cn="关联单据编号" ctlfk="flinkformid" visible="1150" copy="0" lix="300" lock="-1"></th>
            <th el="100" ek="ftaskentity" id="flinkrownumber" fn="flinkrownumber" ts="" cn="关联单行号" visible="1150" copy="0" lix="310" lock="-1"></th>
            <th el="100" ek="ftaskentity" id="flinkrowinterid" fn="flinkrowinterid" ts="" cn="关联单行内码" visible="1086" copy="0" lix="320" lock="-1"></th>
            <th el="140" ek="ftaskentity" id="fsourceformid" fn="fsourceformid" ts="" cn="来源单据" visible="1086" copy="0" lix="330" lock="-1"></th>
            <th el="141" ek="ftaskentity" id="fsourcebillno" fn="fsourcebillno" ts="" cn="来源单据编号" ctlfk="fsourceformid" visible="1086" copy="0" lix="340" lock="-1"></th>
            <th el="100" ek="ftaskentity" id="fsourcefid" fn="fsourcefid" ts="" cn="来源单内码" visible="1086" copy="0" lix="345" lock="-1"></th>
            <th el="100" ek="ftaskentity" id="fsourceinterid" fn="fsourceinterid" ts="" cn="来源单行号" visible="1086" copy="0" lix="350" lock="-1"></th>
            <th el="100" ek="ftaskentity" id="fsourceentryid" fn="fsourceentryid" ts="" cn="来源单行内码" visible="1086" copy="0" lix="360" lock="-1" width="100"></th>

            <th el="106" ek="ftaskentity" id="fdeliveryfactoryid" fn="fdeliveryfactoryid" pn="fdeliveryfactoryid" cn="交货工厂" refid="ydj_deliveryfactory" visible="1150" lock="-1" copy="0"></th>

            <th el="100" ek="ftaskentity" lock="-1" lix="180" id="fhqderno" fn="fhqderno" pn="fhqderno" cn="总部合同号" visible="1150"></th>

            <th lix="800" el="103" ek="ftaskentity" visible="1150" id="fcurrworkqty" fn="fcurrworkqty" pn="fcurrworkqty" cn="本次作业数量" copy="0" format="0,000.00" />

            <th lix="370" el="106" ek="ftaskentity" id="fstorehouseid" fn="fstorehouseid" cn="仓库" refid="ydj_storehouse" sformid="" visible="1150" width="100"></th>
            <th lix="380" el="153" ek="ftaskentity" id="fstorelocationid" fn="fstorelocationid" cn="仓位" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="1150" width="100"></th>

            <th lix="400" el="113" ek="ftaskentity" id="fmarksubmitdate" fn="fmarksubmitdate" pn="fmarksubmitdate" cn="标记提交时间" visible="0" lock="-1" copy="0"></th>

            <th lix="410" el="104" ek="ftaskentity" id="fprice" fn="fprice" pn="fprice" must="0" cn="成交单价" visible="0" format="0,000.000000" dformat="0,000.00" notrace="false"></th>
            <th lix="420" el="105" ek="ftaskentity" id="famount" fn="famount" pn="famount" must="0" cn="成交金额" visible="0" notrace="false"></th>
            <th lix="430" el="100" ek="ftaskentity" id="fsupplierorderno" fn="fsupplierorderno" pn="fsupplierorderno" cn="供货方订单号" lock="-1" must="-1" visible="0"></th>
            <th id="fseltypeid" el="107" ek="ftaskentity" fn="fseltypeid" ctlfk="fmaterialid" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="460"></th>

            <th lix="470" el="106" ek="ftaskentity" visible="1086" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" copy="0" width="85" lock="-1" />
            <th lix="480" el="108" ek="ftaskentity" id="fsoorderno" fn="fsoorderno" pn="fsoorderno" visible="1086" cn="销售合同编号"
                lock="-1" copy="0" notrace="false" ts=""></th>
            <!--<th lix="490" el="107" ek="ftaskentity" visible="1086" id="fcustomername" fn="fcustomername" pn="fcustomername" cn="客户姓名" ctlfk="fcustomerid" dispfk="fname" />-->
            <th lix="500" el="107" ek="ftaskentity" visible="1086" id="fcustomerphone" fn="fcustomerphone" pn="fcustomerphone" cn="客户手机号" ctlfk="fcustomerid" dispfk="fphone" />

            <th el="100" lix="299" ek="ftaskentity" id="fvolumeunit" fn="fvolumeunit" pn="fvolumeunit" visible="1150" cn="体积单位" lock="0" must="0" uaul="true"></th>
            <th el="102" lix="300" ek="ftaskentity" id="ftotalvolume" fn="ftotalvolume" pn="ftotalvolume" visible="1150" cn="总体积" lock="0" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
            <th el="102" lix="301" ek="ftaskentity" id="fsinglevolume" fn="fsinglevolume" pn="fsinglevolume" visible="1150" cn="单位体积" lock="0" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
        </tr>
    </table>

    <!--任务提交记录-->
    <table id="fsubmitentity" el="52" pk="fentryid" tn="t_bcm_retasksubentity" pn="fsubmitentity" cn="任务提交记录" kfks="fmaterialid_s">
        <tr>

            <th lix="100" el="106" ek="fsubmitentity" id="fsubmitstaffid" fn="fsubmitstaffid" pn="fsubmitstaffid" cn="提交人" refid="ydj_staff" sformid="" width="110" visible="1150" lock="-1"></th>
            <th lix="101" ek="fsubmitentity" el="113" id="fsubmitdate" fn="fsubmitdate" pn="fsubmitdate" cn="提交时间" visible="1150" width="120" lock="-1"></th>
            <th lix="105" el="107" ek="fsubmitentity" id="fmtrlnumber_s" fn="fmtrlnumber" pn="fmtrlnumber_s" visible="1150" cn="商品编码"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid_s" dispfk="fnumber" refvt="0"></th>
            <th lix="110" el="106" ek="fsubmitentity" id="fmaterialid_s" fn="fmaterialid" pn="fmaterialid_s" visible="1150" cn="商品名称" width="160"
                lock="-1" copy="1" notrace="true" ts="" refid="ydj_product" multsel="true" filter="" reflvt="0" dfld="fspecifica" sformid=""></th>
            <th lix="115" el="132" ek="fsubmitentity" id="fattrinfo_s" fn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid_s" pricefk="" width="160" visible="1150" lock="-1"></th>
            <th lix="120" el="100" ek="fsubmitentity" len="2000" id="fcustomdesc_s" fn="fcustomdesc" cn="定制说明" width="160" visible="1150" lock="-1"></th>
            <th lix="125" el="103" ek="fsubmitentity" id="fworkqty_s" fn="fworkqty" cn="作业数量" visible="1150" lock="-1"></th>
            <th lix="130" el="109" ek="fsubmitentity" id="fbizunitid_s" fn="fbizunitid" cn="单位" ctlfk="fmaterialid_s" refid="ydj_unit" sformid="" visible="1150" width="90" lock="-1"></th>
            <th lix="135" el="103" ek="fsubmitentity" id="fpackagekqty_s" fn="fpackagekqty" cn="作业包数" visible="1150" lock="-1"></th>
            <th lix="140" el="106" ek="fsubmitentity" id="fstorehouseid_s" fn="fstorehouseid" cn="仓库" refid="ydj_storehouse" sformid="" visible="1150" width="100" lock="-1"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fsubmitentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <th lix="145" el="153" ek="fsubmitentity" id="fstorelocationid_s" fn="fstorelocationid" cn="仓位" ctlfk="fstorehouseid_s" luek="fsubmitentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="1150" width="100" lock="-1"></th>
            <th lix="150" el="100" ek="fsubmitentity" id="flotno_s" fn="flotno" pn="flotno" cn="批号" width="100" visible="1150" lock="-1"></th>
            <th lix="155" el="100" ek="fsubmitentity" id="fmtono_s" fn="fmtono" pn="fmtono" cn="物流跟踪号" width="100" visible="1150" lock="-1"></th>
            <th lix="160" el="149" ek="fsubmitentity" id="fownertype_s" fn="fownertype" pn="fownertype" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="1150" lock="-1">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th lix="165" el="150" ek="fsubmitentity" id="fownerid_s" fn="fownerid" pn="fownerid" ctlfk="fownertype" cn="货主" width="100" visible="1150" lock="-1"></th>
            <th lix="170" el="107" ek="fsubmitentity" id="fmtrlmodel_s" fn="fmtrlmodel" pn="fmtrlmodel" cn="规格型号"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid_s" dispfk="fspecifica" refvt="0" visible="1150"></th>
            <th lix="175" el="107" ek="fsubmitentity" id="fbrandid_s" fn="fbrandid" cn="品牌" visible="1150" dispfk="fbrandid" ctlfk="fmaterialid_s" sformid="" width="100" lock="-1"></th>
            <th lix="180" el="107" ek="fsubmitentity" id="fseriesid_s" fn="fseriesid" cn="系列" visible="1150" dispfk="fseriesid" ctlfk="fmaterialid_s" sformid="" width="100" lock="-1"></th>
            <th lix="181" el="107" ek="fsubmitentity" id="fsubseriesid_s" fn="fsubseriesid" cn="子系列" visible="1086" dispfk="fsubseriesid" ctlfk="fmaterialid_s" sformid="" width="100" lock="-1"></th>
            <th lix="185" el="100" ek="fsubmitentity" id="fsuitegroupid_s" fn="fsuitegroupid" cn="套件组合号" visible="1150" lock="-1"></th>
            <th lix="190" el="100" ek="fsubmitentity" id="fcategorygroupid_s" fn="fcategorygroupid" cn="配件组合号" visible="1150" lock="-1"></th>
            <th lix="195" el="100" ek="fsubmitentity" id="fsafagroupid_s" fn="fsafagroupid" cn="沙发组合号" visible="1150" lock="-1"></th>
            <th lix="200" el="103" ek="fsubmitentity" id="fqty_s" fn="fqty" cn="基本单位数量" visible="1150" ctlfk="funitid" width="120" format="0,000.00" lock="-1"></th>
            <th lix="205" el="109" ek="fsubmitentity" id="funitid_s" fn="funitid" cn="基本单位" ctlfk="fmaterialid_s" refid="ydj_unit" sformid="" filter="fisbaseunit='1'" visible="1150" width="80" lock="-1"></th>
            <th lix="210" el="140" ek="fsubmitentity" id="flinkformid_s" fn="flinkformid" ts="" cn="关联单据" visible="1150" copy="0" lock="-1"></th>
            <th lix="215" el="141" ek="fsubmitentity" id="flinkbillno_s" fn="flinkbillno" ts="" cn="关联单据编号" ctlfk="flinkformid_s" visible="1150" copy="0" lock="-1"></th>
            <th lix="220" el="100" ek="fsubmitentity" id="flinkrownumber_s" fn="flinkrownumber" ts="" cn="关联单行号" visible="1150" copy="0" lock="-1"></th>
            <th lix="225" el="100" ek="fsubmitentity" id="flinkrowinterid_s" fn="flinkrowinterid" ts="" cn="关联单行内码" visible="1086" copy="0" lock="-1"></th>
            <th lix="230" el="100" ek="fsubmitentity" id="fscanrowinterid" fn="fscanrowinterid" ts="" cn="扫描任务明细行内码" visible="1086" copy="0" lock="-1"></th>
            <th lix="235" el="140" ek="fsubmitentity" id="fsourceformid_s" fn="fsourceformid" ts="" cn="来源单据" visible="1086" copy="0" lock="-1"></th>
            <th lix="240" el="141" ek="fsubmitentity" id="fsourcebillno_s" fn="fsourcebillno" ctlfk="fsourceformid_s" ts="" cn="来源单据编号" visible="1086" copy="0" lock="-1"></th>
            <th el="100" ek="fsubmitentity" id="fsourcefid_s" fn="fsourcefid" ts="" cn="来源单内码" visible="1086" copy="0" lix="245" lock="-1"></th>
            <th lix="245" el="100" ek="fsubmitentity" id="fsourceinterid_s" fn="fsourceinterid" ts="" cn="来源单行号" visible="1086" copy="0" lock="-1"></th>
            <th lix="250" el="100" ek="fsubmitentity" id="fsourceentryid_s" fn="fsourceentryid" ts="" cn="来源单行内码" visible="1086" copy="0" lock="-1" width="100"></th>

            <th id="fseltypeid_s" el="107" ek="fsubmitentity" fn="fseltypeid" ctlfk="fmaterialid_s" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="460"></th>
        </tr>
    </table>

    <!--已扫描的条码-->
    <table id="fscannedentity" el="52" pk="fentryid" pn="fscannedentity" cn="已扫描的条码">
        <tr>

            <th el="100" ek="fscannedentity" id="fbarcode_fname" pn="fbarcode_fname" cn="条码" width="110" visible="1150" lock="-1" copy="0" lix="1"></th>

            <th el="100" ek="fscannedentity" id="fbarcodetext" pn="fbarcodetext" cn="条码码文" width="110" visible="1150" lock="-1" lix="5"></th>

            <th el="100" ek="fscannedentity" id="foperatorid_fname" pn="foperatorid_fname" visible="1150" cn="操作人" lock="-1" copy="0" lix="7"></th>

            <th ek="fscannedentity" el="113" id="fopdatetime" pn="fopdatetime" visible="1150" cn="操作时间" lock="-1" copy="0" lix="8"></th>

            <th el="100" ek="fscannedentity" id="fscansceneid_txt" pn="fscansceneid_txt" visible="1150" cn="业务操作" lock="-1" copy="0" lix="10" vals="'1':'收货','2':'发货','3':'调拨','4':'盘点','5':'装箱','6':'拆箱'"></th>
            <th lix="15" el="100" ek="fscannedentity" id="fmaterialid_fname_b" pn="fmaterialid_fname_b" visible="1150"  cn="商品" width="160" lock="-1"></th>
            <th el="100" ek="fscannedentity" id="fstorehouseid_fname" pn="fstorehouseid_fname" cn="仓库" lix="20" visible="1150" lock="-1"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fsubmitentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <th el="100" ek="fscannedentity" id="fstorelocationid_fname" pn="fstorelocationid_fname" cn="仓位" lix="25" visible="1150" lock="-1"></th>
            <th ek="fscannedentity" el="100" id="fnote_d" pn="fdescription" cn="备注" lix="30" visible="1150" width="260" copy="0" lock="-1"></th>
            <th ek="fscannedentity" el="100" id="fpda" pn="fpda" cn="PDA" visible="1150" width="100" lix="35" lock="-1"></th>

            <th id="fseltypeid_b" el="100" ek="fscannedentity" pn="fseltypeid"  ts="" cn="型号" visible="1086" lix="460"></th>

        </tr>
    </table>


    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
        </ul>
        <ul el="10" id="delete" op="delete" opn="删除">
            <li el="11" vid="510" cn="【任务状态】不等于”待作业”时不允许删除" data="{'expr':'(ftaskstatus ==\'ftaskstatus_01\')','message':'当前扫描任务的任务状态不等于待作业, 不允许删除 !'}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="continuetask" op="continuetask" opn="继续作业" data="" permid="continuetask"></ul>
        <ul el="10" ek="fbillhead" id="completetask" op="completetask" opn="完成作业" data="" permid="completetask"></ul>
        <ul el="10" ek="fbillhead" id="push2poinstock" op="push2poinstock" opn="入库" data="" permid="push2poinstock"></ul>
    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="continuetask" cn="继续作业"></ul>
        <ul el="12" id="completetask" cn="完成作业"></ul>
        <ul el="12" id="push2poinstock" cn="入库"></ul>
    </div>

    <div id="ListFuzzyFlds" cn="默认支持快捷过滤的字段列表">
        <ul el="14" id="" fldkeys="" cn="默认支持快捷过滤的字段列表，多个用逗号或分号隔开"></ul>
    </div>

</body>
</html>