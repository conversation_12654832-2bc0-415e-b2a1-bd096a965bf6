{
  //规则引擎基类
  "base": "",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "fstatus_ABC",
      "expression": ""
    },
    {
      "id": "fstatus_D",
      "expression": ""
    },
    {
      "id": "fstatus_E",
      "expression": ""
    },
    {
      "id": "fstatus_",
      "expression": ""
    },

    //如果支付方式不是银行，则锁定银行账号字段 对方账号 fotherpartyaccount
    {
      "id": "lock_bankcard",
      "expression": "field:fmybankid,fsynbankid,fotherpartyaccount|fway=='payway_01' or fway=='payway_04' or fway=='payway_05' or fway=='payway_09' or fway=='payway_10' or fway=='payway_12' or fway=='payway_13'"
    },
    {
      "id": "unlock_bankcard",
      "expression": "field:$fmybankid,fsynbankid,fotherpartyaccount|fway!='payway_01' and fway!='payway_04' and fway!='payway_05' and fway!='payway_09' and fway!='payway_10' and fway!='payway_12' and fway!='payway_13'"
    },
    {
      "id": "lock_bankcard_payment",
      "expression": "field:fmybankid,fsynbankid,fpayeraccount|fpaymentway=='payway_01' or fpaymentway=='payway_04' or fpaymentway=='payway_05' or fpaymentway=='payway_09' or fpaymentway=='payway_10' or fpaymentway=='payway_12' or fpaymentway=='payway_13'"
    },
    {
      "id": "unlock_bankcard_payment",
      "expression": "field:$fmybankid,fsynbankid,fpayeraccount|fpaymentway!='payway_01'and fpaymentway!='payway_04' and fpaymentway!='payway_05' and fpaymentway!='payway_09' and fpaymentway!='payway_10' and fpaymentway!='payway_12' and fpaymentway!='payway_13'"
    }
    
    ////对方账号 支付方式=支付宝 或 微信 或 银行转账 或 刷卡 或 刷卡非预置 时, 显示字段并允许编辑, 否则默认清空锁定,
    //{
    //  "id": "lock_fotherpartyaccount",
    //  "expression": "field:fotherpartyaccount|fway!='payway_06' and fway!='payway_11'  and fway!='payway_07'  and fway!='payway_08'"
    //},
    //{
    //  "id": "unlock_fotherpartyaccount",
    //  "expression": "field:$fotherpartyaccount|fway=='payway_06' or fway=='payway_11'or fway=='payway_07' or fway=='payway_08'"
    //}
  ],

  //定义表单可见性规则
  "visibleRules": [
    //账户的显示与隐藏
    {
      "id": "show_account",
      "expression": "other:$.account-info|fway=='payway_01' or fpaymentway=='payway_01'"
    },
    {
      "id": "hide_account",
      "expression": "other:.account-info|fway!='payway_01' and fpaymentway!='payway_01'"
    }
  ],

  //定义表单计算规则
  "calcRules": [

  ]
}