{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",
  //定义表单锁定规则
  "lockRules": [
    //此项规则表示：单据状态=A 或 B 或 C 时，所有字段可用，审核 反审核 撤销 操作不可用，其他操作可用
    {
      "id": "fstatus_ABC",
      "expression": ""
    },
    //此项规则表示：单据状态=D 时，所有字段可用，反审核 提交 操作不可用，其他操作可用
    {
      "id": "fstatus_D",
      "expression": ""
    },
    //此项规则表示：单据状态=E 时，所有字段不可用，反审核 提交 操作不可用，其他操作可用
    {
      "id": "fstatus_E",
      "expression": ""
    },
    //此项规则表示：单据状态='' 时(新增的时候)，所有字段可用，只有 新增 保存 操作可用，其他操作都不可用
    {
      "id": "fstatus_",
      "expression": ""
    }
  ],

  "visibleRules": [],
  //定义表单计算规则
  "calcRules": []
}