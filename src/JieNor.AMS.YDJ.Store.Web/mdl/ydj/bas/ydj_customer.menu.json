{
  "base": "/mdl/bd.menu.json",
  "common": [
    {
      "id": "tbOpenCompany",
      "caption": "创建企业",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 105,
      "parent": "droplist_more",
      "opcode": "opencompany",
      "param": "'dataChangeWarn':true,'dialog':{'formId':'ydj_opencompanydialog'}",
      "group": "standard"
    },
    {
      "id": "tbRecycle",
      "caption": "回收",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 102,
      "parent": "",
      "opcode": "recycle",
      "icon": "tbrecycling",
      "visibleext": 2113
    },
    //{
    //  "id": "tbRecycleToDept",
    //  "caption": "回收到门店",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 102,
    //  "parent": "",
    //  "opcode": "recycletodept",
    //  "icon": "tbrecycling",
    //  "visibleext": 2113
    //},
    //{
    //  "id": "tbRecycleToCompany",
    //  "caption": "回收到公司",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 102,
    //  "parent": "",
    //  "opcode": "recycletocompany",
    //  "icon": "tbrecycling",
    //  "visibleext": 2113
    //}
    {
      "id": "tbChangeDutyByDept",
      "caption": "不按部门调整负责人",
      "visible": "false",
      "disabled": "true",
      "style": "menu",
      "order": 106,
      "parent": "",
      "opcode": "changedutybydept",
      "param": "",
      "group": "standard"
    },
    {
      "id": "tbListAttachment",
      "caption": "附件",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 30,
      "parent": "droplist_more",
      "opcode": "listattachment",
      "param": ""
    }
  ],
  "listmenu": [
    {
      "id": "tbCommonCus",
      "caption": "公海客户",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 103,
      "parent": "",
      "opcode": "commoncus",
      "group": "standard",
      "default": "true"
    },
    {
      "id": "tbBulkSyn",
      "caption": "批量协同邀请",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 104,
      "parent": "droplist_more",
      "opcode": "bulksyn",
      "param": "",
      "group": "standard"
    },
    {
      "id": "tbDistributeDuty",
      "caption": "分配",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 100,
      "parent": "",
      "opcode": "distributeduty",
      "param": "",
      "group": "standard"
    }

  ],
  "billmenu": [
    {
      "id": "tbSave",
      "caption": "保存",
      "visible": "true",
      "disabled": "false",
      "style": "button",
      "order": 0,
      "parent": "",
      "opcode": "save",
      "param": "",
      "group": "standard"
    },
    {
      "id": "tbSaveSubmit",
      "caption": "保存并提交",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 1,
      "parent": "tbSave",
      "opcode": "savesubmit",
      "param": "autosubmit:true",
      "group": "standard"
    },
    {
      "id": "tbSaveAudit",
      "caption": "保存并审核",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 2,
      "parent": "tbSave",
      "opcode": "saveaudit",
      "param": "autoaudit:true",
      "group": "standard"
    },
    {
      "id": "tbsendSyn",
      "caption": "发起协同",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 104,
      "parent": "droplist_more",
      "opcode": "sendsyn",
      "param": "",
      "group": "standard"
    },
    {
      "id": "tbPushAftback",
      "caption": "售后反馈",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 105,
      "parent": "droplist_more",
      "opcode": "pushaftback",
      "param": "ruleId:'ydj_customer2ste_afterfeedback'",
      "group": "standard"
    },
    //{
    //  "id": "tbPush",
    //  "caption": "推荐机会",
    //  "visible": "false",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 107,
    //  "parent": "",
    //  "opcode": "push",
    //  "icon": "tbPush",
    //  "visibleext": 2112
    //},
    {
      "id": "tbPushService",
      "caption": "新增服务",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 108,
      "parent": "",
      "opcode": "pushservice",
      "param": "ruleId:'ydj_customer2ydj_service'",
      "group": "standard"
    },
    {
      "id": "tbfollowerrecord",
      "caption": "跟进",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 120,
      "parent": "",
      "opcode": "followerrecord",
      "param": "",
      "group": "standard",
      "visibleext": 2112,
      "icon": "tbFollower"
    },
    {
      "id": "tbDelete",
      "caption": "删除",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 140,
      "parent": "",
      "opcode": "delete",
      "group": "standard",
      "param": "",
      "visibleext": 2048,
      "icon": "tbDelete"
    },
    {
      "id": "tbModify",
      "caption": "修改",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 150,
      "parent": "",
      "opcode": "modify",
      "group": "standard",
      "param": "",
      "visibleext": 2048,
      "icon": "tbModify"
    },
    {
      "id": "btnThreeDDesign",
      "caption": "3D设计",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 180,
      "parent": "",
      "opcode": "threeddesign",
      "param": "'dataChangeWarn':true",
      "group": "standard"
    },
    {
      "id": "btnAddDuty",
      "caption": "新增负责人",
      "visible": "true",
      "disabled": "false",
      "style": "button",
      "parent": "",
      "opcode": "addduty",
      "param": "listMode:'lookup'",
      "group": "standard",
      "entityKey": "fdutyentry"
    },
    {
      "id": "btnReplaceDuty",
      "caption": "更换负责人",
      "visible": "true",
      "disabled": "false",
      "style": "button",
      "parent": "",
      "opcode": "replaceduty",
      "param": "listMode:'lookup'",
      "group": "standard",
      "entityKey": "fdutyentry"
    },
    {
      "id": "btnRemoveDuty",
      "caption": "移除负责人",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "parent": "",
      "opcode": "removeduty",
      "param": "listMode:'lookup'",
      "group": "standard",
      "entityKey": "fdutyentry"
    }
  ]
}