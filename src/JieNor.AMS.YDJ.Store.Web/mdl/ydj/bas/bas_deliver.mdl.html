<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="bas_deliver" el="3" basemodel="bd_basetmpl" cn="送达方"  isolate="0"  IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bas_deliver" pn="fbillhead" cn="送达方">

        <input el="100" ek="fbillhead" id="fname" len="500" notrace="false"/>

        <input lix="5" group="基本信息" el="107" ek="fbillhead" visible="-1" id="fagentnumber" fn="fagentnumber" pn="fagentnumber" cn="经销商编号" ctlfk="fagentid" lock="-1" dispfk="fnumber" lix="18" />
        <input lix="10" group="基本信息" el="106" ek="fbillhead" visible="-1" id="fagentid" fn="fagentid" pn="fagentid" refid="bas_agent" cn="经销商名称" lix="17" must="1" notrace="false"/>
        <select lix="15" group="基本信息" el="117" ek="fbillhead" visible="-1" id="fcustomertype" fn="fcustomertype" pn="fcustomertype" cn="客户类型" cg="客户类型" refid="bd_enum" dfld="fenumitem" lix="10"></select>
        <input lix="20" group="基本信息" el="106" ek="fbillhead" visible="-1" id="fbrandid" fn="fbrandid" pn="fbrandid" cn="品牌" refid="ydj_brand" lix="16" notrace="false"/>
        <input lix="25" group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcity" fn="fcity" pn="fcity" cn="城市" refid="ydj_city" dfld="flevel,fcountry,fprovince,fstaffid" lix="11" notrace="false"/>
        <select lix="30" group="基本信息" el="107" ek="fbillhead" visible="-1" id="fcitylevel" fn="fcitylevel" pn="fcitylevel" cn="城市等级" ctlfk="fcity" dispfk="flevel" lock="-1" lix="12"></select>
        <select lix="35" group="基本信息" el="107" ek="fbillhead" visible="-1" id="fcountry" fn="fcountry" pn="fcountry" cn="国家/地区" ctlfk="fcity" dispfk="fcountry" lock="-1" lix="13"></select>
        <select lix="40" group="基本信息" el="107" ek="fbillhead" visible="-1" id="fprovince" fn="fprovince" pn="fprovince" cn="省/(直辖市)" ctlfk="fcity" dispfk="fprovince" lock="-1" lix="14"></select>
        <input lix="45" group="基本信息" el="107" ek="fbillhead" visible="-1" id="fregionalmanagerid" fn="fregionalmanagerid" pn="fregionalmanagerid" cn="区域经理" ctlfk="fcity" dispfk="fstaffid" lock="-1" lix="15" />
        <input lix="50" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fmobile" fn="fmobile" pn="fmobile" cn="手机号码" lix="24" notrace="false"/>
        <input lix="55" type="text" id="fdescription" el="100" ek="fbillhead" fn="fdescription" apipn="description" pn="fdescription" cn="备注" width="280" visible="-1" />
        <input lix="60" group="送达方信息" el="100" ek="fbillhead" visible="-1" id="fregno" fn="fregno" pn="fregno" cn="注册号" lix="30" />
        <input lix="65" group="送达方信息" el="100" ek="fbillhead" visible="-1" id="fcorporation" fn="fcorporation" pn="fcorporation" cn="法人" lix="31" notrace="false"/>
        <input lix="70" group="送达方信息" el="100" ek="fbillhead" visible="-1" id="fbizaddress" fn="fbizaddress" pn="fbizaddress" cn="营业执照地址" lix="32" notrace="false"/>
        <input lix="75" group="送达方信息" el="100" ek="fbillhead" visible="-1" id="fcompanyaddress" fn="fcompanyaddress" pn="fcompanyaddress" cn="公司地址" lix="34" />
        <input lix="80" group="送达方信息" el="101" ek="fbillhead" visible="-1" id="fstorequantity" fn="fstorequantity" pn="fstorequantity" cn="门店数量汇总" lix="35" />
        <input lix="85" group="送达方信息" el="106" ek="fbillhead" visible="-1" id="fsaleorgid" fn="fsaleorgid" pn="fsaleorgid" refid="bas_organization" cn="销售组织名称" lix="36" notrace="false"/>
        <input lix="90" group="送达方信息" el="107" ek="fbillhead" visible="-1" id="fsaleorgnumber" fn="fsaleorgnumber" pn="fsaleorgnumber" cn="销售组织编码" ctlfk="fsaleorgid" lock="-1" dispfk="fnumber" lix="37" notrace="false"/>


        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="femail" fn="femail" pn="femail" cn="电子邮件" lix="19" notrace="false"/>
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fsalenumber" fn="fsalenumber" pn="fsalenumber" cn="订单专员工号" lix="20" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fsalename" fn="fsalename" pn="fsalename" cn="总部订单专员" lix="21" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fsaleexecutive" fn="fsaleexecutive" pn="fsaleexecutive" cn="总部订单主管" lix="22" notrace="false"/>
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="ffax" fn="ffax" pn="ffax" cn="传真号码" lix="23" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fphone" fn="fphone" pn="fphone" cn="电话号码" lix="25" notrace="false"/>
        <input group="送达方信息" el="100" ek="fbillhead" visible="1150" id="fdeliveryaddress" fn="fdeliveryaddress" pn="fdeliveryaddress" cn="送货地址" lix="33" />
        <input group="送达方信息" el="106" ek="fbillhead" id="fresultbrandid" fn="fresultbrandid" pn="fresultbrandid" cn="系列" refid="ydj_series" visible="-1" lix="35" notrace="false"/>

        <input group="送达方信息" el="100" ek="fbillhead" visible="0" id="actualownerid" fn="actualownerid" pn="actualownerid" cn="外部实控人ID" desc="记录外部实控人ID，用于匹配实控人" notrace="false"/>
        <input group="送达方信息" el="100" ek="fbillhead" visible="-1" id="actualownernumber" fn="actualownernumber" pn="actualownernumber" cn="实控人ID" desc="实际为实控人编码" width="105" copy="0" lix="200" notrace="false"/>
        <input group="送达方信息" el="100" ek="fbillhead" visible="-1" id="actualownername" fn="actualownername" pn="actualownername" cn="实控人" width="105" copy="0" lix="210" notrace="false"/>

        <input group="基本信息" el="107" ek="fbillhead" visible="-1" id="fcrmdistributornumber" fn="fcrmdistributornumber" pn="fcrmdistributornumber" cn="招商经销商编码" ctlfk="fcrmdistributorid" lock="-1" dispfk="fnumber" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcrmdistributorid" fn="fcrmdistributorid" pn="fcrmdistributorid" refid="ms_crmdistributor" cn="招商经销商名称" notrace="false"/>

        <input group="送达方信息" el="116" ek="fbillhead" visible="-1" id="forderdisable" fn="forderdisable" pn="forderdisable" cn="下单权限" copy="0" defval="false" lix="40" notrace="false"/>
        <input group="送达方信息" el="116" ek="fbillhead" visible="-1" id="fisnew" fn="fisnew" pn="fisnew" cn="新商" copy="0" defval="false"  lix="45" notrace="false"/>
        <input group="送达方信息" type="text" el="100" ek="fbillhead" visible="-1" id="fassresult" fn="fassresult" pn="fassresult" cn="考核结果" lix="50" len="4000" notrace="false"/>

        <table id="fentry" el="52" pk="fentryid" tn="t_bas_deliverentry" pn="fentry" cn="品牌信息" kfks="fserieid">
            <tr>
                <th el="106" ek="fentry" visible="1150" id="fserieid" fn="fserieid" pn="fserieid" cn="系列" dfld="fbrandid" refid="ydj_series" lix="50"></th>
                <th el="107" ek="fentry" visible="1150" id="fseriebrand" fn="fseriebrand" pn="fseriebrand" cn="品牌" ctlfk="fserieid" dispfk="fbrandid" refid="ydj_brand" lock="-1" lix="51"></th>
                <th el="116" ek="fentry" visible="1150" id="fenable" fn="fenable" pn="fenable" cn="是否启用" defval="true" lix="52"></th>
            </tr>
        </table>
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="11" clear></li>
            <li el="11" id="save_valid_fnumber" cn="保存时编码唯一" vid="500" data="fmainorgid,fnumber"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="syncfrommusi" op="syncfrommusi" opn="从慕思拉取数据" permid="fw_syncfrommusi"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_syncfrommusi" cn="从慕思拉取数据"></ul>
    </div>
</body>
</html>