{
  //规则引擎基类
  "base": "",

  //定义表单锁定规则
  "lockRules": [

  ],

  //定义表单可见性规则
  "visibleRules": [
  ],
  //定义表单计算规则
  "calcRules": [
    //选择活动后，带出活动的字段
    { "expression": "fbegindate=fpromotionid__fbegindate| fpromotionid!=''or 1==1" },
    { "expression": "fenddate=fpromotionid__fenddate|fpromotionid!=''or 1==1" },
    { "expression": "ftype=fpromotionid__ftype| fpromotionid!=''or 1==1" },
    { "expression": "fpromotiondescription=fpromotionid__fdescription|fpromotionid!=''" },
    { "expression": "funitid=fproductid__funitid|fproductid=='' or 1==1" }
  ]
}