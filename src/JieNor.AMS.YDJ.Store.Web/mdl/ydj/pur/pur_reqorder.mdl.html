<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="pur_reqorder" basemodel="bill_basetmpl" el="1" cn="采购申请单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_pur_reqorder" pn="fbillhead" cn="采购申请单">

        <!--重写基类模型中的部分字段属性-->
        <input id="fbillno" el="108" visible="-1" lix="1" width="115" align="center" />
        <input id="fcreatorid" el="118" visible="1150" lix="9" width="80" />
        <input id="fcreatedate" el="119" visible="1150" lix="10" width="125" />
        <input id="fdescription" el="100" visible="0" canchange="true" />

        <!--基本信息-->
        <input group="基本信息" el="112" ek="fbillhead" visible="-1" id="fdate" fn="fdate" ts="" cn="申请日期" defval="@currentshortdate" copy="0" lix="5" width="90" must="1" />
        <input group="基本信息" el="106" ek="FBillHead" id="freqdeptid" fn="freqdeptid" pn="freqdeptid" visible="-1" cn="申请部门"
               lock="-1" copy="1" lix="10" notrace="true" ts="" refid="ydj_dept" filter="" reflvt="0" dfld="" defVal="@currentDeptId" canchange="true" />
        <input group="基本信息" el="106" ek="FBillHead" id="freqstaffid" fn="freqstaffid" pn="freqstaffid" visible="-1" cn="申请人" must="1"
               lock="0" copy="1" lix="15" notrace="true" ts="" refid="ydj_staff" filter="" reflvt="0" dfld="" defVal="@currentStaffId" canchange="true" />


        <select group="基本信息" el="123" ek="fbillhead" visible="1150" id="fbilltypeid" fn="fbilltypeid" pn="fbilltypeid" refid="bd_billtype" ts="" cn="单据类型" lix="2" must="1"></select>
        <input group="基本信息" el="105" ek="fbillhead" visible="1150" id="fsumamount" fn="fsumamount" pn="fsumamount" cn="销售总额" copy="0" lock="-1" />


        <!--<input group="基本信息" el="105" ek="FBillHead" id="ftotalamount" fn="ftotalamount" pn="ftotalamount" visible="-1" cn="合计金额"
        lock="-1" copy="1" lix="10" notrace="true" ts="" roundType="0" format="0,000.00" />-->
        <!-- 相关信息 -->
        <input group="相关信息" el="140" ek="fbillhead" visible="1150" id="fsourcetype" />
        <input group="相关信息" el="141" ek="fbillhead" visible="1150" id="fsourcenumber" />

    </div>

    <!--商品明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_pur_reqorderentry" pn="fentity" cn="商品明细" kfks="fmaterialid" must="1">
        <tr>
            <th lix="200" el="106" ek="fentity" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="-1" cn="商品" width="160"
                lock="0" copy="1" notrace="true" ts="" refid="ydj_product" multsel="true" filter="fendpurchase='0'" reflvt="0" dfld="fspecifica" sformid="" must="1"></th>
            <th lix="205" el="107" ek="fentity" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="-1" cn="商品编码"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0"></th>
            <th lix="210" ek="fentity" el="161" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="图片" ctlfk="fmaterialid" width="200" visible="-1"></th>
            <th lix="215" el="107" ek="fentity" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="-1" cn="规格型号"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="100"></th>
            <th lix="220" el="132" ek="fentity" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" visible="-1" cn="辅助属性"
                lock="0" copy="1" notrace="true" ts="" ctlfk="fmaterialid" pricefk="fsalprice" width="140" canchange="true"></th>
            <th lix="225" el="107" ek="fentity" id="fbrandid" fn="fbrandid" cn="品牌" visible="-1" ctlfk="fmaterialid" dispfk="fbrandid" sformid="" width="100" lock="-1"></th>
            <th lix="230" el="107" ek="fentity" id="fseriesid" fn="fseriesid" cn="系列" visible="-1" ctlfk="fmaterialid" dispfk="fseriesid" sformid="" width="100" lock="-1"></th>
            <th lix="235" el="106" ek="fentity" id="fsupplierid" fn="fsupplierid" pn="fsupplierid" visible="-1" cn="建议供应商"
                lock="0" copy="1" notrace="true" ts="" refid="ydj_supplier" filter="" reflvt="0" dfld=""></th>
            <th lix="240" el="106" ek="fentity" id="fpostaffid" fn="fpostaffid" pn="fpostaffid" visible="-1" cn="采购员"
                lock="0" copy="1" notrace="true" ts="" refid="ydj_staff" filter="" reflvt="0" dfld=""></th>


            <th ek="fentity" lix="12" el="107" id="fattribute" fn="fattribute" pn="fattribute" visible="0" cn="属性" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fattribute" refvt="0"></th>

            <th lix="10" el="135" ek="fentity" id="fmulfile" fn="fmulfile" pn="fmulfile" cn="附件" adld="true" maxCount="0" width="240" visible="1150"></th>


            <th el="100" ek="fentity" len="2000" id="fcustomdesc" fn="fcustomdesc" pn="fcustomdesc" visible="1150" cn="定制说明" width="160" lock="0" copy="1" lix="72" notrace="true" ts="" canchange="true"></th>
            <!-- <th lix="94" el="107" ek="fentity" id="fcustom" fn="fcustom" pn="fcustom" visible="1150" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>-->
            <th lix="94" el="107" ek="fentity" id="fcustom" fn="fcustom" pn="fcustom" visible="0" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>

            <!--  <th lix="94" el="107" ek="fentity" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="1150" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>-->
            <th lix="94" el="107" ek="fentity" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="0" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>


            <th ek="fentity" lix="79" el="105" id="fsalprice" fn="fsalprice" ts="" cn="销售单价" visible="1150" width="100" copy="0" lock="-1"></th>
            <th ek="fentity" lix="80" el="105" id="fsalamount" fn="fsalamount" ts="" cn="销售金额" visible="1150" width="100" copy="0" lock="-1"></th>

            <th el="112" ek="fentity" id="fdemanddate" fn="fdemanddate" pn="fdemanddate" visible="1150" cn="需求日期"
                lock="0" copy="1" lix="85" notrace="true" ts=""></th>

            <th el="109" ek="fentity" id="funitid" fn="funitid" pn="funitid" cn="基本单位" filter="fisbaseunit='1'" width="80" visible="1124"
                lock="-1" copy="1" lix="88" notrace="true" ts="" ctlfk="fmaterialid" refid="ydj_unit" reflvt="0" dfld="" sformid="" must="1"></th>
            <th el="103" ek="fentity" id="fqty" fn="fqty" pn="fqty" visible="1124" cn="基本单位需求数量"
                copy="1" lix="89" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" width="140" must="1" canchange="true"></th>

            <th ek="fentity" lix="100" el="109" id="fbizunitid" fn="fbizunitid" pn="fbizunitid" cn="采购单位" must="1"
                refid="ydj_unit" sformid="" ctlfk="fmaterialid" width="90" visible="1124" canchange="true"></th>
            <th ek="fentity" lix="101" el="103" id="fbizqty" fn="fbizqty" pn="fbizqty" cn="需求数量"
                ctlfk="fbizunitid" format="0,000.00" basqtyfk="fqty" width="80" visible="1124" canchange="true"></th>

            <!--<th el="103" ek="fentity" id="finvqty" fn="finvqty" pn="finvqty" visible="0" cn="即时库存量"
        lock="-1" copy="0" lix="81" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
    <th el="103" ek="fentity" id="fsafestockqty" fn="fsafestockqty" pn="fsafestockqty" visible="0" cn="安全库存"
        lock="-1" copy="0" lix="82" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
    <th el="103" ek="fentity" id="forderinqty" fn="forderinqty" pn="forderinqty" visible="0" cn="在途量"
        lock="-1" copy="0" lix="83" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
    <th el="103" ek="fentity" id="fotherinqty" fn="fotherinqty" pn="fotherinqty" visible="0" cn="其它预计入"
        lock="-1" copy="0" lix="84" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
    <th el="103" ek="fentity" id="fsuggestorderqty" fn="fsuggestorderqty" pn="fsuggestorderqty" visible="-1" cn="建议订购量"
        lock="-1" copy="0" lix="85" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
    <th el="103" ek="fentity" id="factualorderqty" fn="factualorderqty" pn="factualorderqty" visible="0" cn="净短缺量"
        lock="0" copy="0" lix="86" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>-->

            <th el="103" ek="fentity" id="forderqty" fn="forderqty" pn="forderqty" visible="1150" cn="基本单位已订购量" width="120"
                lock="-1" copy="0" lix="120" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizorderqty" fn="fbizorderqty" pn="fbizorderqty" visible="1150" cn="采购已订购量" width="100"
                lock="-1" copy="0" lix="121" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="forderqty" roundType="0" format="0,000.00"></th>


            <th el="106" ek="fentity" id="fpostdeptid" fn="fpostdeptid" pn="fpostdeptid" visible="0" cn="采购部门"
                lock="0" copy="1" lix="150" notrace="true" ts="" refid="ydj_dept" filter="" reflvt="0" dfld=""></th>

            <!--<th ek="fentity" el="105" id="fprice" fn="fprice" ts="" cn="单价" visible="-1" width="100" copy="0" lix="90"></th>
    <th ek="fentity" el="105" id="famount" fn="famount" ts="" cn="金额" visible="-1" width="100" lock="-1" copy="0" lix="91"></th>-->

            <th el="100" ek="fentity" id="fmtono" fn="fmtono" pn="fmtono" cn="物流跟踪号" lix="160" width="125" visible="1150" lock="-1"></th>
            <th lix="170" el="149" ek="fentity" id="fownertype" fn="fownertype" pn="fownertype" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="1150" lock="0" defval="'ydj_dept'">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th lix="171" el="150" ek="fentity" id="fownerid" fn="fownerid" pn="fownerid" ctlfk="fownertype" cn="货主" width="100" visible="1150" lock="0"></th>

            <th ek="fentity" el="100" id="fnote" fn="fnote" pn="fnote" cn="备注" visible="1124" width="160" len="1000" copy="0" lix="175" canchange="true"></th>
            <th ek="fentity" el="140" id="fsourceformid" fn="fsourceformid" pn="fsourceformid" cn="源单类型" visible="1126" lock="-1" copy="0" lix="180"></th>
            <th ek="fentity" el="141" id="fsourcebillno" fn="fsourcebillno" pn="fsourcebillno" cn="源单编号" ctlfk="fsourceformid" visible="1126" lock="-1" copy="0" lix="190"></th>
            <th ek="fentity" el="100" id="fsourceinterid" fn="fsourceinterid" pn="fsourceinterid" cn="源单内码" visible="0" lock="-1" copy="0" lix="200"></th>
            <th ek="fentity" el="100" id="fsourceentryid" fn="fsourceentryid" pn="fsourceentryid" cn="源单明细内码" visible="0" lock="-1" copy="0" lix="210"></th>

        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="push2poinstock" op="push" opn="入库" data="{'parameter':{'ruleId':'ydj_purchaseorder2stk_postockin'}}" permid=""></ul>
        <ul el="10" id="submit" op="submit" opn="提交"></ul>

        <ul el="10" id="audit" op="audit" opn="审核">
            <li el="17" sid="1002" cn="反写销售合同采购数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fpurqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fpurqty&gt;fqty',
                'excessMessage':'需求数量不允许大于合同数量！'
                }"></li>
            <li el="17" sid="1004" cn="变更生效执行"></li>
            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
                'executeType':'add'
                }"></li>-->
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="17" sid="1002" cn="反写销售合同采购数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fpurqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fpurqty&gt;fqty',
                'excessMessage':'需求数量不允许大于合同数量！'
                }"></li>
            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
                'executeType':'remove'
                }"></li>-->
        </ul>
        <ul el="10" ek="fbillhead" id="change" op="change" opn="变更">
            <li el="17" sid="1002" cn="反写销售合同采购数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fpurqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fpurqty&gt;fqty',
                'excessMessage':'需求数量不允许大于合同数量！'
                }"></li>
            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
                'executeType':'remove'
                }"></li>-->
        </ul>

        <ul el="10" ek="fentity" id="queryinventory" op="queryinventory" opn="库存查询" permid="fw_queryinventory"
            data="{
                'parameter':{
                    'fieldMaps':{}
                }
            }"></ul>
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" ek="fentity" cn="需求数量必须大于0" data="{'expr':'fbizqty>0 ','message':'需求数量必须大于0！'}"></li>
            <li el="11" vid="510" ek="fentity" cn="货主字段不能为空!" data="{'expr':'(fownertype=\'\' and fownerid=\'\') or (fownertype!=\'\' and fownerid!=\'\')','message':'货主字段不能为空!'}"></li>
            <li el="11" vid="517" id="save_valid_sourcebill" cn="保存时校验源单明细行是否存在于源单中"
                data="{'sourceTypeFieldKey':'fsourceformid','sourceNoFieldKey':'fsourcebillno','sourceEntryIdFieldKey':'fsourceentryid'}" precon=""></li>
        </ul>

        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="1004" cn="变更生效执行"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="submit" op="submit" opn="提交">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unsubmit" op="unsubmit" opn="撤销">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="uncancel" op="uncancel" opn="反作废">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>

    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="fw_queryinventory" cn="库存查询"></ul>
    </div>

</body>
</html>