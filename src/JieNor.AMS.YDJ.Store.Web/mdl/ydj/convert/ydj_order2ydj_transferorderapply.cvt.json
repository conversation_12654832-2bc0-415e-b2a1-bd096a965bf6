//Id是推送目标ID,SrcFieldId是推送源ID,maptype=1是指文本类型映射，maptype=0是字段类型映射
{
  "Id": "ydj_order2ydj_transferorderapply",
  "Number": "ydj_order2ydj_transferorderapply",
  "Name": "销售合同下推转单申请",
  "SourceFormId": "ydj_order",
  "TargetFormId": "ydj_transferorderapply",
  "ActiveEntityKey": "fentry",
  //单据关联检查过滤条件
  "BillLinkCheckFitler": "fissaletransferorder=0",
  "FieldMappings": [
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_transferorderapply_01'",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fcreatedate",
      "Name": "申请日期",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fisreselltransfer",
      "Name": "二级分销转单",
      "MapType": 0,
      "SrcFieldId": "fisresellorder",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "",
      "Name": "",
      "MapType": 0,
      "SrcFieldId": "fsourceid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "合同源编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fremark",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fsourceid",
    //  "Name": "合同源Id",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillhead.id",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "freceivercontractnumber",
      "Name": "接单方合同编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freceivercontractid",
      "Name": "接单方合同id",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freceiveragentid",
      "Name": "接单方经销商ID",
      "MapType": 1,
      "SrcFieldId": "@currentorgid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freceiverstore",
      "Name": "接单方门店",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freceiversaler",
      "Name": "接单方销售员",
      "MapType": 0,
      "SrcFieldId": "fstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fclientphone",
      "Name": "客户手机号",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fshipper",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "flinkstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fshipperphone",
      "Name": "收货人手机号",
      "MapType": 0,
      "SrcFieldId": "fcustomerid.fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "faddress",
      "Name": "详细地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbuildingid",
      "Name": "楼盘",
      "MapType": 0,
      "SrcFieldId": "fbuildingid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fgoodstotalamount",
      "Name": "整单商品金额",
      "MapType": 0,
      "SrcFieldId": "ffaceamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdealtotalamount",
      "Name": "整单成交金额",
      "MapType": 0,
      "SrcFieldId": "fdealamount_h",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freceivedamount",
      "Name": "已收款金额",
      "MapType": 0,
      "SrcFieldId": "freceivable",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotaldiscount",
      "Name": "整单折扣",
      "MapType": 0,
      "SrcFieldId": "fdistsumrate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotaldiscountamount",
      "Name": "整单折扣额",
      "MapType": 0,
      "SrcFieldId": "fdistamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fshipperdate",
      "Name": "要货日期",
      "MapType": 0,
      "SrcFieldId": "fdeliverydate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fwithin",
      "Name": "手工单号",
      "MapType": 0,
      "SrcFieldId": "fwithin",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomeraddress",
      "Name": "客户地址",
      "MapType": 0,
      "SrcFieldId": "fcustomerid.faddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    //表体商品明细字段映射
    {
      "Id": "fproductid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fproductid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "",
      "Name": "",
      "MapType": 0,
      "SrcFieldId": "fsourceentryid_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenentryid",
      "Name": "源单明细内码",
      "MapType": 0,
      "SrcFieldId": "fentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fproductseltype",
      "Name": "规格型号",
      "MapType": 0,
      "SrcFieldId": "fmtrlmodel",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fkitdesc",
      "Name": "套件描述",
      "MapType": 0,
      "SrcFieldId": "fsuitdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizqty",
      "Name": "销售数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbrandid",
      "Name": "品牌",
      "MapType": 0,
      "SrcFieldId": "fbrandid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fseriesid",
      "Name": "系列",
      "MapType": 0,
      "SrcFieldId": "fseriesid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funstdtype",
      "Name": "是否非标",
      "MapType": 0,
      "SrcFieldId": "funstdtype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "零售价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 0,
      "SrcFieldId": "famount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdistrate",
      "Name": "折率",
      "MapType": 0,
      "SrcFieldId": "fdistrate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdistamount",
      "Name": "折扣额",
      "MapType": 0,
      "SrcFieldId": "fdistamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdealprice",
      "Name": "成交单价",
      "MapType": 0,
      "SrcFieldId": "fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdealamount",
      "Name": "成交金额",
      "MapType": 0,
      "SrcFieldId": "fdealamount_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpurqty",
      "Name": "基本单位已采购数量",
      "MapType": 0,
      "SrcFieldId": "fpurqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliveryqty",
      "Name": "已发货数量",
      "MapType": 0,
      "SrcFieldId": "fdeliveryqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsubqty",
      "Name": "子件数量",
      "MapType": 0,
      "SrcFieldId": "fsubqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsuitproductid",
      "Name": "套件商品",
      "MapType": 0,
      "SrcFieldId": "fsuitproductid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsuitcombnumber",
      "Name": "套件组合号",
      "MapType": 0,
      "SrcFieldId": "fsuitcombnumber",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpartscombnumber",
      "Name": "配件组合号",
      "MapType": 0,
      "SrcFieldId": "fpartscombnumber",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fisgiveaway",
      "Name": "赠品",
      "MapType": 0,
      "SrcFieldId": "fisgiveaway",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotalgoodsamount",
      "Name": "整单换算商品金额",
      "MapType": 1,
      "SrcFieldId": "fdistsumrate*famount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fresultbrandid",
      "Name": "业绩品牌",
      "MapType": 0,
      "SrcFieldId": "fresultbrandid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcombonumber",
      "Name": "沙发组合号",
      "MapType": 0,
      "SrcFieldId": "fsofacombnumber",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fisoutspot",
    //  "Name": "出现货",
    //  "MapType": 0,
    //  "SrcFieldId": "fisoutspot",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "funstdtypestatus",
      "Name": "非标审批状态",
      "MapType": 0,
      "SrcFieldId": "funstdtypestatus",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fhqprice",
      "Name": "总部零售价",
      "MapType": 0,
      "SrcFieldId": "fhqprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fhqdistrate",
      "Name": "总部折扣率",
      "MapType": 0,
      "SrcFieldId": "fhqdistrate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}
