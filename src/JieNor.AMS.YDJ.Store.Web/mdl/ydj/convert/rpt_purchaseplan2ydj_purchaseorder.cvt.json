{
  "Id": "rpt_purchaseplan2ydj_purchaseorder",
  "Number": "rpt_purchaseplan2ydj_purchaseorder",
  "Name": "�ɹ��ƻ������ɲɹ�����",
  "SourceFormId": "rpt_purchaseplan",
  "TargetFormId": "ydj_purchaseorder",
  "ActiveEntityKey": "fbillhead",
  "FilterString": "",
  "Message": "",
  "FieldMappings": [
    //{
    //  "Id": "fbilltypeid",
    //  "Name": "��������",
    //  "MapType": 0,
    //  "SrcFieldId": "fbilltype",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsupplierid",
    //  "Name": "��Ӧ��",
    //  "MapType": 0,
    //  "SrcFieldId": "fsupplierid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0,
    //  "AllowFilter": true
    //},
    //{
    //  "Id": "fsupplieraddr",
    //  "Name": "������ַ",
    //  "MapType": 0,
    //  "SrcFieldId": "fsupplierid.faddress",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fdate",
      "Name": "��������",
      "MapType": 1,
      "SrcFieldId": "@currentshortdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpickdate",
      "Name": "��������",
      "MapType": 1,
      "SrcFieldId": "@currentshortdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fcustomerid",
    //  "Name": "�ͻ�",
    //  "MapType": 0,
    //  "SrcFieldId": "fcustomerid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fphone",
    //  "Name": "�ֻ���",
    //  "MapType": 0,
    //  "SrcFieldId": "fphone",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fprovince",
    //  "Name": "ʡ",
    //  "MapType": 0,
    //  "SrcFieldId": "fprovince",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fcity",
    //  "Name": "��",
    //  "MapType": 0,
    //  "SrcFieldId": "fcity",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fregion",
    //  "Name": "��",
    //  "MapType": 0,
    //  "SrcFieldId": "fregion",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "faddress",
    //  "Name": "��ϸ��ַ",
    //  "MapType": 0,
    //  "SrcFieldId": "faddress",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fdeptid",
    //  "Name": "�����ŵ�",
    //  "MapType": 0,
    //  "SrcFieldId": "fdeptid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fstaffid",
    //  "Name": "����Ա",
    //  "MapType": 0,
    //  "SrcFieldId": "fstaffid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fstylistid",
    //  "Name": "���ʦ",
    //  "MapType": 0,
    //  "SrcFieldId": "fstylistid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourcenumber",
    //  "Name": "Դ�����",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillno",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "forderno",
    //  "Name": "���ۺ�ͬ���",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillno",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "forderdate",
    //  "Name": "�µ�����",
    //  "MapType": 0,
    //  "SrcFieldId": "fdate",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fmaterialid",
      "Name": "��Ʒ",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "������λ",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    //{
    //  "Id": "fmulfile",
    //  "Name": "����",
    //  "MapType": 0,
    //  "SrcFieldId": "fmulfile",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fbizunitid",
      "Name": "�ɹ���λ",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "��������",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fsalprice",
    //  "Name": "���۵���",
    //  "MapType": 0,
    //  "SrcFieldId": "fprice",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0,
    //  "IgnoreChangeValidation": true
    //},
    //{
    //  "Id": "fentrystaffid",
    //  "Name": "����Ա",
    //  "MapType": 0,
    //  "SrcFieldId": "fstaffid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0,
    //  "IgnoreChangeValidation": true
    //},
    {
      "Id": "fqty",
      "Name": "������λ����",
      "MapType": 0,
      "SrcFieldId": "sugQty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdes_e",
      "Name": "����˵��",
      "MapType": 0,
      "SrcFieldId": "fcustomdes",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fdistrate",
    //  "Name": "�ۿ�",
    //  "MapType": 1,
    //  "SrcFieldId": "10",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fownertype",
    //  "Name": "��������",
    //  "MapType": 0,
    //  "SrcFieldId": "fownertype",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fownerid",
    //  "Name": "����",
    //  "MapType": 0,
    //  "SrcFieldId": "fownerid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourceentryid_e",
    //  "Name": "Դ����ϸID",
    //  "MapType": 0,
    //  "SrcFieldId": "fentry.id",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourcebillno_e",
    //  "Name": "��Դ�����",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillno",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourceinterid_e",
    //  "Name": "��Դ����¼����",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillhead.id",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourceformid_e",
    //  "Name": "��Դ������",
    //  "MapType": 1,
    //  "SrcFieldId": "'ydj_order'",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourcetype",
    //  "Name": "Դ������",
    //  "MapType": 1,
    //  "SrcFieldId": "'ydj_order'",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fnote",
    //  "Name": "��ע",
    //  "MapType": 0,
    //  "SrcFieldId": "fdescription_e",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fmtono",
    //  "Name": "�������ٺ�",
    //  "MapType": 0,
    //  "SrcFieldId": "fmtono",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 1000
    //},
    //{
    //  "Id": "fdemanddate",
    //  "Name": "��������",
    //  "MapType": 0,
    //  "SrcFieldId": "fexdeliverydate",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 1000
    //},
    //{
    //  "Id": "fmtrlimage",
    //  "Name": "ͼƬ",
    //  "MapType": 0,
    //  "SrcFieldId": "fmtrlimage",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsoorderno",
    //  "Name": "���ۺ�ͬ���",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillno",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsoorderinterid",
    //  "Name": "���ۺ�ͬ����",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillhead.id",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsoorderentryid",
    //  "Name": "���ۺ�ͬ��¼����",
    //  "MapType": 0,
    //  "SrcFieldId": "fentry.id",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fselectionnumber",
    //  "Name": "ѡ����",
    //  "MapType": 0,
    //  "SrcFieldId": "fselectionnumber",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fdescription_suite",
    //  "Name": "���˵��",
    //  "MapType": 0,
    //  "SrcFieldId": "fdescription_suite",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fpackagedescription",
    //  "Name": "�׼����˵��",
    //  "MapType": 0,
    //  "SrcFieldId": "fdescription_suite",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fresultbrandid",
    //  "Name": "ҵ��Ʒ��",
    //  "MapType": 0,
    //  "SrcFieldId": "fresultbrandid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fissuitflag",
    //  "Name": "�Ƿ��׼�",
    //  "MapType": 0,
    //  "SrcFieldId": "fissuitflag",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "funstdtype",
      "Name": "�Ƿ�Ǳ�",
      "MapType": 0,
      "SrcFieldId": "funstdtype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fsaledeptid",
    //  "Name": "���۲���",
    //  "MapType": 0,
    //  "SrcFieldId": "fdeptid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fpodeptid",
    //  "Name": "�ɹ�����",
    //  "MapType": 0,
    //  "SrcFieldId": "fdeptid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fcustomer",
    //  "Name": "�ͻ�",
    //  "MapType": 0,
    //  "SrcFieldId": "fcustomerid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fphone_e",
    //  "Name": "�ֻ���",
    //  "MapType": 0,
    //  "SrcFieldId": "fphone",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fclosestatus",
      "Name": "�ر�״̬",
      "MapType": 1,
      "SrcFieldId": "'0'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fclosestatus_e",
      "Name": "�йر�״̬",
      "MapType": 1,
      "SrcFieldId": "'0'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    //{
    //  "Id": "fsupplierid",
    //  "Name": "��Ӧ��",
    //  "Order": 0
    //}
  ],
  "FieldGroups": [
    //{
    //  "Id": "fentity_fentryid",
    //  "Order": 1
    //}
  ]
}