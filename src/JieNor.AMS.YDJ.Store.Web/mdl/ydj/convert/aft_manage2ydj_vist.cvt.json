//{
//  "Id": "aft_manage2ydj_vist",
//  "Number": "aft_manage2ydj_vist",
//  "Name": "订单回访",
//  "SourceFormId": "aft_manage",
//  "TargetFormId": "ydj_vist",
//  "ActiveEntityKey": "fbillhead",
//  "FilterString": "",
//  "Message": "",
//  "FieldMappings": [
//    {
//      "Id": "fsourcetype",
//      "Name": "源单类型",
//      "MapType": 1,
//      "SrcFieldId": "'aft_manage'",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourcenumber",
//      "Name": "源单编号",
//      "MapType": 0,
//      "SrcFieldId": "fbillno",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fcustomerid",
//      "Name": "客户",
//      "MapType": 0,
//      "SrcFieldId": "fcustomerid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fcontacts",
//      "Name": "联系人",
//      "MapType": 0,
//      "SrcFieldId": "fcontacts",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fphone",
//      "Name": "联系电话",
//      "MapType": 0,
//      "SrcFieldId": "fphone",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "faddress",
//      "Name": "联系地址",
//      "MapType": 0,
//      "SrcFieldId": "faddress",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    }
//  ],
//  "BillGroups": [
//    {
//      "Id": "fsourcenumber",
//      "Order": 1
//    }
//  ],
//  "FieldGroups": [

//  ]
//}
