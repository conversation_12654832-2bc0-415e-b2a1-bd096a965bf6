{
  "base": "/mdl/ydj/ste/base_order.menu.json",
  "common": [
    {
      "id": "tbDeliveryTask",
      "caption": "生成发货任务",
      "visible": "true",
      "disabled": "true",
      "style": "menu",
      "order": 200,
      "parent": "",
      "opcode": "createscantask",
      "param": ""
    }
  ],
  "listmenu": [],
  "billmenu": [
    {
      "id": "tbChange",
      "caption": "变更",
      "visible": "false"
    },
    {
      "id": "tbSubmitChange",
      "caption": "提交变更",
      "visible": "false"
    },
    {
      "id": "tbPull",
      "caption": "选单",
      "visible": "true",
      "disabled": "false",
      "style": "button",
      "order": 7,
      "parent": "",
      "opcode": "pull",
      "param": "enablePushAllEntity:true",
      "group": "standard"
    },
    {
      "id": "tbPushService",
      "caption": "新增服务",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 198,
      "parent": "",
      "opcode": "pushservice",
      "param": "ruleId:'stk_sostockout2ydj_service'",
      "group": "standard"
    },
    //更多菜单组
    {
      "id": "tbQueryBarcode",
      "caption": "扫码记录",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 90,
      "parent": "droplist_more",
      "opcode": "querybarcode",
      "param": "",
      "group": "standard",
      "entityKey": ""
    },
    {
      "id": "tbPushVist",
      "caption": "订单回访",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 88,
      "parent": "droplist_more",
      "opcode": "push",
      "param": "ruleId:'stk_sostockout2ydj_vist'",
      "group": "standard"
    },
    //表体菜单
    {
      "id": "btnEntryCopy",
      "caption": "关联复制行",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 44,
      "parent": "",
      "opcode": "entrycopy",
      "param": "",
      "group": "standard",
      "result": true,
      "entityKey": "fentity"
    },
    {
      "id": "tbBarCodeQuery",
      "caption": "条码联查",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 105,
      "opcode": "isexsitbarcode",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "tbQueryInventory",
      "caption": "库存查询",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 105,
      "parent": "",
      "opcode": "queryinventory",
      "param": "listMode:'lookup'",
      "group": "standard",
      "entityKey": "fentity"
    }
  ]
}
