{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lockfield_sourcepush",
      "expression": "field:fmaterialid,funitid,fstockunitid,fcustomdesc,fattrinfo$|fsourceinterid!='' and fsourceinterid!=' '"
    },
    {
      "id": "lockfield_invflexfield",
      "expression": "field:fstorehouseid,fstorelocationid,fstockstatus,fmtono,flotno,fownertype,fownerid$|fenablebarcode==true"
    },
    {
      "id": "unlockfield_invflexfield",
      "expression": "field:$fstorehouseid,fstorelocationid,fstockstatus,fmtono,flotno,fownertype,fownerid|fenablebarcode==false"
    },
    //此规则表示：该商品对应的商品商品属性“允许定制”为true时放开，为false时，锁定
    {
      "id": "lock_fcustomdesc",
      "expression": "field:fcustomdesc$|fcustom!=true"
    },
    {
      "id": "unlock_fcustomdesc",
      "expression": "field:$fcustomdesc|fcustom==true"
    },
    //此规则表示：该商品对应的商品商品属性“允许选配”=true时，辅助属性放开，为false时，锁定
    {
      "id": "lock_fsel",
      "expression": "field:fattrinfo$|fispresetprop!=true"
    },
    {
      "id": "unlock_fsel",
      "expression": "field:$fattrinfo|fispresetprop==true"
    },
    {
      "id": "lock_fownerid",
      "expression": "field:fownerid$|fownertype=='' or fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_fownerid",
      "expression": "field:$fownerid|fownertype!='' and fstatus!='D' and fstatus!='E'"
    },
    //基本信息字段的锁定与解锁
    {
      "id": "lock_headpushfield",
      "expression": "field:fpostaffid,fpodeptid,fsupplierid,fsupplieraddr$|fsourcenumber!=''"
    },
    //基本信息字段的锁定与解锁
    {
      "id": "unlock_headpushfield",
      "expression": "field:$fpostaffid,fpodeptid,fsupplierid,fsupplieraddr|fsourcenumber==''"
    },
    //根据是否是有源单决定锁定部分字段
    {
      "id": "lock_headpushfield",
      "expression": "field:fmaterialid,fcustomdesc,fattrinfo,funitid,fstockunitid$|fpoorderno!=''"
    },
    {
      "id": "unlock_headpushfield",
      "expression": "field:$fmaterialid,fcustomdesc,fattrinfo,funitid,fstockunitid|fpoorderno==''"
    },
    {
      "id": "fstatus_",
      "expression": "menu:$tbSaveSubmit,tbSaveAudit|fstatus==''"
    },
    //保存并提交锁定锁定&解锁
    {
      "id": "lock_tbSaveSubmit",
      "expression": "menu:tbSaveSubmit|fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_tbSaveSubmit",
      "expression": "menu:$tbSaveSubmit|fstatus!='D' and fstatus!='E'"
    },
    {
      "id": "lock_tbEntryCopy",
      "expression": "menu:btnEntryCopy$|fstatus=='D' or fstatus=='E' or fcancelstatus==true"
    },
    {
      "id": "unlock_tbEntryCopy",
      "expression": "menu:$btnEntryCopy|fstatus!='D' and fstatus!='E' and fcancelstatus==false"
    },
    //保存并审核锁定锁定&解锁
    {
      "id": "lock_tbSaveAudit",
      "expression": "menu:tbSaveAudit$|fstatus=='E'"
    },
    {
      "id": "unlock_tbSaveAudit",
      "expression": "menu:$tbSaveAudit|fstatus!='E'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    //选择收货人信息，自动带出部门与手机号
    { "expression": "fstockdeptid=fstockstaffid__fdeptid" },
    //库存状态=仓库.默认库存状态
    { "expression": "fstockstatus=fstorehouseid__fstockid|fstorehouseid!=''" },
    { "expression": "fstockstaffphone=fstockstaffid__fphone" },
    { "expression": "fstockaddress=fstockstaffid__faddress" },
    //选择商品，携带单位与计价单位
    { "expression": "funitid=fmaterialid__funitid|fmaterialid=='' or 1==1" },
    //携带计价单位=基本单位
    { "expression": "fstockunitid=fmaterialid__fstockunitid|fmaterialid=='' or 1==1" },
    //设置实际数量=计划数量
    { "expression": "fbizqty=fbizplanqty|fmaterialid!='' and fbizunitid!='' and funitid!='' " },
    //计价数量变化时计算金额
    { "expression": "famount=fqty*fprice|fprice!='' or fqty!=''" },
    { "expression": "fprice=famount/fqty|fqty!=0 and fqty!=''" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fmaterialid)" },
    { "expression": "fmtrlimage=getImages(fmaterialid,fattrinfo,fcustomdesc)" },
    //单位成本或数量变化时计算成本
    { "expression": "fcostamt=fqty*fcostprice|fcostprice!='' or fqty!=''" },

    //选择采购员信息，自动带出部门
    { "expression": "fpodeptid=fpostaffid__fdeptid|fpostaffid=='' or 1==1" },
    { "expression": "fbizunitid=fmaterialid__fpurunitid" },
    { "expression": "fplanqty=fbizplanqty" },
    { "expression": "fbizqty=fbizplanqty" },
    { "expression": "fqty=fbizplanqty" },
    // 【金额】= 【采购单价】* 【实收数量】（基本单位实收数量）
    { "expression": "fpoamount=fqty*fpoprice|fpoprice!='' or fqty!=''" },
    //总体积
    { "expression": "ftotalvolume=fbizqty*fsinglevolume|fbizqty!='' and fsinglevolume!=''" },
    //单位体积
    { "expression": "fsinglevolume=ftotalvolume/fbizqty|fbizqty!='' and fbizqty!=0 and ftotalvolume!=''" }
  ]
}