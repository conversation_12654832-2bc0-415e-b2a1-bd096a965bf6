<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    库存单据基类模板
-->
<html lang="en">
<head>
</head>
<body id="base_stkbilltmpl" el="1" basemodel="bill_basetmpl" cn="库存单据模板" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="" pn="fbillhead" cn="单据头">

        <!--重写基类模型中的部分字段属性-->
        <input group="基本信息" el="108" ek="fbillhead" visible="-1" id="fbillno" fn="fbillno" cn="单据编号" lix="1" width="145" />


        <!--基本信息-->
        <input group="基本信息" el="106" ek="fbillhead" id="fdeliverywayid" fn="fdeliverywayid" pn="fdeliverywayid" visible="1150" cn="交货方式"
               lock="0" copy="1" lix="1030" notrace="true" ts="" refid="ydj_deliveryway" filter="" reflvt="0" dfld="" />

        <input group="基本信息" el="123" ek="fbillhead" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="单据类型" visible="1150" refid="bd_billtype" lix="10" width="90" must="1" />
        <input group="基本信息" el="112" ek="fbillhead" id="fdate" fn="fdate" cn="业务日期" visible="-1" defval="@currentshortdate" lix="20" width="105" />
        <input group="基本信息" el="106" ek="fbillhead" id="fstockstaffid" fn="fstockstaffid" pn="fstockstaffid" cn="仓管员" visible="1150" lix="30" refid="ydj_staff" must="1" defVal="@currentStaffId" />
        <input group="基本信息" el="106" ek="fbillhead" id="fstockdeptid" fn="fstockdeptid" pn="fstockdeptid" cn="仓管部门" visible="1150" lix="40" refid="ydj_dept" must="1" defVal="@currentDeptId" />
        <input group="基本信息" el="100" ek="FBillHead" id="fstockstaffphone" fn="fstockstaffphone" pn="fstockstaffphone" visible="1150" cn="仓管员联系方式"
               lock="0" copy="1" lix="41" notrace="true" ts="" />
        <input group="基本信息" el="100" ek="FBillHead" id="fstockaddress" fn="fstockaddress" pn="fstockaddress" visible="1150" cn="仓管员地址"
               lock="0" copy="1" lix="42" notrace="true" ts="" />

        <!--源单类型，源单编号-->
        <input group="基本信息" el="140" ek="fbillhead" visible="1150" id="fsourcetype" lix="50" />
        <input group="基本信息" el="141" ek="fbillhead" visible="1150" id="fsourcenumber" lix="60" />
        <input group="基本信息" el="165" ek="fbillhead" visible="0" id="fsourceinterid_h" fn="fsourceinterid" pn="fsourceinterid" cn="源单Id" lix="60" />
        <input group="基本信息" el="116" ek="FBillHead" id="fenablebarcode" fn="fenablebarcode" pn="fenablebarcode" visible="1150" cn="启用条码作业"
               lock="-1" copy="0" lix="70" notrace="true" ts="" />
        <input group="基本信息" el="116" ek="FBillHead" id="fautobcinstock" fn="fautobcinstock" pn="fautobcinstock" visible="-1" cn="打码收货后同步入库"
               lock="-1" copy="0" lix="60" notrace="true" ts="" />
    </div>

    <!--商品明细-->
    <table id="fentity" el="52" pk="fentryid" tn="" pn="fentity" cn="商品明细" kfks="fmaterialid" must="1">
        <tr>
            <th lix="100" el="106" ek="fentity" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="1150" cn="商品" width="160"
                lock="0" copy="1" notrace="true" ts="" refid="ydj_product" multsel="true" filter="" reflvt="0" dfld="fspecifica,fpackagtype" sformid="" must="1"></th>
            <th lix="105" el="107" ek="fentity" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0"></th>
            <th lix="110" el="107" ek="fentity" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="1150" cn="规格型号"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0"></th>
            <th lix="115" el="132" ek="fentity" id="fattrinfo" fn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid" pricefk="" width="160" visible="1150"></th>
            <th lix="120" ek="fentity" el="161" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="图片" ctlfk="fmaterialid" width="200" visible="1150"></th>
            <th lix="125" el="107" ek="fentity" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" dispfk="fbrandid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="130" el="107" ek="fentity" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" dispfk="fseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>

            <th ek="fentity" lix="12" el="107" id="fattribute" fn="fattribute" pn="fattribute" visible="0" cn="属性" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fattribute" refvt="0"></th>
            <th ek="fentity" lix="85" el="161" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="图片" ctlfk="fmaterialid" width="200" visible="1150"></th>

            <th lix="90" el="107" ek="fentity" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" dispfk="fbrandid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="95" el="107" ek="fentity" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" dispfk="fseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="96" el="107" ek="fentity" id="fsubseriesid" fn="fsubseriesid" cn="子系列" visible="1086" dispfk="fsubseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>


            <th el="132" ek="fentity" id="fattrinfo" fn="fattrinfo" cn="辅助属性" lix="80" ctlfk="fmaterialid" pricefk="" width="160" visible="1150"></th>
            <th el="100" ek="fentity" len="2000" id="fcustomdesc" fn="fcustomdesc" cn="定制说明" lix="90" width="160" visible="1150" xsslv="1"></th>
            <!--  <th lix="94" el="107" ek="fentity" id="fcustom" fn="fcustom" pn="fcustom" visible="1150" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>-->
            <th lix="94" el="107" ek="fentity" id="fcustom" fn="fcustom" pn="fcustom" visible="0" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>
            <!-- <th lix="94" el="107" ek="fentity" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="1150" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>-->
            <th lix="94" el="107" ek="fentity" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="0" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>
            <th el="109" ek="fentity" id="fbizunitid" fn="fbizunitid" cn="业务单位" lix="95" ctlfk="fmaterialid" refid="ydj_unit" sformid="" visible="1150" width="90" must="1"></th>
            <th el="103" ek="fentity" id="fbizplanqty" fn="fbizplanqty" cn="计划数量" ctlfk="fbizunitid" basqtyfk="fplanqty" lix="100" visible="1150" width="100" lock="0" must="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizqty" fn="fbizqty" cn="业务单位数量" lix="103" visible="1150" ctlfk="fbizunitid" basqtyfk="fqty" width="100" format="0,000.00"></th>


            <th el="109" ek="fentity" id="fstockunitid" fn="fstockunitid" cn="库存单位" lix="105" ctlfk="fmaterialid" refid="ydj_unit" sformid="" visible="1150" width="100" must="1" lock="-1"></th>
            <th el="103" ek="fentity" id="fstockqty" fn="fstockqty" cn="库存单位数量" lix="106" visible="1086" ctlfk="fstockunitid" basqtyfk="fqty" width="100" format="0,000.00" lock="-1"></th>

            <th el="109" ek="fentity" id="funitid" fn="funitid" cn="基本单位" lix="108" ctlfk="fmaterialid" refid="ydj_unit" sformid="" filter="fisbaseunit='1'" visible="1150" width="80" must="1" lock="-1"></th>
            <th el="103" ek="fentity" id="fplanqty" fn="fplanqty" cn="基本单位计划数量" ctlfk="funitid" lix="109" visible="1150" width="120" lock="0" must="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fqty" fn="fqty" cn="基本单位数量" lix="110" visible="1150" ctlfk="funitid" width="120" format="0,000.00" lock="-1"></th>


            <th el="104" ek="fentity" id="fprice" fn="fprice" pn="fprice" cn="单价" lix="135" width="70" lock="-1" visible="1086" format="0,000.000000" dformat="0,000.00"></th>
            <th el="105" ek="fentity" id="famount" fn="famount" pn="famount" cn="金额" lix="136" width="90" lock="-1" visible="1086" format="0,000.00"></th>

            <th el="106" ek="fentity" id="fstorehouseid" fn="fstorehouseid" cn="仓库" lix="140" refid="ydj_storehouse" sformid="" visible="1150" width="100" must="2" notrace="false" filter="fname!='直发仓库'"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <th el="153" ek="fentity" id="fstorelocationid" fn="fstorelocationid" cn="仓位" lix="150" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="1150" width="100" notrace="false"></th>
            <th el="106" ek="fentity" id="fstockstatus" fn="fstockstatus" cn="库存状态" lix="160" refid="ydj_stockstatus" defVal="'311858936800219137'" sformid="" visible="1150" width="100" must="2" lock="-1"></th>
            <th el="100" ek="fentity" id="flotno" fn="flotno" pn="flotno" cn="批号" lix="170" width="100" visible="1086" lock="-1"></th>
            <th el="100" ek="fentity" id="fmtono" fn="fmtono" pn="fmtono" cn="物流跟踪号" lix="180" width="100" visible="1086" lock="-1"></th>
            <th el="149" ek="fentity" id="fownertype" fn="fownertype" pn="fownertype" lix="190" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="1086" lock="0">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th el="150" ek="fentity" id="fownerid" fn="fownerid" pn="fownerid" lix="200" ctlfk="fownertype" cn="货主" width="100" visible="1086" lock="0"></th>

            <th el="100" ek="fentity" id="fentrynote" fn="fentrynote" pn="fentrynote" cn="备注" lix="500" len="255" visible="1150" width="160"></th>

            <th el="140" ek="fentity" id="fsourceformid" fn="fsourceformid" ts="" cn="来源单类型" visible="1086" copy="0" lix="220"></th>
            <th el="141" ek="fentity" id="fsourcebillno" fn="fsourcebillno" ts="" cn="来源单编号" ctlfk="fsourceformid" visible="1086" copy="0" lix="222"></th>
            <th el="100" ek="fentity" id="fsourceinterid" fn="fsourceinterid" ts="" cn="来源单内码" visible="0" copy="0" lix="224"></th>
            <th el="100" ek="fentity" id="fsourceentryid" fn="fsourceentryid" ts="" cn="来源单分录内码" visible="0" copy="0" lix="226"></th>

            <th lix="800" el="107" ek="fentity" visible="1086" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" cn="商品类别" ctlfk="fmaterialid" dispfk="fcategoryid" lock="-1"></th>

            <th id="fseltypeid" el="107" ek="fentity" fn="fseltypeid" ctlfk="fmaterialid" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="360"></th>

            <!--单位成本，总成本-->
            <th group="基本信息" el="105" ek="fentity" id="fcostprice" fn="fcostprice" pn="fcostprice" visible="1086" cn="单位成本(加权平均)" lock="-1" copy="0" lix="130" xlsin="0" notrace="true" ts="" roundType="0" format="0,000.00" />
            <th group="基本信息" el="105" ek="fentity" id="fcostamt" fn="fcostamt" pn="fcostamt" visible="1086" cn="总成本(加权平均)" lock="-1" copy="0" lix="130" notrace="true" xlsin="0" ts="" roundType="0" format="0,000.00" />

        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="querybarcode" op="querybarcode" opn="扫码记录" data="{'parameter':{'linkFormIdFieldKey':'fsourceformid','linkBillIdFieldKey':'fsourceinterid'}}" permid=""></ul>

        <ul el="10" id="save" op="save" opn="保存">
            <li id="stockinitcheck" el="11" vid="3000" cn="库存初始化及关账逻辑控制" data=""></li>
            <li id="stocklocationcheck" el="11" vid="3003" cn="仓库启用仓位时必录！" data="{'fieldKey':'fstorelocationid'}"></li>
            <li el="11" vid="510" ek="fentity" cn="货主字段不能为空!" data="{'expr':'(fownertype=\'\' and fownerid=\'\') or (fownertype!=\'\' and fownerid!=\'\')','message':'货主字段不能为空!'}"></li>
            <li el="11" vid="517" id="save_valid_sourcebill" cn="保存时校验源单明细行是否存在于源单中"
                data="{'sourceTypeFieldKey':'fsourceformid','sourceNoFieldKey':'fsourcebillno','sourceEntryIdFieldKey':'fsourceentryid'}" precon=""></li>
        </ul>

        <ul el="10" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="3000" cn="库存初始化及关账逻辑控制" data=""></li>
        </ul>

        <ul el="10" ek="fentity" id="queryinventory" op="queryinventory" opn="库存查询" permid="fw_queryinventory"
            data="{
                'parameter':{
                    'fieldMaps':{}
                }
            }"></ul>

    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="fw_queryinventory" cn="库存查询"></ul>
    </div>
</body>
</html>