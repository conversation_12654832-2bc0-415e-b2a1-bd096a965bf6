<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    初始库存单
    用于系统初始化时录入库存信息
-->
<html lang="en">
<head>
</head>
<body id="stk_initstockbill" el="1" basemodel="bill_basetmpl" cn="初始库存单" isolate="1" nfsa="true" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_initstockbill" pn="fbillhead" cn="单据头">

        <input group="基本信息" el="112" ek="FBillHead" id="FDate" fn="FDate" pn="FDate" visible="-1" cn="业务日期"
               lock="0" copy="1" lix="10" notrace="true" ts="" defval="@currentDate" />
        <input ek="FBillHead" lix="8" el="161" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="图片" ctlfk="fmaterialid" width="200" visible="1150" />

        <input group="基本信息" el="106" ek="fbillhead" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="-1" cn="商品"
               lock="0" copy="1" lix="15" notrace="true" ts="" refid="ydj_product" filter="" reflvt="0" dfld="fspecifica,fvolume,fgrossload,fpacksize" must="1" />
        <input group="基本信息" el="107" ek="fbillhead" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="-1" cn="商品编码"
               lock="-1" copy="1" lix="20" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0" />
        <input group="基本信息" el="107" ek="fbillhead" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="-1" cn="规格型号"
               lock="-1" copy="1" lix="25" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0" />

        <input group="基本信息" el="132" ek="fbillhead" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" visible="-1" cn="辅助属性"
               lock="0" copy="1" lix="30" notrace="true" ts="" ctlfk="fmaterialid" pricefk="" />
        <input group="基本信息" el="100" ek="fbillhead" len="2000" id="fcustomdesc" fn="fcustomdesc" pn="fcustomdesc" visible="-1" cn="定制说明"
               lock="0" copy="1" lix="1010" notrace="true" ts="" />
        <input group="基本信息" el="106" ek="fbillhead" id="fstorehouseid" fn="fstorehouseid" pn="fstorehouseid" visible="-1" cn="仓库"
               lock="0" copy="1" lix="80" notrace="true" ts="" refid="ydj_storehouse" filter="" reflvt="0" dfld="" must="1" />
        <input group="基本信息" el="153" ek="fbillhead" id="fstorelocationid" fn="fstorelocationid" pn="fstorelocationid" visible="-1" cn="仓位"
               lock="0" copy="1" lix="90" notrace="true" ts="" ctlfk="fstorehouseid" luek="fentity" lunbfk="flocnumber" lunmfk="flocname" />
        <input group="基本信息" el="106" ek="fbillhead" id="fstockstatus" fn="fstockstatus" pn="fstockstatus" visible="-1" cn="库存状态"
               lock="-1" copy="1" lix="100" notrace="true" ts="" refid="ydj_stockstatus" defVal="'311858936800219137'" filter="" reflvt="0" dfld="" must="1" />
        <input group="基本信息" el="100" ek="fbillhead" id="flotno" fn="flotno" pn="flotno" visible="1150" cn="批号"
               lock="0" copy="1" lix="1220" notrace="true" ts="" />
        <input group="基本信息" el="100" ek="fbillhead" id="fmtono" fn="fmtono" pn="fmtono" visible="1150" cn="物流跟踪号"
               lock="0" copy="1" lix="1210" notrace="true" ts="" />
        <div group="基本信息" el="149" ek="fbillhead" id="fownertype" fn="fownertype" pn="fownertype" visible="1150" cn="货主类型"
             lock="0" copy="1" lix="1200" notrace="true" ts="" dataviewname="v_bd_ownerdata">
            <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
            <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
            <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
        </div>
        <input group="基本信息" el="150" ek="fbillhead" id="fownerid" fn="fownerid" pn="fownerid" visible="-1" cn="货主"
               lock="0" copy="1" lix="1050" notrace="true" ts="" ctlfk="fownertype" filter="" />

        <input group="基本信息" el="112" ek="FBillHead" id="finstockdate" fn="finstockdate" pn="finstockdate" visible="-1" cn="入库日期"
               lock="0" copy="1" lix="70" notrace="true" ts="" />

        <input group="基本信息" el="109" ek="fbillhead" id="fstockunitid" fn="fstockunitid" pn="fstockunitid" visible="-1" cn="库存单位"
               lock="0" copy="1" lix="50" notrace="true" ts="" ctlfk="fmaterialid" refid="ydj_unit" filter="" reflvt="0" dfld="" must="1" />
        <input group="基本信息" el="103" ek="fbillhead" id="fstockqty" fn="fstockqty" pn="fstockqty" visible="-1" cn="数量"
               lock="0" copy="1" lix="60" notrace="true" ts="" ctlfk="fstockunitid" basqtyfk="fqty" roundType="0" format="0,000.00" />

        <input group="基本信息" el="109" ek="fbillhead" id="funitid" fn="funitid" pn="funitid" visible="-1" cn="基本单位"
               lock="-1" copy="1" lix="40" notrace="true" ts="" ctlfk="fmaterialid" refid="ydj_unit" filter="" reflvt="0" dfld="" must="1" />
        <input group="基本信息" el="103" ek="fbillhead" id="fqty" fn="fqty" pn="fqty" visible="-1" cn="基本单位数量"
               lock="-1" copy="1" lix="1060" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" />

        <input group="基本信息" el="104" ek="FBillHead" id="fprice" fn="fprice" pn="fprice" visible="1150" cn="单价"
               lock="0" copy="1" lix="1000" notrace="true" ts="" roundType="0" format="0,000.000000" dformat="0,000.00" />

        <input group="基本信息" el="105" ek="fbillhead" id="famount" fn="famount" pn="famount" visible="0" cn="金额"
               lock="-1" copy="1" lix="1070" notrace="true" ts="" roundType="0" format="0,000.00" />

        <input group="基本信息" id="fmainorgid" el="148" ek="fbillhead" fn="fmainorgid" pn="fmainorgid" cn="企业主体" xlsin="0" visible="0" copy="0" lix="1080" />
        <input group="基本信息" id="ftranid" el="100" ek="FBillHead" fn="ftranid" ts="" cn="交易流水号" visible="0" xlsin="0" copy="0" lix="1090" />

        <input group="基本信息" el="116" ek="fbillhead" id="fdostandard" fn="fdostandard" pn="fdostandard" visible="0" cn="已转标准品"
               lock="0" copy="1" lix="0" notrace="true" ts="" defval="false" />

    </div>

    <!--条码明细-->
    <!--<table id="fentity" el="52" pk="fentryid" tn="t_stk_initstockbillentry" pn="fentity" cn="库存序列号明细" kfks="fbarcode">
        <tr>
            <th el="100" ek="fentity" id="fbarcode" fn="fbarcode" pn="fbarcode" visible="-1" cn="条码"
                lock="0" copy="0" lix="100" notrace="true" ts="" width="160"></th>

            <th el="100" ek="fentity" id="fserialno" fn="fserialno" pn="fserialno" cn="序列号" len="50" visible="-1" lix="105" width="160"></th>

            <th el="101" ek="fentity" id="fpackageindex" fn="fpackageindex" pn="fpackageindex" visible="-1" cn="包件序号"
                lock="0" copy="0" lix="110" notrace="true" ts="" format="0,000"></th>

            <th el="101" ek="fentity" id="fpackagecount" fn="fpackagecount" pn="fpackagecount" visible="-1" cn="包件总数"
                lock="0" copy="0" lix="115" notrace="true" ts="" format="0,000"></th>

            <th el="100" ek="fentity" id="flinkentryid" fn="flinkentryid" pn="flinkentryid" visible="0" cn="关联实体内码"
                lock="-1" copy="0" lix="0" notrace="true" ts=""></th>
            <th el="100" ek="fentity" id="flinkentitykey" fn="flinkentitykey" pn="flinkentitykey" visible="0" cn="关联实体标识"
                lock="-1" copy="0" lix="0" notrace="true" ts=""></th>
        </tr>
    </table>-->

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" cn="货主类型不为空时，货主字段必录！"
                data="{'expr':'(fownertype!=\'\' and fownertype!=\' \' and fownerid!=\' \' and fownerid!=\'\') or ((fownertype==\'\' or fownertype==\' \') and (fownerid==\' \' or fownerid==\'\'))','message':'货主类型不为空时，货主字段必录！'}"></li>

            <li id="stocklocationcheck" el="11" vid="3003" cn="仓库启用仓位时必录！" data="{'fieldKey':'fstorelocationid'}"></li>

            <li el="11" vid="510" ek="fbillhead" cn="单价必须大于0" data="{'expr':'fprice>=0 ','message':'单价不允许为负数！'}"></li>
            <li el="11" vid="510" ek="fbillhead" cn="金额必须大于0" data="{'expr':'famount>=0 ','message':'金额不允许为负数！'}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="17" sid="2000" cn="审核时更新库存" data="{'factor':1,'activeEntityKey':'fbillhead',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockQty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus=\'c\''}"></li>

            

        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="17" sid="2000" cn="反审核时更新库存" data="{'factor':0,'activeEntityKey':'fbillhead',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockQty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus!=\'c\''}"></li>

            
        </ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="17" sid="2000" cn="删除时更新库存" data="{'factor':0,'activeEntityKey':'fbillhead',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockQty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus!=\'c\''}"></li>

        </ul>
    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <!--<ul el="12" id="fw_view" cn="查看"></ul>
        <ul el="12" id="fw_export" cn="导出"></ul>-->
    </div>

    <div id="ListFuzzyFlds" cn="默认支持快捷过滤的字段列表">
        <ul el="14" id="fw_fuzzyfld" fldkeys="fmaterialid.fname,fmtrlnumber,fmtrlmodel,fmtono" cn="默认支持快捷过滤的字段列表，多个用逗号或分号隔开"></ul>
    </div>
</body>
</html>