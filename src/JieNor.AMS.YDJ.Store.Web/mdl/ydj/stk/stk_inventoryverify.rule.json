{
  //规则引擎基类
  "base": "/mdl/ydj/stk/base_stkbilltmpl.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //提交或者审核后，不允许增加盘点明细
    {
      "id": "lock_allfield",
      "expression": "menu:tbInventRange|fstatus=='D' or fstatus=='E'"
    },
    //此项规则表示：单据状态='' 时(新增的时候)，所有字段可用，只有 新增 保存 选择盘点数据操作可用，其他操作都不可用
    {
      "id": "fstatus_",
      "expression": "field:$*;menu:*$tbNew,tbSave,tbInventRange,btnUnStandardCustom,btnStandardCustom|fstatus==''"
    },
    {
      "id": "lock_tbCountTask",
      "expression": "menu:tbCountTask|fstatus!='D' or fcancelstatus=='0'"
    },
    {
      "id": "unlock_tbCountTask",
      "expression": "menu:$tbCountTask|fstatus=='D' and fcancelstatus!='0'"
    },
    {
      "id": "unlock_tbPyPack",
      "expression": "menu:$tbPyPack|fstatus=='E'"
    },
    {
      "id": "lock_tbPyPack",
      "expression": "menu:tbPyPack$|fstatus!='E'"
    },
    {
      "id": "unlock_tbZcPack",
      "expression": "menu:$tbZcPack|fstatus=='D'"
    },
    {
      "id": "lock_tbZcPack",
      "expression": "menu:tbZcPack$|fstatus!='D'"
    },
    {
      "id": "unlock_fprice",
      "expression": "field:$fprice,famount| fstatus!='' or fstatus==''"
    },

    //期初盘点单，【更新余额】按钮才可用
    {
      "id": "lock_updatebalmenu",
      "expression": "menu:$btnupdateinistkbal|fbilltype!='inventoryverify_billtype_02' "
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    //带出商品是否非标
    { "expression": "funstdtype=fmaterialid__funstdtype|fmaterialid!='' or 1==1" },
    //选择盘点员带出盘点部门
    { "expression": "fstockdeptid=fstockstaffid__fdeptid|fstockstaffid=='' or 1==1" },

    //1、盘盈数量=盘点数量-账存数量，条件是：盘点数量>账存数量
    { "expression": "fpyqty=fpdqty-fqty|fpdqty>=fqty" },
    //如果一行是盘盈数量，盘亏数量为零
    { "expression": "fpkqty=0|fpdqty>=fqty" },

    //2、盘亏数量=账存数量-盘点数量，条件是：盘点数量<账存数量
    { "expression": "fpkqty=fqty-fpdqty|fpdqty<=fqty" },
    //如果一行是盘亏数量，盘盈数量为零
    { "expression": "fpyqty=0|fpdqty<=fqty" },

    //3、盘点误差=（账存数量-盘点数量）/账存数量
    { "expression": "fpderror=(fqty-fpdqty)/fqty*100|fqty!=0" },

    //账存金额、基本单位账存单价计算
    { "expression": "famount=fqty*fprice|fprice!='' or fqty!=''" },
    { "expression": "fprice=famount/fqty|fqty!=0 and fqty!=''" },

    //账存单价
    { "expression": "fbizprice=famount/fbizqty|fbizqty!=0 and fbizqty!=''" },

    //盘点金额、基本单位盘点单价计算
    { "expression": "fpdamount=fpdqty*fpdprice|fpdprice!='' or fpdqty!=''" },
    { "expression": "fpdprice=fpdamount/fpdqty|fpdqty!=0 and fpdqty!=''" },

    //盘点单价
    { "expression": "fbizpdprice=fpdamount/fbizpdqty|fbizpdqty!=0 and fbizpdqty!=''" },

    //盘赢金额、盘亏金额
    { "expression": "fpyamount=fpyqty*fpdprice|fpdprice!='' or fpyqty!=''" },
    { "expression": "fpkamount=fpkqty*fpdprice|fpdprice!='' or fpkqty!=''" },
    //盈亏采购总额
    { "expression": "fpkbuyamount=fbugunitprice*(fbizpyqty-fbizpkqty)" },
    //盈亏零售总额
    { "expression": "fpksaleamount=funifyamount*(fbizpyqty-fbizpkqty)" },
    //总成本,
    //{ "expression": "fcostamt=fcostprice*fbizpdqty" }
    //总体积
    { "expression": "ftotalvolume=fbizpdqty*fsinglevolume|fbizpdqty!='' and fsinglevolume!=''" },
    //单位体积
    { "expression": "fsinglevolume=ftotalvolume/fbizpdqty|fbizpdqty!='' and fbizpdqty!=0 and ftotalvolume!=''" }
  ]
}