{
  "Id": "stk_inventorytransfer2stk_inventorytransfer",
  "Number": "stk_inventorytransfer2stk_inventorytransfer",
  "Name": "库存调拨生成反向调拨",
  "SourceFormId": "stk_inventorytransfer",
  "TargetFormId": "stk_inventorytransfer",
  "ActiveEntityKey": "fentity",
  "FilterString": "ftransferdirection='0' and (fqty-ftransferbackqty)>0 and fstatus='E' and fsourcetype!='ydj_order'",
  "Message": "反向调拨失败：<br>1、只有调拨方向为正常且未反向调拨完成的才可以进行反向调拨！<br>2、调拨单必须是已审核！<br>3、源单类型为【销售合同】，不允许反向调拨！",
  "FieldMappings": [
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 0,
      "SrcFieldId": "fbilltype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftransfertype",
      "Name": "调拨类型",
      "MapType": 0,
      "SrcFieldId": "ftransfertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftransferdirection",
      "Name": "调拨方向",
      "MapType": 1,
      "SrcFieldId": "'1'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "调拨日期",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffid",
      "Name": "发货人",
      "MapType": 0,
      "SrcFieldId": "fstockstaffidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptid",
      "Name": "发货部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffidto",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "fstockstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptidto",
      "Name": "收货部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliverywayid",
      "Name": "货运方式",
      "MapType": 0,
      "SrcFieldId": "fdeliverywayid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_inventorytransfer'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid_h",
      "Name": "调出仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseidto_h",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseidto_h",
      "Name": "调入仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid_h",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //表体商品明细字段映射
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "调入定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcallupcustomdescto",
      "Name": "调出定制说明",
      "MapType": 0,
      "SrcFieldId": "fcallupcustomdescto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位库存数量",
      "MapType": 0,
      "SrcFieldId": "fplanqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "基本单位调拨数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 0,
      "SrcFieldId": "famount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "调出仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationid",
      "Name": "调出仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatus",
      "Name": "调出库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatusto",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fstorehouseidto",
      "Name": "调入仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationidto",
      "Name": "调入仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatusto",
      "Name": "调入库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "flotno",
      "Name": "批号",
      "MapType": 0,
      "SrcFieldId": "flotno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtonoto",
      "Name": "调入物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "调出物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtonoto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_inventorytransfer'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "调出辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfoto",
      "Name": "调入辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfoto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstore",
      "Name": "调出门店名称",
      "MapType": 0,
      "SrcFieldId": "fstoreto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstoreto",
      "Name": "调入门店名称",
      "MapType": 0,
      "SrcFieldId": "fstore",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillhead_fid",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}