{
  "Id": "pur_returnnotice2stk_postockreturn",
  "Number": "pur_returnnotice2stk_postockreturn",
  "Name": "采购退货通知生成采购退货单",
  "SourceFormId": "pur_returnnotice",
  "TargetFormId": "stk_postockreturn",
  "ActiveEntityKey": "fentity",
  "FilterString": "fqty>freturnqty and fstatus='E'",
  "Message": "退货失败：<br>1、采购退货通知必须是已审核状态！<br>2、选择的退货明细必须满足：退货通知数量>已退货数量（有货可退）！",
  "FieldMappings": [
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'postockreturn_billtype_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "退货日期",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffid",
      "Name": "仓管员",
      "MapType": 0,
      "SrcFieldId": "fstockstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptid",
      "Name": "仓管部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsupplierid",
      "Name": "供应商",
      "MapType": 0,
      "SrcFieldId": "fsupplierid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpostaffid",
      "Name": "采购员",
      "MapType": 0,
      "SrcFieldId": "fpostaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpodeptid",
      "Name": "采购部门",
      "MapType": 0,
      "SrcFieldId": "fpodeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsupplieraddr",
      "Name": "供方地址",
      "MapType": 0,
      "SrcFieldId": "flinkaddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freturntype",
      "Name": "退货类型",
      "MapType": 0,
      "SrcFieldId": "freturntype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanreturnamount",
      "Name": "应退货款金额",
      "MapType": 0,
      "SrcFieldId": "fplanreturnamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "factualreturnamount",
      "Name": "实退货款金额",
      "MapType": 0,
      "SrcFieldId": "factualreturnamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeductamount",
      "Name": "扣罚金额",
      "MapType": 0,
      "SrcFieldId": "fdeductamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freturnreason",
      "Name": "退货原因",
      "MapType": 0,
      "SrcFieldId": "freturnreason",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliverywayid",
      "Name": "货运方式",
      "MapType": 0,
      "SrcFieldId": "fdeliverywayid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliverybillno",
      "Name": "物流单号",
      "MapType": 0,
      "SrcFieldId": "fshippingbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'pur_returnnotice'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //表体商品明细字段映射
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "采购单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位应退数量",
      "MapType": 1,
      "SrcFieldId": "fqty-freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "基本单位实收数量",
      "MapType": 1,
      "SrcFieldId": "fqty-freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 1,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-freturnqty)*fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationid",
      "Name": "仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatus",
      "Name": "库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "flotno",
      "Name": "批号",
      "MapType": 0,
      "SrcFieldId": "flotno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fentrynote",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoorderno",
      "Name": "采购订单编号",
      "MapType": 0,
      "SrcFieldId": "fpoorderno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoorderinterid",
      "Name": "采购订单内码",
      "MapType": 0,
      "SrcFieldId": "fpoorderinterid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoorderentryid",
      "Name": "采购订单分录内码",
      "MapType": 0,
      "SrcFieldId": "fpoorderentryid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoinstockno",
      "Name": "采购入库单编号",
      "MapType": 0,
      "SrcFieldId": "fsourcebillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoinstockinterid",
      "Name": "采购入库单内码",
      "MapType": 0,
      "SrcFieldId": "fsourceinterid_h",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoinstockentryid",
      "Name": "采购入库单分录内码",
      "MapType": 0,
      "SrcFieldId": "fsourceentryid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'pur_returnnotice'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderdate",
      "Name": "订单日期",
      "MapType": 0,
      "SrcFieldId": "forderdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderqty",
      "Name": "基本单位订单数量",
      "MapType": 0,
      "SrcFieldId": "forderqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

  ],
  "BillGroups": [
    {
      "Id": "fpoorderno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}