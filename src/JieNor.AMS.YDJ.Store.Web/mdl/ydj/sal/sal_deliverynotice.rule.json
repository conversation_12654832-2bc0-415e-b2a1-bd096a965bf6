{
  //规则引擎基类
  "base": "/mdl/ydj/tpl/ydj_logisticnoticetpl.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lockheadfield_bypush",
      "expression": "field:fsostaffid,fsodeptid,fcustomerid,fprovince,fcity,fregion$|fsourcenumber!='' and fsourcenumber!=' '"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "fprovince=fcustomerid__fprovince" },
    { "expression": "fcity=fcustomerid__fcity" },
    { "expression": "fregion=fcustomerid__fregion" },
    { "expression": "flinkstaffid=fcustomerid__fcontacts" },
    { "expression": "flinkmobile=fcustomerid__fphone" },
    { "expression": "flinkaddress=fcustomerid__faddress" },
    { "expression": "fsodeptid=fsostaffid__fdeptid" },
    { "expression": "fbizunitid=fmaterialid__fsalunitid" },
    { "expression": "fenablebarcode=getBillTypeParam(fbilltype,'fenablebarcode')" },
    { "expression": "fautobcoutstock=getBillTypeParam(fbilltype,'fautobcoutstock')" }
  ]
}