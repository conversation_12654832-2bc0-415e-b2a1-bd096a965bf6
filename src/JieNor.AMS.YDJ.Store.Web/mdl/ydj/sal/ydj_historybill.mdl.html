<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
</head>
<body id="ydj_historybill" basemodel="bill_basetmpl" el="1" cn="历史单据" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_historybill" pn="fbillhead" cn="历史单据">
        <!--重写基类模型中的部分字段属性-->
        <input lix="1" id="fbillno" el="108" cn="单据编号" visible="-1" width="115" align="center" frozen="1" />
        <select lix="13" id="fstatus" el="122" visible="0"></select>
        <input lix="1" id="fdescription" el="100" visible="0"/>

        <!--基本信息-->
        <input lix="1" group="基本信息" el="108" ek="fbillhead" visible="-1" id="fhistoryno" fn="fhistoryno" pn="fhistoryno" cn="历史单据编号" copy="0" />
        <select group="基本信息" type="text" el="152" ek="fbillhead" visible="-1" id="fbiztype" fn="fbiztype" pn="fbiztype" cn="业务对象" vals="'1':'销售合同','2':'服务单'" defval="'1'" lix="2" width="90" must="1" defls="true"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fdept" fn="fdept" pn="fdept" cn="销售部门" lix="3" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fstorename" fn="fstorename" pn="fstorename" cn="门店名称" lix="4" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fstaff" fn="fstaff" pn="fstaff" cn="销售员" lix="5" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fmastername" fn="fmastername" pn="fmastername" cn="服务员" lix="6" copy="0" defls="true"/>
        <input group="基本信息" el="112" type="datetime" id="fbilldate" ek="fbillhead" fn="fbilldate" ts="" cn="业务日期" visible="-1" lix="7" />
        <input lix="8" group="基本信息" el="112" ek="fbillhead" visible="-1" id="fdeliverydate" fn="fdeliverydate" ts="" cn="送货日期" copy="0" width="90" format="yyyy-MM-dd" canchange="true" />
        <input lix="9" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fcustomername" fn="fcustomername" pn="fcustomername" cn="客户名称" ctlfk="fcustomerid" copy="0" width="85" canchange="true" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" lix="2" />
        <input lix="10" group="基本信息" el="100" ek="fbillhead" visible="0" id="fphone" fn="fphone" pn="fphone" cn="手机号" width="80" copy="0" canchange="true" notrace="false" defls="true"/>
        <input lix="10" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fphone_e" fn="" pn="fphone_e" cn="手机号" width="80" copy="0" canchange="true" defls="true"/>
        <input group="基本信息" el="100" ek="fbillhead" id="faddress" fn="faddress" pn="faddress" cn="地址" visible="-1" lix="11" />

        <input group="基本信息" el="105" ek="fbillhead" visible="-1" id="fsalamount" fn="fsalamount" pn="fsalamount" cn="销售金额" lix="12" defls="true"/>
        <input group="基本信息" el="100" type="text" id="fdescription1" fn="fdescription1" cn="备注1" visible="-1" len="1000" width="280" lix="400" />
        <input group="基本信息" el="100" type="text" id="fdescription2" fn="fdescription2" cn="备注2" visible="-1" len="1000" width="280" lix="401" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="ffirstordertime" fn="ffirstordertime" pn="ffirstordertime" cn="新系统初次成单时间" lix="20" width="105" copy="0" lock="-1" notrace="false" ts="" sbm="true" />
        <input lix="252" id="fmodifierid" el="107" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fmodifierid" pn="fmodifierid" cn="更新人" visible="0" copy="0" />
        <input lix="253" type="datetime" id="fmodifydate" el="113" ek="fbillhead" fn="fmodifydate" pn="fmodifydate" cn="更新时间" visible="0" copy="0" xlsin="0" />


        <table id="fentry" el="52" pk="fentryid" tn="t_ydj_historybillentry" pn="fentry" cn="商品明细" kfks="fmaterialname">
            <tr>
                <th lix="150" ek="fentry" el="101" id="fseq_e" fn="fseq_e" pn="fseq_e" visible="0" cn="顺序" copy="0" width="200" lock="-1"></th>
                <th lix="200" el="100" ek="fentry" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="-1" cn="商品编码" copy="0"></th>
                <th lix="201" ek="fentry" el="100" id="fmaterialname" fn="fmaterialname" cn="商品" visible="-1" width="160" copy="0" frozen="1">商品</th>
                <th lix="202" el="100" ek="fentry" id="fbrandname" fn="fbrandname" cn="品牌" visible="-1" width="100"></th>
                <th lix="202" el="100" ek="fentry" id="fseriesname" fn="fseriesname" cn="系列" visible="-1" width="100"></th>
                <th lix="203" el="100" ek="fentry" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="-1" cn="规格型号" copy="1"></th>
                <th lix="204" ek="fentry" el="100" id="fmaterialtype" fn="fmaterialtype" pn="fmaterialtype" cn="商品类别" width="140" lock="0" copy="0" visible="-1"></th>
                <th lix="205" ek="fentry" el="100" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" width="140" lock="0" visible="-1"></th>
                <th lix="206" ek="fentry" el="100" len="2000" id="fcustomdes_e" fn="fcustomdes_e" pn="fcustomdes_e" cn="定制说明" width="200" visible="-1" canchange="true"></th>
                <th lix="207" el="103" ek="fentry" id="fqty" fn="fqty" pn="fqty" cn="销售数量" format="0,000.00" defval="1" width="80" visible="-1" canchange="true"></th>
                <th lix="208" el="100" ek="fentry" id="funit" fn="funit" visible="-1" cn="销售单位" width="100"></th>

                <th lix="209" el="104" ek="fentry" id="fstandardprice" fn="fstandardprice" pn="fstandardprice" cn="标准价格" width="90" copy="0" visible="-1" format="0,000.00" dformat="0,000.00" canchange="true"></th>
                <th lix="210" el="104" ek="fentry" id="fdealprice" fn="fdealprice" pn="fdealprice" cn="成交单价" width="90" visible="-1" format="0,000.00" dformat="0,000.00" apipn="dealPrice" canchange="true" notrace="false"></th>
                <th lix="211" el="105" ek="fentry" id="fdealamount" fn="fdealamount" pn="fdealamount" cn="成交金额" width="90" visible="-1" format="0,000.00" apipn="dealAmount" canchange="true" notrace="false"></th>
                <th lix="212" ek="fentry" el="100" id="fsuitdescription" fn="fsuitdescription" pn="fsuitdescription" cn="套件说明" width="140" copy="0" lock="0" visible="-1"></th>
                <th lix="280" ek="fentry" el="100" id="fnote" fn="fnote" pn="fnote" cn="备注" visible="-1" width="160" len="1000" copy="0" canchange="true"></th>
            </tr>
        </table>
    </div>
</body>
</html>