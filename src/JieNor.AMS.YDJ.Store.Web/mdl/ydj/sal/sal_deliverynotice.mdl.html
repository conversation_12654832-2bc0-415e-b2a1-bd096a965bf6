<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="sal_deliverynotice" basemodel="ydj_logisticnoticetpl" el="1" cn="销售发货通知单" approvalflow="true" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_sal_deliverynotice" pn="fbillhead" cn="销售发货通知单">

        <!--重写基类模型中的部分字段属性-->
        <input id="fbillno" el="108" visible="-1" lix="1" width="115" align="center" />
        <input id="fcreatorid" el="118" visible="1062" lix="9" width="80" />
        <input id="fcreatedate" el="119" visible="1062" lix="10" width="125" />
        <input id="fdescription" el="100" visible="1062" />

        <input el="106" id="fstockdeptid" cn="发货部门" visible="1150" lix="5" must="1" />
        <input el="100" id="fstockaddress" cn="发货人地址" visible="1150" lix="15" />

        <!-- 相关信息 -->

        <input el="100" id="flinkstaffid" cn="收货人" visible="-1" lix="1" />
        <input el="112" id="fdate" cn="发货日期" visible="-1" copy="0" lix="5" must="1" />
        <input group="基本信息" el="106" ek="fbillhead" id="fcustomerid" lix="10" fn="fcustomerid" pn="fcustomerid" cn="客户" visible="-1" refid="ydj_customer" must="1" />
        <input el="100" id="flinkmobile" cn="收货人电话" visible="-1" lix="15" />
        <input el="100" id="flinkaddress" cn="收货人地址" visible="-1" lix="20" />
        <input group="基本信息" el="106" ek="fbillhead" id="fsostaffid" lix="25" fn="fsostaffid" pn="fsostaffid" cn="销售员" visible="-1" refid="ydj_staff" />
        <input group="基本信息" el="106" ek="fbillhead" id="fsodeptid" lix="30" fn="fsodeptid" pn="fsodeptid" cn="销售部门" visible="-1" refid="ydj_dept" />
        <input el="106" id="fstockstaffid" cn="发货人" visible="-1" lix="35" must="1" />
        <input el="100" id="fstockstaffphone" cn="发货人电话" visible="-1" lix="40" />
        <input group="基本信息" el="141" ek="fbillhead" visible="-1" id="fsourcenumber" cn="源单编号" lix="45" />

        <input group="物流信息" el="100" ek="FBillHead" id="fcardno" fn="fcardno" pn="fcardno" visible="-1" cn="车牌号"
               lock="0" copy="1" lix="50" notrace="true" ts="" />
        <input group="物流信息" el="100" ek="FBillHead" id="fdrivername" fn="fdrivername" pn="fdrivername" visible="-1" cn="司机姓名"
               lock="0" copy="1" lix="55" notrace="true" ts="" />
        <input group="物流信息" el="101" ek="fbillhead" id="ftotalpackageqty" fn="ftotalpackageqty" pn="ftotalpackageqty" visible="-1" cn="总件数"
               lock="-1" copy="1" lix="60" notrace="true" ts="" roundType="0" format="0,000.00" />
        <input group="物流信息" el="102" ek="fbillhead" id="ftotalcubeqty" fn="ftotalcubeqty" pn="ftotalcubeqty" visible="-1" cn="总立方数()"
               lock="-1" copy="1" lix="65" notrace="true" ts="" roundType="0" format="0,000.000" />
        <input group="物流信息" el="102" ek="FBillHead" id="ftotalgrossload" fn="ftotalgrossload" pn="ftotalgrossload" visible="-1" cn="总重量(kg)"
               lock="-1" copy="1" lix="70" notrace="true" ts="" roundType="0" format="0,000.00" />

        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="0" lix="27"></select>
        <input group="物流信息" el="106" ek="fbillhead" visible="1150" id="fcarrierid" fn="fcarrierid" pn="fcarrierid" cn="承运公司" lix="61" refid="ydj_supplier" filter="ftype='suppliertype_03'" />
        <input group="物流信息" el="100" ek="fbillhead" visible="1150" id="fshippingbillno" fn="fshippingbillno" pn="fshippingbillno" cn="物流单号" lix="62" />
        <input group="物流信息" el="117" ek="FBillHead" id="fdeliverstatus" fn="fdeliverstatus" pn="fdeliverstatus" visible="1150" cn="物流状态"
               lock="0" copy="1" lix="71" notrace="true" ts="" refid="bd_enum" cg="物流状态" dfld="fenumitem" />
        <input group="物流信息" el="117" ek="FBillHead" id="fcardtype" fn="fcardtype" pn="fcardtype" visible="1150" cn="承运车型"
               lock="0" copy="1" lix="71" notrace="true" ts="" refid="bd_enum" cg="承运车型" dfld="fenumitem" />
        <input group="物流信息" el="102" ek="FBillHead" id="fchargeamount" fn="fchargeamount" pn="fchargeamount" visible="1150" cn="运费"
               lock="0" copy="1" lix="76" notrace="true" ts="" roundType="0" format="0,000.00" />
        <input group="基本信息" el="106" ek="fbillhead" id="fdeliverywayid" fn="fdeliverywayid" pn="fdeliverywayid" visible="0" cn="交货方式"
               lock="0" copy="1" lix="58" notrace="true" ts="" refid="ydj_deliveryway" filter="" reflvt="0" dfld="" />
        <input group="物流信息" el="117" ek="FBillHead" id="fcarriertype" fn="fcarriertype" pn="fcarriertype" visible="1150" cn="承运类型"
               lock="0" copy="1" lix="60" notrace="true" ts="" refid="bd_enum" cg="承运类型" dfld="fenumitem" />
        <input el="140" ek="fbillhead" id="fsourcetype" visible="1150" />
        <input el="141" ek="fbillhead" id="fsourcenumber" visible="1150" />
        <input el="100" ek="fbillhead" visible="1150" id="fsourcebilltype" fn="fsourcebilltype" pn="fsourcebilltype" cn="源单业务类型" lock="-1" />

        <!--基本信息-->
        <select group="基本信息" el="123" ek="fbillhead" visible="1150" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="单据类型" refid="bd_billtype" lix="27" width="90" apipn="billType" must="1"></select>
        <input group="排程信息" el="100" ek="fbillhead" id="fschedulebillno" fn="fschedulebillno" pn="fschedulebillno" visible="1150" cn="排程编号"
               lock="-1" copy="0" lix="10" notrace="true" ts="" />
        <input group="基本信息" el="122" ek="fbillhead" visible="1150" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="64" />
        <input group="基本信息" el="122" ek="fbillhead" visible="1150" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="65" />
        <input group="基本信息" el="122" ek="fbillhead" visible="1150" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem" lix="66" />

        <input group="基本信息" el="116" ek="FBillHead" id="fautobcoutstock" fn="fautobcoutstock" pn="fautobcoutstock" visible="1150" cn="发货备码后同步出库"
               lock="-1" copy="0" lix="68" notrace="true" ts="" />

        <input group="基本信息" el="152" ek="FBillHead" id="fscanstatus" fn="fscanstatus" pn="fscanstatus" visible="1150" cn="扫描状态"
               lock="-1" copy="0" lix="90" notrace="true" ts="" vals="'1':'待备码发货','2':'待核验出库','3':'已出库','4':'处理中','5':'出库失败'" />

        <input group="基本信息" el="100" ek="FBillHead" id="ffailreason" fn="ffailreason" pn="ffailreason" visible="1086" cn="错误原因"
               lock="-1" copy="0" lix="90" notrace="true" ts="" len="1000" />

        <!--二级分销相关-->
        <input el="116" ek="fbillhead" visible="1150" id="fisresellorder" fn="fisresellorder" pn="fisresellorder" cn="二级分销合同" lock="-1" copy="0" width="100" />
    </div>

    <!--商品明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_sal_deliverynoticeentry" pn="fentity" cn="商品明细" kfks="fmaterialid">
        <tr>
            <th el="109" ek="fentity" id="fbizunitid" cn="销售单位"></th>

            <th el="103" ek="fentity" id="fplanqty" cn="基本单位应发数量"></th>
            <th el="103" ek="fentity" id="fbizplanqty" cn="应发数量"></th>

            <th el="103" ek="fentity" id="fqty" cn="基本单位实发数量"></th>
            <th el="103" ek="fentity" id="fbizqty" cn="实发数量"></th>

            <th el="103" ek="fentity" id="foutstockqty" fn="foutstockqty" pn="foutstockqty" visible="1086" cn="基本单位出库数量" width="120"
                lock="-1" copy="0" lix="130" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizoutstockqty" fn="fbizoutstockqty" pn="fbizoutstockqty" visible="1150" cn="销售出库数量" width="100"
                lock="-1" copy="0" lix="132" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="foutstockqty" roundType="0" format="0,000.00"></th>

            <th el="108" ek="fentity" id="fsoorderno" fn="fsoorderno" pn="fsoorderno" visible="1150" cn="销售订单编号"
                lock="-1" copy="0" lix="230" notrace="true" ts=""></th>
            <th el="108" ek="fentity" id="fsoorderinterid" fn="fsoorderinterid" pn="fsoorderinterid" visible="0" cn="销售订单内码"
                lock="-1" copy="0" lix="231" notrace="true" ts=""></th>
            <th el="108" ek="fentity" id="fsoorderentryid" fn="fsoorderentryid" pn="fsoorderentryid" visible="0" cn="销售订单分录内码"
                lock="-1" copy="0" lix="232" notrace="true" ts=""></th>

            <th el="112" ek="FEntity" id="forderdate" fn="forderdate" pn="forderdate" visible="1150" cn="订单日期"
                lock="-1" copy="0" lix="235" notrace="true" ts="">订单日期</th>

            <th el="103" ek="fentity" id="forderqty" fn="forderqty" pn="forderqty" visible="1086" cn="基本单位订单数量" width="120"
                lock="-1" copy="0" lix="238" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizorderqty" fn="fbizorderqty" pn="fbizorderqty" visible="1150" cn="销售订单数量" width="100"
                lock="-1" copy="0" lix="240" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="forderqty" roundType="0" format="0,000.00"></th>
            <th el="100" ek="fentity" id="fmtono" fn="fmtono" pn="fmtono" cn="物流跟踪号" lix="160" width="100" visible="1150" lock="-1"></th>
            <th el="100" ek="fentity" id="fentrynote" fn="fentrynote" pn="fentrynote" cn="备注" lix="500" len="255" visible="-1" width="160"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="push2sostockout" op="push" opn="出库" data="{'parameter':{'ruleId':'sal_deliverynotice2stk_sostockout'}}" permid="">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="createbarcodelink" op="createbarcodelink" opn="备码" data="" permid="ydj_createbarcodelink"></ul>
        <ul el="10" ek="fbillhead" id="deletebarcodelink" op="deletebarcodelink" opn="取消备码" data="" permid="ydj_deletebarcodelink"></ul>

        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">

            <li el="17" sid="1002" cn="反写销售合同发货数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fdeliveryqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fdeliveryqty&gt;fqty+freturnqty',
                'excessMessage':'销售发货数量不允许超过订单数量+退货数量！'
                }"></li>

            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
                'executeType':'add'
                }"></li>-->
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="511" cn="进行了出库后不可以反审核" data='{"expr": [{"linkFormId":"stk_sostockout","linkFieldKey":"fsourceinterid"}],
                "message":"已经生成了下游单据，不允许反审核！"}'></li>

            <li el="17" sid="1002" cn="反写销售合同发货数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fdeliveryqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fdeliveryqty&gt;fqty+freturnqty',
                'excessMessage':'销售发货数量不允许超过订单数量+退货数量！'
                }"></li>

            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
                'executeType':'remove'
                }"></li>-->
        </ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">

            <li el="17" sid="1002" cn="反写销售合同发货数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fdeliveryqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fdeliveryqty&gt;fqty+freturnqty',
                'excessMessage':'销售发货数量不允许超过订单数量+退货数量！'
                }"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="submit" op="submit" opn="提交">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unsubmit" op="unsubmit" opn="撤销">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="uncancel" op="uncancel" opn="反作废">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="ydj_createbarcodelink" cn="备码"></ul>
        <ul el="12" id="ydj_deletebarcodelink" cn="取消备码"></ul>
    </div>

</body>
</html>

