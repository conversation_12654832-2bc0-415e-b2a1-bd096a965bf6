{
  //规则引擎基类
  "base": "",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "fstatus_ABC",
      "expression": ""
    },
    {
      "id": "fstatus_D",
      "expression": ""
    },
    {
      "id": "fstatus_E",
      "expression": ""
    },
    {
      "id": "fstatus_",
      "expression": ""
    },

    //如果支付方式不是银行，则锁定银行账号字段
    //{
    //  "id": "lock_bankcard",
    //  "expression": "field:fmybankid,fsynbankid|fway!='payway_06' and fway!='payway_11'"
    //},
    //{
    //  "id": "unlock_bankcard",
    //  "expression": "field:$fmybankid,fsynbankid|fway=='payway_06' or fway=='payway_11'"
    //}
    //如果支付方式不是银行(支付宝payway_07)(微信payway_08)(刷卡payway_11)，则锁定银行账号字段
    {
      "id": "lock_bankcard",
      "expression": "field:fmybankid,fsynbankid,fpayeraccount|fway=='payway_01' or fway=='payway_04' or fway=='payway_05' or fway=='payway_09' or fway=='payway_10' or fway=='payway_12' or fway=='payway_13'"
    },
    {
      "id": "unlock_bankcard",
      "expression": "field:$fmybankid,fsynbankid,fpayeraccount|fway!='payway_01' and fway!='payway_04' and fway!='payway_05' and fway!='payway_09' and fway!='payway_10' and fway!='payway_12' and fway!='payway_13'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [
    //账户的显示与隐藏
    {
      "id": "show_account",
      "expression": "other:$.account-info|fway=='payway_01'"
    },
    {
      "id": "hide_account",
      "expression": "other:.account-info|fway!='payway_01'"
    }
  ],

  //定义表单计算规则
  "calcRules": [

  ]
}