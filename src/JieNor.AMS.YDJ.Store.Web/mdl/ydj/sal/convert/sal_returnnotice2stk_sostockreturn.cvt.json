{
  "Id": "sal_returnnotice2stk_sostockreturn",
  "Number": "sal_returnnotice2stk_sostockreturn",
  "Name": "销售退货通知下推销售退货单",
  "SourceFormId": "sal_returnnotice",
  "TargetFormId": "stk_sostockreturn",
  "ActiveEntityKey": "fentity",
  "FilterString": "fqty>freturnqty and fstatus='E'",
  "Message": "退货失败：<br>1、销售退货通知单必须是已审核状态！<br>2、选择的退货明细必须满足：退货通知数量>已退货数量（有货可退）！",
  "FieldMappings": [

    //表头字段
    {
      "Id": "fstockstaffid",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "fstockstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsostaffid",
      "Name": "销售员",
      "MapType": 0,
      "SrcFieldId": "fsostaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsodeptid",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fsodeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    {
      "Id": "fstockdeptid",
      "Name": "收货部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fphone",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "flinkmobile",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 2
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "freturntype",
      "Name": "退货类型",
      "MapType": 0,
      "SrcFieldId": "freturntype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanreturnamount",
      "Name": "应退货款金额",
      "MapType": 0,
      "SrcFieldId": "fplanreturnamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "factualreturnamount",
      "Name": "实退货款金额",
      "MapType": 0,
      "SrcFieldId": "factualreturnamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcompensateamount",
      "Name": "赔偿金额",
      "MapType": 0,
      "SrcFieldId": "fcompensateamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freturnreason",
      "Name": "退货说明",
      "MapType": 0,
      "SrcFieldId": "freturnreason",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freturncause",
      "Name": "退货原因",
      "MapType": 0,
      "SrcFieldId": "freturncause",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliverywayid",
      "Name": "货运方式",
      "MapType": 0,
      "SrcFieldId": "fdeliverywayid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcarrierid",
      "Name": "承运公司",
      "MapType": 0,
      "SrcFieldId": "fcarrierid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fshippingbillno",
      "Name": "运输单号",
      "MapType": 0,
      "SrcFieldId": "fshippingbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'sal_returnnotice'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编码",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fenablebarcode",
      "Name": "启用条码作业",
      "MapType": 0,
      "SrcFieldId": "fenablebarcode",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fisresellorder",
      "Name": "二级分销合同",
      "MapType": 0,
      "SrcFieldId": "fisresellorder",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //退货明细字段
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 15
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fbizunitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位应退数量",
      "MapType": 1,
      "SrcFieldId": "fqty-freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fqty",
      "Name": "基本单位实退数量",
      "MapType": 1,
      "SrcFieldId": "fqty-freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 23
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-freturnqty)*fprice",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fstorehouseid",
      "Name": "仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 25
    },
    {
      "Id": "fstorelocationid",
      "Name": "仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 26
    },
    {
      "Id": "fstockstatus",
      "Name": "库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 27
    },
    {
      "Id": "flotno",
      "Name": "批号",
      "MapType": 0,
      "SrcFieldId": "flotno",
      "MapActionWhenGrouping": 0,
      "Order": 28
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 29
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 30
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 35
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fentrynote",
      "MapActionWhenGrouping": 0,
      "Order": 50
    },
    {
      "Id": "fsoorderno",
      "Name": "销售订单编号",
      "MapType": 0,
      "SrcFieldId": "fsoorderno",
      "MapActionWhenGrouping": 0,
      "Order": 60
    },
    {
      "Id": "fsoorderinterid",
      "Name": "销售订单内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderinterid",
      "MapActionWhenGrouping": 0,
      "Order": 61
    },
    {
      "Id": "fsoorderentryid",
      "Name": "销售订单分录内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderentryid",
      "MapActionWhenGrouping": 0,
      "Order": 62
    },
    {
      "Id": "fsooutstockno",
      "Name": "销售出库单编号",
      "MapType": 0,
      "SrcFieldId": "fsooutstockno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsooutstockinterid",
      "Name": "销售出库单内码",
      "MapType": 0,
      "SrcFieldId": "fsooutstockinterid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsooutstockentryid",
      "Name": "销售出库单分录内码",
      "MapType": 0,
      "SrcFieldId": "fsooutstockentryid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'sal_returnnotice'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderqty",
      "Name": "订单数量",
      "MapType": 0,
      "SrcFieldId": "forderqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

  ],
  "BillGroups": [
    {
      "Id": "fsoorderno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}