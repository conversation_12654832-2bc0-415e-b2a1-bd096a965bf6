{
  //�����������
  "base": "/mdl/bill.rule.json",
  "lockRules": [

  ],
  "visibleRules": [
    {
      "id": "hide_fmastername",
      "expression": "other:.fmastername|fbiztype=='1'"
    },
    {
      "id": "show_fmastername",
      "expression": "other:$.fmastername|fbiztype=='2'"
    },
    {
      "id": "hide_fdeliverydate",
      "expression": "other:.fdeliverydate|fbiztype=='2'"
    },
    {
      "id": "show_fdeliverydate",
      "expression": "other:$.fdeliverydate|fbiztype=='1'"
    },
    {
      "id": "hide_fsalamount",
      "expression": "other:.fsalamount|fbiztype=='2'"
    },
    {
      "id": "show_fsalamount",
      "expression": "other:$.fsalamount|fbiztype=='1'"
    },
    //��������ʾ������״̬=A �� B �� C�������ݴ棬������������ˣ�ʱ�������ֶο��ã���� ����� ���� ���������ã�������������
    {
      "id": "fstatus_ABC",
      "expression": "field:fbillno,fmodifierid,fmodifydate,fstatus$|fstatus=='A' or fstatus=='B' or fstatus=='C'"
    }
  ],
  "calcRules": [
  ]
}
