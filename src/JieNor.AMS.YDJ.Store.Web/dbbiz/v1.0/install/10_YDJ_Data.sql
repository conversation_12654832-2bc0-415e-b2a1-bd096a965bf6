/*���ű���Ҫ�������ҵ��ϵͳԤ�õ����ݽű�
**init by linus. at 2016-10-29
*/

--��ֵ��Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('255624222393634817','255624288827215873');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('255624222393634817', 'bas_filterscheme', 'coo_inpourmoney', 1, 'δȷ��', '{"id":"","name":"δȷ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbusinessstatus.fenumitem","operator":"=","value":"δȷ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('255624288827215873', 'bas_filterscheme', 'coo_inpourmoney', 2, '��ȷ��', '{"id":"","name":"��ȷ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbusinessstatus.fenumitem","operator":"=","value":"��ȷ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');

--�ۿԤ����˷����ű�
delete from t_bas_filterscheme where fid in('255626090461138945','255626131414323201');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('255626090461138945', 'bas_filterscheme', 'coo_chargemoney', 1, 'δȷ��', '{"id":"","name":"δȷ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbusinessstatus.fenumitem","operator":"=","value":"δȷ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('255626131414323201', 'bas_filterscheme', 'coo_chargemoney', 2, '��ȷ��', '{"id":"","name":"��ȷ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbusinessstatus.fenumitem","operator":"=","value":"��ȷ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');

--���ʹ���Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('205016696828698625','205017000827817985');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205016696828698625', 'bas_filterscheme', 'ser_reward', 1, '����', '{"id":"","name":"����","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"frewardpunish.fenumitem","operator":"=","value":"����","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205017000827817985', 'bas_filterscheme', 'ser_reward', 2, '����', '{"id":"","name":"����","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"frewardpunish.fenumitem","operator":"=","value":"����","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');

--Ͷ�߼�¼��Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('205016177515143169','205016477248495617');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205016177515143169', 'bas_filterscheme', 'ser_complaintrecord', 1, '�ѳ���', '{"id":"","name":"�ѳ���","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"frewithdrawn","operator":"=1","value":"","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205016477248495617', 'bas_filterscheme', 'ser_complaintrecord', 2, 'δ����', '{"id":"","name":"δ����","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"frewithdrawn","operator":"=0","value":"","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');

--��������Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('205015560541413377','205015948439035905');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205015560541413377', 'bas_filterscheme', 'ser_servicefeed', 1, 'δ����', '{"id":"","name":"δ����","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fhandlestatus.fenumitem","operator":"=","value":"���ύ","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205015948439035905', 'bas_filterscheme', 'ser_servicefeed', 2, '������', '{"id":"","name":"������","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fhandlestatus.fenumitem","operator":"=","value":"������","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');

--ʦ��Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('220217118057172993','220217302371667969','226298731140616193');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('220217118057172993', 'bas_filterscheme', 'ydj_master', 1, '����֤', '{"id":"","name":"����֤","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fapprovestatus.fenumitem","operator":"=","value":"����֤","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('220217302371667969', 'bas_filterscheme', 'ydj_master', 2, 'δ��֤', '{"id":"","name":"δ��֤","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fapprovestatus.fenumitem","operator":"=","value":"δ��֤","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('226298731140616193', 'bas_filterscheme', 'ydj_master', 3, '�����', '{"id":"","name":"�����","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fapprovestatus.fenumitem","operator":"=","value":"�ȴ����","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');

--������Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('204993278592831489','204993453008769025');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('204993278592831489', 'bas_filterscheme', 'ydj_cash', 1, '����', '{"id":"","name":"����","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fusage.fenumitem","operator":"=","value":"����","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', 'fmoney');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('204993453008769025', 'bas_filterscheme', 'ydj_cash', 2, '����', '{"id":"","name":"����","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fusage.fenumitem","operator":"=","value":"����","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', 'fmoney');

--������ĿԤ����˷����ű�
delete from t_bas_filterscheme where fid in('223124378496405505','223125616306819073');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('223124378496405505', 'bas_filterscheme', 'ydj_expenseitem', 1, '��������', '{"id":"","name":"��������","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":{"leftBracket":null,"id":"ftype.fenumitem","operator":"=","value":"��������","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0},"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('223125616306819073', 'bas_filterscheme', 'ydj_expenseitem', 2, '����֧��', '{"id":"","name":"����֧��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":{"leftBracket":null,"id":"ftype.fenumitem","operator":"=","value":"����֧��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0},"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');

--�ɹ�����Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('254972349240184833','254972393922105345','254972468903677953','254972468903677954','254972536390029313','255653352078184449');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('254972349240184833', 'bas_filterscheme', 'ydj_purchaseorder', 1, '���ύ', '{"id":"","name":"���ύ","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbizstatus.fenumitem","operator":"=","value":"������","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('254972393922105345', 'bas_filterscheme', 'ydj_purchaseorder', 2, '������', '{"id":"","name":"������","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbizstatus.fenumitem","operator":"=","value":"������","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('254972468903677953', 'bas_filterscheme', 'ydj_purchaseorder', 3, '������ȷ��', '{"id":"","name":"������ȷ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbizstatus.fenumitem","operator":"=","value":"������ȷ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('254972468903677954', 'bas_filterscheme', 'ydj_purchaseorder', 4, '��ȷ��', '{"id":"","name":"��ȷ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbizstatus.fenumitem","operator":"=","value":"��ȷ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('254972536390029313', 'bas_filterscheme', 'ydj_purchaseorder', 5, '������', '{"id":"","name":"������","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '(fpaystatus=''paystatus_type_01'' or fpaystatus=''paystatus_type_02'')', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('255653352078184449', 'bas_filterscheme', 'ydj_purchaseorder', 6, 'Эͬ���', '{"id":"","name":"Эͬ���","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbizstatus.fenumitem","operator":"=","value":"Эͬ���","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '', ' ');

--��������Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('254980425229930497','254980494272368641','254980551092604929','254980605404647425','254980650283700225','254980908204036097','255654008646144001');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('254980425229930497', 'bas_filterscheme', 'ydj_saleintention', 1, '������', '{"id":"","name":"������","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbizstatus.fenumitem","operator":"=","value":"������","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('254980494272368641', 'bas_filterscheme', 'ydj_saleintention', 2, '������ȷ��', '{"id":"","name":"������ȷ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbizstatus.fenumitem","operator":"=","value":"������ȷ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('254980551092604929', 'bas_filterscheme', 'ydj_saleintention', 3, '��ȷ��', '{"id":"","name":"��ȷ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbizstatus.fenumitem","operator":"=","value":"��ȷ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('254980908204036097', 'bas_filterscheme', 'ydj_saleintention', 4, '������', '{"id":"","name":"������","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '(freceiptstatus=''receiptstatus_type_01'' or freceiptstatus=''receiptstatus_type_02'')', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('255654008646144001', 'bas_filterscheme', 'ydj_saleintention', 5, 'Эͬ���', '{"id":"","name":"Эͬ���","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fbizstatus.fenumitem","operator":"=","value":"Эͬ���","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');

--����Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('205000749095194625','205009157725507585','205009913505533953','205377304099164161','223046168886775809');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205000749095194625', 'bas_filterscheme', 'ydj_service', 1, 'δ�ɵ�', '{"id":"","name":"δ�ɵ�","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '(fserstatus=''sersta01'' or fserstatus=''sersta03'')', 'fexpectamount');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205009157725507585', 'bas_filterscheme', 'ydj_service', 2, '��ָ��', '{"id":"","name":"��ָ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '(fserstatus=''sersta02'' or fserstatus=''sersta04'' or fserstatus=''sersta06'')', 'fexpectamount');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205009913505533953', 'bas_filterscheme', 'ydj_service', 3, '���깤', '{"id":"","name":"���깤","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '(fserstatus=''sersta05'' or fserstatus=''sersta08'')', 'fexpectamount');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('205377304099164161', 'bas_filterscheme', 'ydj_service', 4, '�ѻط�', '{"id":"","name":"�ѻط�","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fserstatus.fenumitem","operator":"=","value":"������","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', 'frealamount');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('223046168886775809', 'bas_filterscheme', 'ydj_service', 5, '�ѽ���', '{"id":"","name":"�ѽ���","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fserstatus.fenumitem","operator":"=","value":"�ر�","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');

--�̻�����Ԥ����˷����ű�
delete from t_bas_filterscheme where fid in('244787722181545985','244789088727732225','244789171892391937','244789914716213249','244789953182175233','244789999113998337','244789999113998338','244789999126725890');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('244787722181545985', 'bas_filterscheme', 'ydj_merchantorder', 1, 'δ����', '{"id":"","name":"δ����","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fserstatus.fenumitem","operator":"=","value":"���̻�����","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', 'fserstatus=''sht_serstatus00'' or fserstatus=''sht_serstatus02''', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('244789088727732225', 'bas_filterscheme', 'ydj_merchantorder', 2, '��ƽ̨�˼�', '{"id":"","name":"��ƽ̨�˼�","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fserstatus.fenumitem","operator":"=","value":"��ƽ̨�˼�","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('244789171892391937', 'bas_filterscheme', 'ydj_merchantorder', 3, '���̻�ȷ��', '{"id":"","name":"���̻�ȷ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fserstatus.fenumitem","operator":"=","value":"���̻�ȷ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('244789914716213249', 'bas_filterscheme', 'ydj_merchantorder', 4, '��ƽ̨�ɵ�', '{"id":"","name":"��ƽ̨�ɵ�","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('244789953182175233', 'bas_filterscheme', 'ydj_merchantorder', 5, '������', '{"id":"","name":"������","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('244789999113998337', 'bas_filterscheme', 'ydj_merchantorder', 6, '���깤', '{"id":"","name":"���깤","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('244789999113998338', 'bas_filterscheme', 'ydj_merchantorder', 7, '��ȡ��', '{"id":"","name":"��ȡ��","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"fserstatus.fenumitem","operator":"=","value":"��ȡ��","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', '', ' ');
insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('244789999126725890', 'bas_filterscheme', 'ydj_merchantorder', 8, '�������', '{"id":"","name":"�������","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[],"filterString":null,"sumExpression":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '1', '1', 'fserstatus=''sht_serstatus08''', ' ');

--���ۺ�ͬԤ����˷����ű�
delete from t_bas_filterscheme where fid in('202456128636981249','202456415485431809','202456626542809089');
--insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('202456128636981249', 'bas_filterscheme', 'ydj_order', 1, '�ȴ��տ�', '{"id":"","name":"�ȴ��տ�","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"freceivable","operator":"<","value":"fsumamount","isCompareToField":true,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":"fdealamount_h","linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', 'fdealamount_h');
--insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('202456415485431809', 'bas_filterscheme', 'ydj_order', 2, 'ȫ������', '{"id":"","name":"ȫ������","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"freceivable","operator":">=","value":"fsumamount","isCompareToField":true,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"sumExpression":"fdealamount_h","linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', 'fdealamount_h');
--insert t_bas_filterscheme (fid, fformid, fbillformid, forder, fname, ffilterdata, fshare, fispreset, ffilterstring, fsumexpr) values ('202456626542809089', 'bas_filterscheme', 'ydj_order', 3, '����', '{"id":"","name":"����","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":false,"isShare":false,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":null,"id":"ftype.fenumitem","operator":"=","value":"������","isCompareToField":false,"rightBracket":null,"logic":"","rowIndex":0}],"filterString":null,"linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', '0', '1', ' ', 'fdealamount_h');

--�׵��ң�������������
delete from t_sys_smsgetwayconfig where fid='217572835785314304';
insert t_sys_smsgetwayconfig (fid,fformid,fnumber,fname,fdescription,fcreatorid,fcreatedate,fmodifierid,fmodifydate,fispreset,fstatus,fapproveid,fapprovedate,fforbidstatus,fforbidid,fforbiddate,fmainorgid,fbizruleid,fname_py,fname_py2,ftopic,faccessid,faccesskey,fendpoint) 
values ('217572835785314304','sys_smsgetwayconfig','No.0000000001',' ','�׵��Ҷ�����������','',null,'',null,'1','B',' ',NULL,'0',' ',NULL,'0',' ',' ',' ','sms.topic-cn-shenzhen','LTAIS3oJhs3uaUCQ','aOZnWOg9igyjbEBEYP4nylJblhiaZF','http://1038880401653050.mns.cn-shenzhen.aliyuncs.com/');

--�׵��ң�����ģ������Ԥ��ű�
delete from t_sys_smstemplate where fid in('219842563669102592','217960887900835840');
insert t_sys_smstemplate (fid,fformid,fnumber,fname,fdescription,fcreatorid,fcreatedate,fmodifierid,fmodifydate,fispreset,fstatus,fapproveid,fapprovedate,fforbidstatus,fforbidid,fforbiddate,fmainorgid,fbizruleid,fname_py,fname_py2,fcode,fcontent,fsignname) values ('219842563669102592','sys_smstemplate','No.0000000003','ʦ��ԤԼȷ��',' ','',null,'',null,'1','B',' ',NULL,'0',' ',NULL,'0',' ','S','SFYYQR - XMB','SMS_94695133','�������ģ�${fderbrand}����װʦ���ѽӵ�����${fserdate}����Ϊ����װ�����������벦��0755-86577742','�׵���');
insert t_sys_smstemplate (fid,fformid,fnumber,fname,fdescription,fcreatorid,fcreatedate,fmodifierid,fmodifydate,fispreset,fstatus,fapproveid,fapprovedate,fforbidstatus,fforbidid,fforbiddate,fmainorgid,fbizruleid,fname_py,fname_py2,fcode,fcontent,fsignname) values ('217960887900835840','sys_smstemplate','No.0000000002','ʦ���ӵ�ȷ��',' ','',null,'',null,'1','B',' ',NULL,'0',' ',NULL,'0',' ','S','SFJDQR','SMS_76480038','���ã����Ķ���${fbillno}��װʦ���ѽӵ����Ժ�����ȡ����ϵ���������ʣ��벦��0755-86577741','�׵���');

--�׵��ң�����������������Ԥ��ű�
delete from t_sys_opserconfig where fid in('217947287777841152','218787332629860352');
delete from t_sys_opserconfigentry where fid in('217947287777841152','218787332629860352');
insert t_sys_opserconfig (fid,fformid,fnumber,fname,fdescription,fcreatorid,fcreatedate,fmodifierid,fmodifydate,fispreset,fstatus,fapproveid,fapprovedate,fforbidstatus,fforbidid,fforbiddate,fmainorgid,fbizruleid,fname_py,fname_py2,fbizobject,fbizop,fbizop_txt) values ('218787332629860352','sys_opserconfig','No.0000000007',' ','���񵥣�ԤԼȷ�ϲ���ʱ����ҵ�����Ͷ���','',null,'',null,'1','B',' ',NULL,'0',' ',NULL,'0',' ',' ',' ','ydj_service','setstatus07','ԤԼȷ��');
insert t_sys_opserconfigentry (fentryid,fseq,fopser,fopser_txt,fid,fparamform,fserconfig,fserconfig_txt) values ('218787332629860353',1,'sendsms','�����ŷ���','218787332629860352','sys_smssereditor','{"ftemplate":{"id":"219842563669102592","fnumber":"","fname":"ʦ��ԤԼȷ��"},"fbizobject":{"id":"ydj_service","fnumber":"ydj_service","fname":"����"},"fphonesource":{"id":"1","fnumber":"","fname":""},"ffield":{"id":"fphone","name":"��ϵ�绰"},"fbdfield":{"id":"","name":""},"fphone":""}','���ŷ�������');

--Ԥ�ý�ɫ���ݣ���װʦ��
DELETE FROM [T_SEC_ROLE] WHERE FID='10000';
INSERT INTO [T_SEC_ROLE]([fid],[FFormId],[fnumber],[fname],[fdescription],[fcreatorid],[fcreatedate],[fmodifierid],[fmodifydate],[fispreset],[fstatus],[fapproveid],[fapprovedate],[fforbidstatus],[fforbidid],[fforbiddate],[fmainorgid],[fbizruleid],[fname_py],[fname_py2])
VALUES('10000','sec_role','AZSF',N'��װʦ��',N'��װʦ��Ȩ������','216927826786521088','2017-09-07 14:17:06.937','216927826786521088','2017-09-07 14:17:06.937','1','B','',null,'0','',null,'','','A','AZSF');


--���״̬Ԥ������ add by linus.
delete from T_YDJ_STOCKSTATUS where fid='311858936800219137';
insert into T_YDJ_STOCKSTATUS(fid,FFormId,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,ftranid,fsendstatus,fname_py,fname_py2,fdescription)
values('311858936800219137','ydj_stockstatus','KCZT_SYS_01','����','0','2016-10-01 00:00:00','1','E','0','','','311858936800219137',N'δ����','K','KY',N'ϵͳԤ��');

delete from T_YDJ_STOCKSTATUS where fid='311859021135089665';
insert into T_YDJ_STOCKSTATUS(fid,FFormId,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,ftranid,fsendstatus,fname_py,fname_py2,fdescription)
values('311859021135089665','ydj_stockstatus','KCZT_SYS_02','�ѱ�','0','2016-10-01 00:00:00','1','E','0','','','311859021135089665',N'δ����','K','KY',N'ϵͳԤ��');

delete from T_YDJ_STOCKSTATUS where fid='311859071986831361';
insert into T_YDJ_STOCKSTATUS(fid,FFormId,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,ftranid,fsendstatus,fname_py,fname_py2,fdescription)
values('311859071986831361','ydj_stockstatus','KCZT_SYS_03','����','0','2016-10-01 00:00:00','1','E','0','','','311859071986831361',N'δ����','K','KY',N'ϵͳԤ��');

delete from T_YDJ_STOCKSTATUS where fid='311859116119298049';
insert into T_YDJ_STOCKSTATUS(fid,FFormId,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,ftranid,fsendstatus,fname_py,fname_py2,fdescription)
values('311859116119298049','ydj_stockstatus','KCZT_SYS_04','����','0','2016-10-01 00:00:00','1','E','0','','','311859116119298049',N'δ����','K','KY',N'ϵͳԤ��');

delete from T_YDJ_STOCKSTATUS where fid='311859160906076161';
insert into T_YDJ_STOCKSTATUS(fid,FFormId,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,ftranid,fsendstatus,fname_py,fname_py2,fdescription)
values('311859160906076161','ydj_stockstatus','KCZT_SYS_05','����','0','2016-10-01 00:00:00','1','E','0','','','311859160906076161',N'δ����','K','KY',N'ϵͳԤ��');

delete from T_YDJ_STOCKSTATUS where fid='311859200592580609';
insert into T_YDJ_STOCKSTATUS(fid,FFormId,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,ftranid,fsendstatus,fname_py,fname_py2,fdescription)
values('311859200592580609','ydj_stockstatus','KCZT_SYS_06','ȱ��','0','2016-10-01 00:00:00','1','E','0','','','311859200592580609',N'δ����','K','KY',N'ϵͳԤ��');



--������������Ԥ��ű���ʼ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

--�ɹ���������
delete from t_bd_enumdata where fid='242d7b7f6a024d2587964ffb4ca3c641';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('242d7b7f6a024d2587964ffb4ca3c641','bd_enumdata','�ɹ���������','1','B','0','�ɹ�����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('po_type_01','po_type_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('po_type_01','242d7b7f6a024d2587964ffb4ca3c641',1,'0','1','','��ͨ�ɹ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('po_type_02','242d7b7f6a024d2587964ffb4ca3c641',2,'0','1','','Эͬ�ɹ�');

--�ɹ����뵥������
delete from t_bd_enumdata where fid='b35549b0b7d24a359b034d101cf6146b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('b35549b0b7d24a359b034d101cf6146b','bd_enumdata','�ɹ����뵥������','1','B','0','�ɹ�����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('reqorder_type_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('reqorder_type_01','b35549b0b7d24a359b034d101cf6146b',1,'0','1','','��׼�ɹ�����');

--����״̬
delete from t_bd_enumdata where fid='f191997b63f24032ac1237e8ab7a5a6a';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('f191997b63f24032ac1237e8ab7a5a6a','bd_enumdata','����״̬','1','B','0','�ɹ�����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('paystatus_type_01','paystatus_type_02','paystatus_type_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('paystatus_type_01','f191997b63f24032ac1237e8ab7a5a6a',1,'0','1','','ȫ��δ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('paystatus_type_02','f191997b63f24032ac1237e8ab7a5a6a',2,'0','1','','���ָ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('paystatus_type_03','f191997b63f24032ac1237e8ab7a5a6a',3,'0','1','','ȫ���Ѹ�');

--���
delete from t_bd_enumdata where fid='f789c296a06247ffad978acb945a3fa0';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('f789c296a06247ffad978acb945a3fa0','bd_enumdata','���','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('style_01','style_02','style_03','style_04','style_05','style_06','style_07','style_08');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('style_01','f789c296a06247ffad978acb945a3fa0',1,'0','1','','�ִ���Լ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('style_02','f789c296a06247ffad978acb945a3fa0',2,'0','1','','��ŷ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('style_03','f789c296a06247ffad978acb945a3fa0',3,'0','1','','ŷʽ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('style_04','f789c296a06247ffad978acb945a3fa0',4,'0','1','','����ʽ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('style_05','f789c296a06247ffad978acb945a3fa0',5,'0','1','','��ʽ��԰');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('style_06','f789c296a06247ffad978acb945a3fa0',6,'0','1','','��ʽ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('style_07','f789c296a06247ffad978acb945a3fa0',7,'0','1','','��ҵ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('style_08','f789c296a06247ffad978acb945a3fa0',8,'0','1','','�¹ŵ�');

--װ�޽���
delete from t_bd_enumdata where fid='f00772dcfb674965aeb9f6a42c772c7f';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('f00772dcfb674965aeb9f6a42c772c7f','bd_enumdata','װ�޽���','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('renovation_01','renovation_02','renovation_03','renovation_04','renovation_05','renovation_06','renovation_07','renovation_08');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_01','f00772dcfb674965aeb9f6a42c772c7f',1,'0','1','','δ��¥');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_02','f00772dcfb674965aeb9f6a42c772c7f',2,'0','1','','׼��װ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_03','f00772dcfb674965aeb9f6a42c772c7f',3,'0','1','','����װ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_04','f00772dcfb674965aeb9f6a42c772c7f',4,'0','1','','װ�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_05','f00772dcfb674965aeb9f6a42c772c7f',5,'0','1','','��ˮ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_06','f00772dcfb674965aeb9f6a42c772c7f',6,'0','1','','ľ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_07','f00772dcfb674965aeb9f6a42c772c7f',7,'0','1','','���Ṥ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_08','f00772dcfb674965aeb9f6a42c772c7f',8,'0','1','','ˮ�繤');

--���ʦ�ֹ�
delete from t_bd_enumdata where fid='f90d12daebf24461b71d163a6761df08';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('f90d12daebf24461b71d163a6761df08','bd_enumdata','���ʦ�ֹ�','1','B','0','�������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('design_type_01','design_type_02','design_type_03','design_type_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('design_type_01','f90d12daebf24461b71d163a6761df08',1,'0','1','','�����ʦ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('design_type_02','f90d12daebf24461b71d163a6761df08',2,'0','1','','CAD���ʦ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('design_type_03','f90d12daebf24461b71d163a6761df08',3,'0','1','','2020���ʦ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('design_type_04','f90d12daebf24461b71d163a6761df08',4,'0','1','','����');

--��λ
delete from t_bd_enumdata where fid='fa104d958c0a451c879e326d938pf3e7';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('fa104d958c0a451c879e326d938pf3e7','bd_enumdata','��λ','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('post01','post02','post03','post04','post05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('post01','fa104d958c0a451c879e326d938pf3e7',1,'0','1','','�ܾ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('post02','fa104d958c0a451c879e326d938pf3e7',2,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('post03','fa104d958c0a451c879e326d938pf3e7',3,'0','1','','�곤');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('post04','fa104d958c0a451c879e326d938pf3e7',4,'0','1','','������Ա');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('post05','fa104d958c0a451c879e326d938pf3e7',5,'0','1','','ʦ��');

--������ʽ
delete from t_bd_enumdata where fid='b90efab51a7b47cd9a4ea9f0bcf29cde';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('b90efab51a7b47cd9a4ea9f0bcf29cde','bd_enumdata','������ʽ','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('punish_type01','punish_type02','punish_type03','punish_type04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('punish_type01','b90efab51a7b47cd9a4ea9f0bcf29cde',1,'0','1','','���ž���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('punish_type02','b90efab51a7b47cd9a4ea9f0bcf29cde',2,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('punish_type03','b90efab51a7b47cd9a4ea9f0bcf29cde',3,'0','1','','�����־�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('punish_type04','b90efab51a7b47cd9a4ea9f0bcf29cde',4,'0','1','','�����־�(�������γе��õ���20%)');

--��Դ����
delete from t_bd_enumdata where fid='a72f040851454cdd8dc2bdef3ea39808';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('a72f040851454cdd8dc2bdef3ea39808','bd_enumdata','��Դ����','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('164743697441886208','164743697441886209','164743697441886210','248802607286587394','248802607286587395');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164743697441886208','a72f040851454cdd8dc2bdef3ea39808',1,'0','1','','�绰');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164743697441886209','a72f040851454cdd8dc2bdef3ea39808',2,'0','1','','΢��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164743697441886210','a72f040851454cdd8dc2bdef3ea39808',3,'0','1','','QQ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('248802607286587394','a72f040851454cdd8dc2bdef3ea39808',4,'0','1','','��ͳý��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('248802607286587395','a72f040851454cdd8dc2bdef3ea39808',5,'0','1','','¥��С��');

--�����
delete from t_bd_enumdata where fid='8bc75b5f44d6445e87675f403413c3af';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('8bc75b5f44d6445e87675f403413c3af','bd_enumdata','�����','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('age_01','age_02','age_03','age_04','age_05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('age_01','8bc75b5f44d6445e87675f403413c3af',1,'0','1','','20������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('age_02','8bc75b5f44d6445e87675f403413c3af',2,'0','1','','25������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('age_03','8bc75b5f44d6445e87675f403413c3af',3,'0','1','','30������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('age_04','8bc75b5f44d6445e87675f403413c3af',4,'0','1','','40-50��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('age_05','8bc75b5f44d6445e87675f403413c3af',5,'0','1','','55������');

--�ͻ���Դ
delete from t_bd_enumdata where fid='bcf7483b775446c18f7b1c68a389c034';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('bcf7483b775446c18f7b1c68a389c034','bd_enumdata','�ͻ���Դ','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('cussource_01','cussource_02','cussource_03','cussource_04','cussource_05','cussource_06','cussource_07');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cussource_01','bcf7483b775446c18f7b1c68a389c034',1,'0','1','','��Ȼ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cussource_02','bcf7483b775446c18f7b1c68a389c034',2,'0','1','','�绰��Լ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cussource_03','bcf7483b775446c18f7b1c68a389c034',3,'0','1','','С������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cussource_04','bcf7483b775446c18f7b1c68a389c034',4,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cussource_05','bcf7483b775446c18f7b1c68a389c034',5,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cussource_06','bcf7483b775446c18f7b1c68a389c034',6,'0','1','','�Ͽͻ�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cussource_07','bcf7483b775446c18f7b1c68a389c034',7,'0','1','','���ʦ����');

--ԤԼ����
delete from t_bd_enumdata where fid='ce010757d6344ada83dfbe26d4dfe534';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ce010757d6344ada83dfbe26d4dfe534','bd_enumdata','ԤԼ����','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('bespeak_type_01','bespeak_type_02','bespeak_type_03','bespeak_type_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bespeak_type_01','ce010757d6344ada83dfbe26d4dfe534',1,'0','1','','ԤԼ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bespeak_type_02','ce010757d6344ada83dfbe26d4dfe534',2,'0','1','','ԤԼά��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bespeak_type_03','ce010757d6344ada83dfbe26d4dfe534',3,'0','1','','ԤԼ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bespeak_type_04','ce010757d6344ada83dfbe26d4dfe534',4,'0','1','','ԤԼ��ͨ����');

--ҵ��״̬(���ۻ���)
delete from t_bd_enumdata where fid='cfb7a910d8934a20b452e6c4af94b5d3';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('cfb7a910d8934a20b452e6c4af94b5d3','bd_enumdata','ҵ��״̬(���ۻ���)','1','B','0','�������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('chance_status_01','chance_status_02','chance_status_03','chance_status_04','chance_status_05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('chance_status_01','cfb7a910d8934a20b452e6c4af94b5d3',1,'0','1','','δ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('chance_status_02','cfb7a910d8934a20b452e6c4af94b5d3',2,'0','1','','�ѷ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('chance_status_03','cfb7a910d8934a20b452e6c4af94b5d3',3,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('chance_status_04','cfb7a910d8934a20b452e6c4af94b5d3',4,'0','1','','��Ч�ر�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('chance_status_05','cfb7a910d8934a20b452e6c4af94b5d3',5,'0','1','','��ת�ͻ�');

--��������
delete from t_bd_enumdata where fid='d32850c2ec044ac89bacef73e1643ffd';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('d32850c2ec044ac89bacef73e1643ffd','bd_enumdata','��������','1','B','0','�������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('channel_type_01','channel_type_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('channel_type_01','d32850c2ec044ac89bacef73e1643ffd',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('channel_type_02','d32850c2ec044ac89bacef73e1643ffd',2,'0','1','','��˾');

--�������
delete from t_bd_enumdata where fid='d79acb6762674c6b94d3309799d5c14c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('d79acb6762674c6b94d3309799d5c14c','bd_enumdata','�������','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('cooperation_01','cooperation_02','cooperation_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cooperation_01','d79acb6762674c6b94d3309799d5c14c',1,'0','1','','��δ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cooperation_02','d79acb6762674c6b94d3309799d5c14c',2,'0','1','','һ�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cooperation_03','d79acb6762674c6b94d3309799d5c14c',3,'0','1','','���ܺ���');

--���۷�ʽ
delete from t_bd_enumdata where fid='dc4a0db14dfe440fbecf4dc13a90a62b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('dc4a0db14dfe440fbecf4dc13a90a62b','bd_enumdata','���۷�ʽ','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('price_mode_1','price_mode_2','price_mode_3');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('price_mode_1','dc4a0db14dfe440fbecf4dc13a90a62b',1,'0','1','','���Զ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('price_mode_2','dc4a0db14dfe440fbecf4dc13a90a62b',2,'0','1','','�ߴ綨��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('price_mode_3','dc4a0db14dfe440fbecf4dc13a90a62b',3,'0','1','','�������');

--����ȼ�
delete from t_bd_enumdata where fid='e4e1f9dd3bc544ababe0073d24cfd33b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e4e1f9dd3bc544ababe0073d24cfd33b','bd_enumdata','����ȼ�','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('demand_level_01','demand_level_02','demand_level_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('demand_level_01','e4e1f9dd3bc544ababe0073d24cfd33b',1,'0','1','','A�ࣨ�����󣬽��ڻ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('demand_level_02','e4e1f9dd3bc544ababe0073d24cfd33b',2,'0','1','','B�ࣨ�����󣬵����ڲ�׼������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('demand_level_03','e4e1f9dd3bc544ababe0073d24cfd33b',3,'0','1','','C�ࣨ����������ȷ��');

--�ͻ��׶�״̬
delete from t_bd_enumdata where fid='1bd19a1dfeb44973abbefe2fe5966f74';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('1bd19a1dfeb44973abbefe2fe5966f74','bd_enumdata','�ͻ��׶�״̬','1','B','0','�������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('stage_status_01','stage_status_02','stage_status_03','stage_status_04','stage_status_05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('stage_status_01','1bd19a1dfeb44973abbefe2fe5966f74',1,'0','1','','�ͻ�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('stage_status_02','1bd19a1dfeb44973abbefe2fe5966f74',2,'0','1','','�������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('stage_status_03','1bd19a1dfeb44973abbefe2fe5966f74',3,'0','1','','�ɽ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('stage_status_04','1bd19a1dfeb44973abbefe2fe5966f74',4,'0','1','','�Ͽͻ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('stage_status_05','1bd19a1dfeb44973abbefe2fe5966f74',5,'0','1','','��ʧ');

--�ռ�
delete from t_bd_enumdata where fid='2db5b72c650541e981d1b643fed9d096';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('2db5b72c650541e981d1b643fed9d096','bd_enumdata','�ռ�','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('space_01','space_02','space_03','space_04','space_05','space_06','space_07','space_08','space_09','space_10','space_11');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_01','2db5b72c650541e981d1b643fed9d096',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_02','2db5b72c650541e981d1b643fed9d096',2,'0','1','','��ñ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_03','2db5b72c650541e981d1b643fed9d096',3,'0','1','','ԡ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_04','2db5b72c650541e981d1b643fed9d096',4,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_05','2db5b72c650541e981d1b643fed9d096',5,'0','1','','�鷿');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_06','2db5b72c650541e981d1b643fed9d096',6,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_07','2db5b72c650541e981d1b643fed9d096',7,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_08','2db5b72c650541e981d1b643fed9d096',8,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_09','2db5b72c650541e981d1b643fed9d096',9,'0','1','','��ͯ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_10','2db5b72c650541e981d1b643fed9d096',10,'0','1','','��̨');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('space_11','2db5b72c650541e981d1b643fed9d096',11,'0','1','','ȫ�ݶ���');

--�ƻ���סʱ��
delete from t_bd_enumdata where fid='3bf2d68a234a4b85b5ff24ecaa75215e';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('3bf2d68a234a4b85b5ff24ecaa75215e','bd_enumdata','�ƻ���סʱ��','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('checkdate_01','checkdate_02','checkdate_03','checkdate_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('checkdate_01','3bf2d68a234a4b85b5ff24ecaa75215e',1,'0','1','','1������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('checkdate_02','3bf2d68a234a4b85b5ff24ecaa75215e',2,'0','1','','3������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('checkdate_03','3bf2d68a234a4b85b5ff24ecaa75215e',3,'0','1','','3-6����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('checkdate_04','3bf2d68a234a4b85b5ff24ecaa75215e',3,'0','1','','6��������');

--���
delete from t_bd_enumdata where fid='2f420872f354471185a4350a21d59956';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('2f420872f354471185a4350a21d59956','bd_enumdata','���','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('area_01','area_02','area_03','area_04','area_05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('area_01','2f420872f354471185a4350a21d59956',1,'0','1','','60����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('area_02','2f420872f354471185a4350a21d59956',2,'0','1','','60-90');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('area_03','2f420872f354471185a4350a21d59956',3,'0','1','','90-120');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('area_04','2f420872f354471185a4350a21d59956',4,'0','1','','120-180');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('area_05','2f420872f354471185a4350a21d59956',5,'0','1','','180����');

--ְҵ
delete from t_bd_enumdata where fid='25a0b0a906ad4145a6d790b7dc6b1f2b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('25a0b0a906ad4145a6d790b7dc6b1f2b','bd_enumdata','ְҵ','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('248802789973692420','248802789973692418','248802789973692419','161198696694943744','161198696694943745','161198696694943746');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('248802789973692420','25a0b0a906ad4145a6d790b7dc6b1f2b',1,'0','1','','��˾ְԱ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('248802789973692418','25a0b0a906ad4145a6d790b7dc6b1f2b',2,'0','1','','��ͥ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('248802789973692419','25a0b0a906ad4145a6d790b7dc6b1f2b',3,'0','1','','С��ҵ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('161198696694943744','25a0b0a906ad4145a6d790b7dc6b1f2b',4,'0','1','','�������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('161198696694943745','25a0b0a906ad4145a6d790b7dc6b1f2b',5,'0','1','','ʵʩ��Ա');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('161198696694943746','25a0b0a906ad4145a6d790b7dc6b1f2b',6,'0','1','','��Ŀ����');

--��������
delete from t_bd_enumdata where fid='3ea8d51885c24272965f782b11b786c9';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('3ea8d51885c24272965f782b11b786c9','bd_enumdata','��������','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('reward01','reward02','reward03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('reward01','3ea8d51885c24272965f782b11b786c9',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('reward02','3ea8d51885c24272965f782b11b786c9',2,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('reward03','3ea8d51885c24272965f782b11b786c9',3,'0','1','','�˻ط���');

--�ۺ�����������
delete from t_bd_enumdata where fid='4827521806544d0bb941a0fdff6b1e85';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4827521806544d0bb941a0fdff6b1e85','bd_enumdata','�ۺ�����������','1','B','0','�������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('afterfeedback_type_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('afterfeedback_type_01','4827521806544d0bb941a0fdff6b1e85',1,'0','1','','�����ۺ���');

--ͼֽ����
delete from t_bd_enumdata where fid='4ee8b8c510e54ba5b66903708f0ffd58';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4ee8b8c510e54ba5b66903708f0ffd58','bd_enumdata','ͼֽ����','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('drawing_type_01','drawing_type_02','drawing_type_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('drawing_type_01','4ee8b8c510e54ba5b66903708f0ffd58',1,'0','1','','��ľ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('drawing_type_02','4ee8b8c510e54ba5b66903708f0ffd58',2,'0','1','','CAD');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('drawing_type_03','4ee8b8c510e54ba5b66903708f0ffd58',3,'0','1','','��ά��');

--(��������)ҵ��״̬
delete from t_bd_enumdata where fid='7fa39c8cad5a4e8eaca954cfaa7ef023';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('7fa39c8cad5a4e8eaca954cfaa7ef023','bd_enumdata','(��������)ҵ��״̬','1','B','0','�������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('leads_status_01','leads_status_02','leads_status_03','leads_status_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('leads_status_01','7fa39c8cad5a4e8eaca954cfaa7ef023',1,'0','1',' ','δ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('leads_status_02','7fa39c8cad5a4e8eaca954cfaa7ef023',2,'0','1',' ','�ѷ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('leads_status_03','7fa39c8cad5a4e8eaca954cfaa7ef023',3,'0','1',' ','��ת�̻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('leads_status_04','7fa39c8cad5a4e8eaca954cfaa7ef023',4,'0','1',' ','��Ч�ر�');

--����ԭ��
delete from t_bd_enumdata where fid='8152f3cef98e4f81b09f44399020f836';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('8152f3cef98e4f81b09f44399020f836','bd_enumdata','����ԭ��','1','B','0','�������',3,'1','0');
delete from t_bd_enumdataentry where fentryid in('subjoincause_01','subjoincause_02','subjoincause_03','subjoincause_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('subjoincause_01','8152f3cef98e4f81b09f44399020f836',1,'0','1','','��װ���ʱ������/ȱ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('subjoincause_02','8152f3cef98e4f81b09f44399020f836',2,'0','1','','��װ��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('subjoincause_03','8152f3cef98e4f81b09f44399020f836',3,'0','1','','ʹ���з�����������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('subjoincause_04','8152f3cef98e4f81b09f44399020f836',4,'0','1','','���ȱ�ݣ���Ҫ���Ӳ���');

--�Żݷ�ʽ
delete from t_bd_enumdata where fid='43f6544940a24b7d970298a8f2c899a2';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('43f6544940a24b7d970298a8f2c899a2','bd_enumdata','�Żݷ�ʽ','1','B','0','�������',3,'1','0');
delete from t_bd_enumdataentry where fentryid in('favorway_01','favorway_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('favorway_01','43f6544940a24b7d970298a8f2c899a2',1,'0','1','','Ĩβ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('favorway_02','43f6544940a24b7d970298a8f2c899a2',2,'0','1','','һ�ڼ�');

--�Ż�Ĩβ��ѡ��
delete from t_bd_enumdata where fid='17e4e1caa90c4e80929c065e37464d4c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('17e4e1caa90c4e80929c065e37464d4c','bd_enumdata','�Ż�Ĩβ��ѡ��','1','B','0','�������',3,'1','0');
delete from t_bd_enumdataentry where fentryid in('favoropt_g','favoropt_s','favoropt_b','favoropt_q');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('favoropt_g','17e4e1caa90c4e80929c065e37464d4c',1,'0','1','','��λȥ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('favoropt_s','17e4e1caa90c4e80929c065e37464d4c',2,'0','1','','ʮλȥ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('favoropt_b','17e4e1caa90c4e80929c065e37464d4c',3,'0','1','','��λȥ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('favoropt_q','17e4e1caa90c4e80929c065e37464d4c',4,'0','1','','ǧλȥ��');

--�ͻ���ǩ
delete from t_bd_enumdata where fid='19b8bd9b874f42cfa69a4104aa7278c4';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('19b8bd9b874f42cfa69a4104aa7278c4','bd_enumdata','�ͻ���ǩ','1','B','0','�������',3,'1','0');
delete from t_bd_enumdataentry where fentryid in('customertag_01','customertag_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customertag_01','19b8bd9b874f42cfa69a4104aa7278c4',1,'0','1','','Ǳ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customertag_02','19b8bd9b874f42cfa69a4104aa7278c4',2,'0','1','','����');

--�ۿ۷�ʽ
delete from t_bd_enumdata where fid='ae13087e5d424baa8bf0999ad3ffd199';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ae13087e5d424baa8bf0999ad3ffd199','bd_enumdata','�ۿ۷�ʽ','1','B','0','�������',3,'1','0');
delete from t_bd_enumdataentry where fentryid in('fdisopt_9','fdisopt_8','fdisopt_7','fdisopt_6');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fdisopt_9','ae13087e5d424baa8bf0999ad3ffd199',1,'0','1','','9��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fdisopt_8','ae13087e5d424baa8bf0999ad3ffd199',2,'0','1','','8��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fdisopt_7','ae13087e5d424baa8bf0999ad3ffd199',3,'0','1','','7��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fdisopt_6','ae13087e5d424baa8bf0999ad3ffd199',4,'0','1','','6��');

--�ɱ���Դ
delete from t_bd_enumdata where fid='eddde871b48e409a9fcbc03e65b13a47';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('eddde871b48e409a9fcbc03e65b13a47','bd_enumdata','�ɱ���Դ','1','B','0','�������',3,'1','0');
delete from t_bd_enumdataentry where fentryid in('costsource_01','costsource_02','costsource_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('costsource_01','eddde871b48e409a9fcbc03e65b13a47',1,'0','1','','��Ʒ��׼�ɱ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('costsource_02','eddde871b48e409a9fcbc03e65b13a47',2,'0','1','','��Ŀ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('costsource_03','eddde871b48e409a9fcbc03e65b13a47',3,'0','1','','Эͬ����');

--�ͻ����
delete from t_bd_enumdata where fid='5c61d044eef24d30a7af4d540ca1db93';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('5c61d044eef24d30a7af4d540ca1db93','bd_enumdata','�ͻ����','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('customercate_01','customercate_02','customercate_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customercate_01','5c61d044eef24d30a7af4d540ca1db93',1,'0','1','','���ۿͻ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customercate_02','5c61d044eef24d30a7af4d540ca1db93',2,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customercate_03','5c61d044eef24d30a7af4d540ca1db93',3,'0','1','','�̻�');

--���ⷴ������
delete from t_bd_enumdata where fid='9c02c044a8314de2b546f77cf4368cd8';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('9c02c044a8314de2b546f77cf4368cd8','bd_enumdata','���ⷴ������','1','B','0','����Ԥ������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('feedback_content01','feedback_content02','feedback_content03','feedback_content04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('feedback_content01','9c02c044a8314de2b546f77cf4368cd8',1,'0','1','','��Ʒ���⣺�����ġ��޴���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('feedback_content02','9c02c044a8314de2b546f77cf4368cd8',2,'0','1','','������⣺���³�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('feedback_content03','9c02c044a8314de2b546f77cf4368cd8',3,'0','1','','��װ���������ġ�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('feedback_content04','9c02c044a8314de2b546f77cf4368cd8',4,'0','1','','�������⣺�ͻ����ġ�����������');

--ԤԼʧ��ԭ��
delete from t_bd_enumdata where fid='e068276ac6f3470989b7bd9d57d50b23';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e068276ac6f3470989b7bd9d57d50b23','bd_enumdata','ԤԼʧ��ԭ��','1','B','0','����Ԥ������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('appoint_failreason01','appoint_failreason02','appoint_failreason03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('appoint_failreason01','e068276ac6f3470989b7bd9d57d50b23',1,'0','1','','δ�ͻ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('appoint_failreason02','e068276ac6f3470989b7bd9d57d50b23',2,'0','1','','�ֳ����߱���װ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('appoint_failreason03','e068276ac6f3470989b7bd9d57d50b23',3,'0','1','','�ͻ�����');

--�깤�㱨˵��
delete from t_bd_enumdata where fid='4862c51076e847d2ab3ccc89849d24b6';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4862c51076e847d2ab3ccc89849d24b6','bd_enumdata','�깤�㱨˵��','1','B','0','����Ԥ������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('finish_result01','finish_result02','finish_result03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('finish_result01','4862c51076e847d2ab3ccc89849d24b6',1,'0','1','','ȫ���깤');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('finish_result02','4862c51076e847d2ab3ccc89849d24b6',2,'0','1','','�����깤�����ٴ�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('finish_result03','4862c51076e847d2ab3ccc89849d24b6',3,'0','1','','�����깤������Ҫ������');

--������Ŀ
delete from t_bd_enumdata where fid='dc4a0db14dfe440fbecf4dc13a78a62b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('dc4a0db14dfe440fbecf4dc13a78a62b','bd_enumdata','������Ŀ','1','B','0','�������',4,'1','0');
delete from t_bd_enumdataentry where fentryid in('service_01','service_02','service_03','service_04','service_05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('service_01','dc4a0db14dfe440fbecf4dc13a78a62b',1,'0','1','','��װ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('service_02','dc4a0db14dfe440fbecf4dc13a78a62b',2,'0','1','','ά�޷���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('service_03','dc4a0db14dfe440fbecf4dc13a78a62b',3,'0','1','','�������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('service_04','dc4a0db14dfe440fbecf4dc13a78a62b',4,'0','1','','̨�氲װ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('service_05','dc4a0db14dfe440fbecf4dc13a78a62b',5,'0','1','','��ʱ��װ');

--��������
delete from t_bd_enumdata where fid='99222505751543559a216725d96d6094';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('99222505751543559a216725d96d6094','bd_enumdata','��������','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('quote_type_01','quote_type_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('quote_type_01','99222505751543559a216725d96d6094',1,'0','1','','��׼����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('quote_type_02','99222505751543559a216725d96d6094',2,'0','1','','���屨��');

--ȡ������
delete from t_bd_enumdata where fid='ab4dd4d76a05448dbbdb1cb2fffa0b96';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ab4dd4d76a05448dbbdb1cb2fffa0b96','bd_enumdata','ȡ������','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('roundingrule_01','roundingrule_02','roundingrule_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('roundingrule_01','ab4dd4d76a05448dbbdb1cb2fffa0b96',1,'0','1','','��λ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('roundingrule_02','ab4dd4d76a05448dbbdb1cb2fffa0b96',2,'0','1','','ʮλ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('roundingrule_03','ab4dd4d76a05448dbbdb1cb2fffa0b96',3,'0','1','','��λ');

--װ��״̬
delete from t_bd_enumdata where fid='b6fbaac695ec4e0c88de136643e82348';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('b6fbaac695ec4e0c88de136643e82348','bd_enumdata','װ��״̬','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('renovation_sta_01','renovation_sta_02','renovation_sta_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_sta_01','b6fbaac695ec4e0c88de136643e82348',1,'0','1','','ë��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_sta_02','b6fbaac695ec4e0c88de136643e82348',2,'0','1','','��װ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('renovation_sta_03','b6fbaac695ec4e0c88de136643e82348',3,'0','1','','��װ');

--�������ۼ�(ѡ�����)
delete from t_bd_enumdata where fid='3f8fba3552b947808da532d117f87543';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('3f8fba3552b947808da532d117f87543','bd_enumdata','�������ۼ�(ѡ�����)','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('salprice_01','salprice_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('salprice_01','3f8fba3552b947808da532d117f87543',1,'0','1','','���ۼ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('salprice_02','3f8fba3552b947808da532d117f87543',2,'0','1','','�ɹ���');

--¥������
delete from t_bd_enumdata where fid='4c080bdcff5b4af49623b71a272f577f';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4c080bdcff5b4af49623b71a272f577f','bd_enumdata','¥������','1','B','0','��������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('building_type_01','building_type_02','building_type_03','building_type_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('building_type_01','4c080bdcff5b4af49623b71a272f577f',1,'0','1','','��Ʒ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('building_type_02','4c080bdcff5b4af49623b71a272f577f',2,'0','1','','�Խ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('building_type_03','4c080bdcff5b4af49623b71a272f577f',3,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('building_type_04','4c080bdcff5b4af49623b71a272f577f',4,'0','1','','���ʷ�');

--֪ʶ����
delete from t_bd_enumdata where fid='4dc6a4fae1db424d9749eaf0db22eb59';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4dc6a4fae1db424d9749eaf0db22eb59','bd_enumdata','֪ʶ����','1','B','0','��������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('knowledge_01','knowledge_02','knowledge_03','knowledge_04','knowledge_05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('knowledge_01','4dc6a4fae1db424d9749eaf0db22eb59',1,'0','1','','�ҵ��ĵ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('knowledge_02','4dc6a4fae1db424d9749eaf0db22eb59',2,'0','1','','��˾�ĵ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('knowledge_03','4dc6a4fae1db424d9749eaf0db22eb59',3,'0','1','','��ͬ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('knowledge_04','4dc6a4fae1db424d9749eaf0db22eb59',4,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('knowledge_05','4dc6a4fae1db424d9749eaf0db22eb59',5,'0','1','','��չѧϰ');

--���۷�ʽ
delete from t_bd_enumdata where fid='4f64c8be2d1c4b13bc3b3cdf0d99e2a2';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4f64c8be2d1c4b13bc3b3cdf0d99e2a2','bd_enumdata','���۷�ʽ','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('price_adjust_01','price_adjust_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('price_adjust_01','4f64c8be2d1c4b13bc3b3cdf0d99e2a2',1,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('price_adjust_02','4f64c8be2d1c4b13bc3b3cdf0d99e2a2',2,'0','1','','���¶���');

--�ɹ��˻���������
delete from t_bd_enumdata where fid='52ce0849e2b9404c92632115aeee267f';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('52ce0849e2b9404c92632115aeee267f','bd_enumdata','�ɹ��˻���������','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('postockreturn_billtype_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('postockreturn_billtype_01','52ce0849e2b9404c92632115aeee267f',1,'0','1','','��׼�ɹ��˻�');

--�����˻�ҵ������
delete from t_bd_enumdata where fid='568da3781a664adb985bee1bc1199355';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('568da3781a664adb985bee1bc1199355','bd_enumdata','�����˻�ҵ������','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('sostockreturn_biztype_01','sostockreturn_biztype_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sostockreturn_biztype_01','568da3781a664adb985bee1bc1199355',1,'0','1','','�����˻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sostockreturn_biztype_02','568da3781a664adb985bee1bc1199355',1,'0','1','','�˻��˿�');

--�����˻�����
delete from t_bd_enumdata where fid='7d7c14c84eb248c68cb734f5977b149a';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('7d7c14c84eb248c68cb734f5977b149a','bd_enumdata','�����˻�����','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('sostockreturn_scenetype_01','sostockreturn_scenetype_02','sostockreturn_scenetype_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sostockreturn_scenetype_01','7d7c14c84eb248c68cb734f5977b149a',1,'0','1','','�����˻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sostockreturn_scenetype_02','7d7c14c84eb248c68cb734f5977b149a',2,'0','1','','�˻��˿�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sostockreturn_scenetype_03','7d7c14c84eb248c68cb734f5977b149a',3,'0','1','','�˻�����');

--�ɹ��˻�����
delete from t_bd_enumdata where fid='142264e911fa4ff2a80c41b62e477192';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('142264e911fa4ff2a80c41b62e477192','bd_enumdata','�ɹ��˻�����','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('postockreturn_scenetype_01','postockreturn_scenetype_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('postockreturn_scenetype_01','142264e911fa4ff2a80c41b62e477192',1,'0','1','','�����˻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('postockreturn_scenetype_02','142264e911fa4ff2a80c41b62e477192',2,'0','1','','�˻��˿�');

--���۵����״̬
delete from t_bd_enumdata where fid='0c16447ff2dc44858ea788ed4431d350';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('0c16447ff2dc44858ea788ed4431d350','bd_enumdata','���۵����״̬','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('price_audit01','price_audit02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('price_audit01','0c16447ff2dc44858ea788ed4431d350',1,'0','1','','δ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('price_audit02','0c16447ff2dc44858ea788ed4431d350',2,'0','1','','�����');

--��֧��¼֧����ʽ
delete from t_bd_enumdata where fid='0c65220772934802ad4d83b5a334854c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('0c65220772934802ad4d83b5a334854c','bd_enumdata','��֧��¼֧����ʽ','1','B','0','��������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('payway_01','payway_04','payway_05','payway_06','payway_07','payway_08','payway_09','payway_10','payway_11','payway_12','payway_13');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_01','0c65220772934802ad4d83b5a334854c',1,'0','1','','�˻�֧��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_04','0c65220772934802ad4d83b5a334854c',4,'0','1','','����֧��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_05','0c65220772934802ad4d83b5a334854c',5,'0','1','','�ֽ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_06','0c65220772934802ad4d83b5a334854c',6,'0','1','','����ת��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_07','0c65220772934802ad4d83b5a334854c',7,'0','1','','֧����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_08','0c65220772934802ad4d83b5a334854c',8,'0','1','','΢��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_09','0c65220772934802ad4d83b5a334854c',9,'0','1','','��ʼ������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_10','0c65220772934802ad4d83b5a334854c',10,'0','1','','�����ֵ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_11','0c65220772934802ad4d83b5a334854c',11,'0','1','','ˢ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_12','0c65220772934802ad4d83b5a334854c',12,'0','1','','���˴���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('payway_13','0c65220772934802ad4d83b5a334854c',13,'0','1','','�̳�����');

--��������(ѡ�����)
delete from t_bd_enumdata where fid='38623191fe32498dbfcbd69405620c45';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('38623191fe32498dbfcbd69405620c45','bd_enumdata','��������(ѡ�����)','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('definedprice_01','definedprice_02','definedprice_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('definedprice_01','38623191fe32498dbfcbd69405620c45',1,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('definedprice_02','38623191fe32498dbfcbd69405620c45',2,'0','1','','���ۼ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('definedprice_03','38623191fe32498dbfcbd69405620c45',3,'0','1','','�ɹ���');

--���淢����Χ
delete from t_bd_enumdata where fid='277c837437a0403bb3b2037150e9a1d8';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('277c837437a0403bb3b2037150e9a1d8','bd_enumdata','���淢����Χ','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('send_range01','send_range02','send_range03','send_range04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('send_range01','277c837437a0403bb3b2037150e9a1d8',1,'0','1','','ȫ���Ա');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('send_range02','277c837437a0403bb3b2037150e9a1d8',2,'0','1','','ָ����Ա');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('send_range03','277c837437a0403bb3b2037150e9a1d8',3,'0','1','','����Эͬ��ҵ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('send_range04','277c837437a0403bb3b2037150e9a1d8',4,'0','1','','ָ��Эͬ��ҵ');

--����ɹ���(ѡ�����)
delete from t_bd_enumdata where fid='29760c0b11614b01b70635e2cd8ae208';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('29760c0b11614b01b70635e2cd8ae208','bd_enumdata','����ɹ���(ѡ�����)','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('purprice_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('purprice_01','29760c0b11614b01b70635e2cd8ae208',1,'0','1','','�ɹ���');

--�̻�����״̬
delete from t_bd_enumdata where fid='ea62f11ac91e49a4b4ed768fd2dc5239';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ea62f11ac91e49a4b4ed768fd2dc5239','bd_enumdata','�̻�����״̬','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('sht_serstatus00','sht_serstatus01','sht_serstatus02','sht_serstatus03','sht_serstatus04','sht_serstatus05','sht_serstatus06','sht_serstatus07','sht_serstatus08','sht_serstatus09');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus00','ea62f11ac91e49a4b4ed768fd2dc5239',0,'0','1','','�ݸ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus01','ea62f11ac91e49a4b4ed768fd2dc5239',1,'0','1','','��ȡ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus02','ea62f11ac91e49a4b4ed768fd2dc5239',2,'0','1','','�Ѿܵ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus03','ea62f11ac91e49a4b4ed768fd2dc5239',3,'0','1','','���ύ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus04','ea62f11ac91e49a4b4ed768fd2dc5239',4,'0','1','','ƽ̨���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus05','ea62f11ac91e49a4b4ed768fd2dc5239',5,'0','1','','�̻�ȷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus06','ea62f11ac91e49a4b4ed768fd2dc5239',6,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus07','ea62f11ac91e49a4b4ed768fd2dc5239',7,'0','1','','���깤');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus08','ea62f11ac91e49a4b4ed768fd2dc5239',8,'0','1','','�������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sht_serstatus09','ea62f11ac91e49a4b4ed768fd2dc5239',9,'0','1','','��ָ�ɷ�����');

--�ɹ��˻�ҵ������
delete from t_bd_enumdata where fid='ec5d272c38d84b3685b29a3e0f6a2d4d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ec5d272c38d84b3685b29a3e0f6a2d4d','bd_enumdata','�ɹ��˻�ҵ������','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('postockreturn_biztype_01','postockreturn_biztype_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('postockreturn_biztype_01','ec5d272c38d84b3685b29a3e0f6a2d4d',1,'0','1','','�����˻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('postockreturn_biztype_02','ec5d272c38d84b3685b29a3e0f6a2d4d',1,'0','1','','�˻��˿�');

--�����˻���������
delete from t_bd_enumdata where fid='fe03b519ad66428889020f506cf6316d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('fe03b519ad66428889020f506cf6316d','bd_enumdata','�����˻���������','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('sostockreturn_billtype_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sostockreturn_billtype_01','fe03b519ad66428889020f506cf6316d',1,'0','1','','��׼�����˻�');

--����״̬
delete from t_bd_enumdata where fid='fa104d958c0a451c879e326d937ef3d9';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('fa104d958c0a451c879e326d937ef3d9','bd_enumdata','����״̬','1','B','0','��������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('handle_sta001','handle_sta002','handle_sta003');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('handle_sta001','fa104d958c0a451c879e326d937ef3d9',1,'0','1','','���ύ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('handle_sta002','fa104d958c0a451c879e326d937ef3d9',2,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('handle_sta003','fa104d958c0a451c879e326d937ef3d9',3,'0','1','','��ȡ��');

--�ۺ����
delete from t_bd_enumdata where fid='2be014babfd6463480aa7f789cac2527';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('2be014babfd6463480aa7f789cac2527','bd_enumdata','�ۺ����','1','B','0','�ͷ�����',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('aft_origin_01','aft_origin_02','aft_origin_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_origin_01','2be014babfd6463480aa7f789cac2527',1,'0','1','','��ƷƷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_origin_02','2be014babfd6463480aa7f789cac2527',2,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_origin_03','2be014babfd6463480aa7f789cac2527',3,'0','1','','��װ����');

--��������
delete from t_bd_enumdata where fid='3452aacaeae6425699559a39c98a583d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('3452aacaeae6425699559a39c98a583d','bd_enumdata','��������','1','B','0','�ͷ�����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('customerscore_01','customerscore_02','customerscore_03','customerscore_04','customerscore_05','customerscore_06','customerscore_07','customerscore_08','customerscore_09','customerscore_10');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_01','3452aacaeae6425699559a39c98a583d',1,'0','1','','1');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_02','3452aacaeae6425699559a39c98a583d',2,'0','1','','2');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_03','3452aacaeae6425699559a39c98a583d',3,'0','1','','3');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_04','3452aacaeae6425699559a39c98a583d',4,'0','1','','4');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_05','3452aacaeae6425699559a39c98a583d',5,'0','1','','5');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_06','3452aacaeae6425699559a39c98a583d',6,'0','1','','6');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_07','3452aacaeae6425699559a39c98a583d',7,'0','1','','7');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_08','3452aacaeae6425699559a39c98a583d',8,'0','1','','8');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_09','3452aacaeae6425699559a39c98a583d',9,'0','1','','9');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customerscore_10','3452aacaeae6425699559a39c98a583d',10,'0','1','','10');

--��Ա����
delete from t_bd_enumdata where fid='eeda92534af245238ec6292526a79d29';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('eeda92534af245238ec6292526a79d29','bd_enumdata','��Ա����','1','B','0','�ͷ�����',1,'1','0');

--���������
delete from t_bd_enumdata where fid='3774fd5341964f73a49a31f3636e5817';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('3774fd5341964f73a49a31f3636e5817','bd_enumdata','���������','1','B','0','�ͷ�����',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('satisfaction_01','satisfaction_02','satisfaction_03','satisfaction_04','satisfaction_05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('satisfaction_01','3774fd5341964f73a49a31f3636e5817',1,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('satisfaction_02','3774fd5341964f73a49a31f3636e5817',2,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('satisfaction_03','3774fd5341964f73a49a31f3636e5817',3,'0','1','','һ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('satisfaction_04','3774fd5341964f73a49a31f3636e5817',4,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('satisfaction_05','3774fd5341964f73a49a31f3636e5817',5,'0','1','','�ǳ�������');

--�طõ�������
delete from t_bd_enumdata where fid='b54ab32a90334852b889dc5337605b3c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('b54ab32a90334852b889dc5337605b3c','bd_enumdata','�طõ�������','1','B','0','�ͷ�����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('vist_type_01','vist_type_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_type_01','b54ab32a90334852b889dc5337605b3c',1,'0','1','','����ط�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_type_02','b54ab32a90334852b889dc5337605b3c',2,'0','1','','�����ط�');

--���ε�λ����
delete from t_bd_enumdata where fid='bac7b6e59af74bdf8865ecbc562aac76';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('bac7b6e59af74bdf8865ecbc562aac76','bd_enumdata','���ε�λ����','1','B','0','�ͷ�����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('dutyunit_type_01','dutyunit_type_02','dutyunit_type_03','dutyunit_type_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('dutyunit_type_01','bac7b6e59af74bdf8865ecbc562aac76',1,'0','1','','��Ӧ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('dutyunit_type_02','bac7b6e59af74bdf8865ecbc562aac76',2,'0','1','','�ͻ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('dutyunit_type_03','bac7b6e59af74bdf8865ecbc562aac76',3,'0','1','','Ա��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('dutyunit_type_04','bac7b6e59af74bdf8865ecbc562aac76',4,'0','1','','����');

--�ۺ�״̬
delete from t_bd_enumdata where fid='901e070681fd48739e89929fc1f74656';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('901e070681fd48739e89929fc1f74656','bd_enumdata','�ۺ�״̬','1','B','0','�ͷ�����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('aft_service_01','aft_service_02','aft_service_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_service_01','901e070681fd48739e89929fc1f74656',1,'0','1','','ִ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_service_02','901e070681fd48739e89929fc1f74656',2,'0','1','','���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_service_03','901e070681fd48739e89929fc1f74656',3,'0','1','','�ر�');

--�ۺ��������
delete from t_bd_enumdata where fid='84b45a846d474552b02c74728d21e1c3';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('84b45a846d474552b02c74728d21e1c3','bd_enumdata','�ۺ��������','1','B','0','�ͷ�����',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('afterquestion_type_01','afterquestion_type_02','afterquestion_type_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('afterquestion_type_01','84b45a846d474552b02c74728d21e1c3',1,'0','1','','��Ʒ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('afterquestion_type_02','84b45a846d474552b02c74728d21e1c3',2,'0','1','','��װ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('afterquestion_type_03','84b45a846d474552b02c74728d21e1c3',3,'0','1','','��������');

--�ط÷�ʽ
delete from t_bd_enumdata where fid='b058b5acafed4091a0bf2e178330b68f';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('b058b5acafed4091a0bf2e178330b68f','bd_enumdata','�ط÷�ʽ','1','B','0','�ͷ�����',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('vist_canal_01','vist_canal_02','vist_canal_03','vist_canal_04','vist_canal_05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_canal_01','b058b5acafed4091a0bf2e178330b68f',1,'0','1','','�绰�ط�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_canal_02','b058b5acafed4091a0bf2e178330b68f',2,'0','1','','΢�Żط�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_canal_03','b058b5acafed4091a0bf2e178330b68f',3,'0','1','','�ʼ��ط�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_canal_04','b058b5acafed4091a0bf2e178330b68f',4,'0','1','','���Żط�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_canal_05','b058b5acafed4091a0bf2e178330b68f',5,'0','1','','������ʽ');

--����֪ͨ��������
delete from t_bd_enumdata where fid='e393e06cf3a542e8bd15cb90a6678798';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e393e06cf3a542e8bd15cb90a6678798','bd_enumdata','����֪ͨ��������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('deliverynotice_type_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('deliverynotice_type_01','e393e06cf3a542e8bd15cb90a6678798',1,'0','1','','��׼����֪ͨ');

--����������������
delete from t_bd_enumdata where fid='e6471ad0b15f4dcca9175b642ee016ed';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e6471ad0b15f4dcca9175b642ee016ed','bd_enumdata','����������������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('invtransfer_biztype_01','invtransfer_biztype_02','invtransfer_biztype_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('invtransfer_biztype_01','e6471ad0b15f4dcca9175b642ee016ed',1,'0','1','','��׼');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('invtransfer_biztype_02','e6471ad0b15f4dcca9175b642ee016ed',2,'0','1','','���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('invtransfer_biztype_03','e6471ad0b15f4dcca9175b642ee016ed',3,'0','1','','ά��');

--����״̬
delete from t_bd_enumdata where fid='e71b8843c8054e208f8d981023e6a577';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e71b8843c8054e208f8d981023e6a577','bd_enumdata','����״̬','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('allotstatus_01','allotstatus_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('allotstatus_01','e71b8843c8054e208f8d981023e6a577',1,'0','1','','��;');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('allotstatus_02','e71b8843c8054e208f8d981023e6a577',2,'0','1','','�ѵ���');

--��������������
delete from t_bd_enumdata where fid='e3fd458b88d2422b9db5bcb8aa263280';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e3fd458b88d2422b9db5bcb8aa263280','bd_enumdata','��������������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('invtransfer_billtype_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('invtransfer_billtype_01','e3fd458b88d2422b9db5bcb8aa263280',1,'0','1',' ','��׼������');

--�ɹ��˻�֪ͨ��������
delete from t_bd_enumdata where fid='e483db916c514fa3a4e904b797c58c39';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e483db916c514fa3a4e904b797c58c39','bd_enumdata','�ɹ��˻�֪ͨ��������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('poreturnnotice_type_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('poreturnnotice_type_01','e483db916c514fa3a4e904b797c58c39',1,'0','1','','��׼�ɹ��˻�֪ͨ');

--�����������뵥������
delete from t_bd_enumdata where fid='de9076ecf7404809b2a8415052acc756';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('de9076ecf7404809b2a8415052acc756','bd_enumdata','�����������뵥������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('otheroutstockreq_billtype_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('otheroutstockreq_billtype_01','de9076ecf7404809b2a8415052acc756',1,'0','1','','��׼������������');

--���������뵥������
delete from t_bd_enumdata where fid='8ad9eca64b8546f9b922e987998e6617';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('8ad9eca64b8546f9b922e987998e6617','bd_enumdata','���������뵥������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('invtransferreq_billtype_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('invtransferreq_billtype_01','8ad9eca64b8546f9b922e987998e6617',1,'0','1','','��׼����������');

--�ɹ���ⵥ������
delete from t_bd_enumdata where fid='9ad0515262c74491a429b52fd12c7f0d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('9ad0515262c74491a429b52fd12c7f0d','bd_enumdata','�ɹ���ⵥ������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('poinstock_billtype_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('poinstock_billtype_01','9ad0515262c74491a429b52fd12c7f0d',1,'0','1','','��׼�ɹ����');

--������ⵥ������
delete from t_bd_enumdata where fid='ad2116beb77b4fa5b6a44bb862d74865';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ad2116beb77b4fa5b6a44bb862d74865','bd_enumdata','������ⵥ������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('otherinstock_billtype_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('otherinstock_billtype_01','ad2116beb77b4fa5b6a44bb862d74865',1,'0','1','','��׼�������');

--���۳��ⵥ������
delete from t_bd_enumdata where fid='a939ca355b4940df88fa95ec9da7cea2';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('a939ca355b4940df88fa95ec9da7cea2','bd_enumdata','���۳��ⵥ������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('sostockout_type_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sostockout_type_01','a939ca355b4940df88fa95ec9da7cea2',1,'0','1','','��׼���۳���');

--�ջ�֪ͨ��������
delete from t_bd_enumdata where fid='30bfd5a4e55f4d6da5209b16a28d9015';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('30bfd5a4e55f4d6da5209b16a28d9015','bd_enumdata','�ջ�֪ͨ��������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('receiptnotice_type_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('receiptnotice_type_01','30bfd5a4e55f4d6da5209b16a28d9015',1,'0','1','','��׼�ջ�֪ͨ');

--ҵ�񳡾�
delete from t_bd_enumdata where fid='172058da4c914c0e903f0905a2aaae6b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('172058da4c914c0e903f0905a2aaae6b','bd_enumdata','ҵ�񳡾�','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('scene_01','scene_02','scene_03','scene_04','scene_05','scene_06','scene_07','scene_08','scene_09');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scene_01','172058da4c914c0e903f0905a2aaae6b',1,'0','1','','�ɹ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scene_02','172058da4c914c0e903f0905a2aaae6b',2,'0','1','','�������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scene_03','172058da4c914c0e903f0905a2aaae6b',3,'0','1','','�����˻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scene_04','172058da4c914c0e903f0905a2aaae6b',4,'0','1','','��ӯ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scene_05','172058da4c914c0e903f0905a2aaae6b',5,'0','1','','���۳���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scene_06','172058da4c914c0e903f0905a2aaae6b',6,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scene_07','172058da4c914c0e903f0905a2aaae6b',7,'0','1','','�ɹ��˻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scene_08','172058da4c914c0e903f0905a2aaae6b',8,'0','1','','��ӯ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scene_09','172058da4c914c0e903f0905a2aaae6b',9,'0','1','','������');

--��淽��
delete from t_bd_enumdata where fid='60f44ac04ab1460a9f2239459c1df3b1';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('60f44ac04ab1460a9f2239459c1df3b1','bd_enumdata','��淽��','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('stockdirection_01','stockdirection_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('stockdirection_01','60f44ac04ab1460a9f2239459c1df3b1',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('stockdirection_02','60f44ac04ab1460a9f2239459c1df3b1',2,'0','1','','���');

--����������뵥������
delete from t_bd_enumdata where fid='7b8dbe4cbfee4bdf822b623255bc90a6';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('7b8dbe4cbfee4bdf822b623255bc90a6','bd_enumdata','����������뵥������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('otherinstockreq_billtype_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('otherinstockreq_billtype_01','7b8dbe4cbfee4bdf822b623255bc90a6',1,'0','1','','��׼�����������');

--�������ⵥ������
delete from t_bd_enumdata where fid='5da8f1dc361445d497b5f054c6f1679c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('5da8f1dc361445d497b5f054c6f1679c','bd_enumdata','�������ⵥ������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('otherstockout_type_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('otherstockout_type_01','5da8f1dc361445d497b5f054c6f1679c',1,'0','1',' ','��׼��������');

--�����˻�֪ͨ��������
delete from t_bd_enumdata where fid='4ad792ba14a448caa823c726a15ac301';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4ad792ba14a448caa823c726a15ac301','bd_enumdata','�����˻�֪ͨ��������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('soreturnnotice_type_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('soreturnnotice_type_01','4ad792ba14a448caa823c726a15ac301',1,'0','1','','��׼�����˻�֪ͨ');

--Դ������
delete from t_bd_enumdata where fid='ee574cec07424863867b7185738310c2';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ee574cec07424863867b7185738310c2','bd_enumdata','Դ������','1','B','0','������',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('ydj_purchaseorder','ydj_order','ydj_inventorysheet','ydj_saleintention');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ydj_purchaseorder','ee574cec07424863867b7185738310c2',1,'0','1','','�ɹ�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ydj_order','ee574cec07424863867b7185738310c2',2,'0','1','','���ۺ�ͬ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ydj_inventorysheet','ee574cec07424863867b7185738310c2',3,'0','1','','����̵�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ydj_saleintention','ee574cec07424863867b7185738310c2',4,'0','1','','��������');

--�ļ�����
delete from t_bd_enumdata where fid='611a2dac36ba4dafbbcca2f7e4ac908b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('611a2dac36ba4dafbbcca2f7e4ac908b','bd_enumdata','�ļ�����','1','B','0','����',0,'1','0');
delete from t_bd_enumdataentry where fentryid in('Effect_Drawing','Other','Primeval_Drawing');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('Effect_Drawing','611a2dac36ba4dafbbcca2f7e4ac908b',0,'0','1','','Ч��ͼ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('Other','611a2dac36ba4dafbbcca2f7e4ac908b',0,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('Primeval_Drawing','611a2dac36ba4dafbbcca2f7e4ac908b',0,'0','1','','ԭʼͼֽ');

--��������
delete from t_bd_enumdata where fid='dd815e8aa19d4728b573f354ac08a271';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('dd815e8aa19d4728b573f354ac08a271','bd_enumdata','��������','1','B','0','����',0,'0','0');
delete from t_bd_enumdataentry where fentryid in('workyear1','workyear2','workyear3','workyear4','workyear5');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('workyear1','dd815e8aa19d4728b573f354ac08a271',0,'0','1','','�޹�������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('workyear2','dd815e8aa19d4728b573f354ac08a271',0,'0','1','','1-3��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('workyear3','dd815e8aa19d4728b573f354ac08a271',0,'0','1','','3-5��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('workyear4','dd815e8aa19d4728b573f354ac08a271',0,'0','1','','5-8��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('workyear5','dd815e8aa19d4728b573f354ac08a271',0,'0','1','','8������');

--����״̬
delete from t_bd_enumdata where fid='d8dbace340d7407b838e84ddaa982900';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('d8dbace340d7407b838e84ddaa982900','bd_enumdata','����״̬','1','B','0','����',0,'0','0');
delete from t_bd_enumdataentry where fentryid in('send_status01','send_status02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('send_status01','d8dbace340d7407b838e84ddaa982900',0,'0','1',' ','δ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('send_status02','d8dbace340d7407b838e84ddaa982900',0,'0','1',' ','�ѷ���');

--����״̬
delete from t_bd_enumdata where fid='eebfbbc9bd214fa0bcadac297bdefdcf';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('eebfbbc9bd214fa0bcadac297bdefdcf','bd_enumdata','����״̬','1','B','0','����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in ('settle_status01','settle_status01_01','settle_status02_01','settle_status02','settle_status03','settle_status04','settle_status05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settle_status01','eebfbbc9bd214fa0bcadac297bdefdcf',1,'0','1','','δ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settle_status01_01','eebfbbc9bd214fa0bcadac297bdefdcf',2,'0','1','','�ɽ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settle_status02_01','eebfbbc9bd214fa0bcadac297bdefdcf',3,'0','1','','���ֽ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settle_status02','eebfbbc9bd214fa0bcadac297bdefdcf',4,'0','1','','�ѽ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settle_status03','eebfbbc9bd214fa0bcadac297bdefdcf',5,'0','1','','�˿���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settle_status04','eebfbbc9bd214fa0bcadac297bdefdcf',6,'0','1','','���˿�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settle_status05','eebfbbc9bd214fa0bcadac297bdefdcf',7,'0','1','','���踶��');

--����
delete from t_bd_enumdata where fid='f789c296a06247ffad978acb945a3fab';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('f789c296a06247ffad978acb945a3fab','bd_enumdata','����','1','B','0','ϵͳԤ��',0,'0','0');
delete from t_bd_enumdataentry where fentryid in('remind_1','remind_2','remind_3','remind_4','remind_5','remind_6');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('remind_1','f789c296a06247ffad978acb945a3fab',0,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('remind_2','f789c296a06247ffad978acb945a3fab',0,'0','1','','ʱ�䷢��ʱ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('remind_3','f789c296a06247ffad978acb945a3fab',0,'0','1','','30����ǰ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('remind_4','f789c296a06247ffad978acb945a3fab',0,'0','1','','1Сʱǰ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('remind_5','f789c296a06247ffad978acb945a3fab',0,'0','1','','2Сʱǰ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('remind_6','f789c296a06247ffad978acb945a3fab',0,'0','1','','1��ǰ');

--��֤״̬
delete from t_bd_enumdata where fid='dd815e8aa19d4728b573f354ac08a280';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('dd815e8aa19d4728b573f354ac08a280','bd_enumdata','��֤״̬','1','B','0','ϵͳԤ��',0,'0','0');
delete from t_bd_enumdataentry where fentryid in('auth1','auth2','auth3','auth4');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('auth1','dd815e8aa19d4728b573f354ac08a280',1,'0','1','','δ��֤');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('auth2','dd815e8aa19d4728b573f354ac08a280',2,'0','1','','����֤');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('auth3','dd815e8aa19d4728b573f354ac08a280',3,'0','1','','�ȴ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('auth4','dd815e8aa19d4728b573f354ac08a280',4,'0','1','','��֤��ͨ��');

--֪ͨ��ʽ
delete from t_bd_enumdata where fid='9759b271e357469089b542ccba79023d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('9759b271e357469089b542ccba79023d','bd_enumdata','֪ͨ��ʽ','1','B','0','ϵͳԤ��',0,'0','0');
delete from t_bd_enumdataentry where fentryid in('ew_message001','ew_message002');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_message001','9759b271e357469089b542ccba79023d',1,'0','1','','ϵͳ��Ϣ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_message002','9759b271e357469089b542ccba79023d',2,'0','1','','�ʼ�');

--����״̬
delete from t_bd_enumdata where fid='832bd3b98cd34d8481242564485ec733';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('832bd3b98cd34d8481242564485ec733','bd_enumdata','����״̬','1','B','0','ϵͳԤ��',0,'0','0');
delete from t_bd_enumdataentry where fentryid in('ew_start001','ew_start002','ew_start003','ew_start004','ew_start005','ew_start006');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_start001','832bd3b98cd34d8481242564485ec733',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_start002','832bd3b98cd34d8481242564485ec733',2,'0','1','','�ر�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_start003','832bd3b98cd34d8481242564485ec733',3,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_start004','832bd3b98cd34d8481242564485ec733',4,'0','1','','ִ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_start005','832bd3b98cd34d8481242564485ec733',5,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_start006','832bd3b98cd34d8481242564485ec733',6,'0','1','','�˹���ֹ');

--��¼����
delete from t_bd_enumdata where fid='59030697371e4e09975e99fb040a61cf';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('59030697371e4e09975e99fb040a61cf','bd_enumdata','��¼����','1','B','0','ϵͳԤ��',0,'0','0');
delete from t_bd_enumdataentry where fentryid in('recordtype_01','recordtype_02','recordtype_03','recordtype_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('recordtype_01','59030697371e4e09975e99fb040a61cf',1,'0','1','','�ֹ���¼');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('recordtype_02','59030697371e4e09975e99fb040a61cf',2,'0','1','','ϵͳ��־');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('recordtype_03','59030697371e4e09975e99fb040a61cf',3,'0','1','','������־');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('recordtype_04','59030697371e4e09975e99fb040a61cf',4,'0','1','','Эͬ��־');

--������Դ����
delete from t_bd_enumdata where fid='077d5991b58546019a05bee9a5497d26';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('077d5991b58546019a05bee9a5497d26','bd_enumdata','������Դ����','1','B','0','ϵͳԤ��',0,'0','0');
delete from t_bd_enumdataentry where fentryid in('billsourcetype_01','billsourcetype_02','billsourcetype_03','billsourcetype_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('billsourcetype_01','077d5991b58546019a05bee9a5497d26',1,'0','1','','�û��ֹ�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('billsourcetype_02','077d5991b58546019a05bee9a5497d26',2,'0','1','','����ת������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('billsourcetype_03','077d5991b58546019a05bee9a5497d26',3,'0','1','','����Эͬ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('billsourcetype_04','077d5991b58546019a05bee9a5497d26',4,'0','1','','��̨ϵͳ����');

--��ҵ֤������
delete from t_bd_enumdata where fid='244052297427062785';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('244052297427062785','bd_enumdata','��ҵ֤������','1','B','0','ϵͳԤ��',0,'0','0');
delete from t_bd_enumdataentry where fentryid in('cer_type001','cer_type002');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cer_type001','244052297427062785',1,'0','1','','��֤��һִ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cer_type002','244052297427062785',2,'0','1','','��ʽӪҵִ��');

--˰����
delete from t_bd_enumdata where fid='104814d22a544f7bb448fe709bc05adb';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('104814d22a544f7bb448fe709bc05adb','bd_enumdata','˰����','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('taxtype_01','taxtype_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('taxtype_01','104814d22a544f7bb448fe709bc05adb',1,'0','1','','С��ģ��˰��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('taxtype_02','104814d22a544f7bb448fe709bc05adb',2,'0','1','','һ����˰��');

--�ұ�
delete from t_bd_enumdata where fid='105bc9da1d0046b89e38bf5dbd690f10';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('105bc9da1d0046b89e38bf5dbd690f10','bd_enumdata','�ұ�','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('fcurrency_type_01');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fcurrency_type_01','105bc9da1d0046b89e38bf5dbd690f10',1,'0','1','','�����');

--ֵ����
delete from t_bd_enumdata where fid='02c887c00614441bbd417a01a2e96002';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('02c887c00614441bbd417a01a2e96002','bd_enumdata','ֵ����','1','B','0','ϵͳԤ��',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('val_type_01','val_type_02','val_type_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('val_type_01','02c887c00614441bbd417a01a2e96002',1,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('val_type_02','02c887c00614441bbd417a01a2e96002',2,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('val_type_03','02c887c00614441bbd417a01a2e96002',3,'0','1','','�ı�');

--�����λ
delete from t_bd_enumdata where fid='03864152a64049f2801a8661daf8f5e5';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('03864152a64049f2801a8661daf8f5e5','bd_enumdata','�����λ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('areaunit_01','areaunit_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('areaunit_01','03864152a64049f2801a8661daf8f5e5',1,'0','1','','��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('areaunit_02','03864152a64049f2801a8661daf8f5e5',2,'0','1','','����');

--����״̬
delete from t_bd_enumdata where fid='1240bfd674d64f949d6df19c54ecbce4';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('1240bfd674d64f949d6df19c54ecbce4','bd_enumdata','����״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('A','B','C','D','E');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('A','1240bfd674d64f949d6df19c54ecbce4',1,'0','1','','�����ݴ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('B','1240bfd674d64f949d6df19c54ecbce4',2,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('C','1240bfd674d64f949d6df19c54ecbce4',3,'0','1','','�������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('D','1240bfd674d64f949d6df19c54ecbce4',4,'0','1','','���ύ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('E','1240bfd674d64f949d6df19c54ecbce4',5,'0','1','','�����');



--�طý���
delete from t_bd_enumdata where fid='12cc281db663492aa88a59f74390dca0';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('12cc281db663492aa88a59f74390dca0','bd_enumdata','�طý���','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('vist_conclusion_01','vist_conclusion_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_conclusion_01','12cc281db663492aa88a59f74390dca0',0,'0','1',' ','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_conclusion_02','12cc281db663492aa88a59f74390dca0',0,'0','1',' ','ת�ۺ�');

--���ڸ�ʽ
delete from t_bd_enumdata where fid='2c6ed1337af746b6b0990f145b146110';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('2c6ed1337af746b6b0990f145b146110','bd_enumdata','���ڸ�ʽ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('yyyy','yyyyMM','yyyyMMdd');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('yyyy','2c6ed1337af746b6b0990f145b146110',1,'0','1','','yyyy');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('yyyyMM','2c6ed1337af746b6b0990f145b146110',2,'0','1','','yyyyMM');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('yyyyMMdd','2c6ed1337af746b6b0990f145b146110',3,'0','1','','yyyyMMdd');

--��������
delete from t_bd_enumdata where fid='271ae61e73744454b36c05fe6dc602fe';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('271ae61e73744454b36c05fe6dc602fe','bd_enumdata','��������','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('164748379899957248','164748379899957249');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164748379899957248','271ae61e73744454b36c05fe6dc602fe',0,'0','1',' ','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164748379899957249','271ae61e73744454b36c05fe6dc602fe',0,'0','1',' ','����');

--֧����;
delete from t_bd_enumdata where fid='3bf2d68a234a4b85b5ff24ecaa75218b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('3bf2d68a234a4b85b5ff24ecaa75218b','bd_enumdata','֧����;','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('usage_type_01','usage_type_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('usage_type_01','3bf2d68a234a4b85b5ff24ecaa75218b',0,'0','1',' ','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('usage_type_02','3bf2d68a234a4b85b5ff24ecaa75218b',0,'0','1',' ','����');

--��ֵ��;
delete from t_bd_enumdata where fid='3e10e1e060b8400b98058acbe8f670a3';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('3e10e1e060b8400b98058acbe8f670a3','bd_enumdata','��ֵ��;','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('inpourtype_01','inpourtype_02','inpourtype_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('inpourtype_01','3e10e1e060b8400b98058acbe8f670a3',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('inpourtype_02','3e10e1e060b8400b98058acbe8f670a3',2,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('inpourtype_03','3e10e1e060b8400b98058acbe8f670a3',3,'0','1','','��֤���');

--���㵥���㷽ʽ
delete from t_bd_enumdata where fid='5cabe62de116444d945f6cdaed058701';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('5cabe62de116444d945f6cdaed058701','bd_enumdata','���㵥���㷽ʽ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('settleway_01','settleway_02','settleway_03','settleway_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleway_01','5cabe62de116444d945f6cdaed058701',1,'0','1','','����ת��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleway_02','5cabe62de116444d945f6cdaed058701',2,'0','1','','�ֽ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleway_03','5cabe62de116444d945f6cdaed058701',3,'0','1','','Эͬ�˻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleway_04','5cabe62de116444d945f6cdaed058701',4,'0','1','','����');

--��ַ����
delete from t_bd_enumdata where fid='5e1e3c7bda86427b9eb00f1be96a8738';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('5e1e3c7bda86427b9eb00f1be96a8738','bd_enumdata','��ַ����','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('169866826300395520','169866826300395521');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('169866826300395520','5e1e3c7bda86427b9eb00f1be96a8738',0,'0','1',' ','�����ַ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('169866826300395521','5e1e3c7bda86427b9eb00f1be96a8738',0,'0','1',' ','�����ַ');

--����״̬
delete from t_bd_enumdata where fid='4ad9d1898c0442f5b2829466b2da1161';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4ad9d1898c0442f5b2829466b2da1161','bd_enumdata','����״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('sersta01','sersta02','sersta03','sersta04','sersta05','sersta06','sersta07','sersta08','sersta09','sersta10','sersta11','sersta12','sersta13','sersta14');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta01','4ad9d1898c0442f5b2829466b2da1161',1,'0','1','','��ƽ̨�ɵ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta02','4ad9d1898c0442f5b2829466b2da1161',2,'0','1','','��ʦ��ȷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta03','4ad9d1898c0442f5b2829466b2da1161',3,'0','1','','�ܵ������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta04','4ad9d1898c0442f5b2829466b2da1161',4,'0','1','','��ʦ��ԤԼ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta05','4ad9d1898c0442f5b2829466b2da1161',5,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta06','4ad9d1898c0442f5b2829466b2da1161',6,'0','1','','���깤');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta07','4ad9d1898c0442f5b2829466b2da1161',7,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta08','4ad9d1898c0442f5b2829466b2da1161',8,'0','1','','���ط�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta09','4ad9d1898c0442f5b2829466b2da1161',9,'0','1','','�Զ��ɵ�ʧ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta10','4ad9d1898c0442f5b2829466b2da1161',10,'0','1','','���������ɵ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta11','4ad9d1898c0442f5b2829466b2da1161',11,'0','1','','�ر�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta12','4ad9d1898c0442f5b2829466b2da1161',12,'0','1','','���̻�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta13','4ad9d1898c0442f5b2829466b2da1161',13,'0','1','','��ƽ̨�˼�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta14','4ad9d1898c0442f5b2829466b2da1161',14,'0','1','','���̻�ȷ��');

--�ϼ�״̬
delete from t_bd_enumdata where fid='4ebecb64b4c549a2a3e2b459145491e8';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4ebecb64b4c549a2a3e2b459145491e8','bd_enumdata','�ϼ�״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('send_status_01','send_status_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('send_status_01','4ebecb64b4c549a2a3e2b459145491e8',1,'0','1','','���ϼ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('send_status_02','4ebecb64b4c549a2a3e2b459145491e8',2,'0','1','','���¼�');

--�ط�����
delete from t_bd_enumdata where fid='43140385e8164ec5a43059a2ad5aff59';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('43140385e8164ec5a43059a2ad5aff59','bd_enumdata','�ط�����','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('164744627453300736','164744627453300737','164744627453300738');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164744627453300736','43140385e8164ec5a43059a2ad5aff59',0,'0','1',' ','�ۺ�ط�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164744627453300737','43140385e8164ec5a43059a2ad5aff59',0,'0','1',' ','��ǰ�ط�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164744627453300738','43140385e8164ec5a43059a2ad5aff59',0,'0','1',' ','���ڻط�');

--��Ӫ����
delete from t_bd_enumdata where fid='49ff13782fea40448b5eced03e085d1b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('49ff13782fea40448b5eced03e085d1b','bd_enumdata','��Ӫ����','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('manage_type_01','manage_type_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('manage_type_01','49ff13782fea40448b5eced03e085d1b',1,'0','1','','��˾');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('manage_type_02','49ff13782fea40448b5eced03e085d1b',2,'0','1','','����');

--����Эͬ
delete from t_bd_enumdata where fid='62a2a640b015438dadb08e8d381b96db';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('62a2a640b015438dadb08e8d381b96db','bd_enumdata','����Эͬ','1','B','0','ϵͳԤ��',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('testsync_01','testsync_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('testsync_01','62a2a640b015438dadb08e8d381b96db',1,'0','1',' ','����1');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('testsync_02','62a2a640b015438dadb08e8d381b96db',2,'0','1',' ','����2');

--�ͻ���ν
delete from t_bd_enumdata where fid='648d8818db804b5fac281fb9462199c9';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('648d8818db804b5fac281fb9462199c9','bd_enumdata','�ͻ���ν','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('khcw0001','khcw0002');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('khcw0001','648d8818db804b5fac281fb9462199c9',0,'0','1',' ','��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('khcw0002','648d8818db804b5fac281fb9462199c9',0,'0','1',' ','Ů');

--��Ա��ģ
delete from t_bd_enumdata where fid='652e2b6256394f95ae204eaba304d6f5';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('652e2b6256394f95ae204eaba304d6f5','bd_enumdata','��Ա��ģ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('staff_type01','staff_type02','staff_type03','staff_type04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('staff_type01','652e2b6256394f95ae204eaba304d6f5',1,'0','1','','0-50��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('staff_type02','652e2b6256394f95ae204eaba304d6f5',2,'0','1','','50-500��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('staff_type03','652e2b6256394f95ae204eaba304d6f5',3,'0','1','','500-1000��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('staff_type04','652e2b6256394f95ae204eaba304d6f5',4,'0','1','','1000������');

--Ͷ�ߵ�״̬
delete from t_bd_enumdata where fid='68ffad4c193e42ec83a241b3cbbb7fd5';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('68ffad4c193e42ec83a241b3cbbb7fd5','bd_enumdata','Ͷ�ߵ�״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('complain_status01','complain_status02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('complain_status01','68ffad4c193e42ec83a241b3cbbb7fd5',1,'0','1','','����Ч');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('complain_status02','68ffad4c193e42ec83a241b3cbbb7fd5',2,'0','1','','�ѳ���');

--������������
delete from t_bd_enumdata where fid='6db01009193d4c8d8560d4e659b822c0';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('6db01009193d4c8d8560d4e659b822c0','bd_enumdata','������������','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('flowtype_01','flowtype_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('flowtype_01','6db01009193d4c8d8560d4e659b822c0',1,'0','1','','Ԥ������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('flowtype_02','6db01009193d4c8d8560d4e659b822c0',2,'0','1','','�����������˹�ָ��');

--�����嵥״̬
delete from t_bd_enumdata where fid='6e141fde5cd7441fbac5ae3f6fcac7d7';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('6e141fde5cd7441fbac5ae3f6fcac7d7','bd_enumdata','�����嵥״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('parcel_status_01','parcel_status_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('parcel_status_01','6e141fde5cd7441fbac5ae3f6fcac7d7',1,'0','1','','�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('parcel_status_02','6e141fde5cd7441fbac5ae3f6fcac7d7',2,'0','1','','�ѳ���');

--ҵ��״̬
delete from t_bd_enumdata where fid='82701bb9e13d463abd640b111e7d4dc4';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('82701bb9e13d463abd640b111e7d4dc4','bd_enumdata','ҵ��״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('ywzt0005');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ywzt0005','82701bb9e13d463abd640b111e7d4dc4',0,'0','1',' ','�ѱ���');

--����
delete from t_bd_enumdata where fid='82cfc926d1c84ac09d300481ff302dd0';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('82cfc926d1c84ac09d300481ff302dd0','bd_enumdata','����','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('gradestar_01','gradestar_02','gradestar_03','gradestar_04','gradestar_05');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('gradestar_01','82cfc926d1c84ac09d300481ff302dd0',1,'0','1','','��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('gradestar_02','82cfc926d1c84ac09d300481ff302dd0',2,'0','1','','���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('gradestar_03','82cfc926d1c84ac09d300481ff302dd0',3,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('gradestar_04','82cfc926d1c84ac09d300481ff302dd0',4,'0','1','','�����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('gradestar_05','82cfc926d1c84ac09d300481ff302dd0',5,'0','1','','������');

--Эͬ״̬
delete from t_bd_enumdata where fid='88a94994370c4fe783b85a5551e4bfee';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('88a94994370c4fe783b85a5551e4bfee','bd_enumdata','Эͬ״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('syn_status_01','syn_status_02','syn_status_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('syn_status_01','88a94994370c4fe783b85a5551e4bfee',1,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('syn_status_02','88a94994370c4fe783b85a5551e4bfee',2,'0','1','','δ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('syn_status_03','88a94994370c4fe783b85a5551e4bfee',3,'0','1','','ȫ��');

--������Ŀ
delete from t_bd_enumdata where fid='9a3b4c8e171d4f298d086a5b61eee9e9';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('9a3b4c8e171d4f298d086a5b61eee9e9','bd_enumdata','������Ŀ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('scoringitem_01','scoringitem_02','scoringitem_03','scoringitem_04','scoringitem_05','scoringitem_06','scoringitem_07','scoringitem_08','scoringitem_09','scoringitem_10');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_01','9a3b4c8e171d4f298d086a5b61eee9e9',1,'0','1','����','����1');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_02','9a3b4c8e171d4f298d086a5b61eee9e9',2,'0','1','����','����2');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_03','9a3b4c8e171d4f298d086a5b61eee9e9',3,'0','1','����','����3');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_04','9a3b4c8e171d4f298d086a5b61eee9e9',4,'0','1','����','����4');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_05','9a3b4c8e171d4f298d086a5b61eee9e9',5,'0','1','����','����5');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_06','9a3b4c8e171d4f298d086a5b61eee9e9',6,'0','1','����','����1');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_07','9a3b4c8e171d4f298d086a5b61eee9e9',7,'0','1','����','����2');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_08','9a3b4c8e171d4f298d086a5b61eee9e9',8,'0','1','����','����3');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_09','9a3b4c8e171d4f298d086a5b61eee9e9',9,'0','1','����','����4');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('scoringitem_10','9a3b4c8e171d4f298d086a5b61eee9e9',10,'0','1','����','����5');

--�������̷���
delete from t_bd_enumdata where fid='9d5ba29ddb0f4e72a8ebe008fb7f1b1e';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('9d5ba29ddb0f4e72a8ebe008fb7f1b1e','bd_enumdata','�������̷���','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('flowscheme_01','flowscheme_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('flowscheme_01','9d5ba29ddb0f4e72a8ebe008fb7f1b1e',1,'0','1','','������������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('flowscheme_02','9d5ba29ddb0f4e72a8ebe008fb7f1b1e',2,'0','1','','��Ʒ��������');

--������ʽ
delete from t_bd_enumdata where fid='a14d5e8493004179b784960fd4c4ef68';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('a14d5e8493004179b784960fd4c4ef68','bd_enumdata','������ʽ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('fchannelmode_01','fchannelmode_02','fchannelmode_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fchannelmode_01','a14d5e8493004179b784960fd4c4ef68',1,'0','1','','�˻����֧��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fchannelmode_02','a14d5e8493004179b784960fd4c4ef68',2,'0','1','','����֧��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fchannelmode_03','a14d5e8493004179b784960fd4c4ef68',3,'0','1','','����֧��');

--��������
delete from t_bd_enumdata where fid='a299d3fa3c4d49e4b72049a289e8b455';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('a299d3fa3c4d49e4b72049a289e8b455','bd_enumdata','��������','1','B','0','ϵͳԤ��',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('fres_type_01','fres_type_02','fres_type_03','fres_type_04','fres_type_05','fres_type_06');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fres_type_01','a299d3fa3c4d49e4b72049a289e8b455',1,'0','1','','���Ͳ���װ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fres_type_02','a299d3fa3c4d49e4b72049a289e8b455',2,'0','1','','��װ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fres_type_03','a299d3fa3c4d49e4b72049a289e8b455',3,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fres_type_04','a299d3fa3c4d49e4b72049a289e8b455',4,'0','1','','�ۺ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fres_type_05','a299d3fa3c4d49e4b72049a289e8b455',5,'0','1','','��β');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fres_type_06','a299d3fa3c4d49e4b72049a289e8b455',6,'0','1','','��װ');

--����״̬
delete from t_bd_enumdata where fid='861787e9315345bfb460af870b24e4d8';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('861787e9315345bfb460af870b24e4d8','bd_enumdata','����״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('feedback_status01','feedback_status02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('feedback_status01','861787e9315345bfb460af870b24e4d8',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('feedback_status02','861787e9315345bfb460af870b24e4d8',2,'0','1','','ת�ۺ�');


--��;
delete from t_bd_enumdata where fid='af18ebc8e8544c8495504314d59b6674';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('af18ebc8e8544c8495504314d59b6674','bd_enumdata','��;','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('fpurpose_01','fpurpose_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fpurpose_01','af18ebc8e8544c8495504314d59b6674',1,'0','1','','����ҵ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fpurpose_02','af18ebc8e8544c8495504314d59b6674',2,'0','1','','���ҵ��');

--��������
delete from t_bd_enumdata where fid='bbde3907872447d9869f1d42f8eed21d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('bbde3907872447d9869f1d42f8eed21d','bd_enumdata','��������','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('expensetype_01','expensetype_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('expensetype_01','bbde3907872447d9869f1d42f8eed21d',1,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('expensetype_02','bbde3907872447d9869f1d42f8eed21d',2,'0','1','','����֧��');

--�Ƿ���֤��һ
delete from t_bd_enumdata where fid='bc772d71988b47098b7504f7e31a5050';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('bc772d71988b47098b7504f7e31a5050','bd_enumdata','�Ƿ���֤��һ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('isthree_01','isthree_02','isthree_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('isthree_01','bc772d71988b47098b7504f7e31a5050',1,'0','1','','��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('isthree_02','bc772d71988b47098b7504f7e31a5050',2,'0','1','','��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('isthree_03','bc772d71988b47098b7504f7e31a5050',3,'0','1','','��������');

--ҵ������
delete from t_bd_enumdata where fid='ba06c5e4a5864a8fb3a9c46747075718';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ba06c5e4a5864a8fb3a9c46747075718','bd_enumdata','ҵ������','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('order_type_01','order_type_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('order_type_01','ba06c5e4a5864a8fb3a9c46747075718',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('order_type_02','ba06c5e4a5864a8fb3a9c46747075718',2,'0','1','','������');

--����״̬
delete from t_bd_enumdata where fid='dd815e8aa19d4728b573f354ac08a287';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('dd815e8aa19d4728b573f354ac08a287','bd_enumdata','����״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('taskstatus_01','taskstatus_02','taskstatus_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('taskstatus_01','dd815e8aa19d4728b573f354ac08a287',1,'0','1',' ','δ�ɷ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('taskstatus_02','dd815e8aa19d4728b573f354ac08a287',2,'0','1',' ','ִ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('taskstatus_03','dd815e8aa19d4728b573f354ac08a287',3,'0','1',' ','�����');

--��Ӧ���
delete from t_bd_enumdata where fid='dead722c6414406487e6cd2c57219d8b';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('dead722c6414406487e6cd2c57219d8b','bd_enumdata','��Ӧ���','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('supply_type_01','supply_type_02','supply_type_03','supply_type_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('supply_type_01','dead722c6414406487e6cd2c57219d8b',1,'0','1','','�ɹ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('supply_type_02','dead722c6414406487e6cd2c57219d8b',2,'0','1','','ί��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('supply_type_03','dead722c6414406487e6cd2c57219d8b',3,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('supply_type_04','dead722c6414406487e6cd2c57219d8b',4,'0','1','','�ۺ�');

--������ʽ
delete from t_bd_enumdata where fid='ed7734c979004624b6e083f1b42482e4';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ed7734c979004624b6e083f1b42482e4','bd_enumdata','������ʽ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('cooperate_type_01','cooperate_type_02','cooperate_type_03','cooperate_type_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cooperate_type_01','ed7734c979004624b6e083f1b42482e4',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cooperate_type_02','ed7734c979004624b6e083f1b42482e4',2,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cooperate_type_03','ed7734c979004624b6e083f1b42482e4',3,'0','1','','��Ӫ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cooperate_type_04','ed7734c979004624b6e083f1b42482e4',4,'0','1','','����');

--���㵥�˻�����
delete from t_bd_enumdata where fid='e3959c5a41004049b5853d43bb5881a0';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e3959c5a41004049b5853d43bb5881a0','bd_enumdata','���㵥�˻�����','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('settleaccount_type_01','settleaccount_type_02','settleaccount_type_03','settleaccount_type_04','settleaccount_type_05','settleaccount_type_06');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleaccount_type_01','e3959c5a41004049b5853d43bb5881a0',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleaccount_type_02','e3959c5a41004049b5853d43bb5881a0',2,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleaccount_type_03','e3959c5a41004049b5853d43bb5881a0',3,'0','1','','��֤��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleaccount_type_04','e3959c5a41004049b5853d43bb5881a0',4,'0','1','','�ۺ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleaccount_type_05','e3959c5a41004049b5853d43bb5881a0',5,'0','1','','PK��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleaccount_type_06','e3959c5a41004049b5853d43bb5881a0',6,'0','1','','�����');

--(��ֵ��)ҵ��״̬
delete from t_bd_enumdata where fid='d0b462f4bb834ecab641cbdc67eaa56a';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('d0b462f4bb834ecab641cbdc67eaa56a','bd_enumdata','(��ֵ��)ҵ��״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('inpour_status_01','inpour_status_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('inpour_status_01','d0b462f4bb834ecab641cbdc67eaa56a',1,'0','1','','δȷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('inpour_status_02','d0b462f4bb834ecab641cbdc67eaa56a',2,'0','1','','��ȷ��');

--���ȼ�
delete from t_bd_enumdata where fid='d195bc2bdbaa43589d0e458773787778';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('d195bc2bdbaa43589d0e458773787778','bd_enumdata','���ȼ�','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('172334470668619776','172334470672814080','172334470672814081');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('172334470668619776','d195bc2bdbaa43589d0e458773787778',0,'0','1',' ','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('172334470672814080','d195bc2bdbaa43589d0e458773787778',0,'0','1',' ','һ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('172334470672814081','d195bc2bdbaa43589d0e458773787778',0,'0','1',' ','�ǽ���');

--�Ż���Ŀ
delete from t_bd_enumdata where fid='bfc0f58c418a4827941e95240607b070';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('bfc0f58c418a4827941e95240607b070','bd_enumdata','�Ż���Ŀ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('favoritem_01','favoritem_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('favoritem_01','bfc0f58c418a4827941e95240607b070',1,'0','1','','Ĩβ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('favoritem_02','bfc0f58c418a4827941e95240607b070',2,'0','1','','����');

--���η�
delete from t_bd_enumdata where fid='c17a422e0bb34b6db7c182f12109f393';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('c17a422e0bb34b6db7c182f12109f393','bd_enumdata','���η�','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('duty_01','duty_02','duty_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('duty_01','c17a422e0bb34b6db7c182f12109f393',1,'0','1','','��Ӧ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('duty_02','c17a422e0bb34b6db7c182f12109f393',2,'0','1','','�ͻ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('duty_03','c17a422e0bb34b6db7c182f12109f393',3,'0','1','','��˾');

--���۶���
delete from t_bd_enumdata where fid='c25688f6e3a641328f9cf3f5fdfcb3cb';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('c25688f6e3a641328f9cf3f5fdfcb3cb','bd_enumdata','���۶���','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('164748171099115520','164748171099115521','164748171099115522');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164748171099115520','c25688f6e3a641328f9cf3f5fdfcb3cb',0,'0','1',' ','��Ʒ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164748171099115521','c25688f6e3a641328f9cf3f5fdfcb3cb',0,'0','1',' ','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164748171099115522','c25688f6e3a641328f9cf3f5fdfcb3cb',0,'0','1',' ',' ');

--�����嵥��Դ
delete from t_bd_enumdata where fid='c9edc59d87ae47bdb3b5c05cbc0cdf9d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('c9edc59d87ae47bdb3b5c05cbc0cdf9d','bd_enumdata','�����嵥��Դ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('parcel_source_01','parcel_source_02','parcel_source_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('parcel_source_01','c9edc59d87ae47bdb3b5c05cbc0cdf9d',1,'0','1','','ERPͬ������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('parcel_source_02','c9edc59d87ae47bdb3b5c05cbc0cdf9d',2,'0','1','','PDAɨ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('parcel_source_03','c9edc59d87ae47bdb3b5c05cbc0cdf9d',3,'0','1','','Эͬ����');

--�����嵥ҵ������
delete from t_bd_enumdata where fid='ca01b373ad434cceb00fa42af3562597';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('ca01b373ad434cceb00fa42af3562597','bd_enumdata','�����嵥ҵ������','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('parcel_list_01','parcel_list_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('parcel_list_01','ca01b373ad434cceb00fa42af3562597',1,'0','1','','��׼����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('parcel_list_02','ca01b373ad434cceb00fa42af3562597',2,'0','1','','Эͬ����');

--(��ֵ��)���㷽ʽ
delete from t_bd_enumdata where fid='cbcd016825a64657b9f85668451143c5';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('cbcd016825a64657b9f85668451143c5','bd_enumdata','(��ֵ��)���㷽ʽ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('dealtype_01','dealtype_02','dealtype_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('dealtype_01','cbcd016825a64657b9f85668451143c5',1,'0','1','','����ת��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('dealtype_02','cbcd016825a64657b9f85668451143c5',2,'0','1','','�ֽ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('dealtype_03','cbcd016825a64657b9f85668451143c5',3,'0','1','','����');

--����֧����ʽ
delete from t_bd_enumdata where fid='f7d545ef607041bf8eb9a7242b28831c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('f7d545ef607041bf8eb9a7242b28831c','bd_enumdata','����֧����ʽ','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('fpaymode_01','fpaymode_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fpaymode_01','f7d545ef607041bf8eb9a7242b28831c',1,'0','1','','�ֽ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fpaymode_02','f7d545ef607041bf8eb9a7242b28831c',2,'0','1','','����');

--�����̶�
delete from t_bd_enumdata where fid='fa104d958c0a451c879e326d937ef3d8';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('fa104d958c0a451c879e326d937ef3d8','bd_enumdata','�����̶�','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('173743914241626112','173743914241626113','173743914241626114');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('173743914241626112','fa104d958c0a451c879e326d937ef3d8',0,'0','1',' ','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('173743914241626113','fa104d958c0a451c879e326d937ef3d8',0,'0','1',' ','�ǽ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('173743914241626114','fa104d958c0a451c879e326d937ef3d8',0,'0','1',' ','һ��');

--����
delete from t_bd_enumdata where fid='f0e7ebbac55f419c8c49a864aa1c974e';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('f0e7ebbac55f419c8c49a864aa1c974e','bd_enumdata','����','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('fdirection_01','fdirection_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fdirection_01','f0e7ebbac55f419c8c49a864aa1c974e',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fdirection_02','f0e7ebbac55f419c8c49a864aa1c974e',2,'0','1','','֧��');

--�ֿ�����
delete from t_bd_enumdata where fid='eef0ee93bc864b92937c17b45f02eafb';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('eef0ee93bc864b92937c17b45f02eafb','bd_enumdata','�ֿ�����','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('storehouse_type_01','storehouse_type_02','storehouse_type_03','storehouse_type_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('storehouse_type_01','eef0ee93bc864b92937c17b45f02eafb',1,'0','1','','���вֿ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('storehouse_type_02','eef0ee93bc864b92937c17b45f02eafb',2,'0','1','','��Ӧ�ֿ̲�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('storehouse_type_03','eef0ee93bc864b92937c17b45f02eafb',3,'0','1','','�ͻ��ֿ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('storehouse_type_04','eef0ee93bc864b92937c17b45f02eafb',4,'0','1','','�������ִ�');

--֧��״̬
delete from t_bd_enumdata where fid='eff589d0901b484d93b7447795d6f938';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('eff589d0901b484d93b7447795d6f938','bd_enumdata','֧��״̬','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('fpaystatus_01','fpaystatus_02','fpaystatus_03','fpaystatus_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fpaystatus_01','eff589d0901b484d93b7447795d6f938',1,'0','1','','�ȴ�ȷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fpaystatus_02','eff589d0901b484d93b7447795d6f938',2,'0','1','','��֧��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fpaystatus_03','eff589d0901b484d93b7447795d6f938',3,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fpaystatus_04','eff589d0901b484d93b7447795d6f938',4,'0','1','','����');

--�Ա�
delete from t_bd_enumdata where fid='e8dbace340d7407b838e84ddaa982900';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e8dbace340d7407b838e84ddaa982900','bd_enumdata','�Ա�','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('sex1','sex2');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sex1','e8dbace340d7407b838e84ddaa982900',1,'0','1','','��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sex2','e8dbace340d7407b838e84ddaa982900',2,'0','1','','Ů');

--��ν
delete from t_bd_enumdata where fid='e8dbace340d7407b838e84ddaa98290d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e8dbace340d7407b838e84ddaa98290d','bd_enumdata','��ν','1','B','0','ϵͳԤ��',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('164737133758779392','164737133758779393');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164737133758779392','e8dbace340d7407b838e84ddaa98290d',0,'0','1',' ','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164737133758779393','e8dbace340d7407b838e84ddaa98290d',0,'0','1',' ','Ůʿ');


--�̼�����
delete from t_bd_enumdata where fid='b870b8c44c6d4ab58da05d1c82b2890c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('b870b8c44c6d4ab58da05d1c82b2890c','bd_enumdata','�̼�����','1','B','0','ϵͳԤ��',4,'1','0');
delete from t_bd_enumdataentry where fentryid in('merchant_type01','merchant_type02','merchant_type03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('merchant_type01','b870b8c44c6d4ab58da05d1c82b2890c',1,'0','1','','���ƼҾ��̼�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('merchant_type02','b870b8c44c6d4ab58da05d1c82b2890c',2,'0','1','','��Ʒ�Ҿ��̼�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('merchant_type03','b870b8c44c6d4ab58da05d1c82b2890c',3,'0','1','','��ԡ�̼�');

--�ɵ���ʽ
delete from t_bd_enumdata where fid='a04edb99c9aa4bdf94bb260c390c13b9';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('a04edb99c9aa4bdf94bb260c390c13b9','bd_enumdata','�ɵ���ʽ','1','B','0','ϵͳԤ��',4,'1','0');
delete from t_bd_enumdataentry where fentryid in('single_mode_01','single_mode_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('single_mode_01','a04edb99c9aa4bdf94bb260c390c13b9',1,'0','1','','�ֿ��ɵ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('single_mode_02','a04edb99c9aa4bdf94bb260c390c13b9',2,'0','1','','�ϲ��ɵ�');

--�������
delete from t_bd_enumdata where fid='7cad95d5201140a5b2eb178248e90d01';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('7cad95d5201140a5b2eb178248e90d01','bd_enumdata','�������','1','B','0','ϵͳԤ��',4,'1','0');
delete from t_bd_enumdataentry where fentryid in('res_type_01','res_type_02','res_type_03','res_type_04');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_01','7cad95d5201140a5b2eb178248e90d01',1,'0','1','','ά��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_02','7cad95d5201140a5b2eb178248e90d01',2,'0','1','','�˻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_03','7cad95d5201140a5b2eb178248e90d01',3,'0','1','','�˻�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_04','7cad95d5201140a5b2eb178248e90d01',4,'0','1','','����');

--���۶�������
delete from t_bd_enumdata where fid='7f39b597c6b546629edc9895b9ea4c68';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('7f39b597c6b546629edc9895b9ea4c68','bd_enumdata','���۶�������','1','B','0','���۹���',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('salebiz_type_01','salebiz_type_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('salebiz_type_01','7f39b597c6b546629edc9895b9ea4c68',1,'0','1','','��ͨ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('salebiz_type_02','7f39b597c6b546629edc9895b9ea4c68',2,'0','1','','Эͬ����');

--��Ӫģʽ
delete from t_bd_enumdata where fid='7ba57ec5e04c4bf1a70cda3df63e4f43';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('7ba57ec5e04c4bf1a70cda3df63e4f43','bd_enumdata','��Ӫģʽ','1','B','0','���۹���',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('busmodel_01','busmodel_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('busmodel_01','7ba57ec5e04c4bf1a70cda3df63e4f43',1,'0','1','','�ܲ�ֱӪ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('busmodel_02','7ba57ec5e04c4bf1a70cda3df63e4f43',1,'0','1','','������Ӫ');

--����״̬
delete from t_bd_enumdata where fid='5328447ab5cf41398083150b2d6c9955';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('5328447ab5cf41398083150b2d6c9955','bd_enumdata','����״̬','1','B','0','���۹���',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('sendgoods_status_01','sendgoods_status_02','sendgoods_status_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sendgoods_status_01','5328447ab5cf41398083150b2d6c9955',1,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sendgoods_status_02','5328447ab5cf41398083150b2d6c9955',1,'0','1','','�ѱ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sendgoods_status_03','5328447ab5cf41398083150b2d6c9955',1,'0','1','','�ѷ���');

--���㵥ȷ��״̬
delete from t_bd_enumdata where fid='5579b9f2193b45b2ad05e67ff860289e';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('5579b9f2193b45b2ad05e67ff860289e','bd_enumdata','���㵥ȷ��״̬','1','B','0','���۹���',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('settleconfirm_status_01','settleconfirm_status_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleconfirm_status_01','5579b9f2193b45b2ad05e67ff860289e',1,'0','1','','δȷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settleconfirm_status_02','5579b9f2193b45b2ad05e67ff860289e',2,'0','1','','��ȷ��');

--���㵥ҵ������
delete from t_bd_enumdata where fid='3be276713adc4bae93c2c1d6c495ebab';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('3be276713adc4bae93c2c1d6c495ebab','bd_enumdata','���㵥ҵ������','1','B','0','���۹���',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('settlebiz_type_01','settlebiz_type_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settlebiz_type_01','3be276713adc4bae93c2c1d6c495ebab',1,'0','1','','��׼����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('settlebiz_type_02','3be276713adc4bae93c2c1d6c495ebab',2,'0','1','','Эͬ����');

--�տ�״̬
delete from t_bd_enumdata where fid='324d1ca3aa1a4e38ab59a4ba22740afe';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('324d1ca3aa1a4e38ab59a4ba22740afe','bd_enumdata','�տ�״̬','1','B','0','���۹���',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('receiptstatus_type_01','receiptstatus_type_02','receiptstatus_type_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('receiptstatus_type_01','324d1ca3aa1a4e38ab59a4ba22740afe',1,'0','1','','ȫ��δ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('receiptstatus_type_02','324d1ca3aa1a4e38ab59a4ba22740afe',2,'0','1','','�����տ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('receiptstatus_type_03','324d1ca3aa1a4e38ab59a4ba22740afe',3,'0','1','','ȫ������');

--����״̬
delete from t_bd_enumdata where fid='0686d40245bc4f5288fd92222b479d66';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('0686d40245bc4f5288fd92222b479d66','bd_enumdata','����״̬','1','B','0','���۹���',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('order_status_01','order_status_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('order_status_01','0686d40245bc4f5288fd92222b479d66',1,'0','1','','���µ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('order_status_02','0686d40245bc4f5288fd92222b479d66',1,'0','1','','���ջ�');

--��������
delete from t_bd_enumdata where fid='114bd366b4264678b3944f811c8fd7e5';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('114bd366b4264678b3944f811c8fd7e5','bd_enumdata','��������','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('direction_01','direction_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('direction_01','114bd366b4264678b3944f811c8fd7e5',1,'0','1','','��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('direction_02','114bd366b4264678b3944f811c8fd7e5',2,'0','1','','��');

--ҵ����;
delete from t_bd_enumdata where fid='682a20a467a14acab166e3bc66ab0142';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('682a20a467a14acab166e3bc66ab0142','bd_enumdata','ҵ����;','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('bizpurpose_01','bizpurpose_02','bizpurpose_03','bizpurpose_04','bizpurpose_06','bizpurpose_07','bizpurpose_08');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizpurpose_01','682a20a467a14acab166e3bc66ab0142',1,'0','1','','�˻���ֵ');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizpurpose_02','682a20a467a14acab166e3bc66ab0142',2,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizpurpose_03','682a20a467a14acab166e3bc66ab0142',3,'0','1','','�����ۿ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizpurpose_04','682a20a467a14acab166e3bc66ab0142',4,'0','1','','���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizpurpose_06','682a20a467a14acab166e3bc66ab0142',6,'0','1','','�˿�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizpurpose_07','682a20a467a14acab166e3bc66ab0142',7,'0','1','','Эͬ�˻���ʼ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizpurpose_08','682a20a467a14acab166e3bc66ab0142',8,'0','1','','�˻�ת��');

--��֧��¼ҵ��״̬
delete from t_bd_enumdata where fid='a067e929399242a480d8730153b9158c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('a067e929399242a480d8730153b9158c','bd_enumdata','��֧��¼ҵ��״̬','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('bizstatus_01','bizstatus_02','bizstatus_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizstatus_01','a067e929399242a480d8730153b9158c',1,'0','1','','��ȷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizstatus_02','a067e929399242a480d8730153b9158c',2,'0','1','','��ȷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizstatus_03','a067e929399242a480d8730153b9158c',3,'0','1','','�Ѻ��');

--Эͬ��������״̬
delete from t_bd_enumdata where fid='95e7dd462c1b4780b4d15d447050046d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('95e7dd462c1b4780b4d15d447050046d','bd_enumdata','Эͬ��������״̬','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('publish_status_01','publish_status_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('publish_status_01','95e7dd462c1b4780b4d15d447050046d',1,'0','1','','δ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('publish_status_02','95e7dd462c1b4780b4d15d447050046d',2,'0','1','','�ѷ���');

--�ͻ�����
delete from t_bd_enumdata where fid='96a51c6412014864be18a6475aae8993';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('96a51c6412014864be18a6475aae8993','bd_enumdata','�ͻ�����','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('customertype_01','customertype_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customertype_01','96a51c6412014864be18a6475aae8993',1,'0','1','','�ͻ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('customertype_02','96a51c6412014864be18a6475aae8993',2,'0','1','','������');

--��֧��¼���յ�λ����
delete from t_bd_enumdata where fid='8d96c69f94bf4756bb94a433d03813cd';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('8d96c69f94bf4756bb94a433d03813cd','bd_enumdata','��֧��¼���յ�λ����','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('contactunittype_01','contactunittype_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('contactunittype_01','8d96c69f94bf4756bb94a433d03813cd',1,'0','1','','��������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('contactunittype_02','8d96c69f94bf4756bb94a433d03813cd',2,'0','1','','������');

--��Ӧ������
delete from t_bd_enumdata where fid='e0558b647aec439c91d5b665761e3bdc';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e0558b647aec439c91d5b665761e3bdc','bd_enumdata','��Ӧ������','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('suppliertype_01','suppliertype_02','suppliertype_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('suppliertype_01','e0558b647aec439c91d5b665761e3bdc',1,'0','1','','��Ӧ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('suppliertype_02','e0558b647aec439c91d5b665761e3bdc',2,'0','1','','�ܲ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('suppliertype_03','e0558b647aec439c91d5b665761e3bdc',3,'0','1','','��������');

--Эͬ����ҵ��״̬
delete from t_bd_enumdata where fid='c88fae3fd43145cbbc41cee89d0656b0';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('c88fae3fd43145cbbc41cee89d0656b0','bd_enumdata','Эͬ����ҵ��״̬','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('business_status_01','business_status_02','business_status_07','business_status_05','business_status_09','business_status_10');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('business_status_01','c88fae3fd43145cbbc41cee89d0656b0',1,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('business_status_02','c88fae3fd43145cbbc41cee89d0656b0',2,'0','1','','������');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('business_status_07','c88fae3fd43145cbbc41cee89d0656b0',3,'0','1','','������ȷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('business_status_05','c88fae3fd43145cbbc41cee89d0656b0',4,'0','1','','��ȷ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('business_status_09','c88fae3fd43145cbbc41cee89d0656b0',5,'0','1','','Эͬ���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('business_status_10','c88fae3fd43145cbbc41cee89d0656b0',6,'0','1','','���ջ�');

--ҵ����
delete from t_bd_enumdata where fid='c225ba24684a41b1a961f3c38b6e9cc8';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('c225ba24684a41b1a961f3c38b6e9cc8','bd_enumdata','ҵ����','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('bizdirection_01','bizdirection_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizdirection_01','c225ba24684a41b1a961f3c38b6e9cc8',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('bizdirection_02','c225ba24684a41b1a961f3c38b6e9cc8',2,'0','1','','֧��');

--��֧��¼����״̬
delete from t_bd_enumdata where fid='da253755fe924ae9b7586deff2777f86';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('da253755fe924ae9b7586deff2777f86','bd_enumdata','��֧��¼����״̬','1','B','0','Эͬ����',1,'0','0');
delete from t_bd_enumdataentry where fentryid in('verificstatus_01','verificstatus_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('verificstatus_01','da253755fe924ae9b7586deff2777f86',1,'0','1','','δ����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('verificstatus_02','da253755fe924ae9b7586deff2777f86',2,'0','1','','�Ѻ���');

--���˶���
delete from t_bd_enumdata where fid='0ec8bfd753534869bc9af84ff449ef77';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('0ec8bfd753534869bc9af84ff449ef77','bd_enumdata','���˶���','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('fao_001','fao_002');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fao_001','0ec8bfd753534869bc9af84ff449ef77',1,'0','1','','Ա��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fao_002','0ec8bfd753534869bc9af84ff449ef77',2,'0','1','','����');

--����ά��
delete from t_bd_enumdata where fid='0ec8bfd753534869bc9af84ff749ef77';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('0ec8bfd753534869bc9af84ff749ef77','bd_enumdata','����ά��','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('fal_001');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fal_001','0ec8bfd753534869bc9af84ff749ef77',1,'0','1','','������');

--���
delete from t_bd_enumdata where fid='0ec8bfd753534869bc9er74ff449ef77';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('0ec8bfd753534869bc9er74ff449ef77','bd_enumdata','���','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('fy_2018','fy_2019','fy_2020','fy_2021','fy_2022','fy_2023','fy_2024','fy_2025');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fy_2018','0ec8bfd753534869bc9er74ff449ef77',1,'0','1','','2018');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fy_2019','0ec8bfd753534869bc9er74ff449ef77',2,'0','1','','2019');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fy_2020','0ec8bfd753534869bc9er74ff449ef77',3,'0','1','','2020');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fy_2021','0ec8bfd753534869bc9er74ff449ef77',4,'0','1','','2021');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fy_2022','0ec8bfd753534869bc9er74ff449ef77',5,'0','1','','2022');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fy_2023','0ec8bfd753534869bc9er74ff449ef77',6,'0','1','','2023');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fy_2024','0ec8bfd753534869bc9er74ff449ef77',7,'0','1','','2024');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fy_2025','0ec8bfd753534869bc9er74ff449ef77',8,'0','1','','2025');

--��ϸ���״̬
delete from t_bd_enumdata where fid='a14b63e1771f4348abf170b87d080b74';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('a14b63e1771f4348abf170b87d080b74','bd_enumdata','��ϸ���״̬','1','B','0','ϵͳԤ��',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('entrychange_01','entrychange_02','entrychange_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('entrychange_01','a14b63e1771f4348abf170b87d080b74',1,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('entrychange_02','a14b63e1771f4348abf170b87d080b74',2,'0','1','','����');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('entrychange_03','a14b63e1771f4348abf170b87d080b74',3,'0','1','','ɾ��');

--ë����
delete from t_bd_enumdata where fid='e6ebff19174d41458a1d411d09b90eca';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e6ebff19174d41458a1d411d09b90eca','bd_enumdata','ë����','1','B','0','ϵͳԤ��',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('fdeptid','fstaffid','fcustomerid');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fdeptid','e6ebff19174d41458a1d411d09b90eca',1,'0','1','','���۲���');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fstaffid','e6ebff19174d41458a1d411d09b90eca',2,'0','1','','����Ա');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('fcustomerid','e6ebff19174d41458a1d411d09b90eca',3,'0','1','','�ͻ�');
update t_bd_enumdataentry set fenmainorgid=0 where  fid='e6ebff19174d41458a1d411d09b90eca'
update t_bd_enumdata set fnumber='ë����' where  fid='e6ebff19174d41458a1d411d09b90eca'

--�ɱ�ȡֵ��Դ
delete from t_bd_enumdata where fid='205f345c36af4088a6244aa5aa503da8';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('205f345c36af4088a6244aa5aa503da8','bd_enumdata','�ɱ�ȡֵ��Դ','1','B','0','ϵͳԤ��',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('purchaseprice','purchaseorder');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('purchaseprice','205f345c36af4088a6244aa5aa503da8',1,'0','1','','�ɹ���Ŀ��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('purchaseorder','205f345c36af4088a6244aa5aa503da8',2,'0','1','','�ɹ�����');

--ִ����ϸ��Դ
delete from t_bd_enumdata where fid='caf1fe3cf18c48abb857b0ec30432e91';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('caf1fe3cf18c48abb857b0ec30432e91','bd_enumdata','ִ����ϸ','1','B','0','ϵͳԤ��',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('accord','hidden');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('accord','caf1fe3cf18c48abb857b0ec30432e91',1,'0','1','','��');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('hidden','caf1fe3cf18c48abb857b0ec30432e91',2,'0','1','','��');


--��������
delete from t_bd_enumdata where fid='06439fa8733f42fa835f293fa82876d3';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('06439fa8733f42fa835f293fa82876d3','bd_enumdata','��������','1','B','0','��������',1,'1','0');

--���˳���
delete from t_bd_enumdata where fid='51b7377b8e7b4ba89ecae4d0c08fbe1c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('51b7377b8e7b4ba89ecae4d0c08fbe1c','bd_enumdata','���˳���','1','B','0','��������',1,'1','0');

--�޶��ͻ�
delete from t_bd_enumdata where fid='40d982c21043460dbb3b813f8292e09c';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('40d982c21043460dbb3b813f8292e09c','bd_enumdata','�޶��ͻ�','1','B','0','�������',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('limit_01','limit_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('limit_01','40d982c21043460dbb3b813f8292e09c',1,'0','1','','�ͻ�');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('limit_02','40d982c21043460dbb3b813f8292e09c',2,'0','1','','�ͻ�����');

--����״̬
delete from t_bd_enumdata where fid='1ac3c10cb6564c45a434996d9c63d01f';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('1ac3c10cb6564c45a434996d9c63d01f','bd_enumdata','����״̬','1','B','0','��������',1,'1','0');
--�ر�ԭ��
delete from t_bd_enumdata where fid='6277d2cea3d54f57abd92a8a1b6a2751';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('6277d2cea3d54f57abd92a8a1b6a2751','bd_enumdata','�ر�ԭ��','1','B','0','�������',1,'1','0');

--�����������������Ϊ�գ��򽫱����Ϊ�͸����������һ�£������û��ڱ���Ԥ�ø�������ֵʱ��ʾ�������ظ���������
update t_bd_enumdata set fnumber=fname where fnumber='' or fnumber is null
--������������Ԥ��ű�����------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------



--������������Ԥ��ű���ʼ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

--�ɹ�����
delete from t_bd_billtype where fid='po_type_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('po_type_01','bd_billtype','BTCGDD_SYS_01','��׼�ɹ�','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ydj_purchaseorder','','1','');

--��������
delete from t_bd_billtype where fid='salebiz_type_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('salebiz_type_01','bd_billtype','BTXSYX_SYS_01','��ͨ����','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','P','PTXS',N'ϵͳԤ��','ydj_saleintention','','1','');

--���ۺ�ͬ
delete from t_bd_billtype where fid in('order_billtype_01','order_billtype_02');
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('order_billtype_01','bd_billtype','BTXSHT_SYS_01','��׼���ۺ�ͬ','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ydj_order','','1','');
--insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
--values('order_billtype_02','bd_billtype','BTXSHT_SYS_02','֧��ͼֽ�ϴ�','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ydj_order','','0','');


--���������뵥
delete from t_bd_billtype where fid='invtransferreq_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('invtransferreq_billtype_01','bd_billtype','BTKCDBSQD_SYS_01','��׼����������','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_inventorytransferreq','','1','');

--��������
delete from t_bd_billtype where fid='invtransfer_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('invtransfer_billtype_01','bd_billtype','BTKCDBD_SYS_01','��׼������','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_inventorytransfer','','1','');

--�����������뵥
delete from t_bd_billtype where fid='otheroutstockreq_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('otheroutstockreq_billtype_01','bd_billtype','BTQTCKSQD_SYS_01','��׼������������','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_otherstockoutreq','','1','');

--�������ⵥ
delete from t_bd_billtype where fid='otherstockout_type_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('otherstockout_type_01','bd_billtype','BTQTCKD_SYS_01','��׼��������','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_otherstockout','','1','');

--����������뵥
delete from t_bd_billtype where fid='otherinstockreq_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('otherinstockreq_billtype_01','bd_billtype','BTQTRKSQD_SYS_01','��׼�����������','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_otherstockinreq','','1','');

--������ⵥ
delete from t_bd_billtype where fid='otherinstock_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('otherinstock_billtype_01','bd_billtype','BTQTRKD_SYS_01','��׼�������','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_otherstockin','','1','');

--�ɹ���ⵥ
delete from t_bd_billtype where fid='poinstock_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('poinstock_billtype_01','bd_billtype','BTCGRKD_SYS_01','��׼�ɹ����','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_postockin','','1','');

--�ɹ��˻���
delete from t_bd_billtype where fid='postockreturn_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('postockreturn_billtype_01','bd_billtype','BTCGTHD_SYS_01','��׼�ɹ��˻�','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_postockreturn','','1','');

--�ɹ��ջ�֪ͨ��
delete from t_bd_billtype where fid='receiptnotice_type_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('receiptnotice_type_01','bd_billtype','BTCGSHTZD_SYS_01','��׼�ջ�֪ͨ','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','pur_receiptnotice','','1','');

--�ɹ����뵥
delete from t_bd_billtype where fid='reqorder_type_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('reqorder_type_01','bd_billtype','BTCGSQD_SYS_01','��׼�ɹ�����','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','pur_reqorder','','1','');

--�ɹ��˻�֪ͨ��
delete from t_bd_billtype where fid='poreturnnotice_type_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('poreturnnotice_type_01','bd_billtype','BTCGTHTZD_SYS_01','��׼�ɹ��˻�֪ͨ','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','pur_returnnotice','','1','');

--���۳��ⵥ
delete from t_bd_billtype where fid='sostockout_type_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('sostockout_type_01','bd_billtype','BTXSCKD_SYS_01','��׼���۳���','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_sostockout','','1','');

--�����˻���
delete from t_bd_billtype where fid='sostockreturn_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('sostockreturn_billtype_01','bd_billtype','BTXSTHD_SYS_01','��׼�����˻�','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_sostockreturn','','1','');

--���۷���֪ͨ��
delete from t_bd_billtype where fid='deliverynotice_type_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('deliverynotice_type_01','bd_billtype','BTXSFHTZD_SYS_01','��׼����֪ͨ','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','sal_deliverynotice','','1','');

--�����˻�֪ͨ��
delete from t_bd_billtype where fid='soreturnnotice_type_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('soreturnnotice_type_01','bd_billtype','BTXSTHTZD_SYS_01','��׼�����˻�֪ͨ','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','sal_returnnotice','','1','');

--�̵㵥
delete from t_bd_billtype where fid='inventoryverify_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('inventoryverify_billtype_01','bd_billtype','BZPDD_SYS_01','��׼�̵㵥','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_inventoryverify','','1','');

--����
delete from t_bd_billtype where fid='ydj_service_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_service_billtype_01','bd_billtype','BTFWD_SYS_01','��׼����','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ydj_service','','1','');

--�ŵ����뵥
delete from t_bd_billtype where fid='stk_scheduleapply_billtype_01';
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('stk_scheduleapply_billtype_01','bd_billtype','BTPDSQD_SYS_01','��ͨ�ŵ�����','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','stk_scheduleapply','','1','');

--���õǼ�
delete from t_bd_billtype where fid in('registfee_billtype_01','registfee_billtype_02');
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('registfee_billtype_01','bd_billtype','BTXSHT_SYS_01','��׼���õ�','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ste_registfee','','1','');
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('registfee_billtype_02','bd_billtype','BTXSHT_SYS_02','����Ӧ����','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ste_registfee','','0','');

--���۷�Ʊ
delete from t_bd_billtype where fid in('saleinvoice_billtype_01','saleinvoice_billtype_02');
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('saleinvoice_billtype_01','bd_billtype','BTXSFP_SYS_01','������ֵ˰ר�÷�Ʊ','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ste_saleinvoice','','1','');
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('saleinvoice_billtype_02','bd_billtype','BTXSFP_SYS_02','������ͨ��Ʊ','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ste_saleinvoice','','0','');

--�طõ�
delete from t_bd_billtype where fid in('vist_type_01','vist_type_02');
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('vist_type_01','bd_billtype','BTHFD_SYS_01','����ط�','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ydj_vist','','1','');
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('vist_type_02','bd_billtype','BTHFD_SYS_02','�����ط�','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ydj_vist','','0','');

--�ۺ�����
delete from t_bd_billtype where fid in('afterfeedback_type_01');
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('afterfeedback_type_01','bd_billtype','BTSHFKD_SYS_01','�����ۺ���','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ste_afterfeedback','','1','');

--�������뵥
delete from t_bd_billtype where fid in('ser_apply_01');
insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ser_apply_01','bd_billtype','BTFWSQD_SYS_01','��׼��������','0','2018-07-02 00:00:00','1','E','0','0','','',N'δ����','','',N'ϵͳԤ��','ser_apply','','1','');


--������������Ԥ��ű�����------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------


--������Ԥ�ýű�ִ�к�ͳһ��������״̬ �� ��ҵID
update t_bd_enumdata set fforbidstatus='0' where fforbidstatus='';
update t_bd_enumdataentry set fenmainorgid='0' where fenmainorgid='';

