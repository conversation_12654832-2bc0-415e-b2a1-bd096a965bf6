#短消息服务配置，内部开发可以放开以下参数进行测试
aliyun:sms.accessid LTAI5tGvXHaX8oq7ZQo4eSHn
aliyun:sms.accesskey ******************************
aliyun:sms.endpoint http://1038880401653050.mns.cn-shenzhen.aliyuncs.com/
aliyun:sms.topic sms.topic-cn-shenzhen
aliyun:sms.signname 易到家
aliyun:sms.templatecode SMS_61665244
aliyun:sms.enable 1

#配置缓存的读写分离信息
fw:redis.masterhost 123456@127.0.0.1:6379
fw:redis.slavehost 123456@127.0.0.1:6379
fw:redis.maxpoolsize 10000
fw:redis.connecttimeout 3
fw:redis.receivetimeout 15
fw:redis.sendtimeout 15
fw:redis.retrytimeout 1

#消息队列的主机名用户及密码配置
fw:rabbitmq.host www.jienor.com
fw:rabbitmq.username jienorf
fw:rabbitmq.password jienor.com

#站点email信使配置
fw:mail.pop3 pop.exmail.qq.com
fw:mail.smtp smtp.exmail.qq.com
fw:mail.username <EMAIL>
fw:mail.password Jn@12345

#是否启用耗时日志记录（商品数据权限隔离规则取数）
enableTimeConsumingLog true

#强制要求当前站点使用指定缓存区域
fw.cache.regionid 6

#是否自动更新库表结构，不设置则为true
fw.AutoUpdateMetaData true

#启用调试模式
fw.debugmode 0
#启用服务请求日志
fw.requestlog.enable 1
#请求日志呈现格式：memory（内存），csv（文件）
fw.requestlog.format memory

#默认登录页面地址
#fw.defaultpage /views/oem/login_821347239912935425.html
#普通pc浏览时的首页
#fw.defaultpage /views/oem/login_821347239912935425.html

#是否启用选配约束新逻辑
enableSelConstraintNewLogic true

#帮助中心页面地址
fw.helpcenter https://www.kancloud.cn/ydj_dev/xtsybzsc/1069786
#更新日志页面地址
fw.updatelog https://www.kancloud.cn/ydj_dev/xtsybzsc/2556194
#产品版本号
fw.versionno V8.7.3.760
#产品发版日期
fw.versiondate 2025-07-30 05:50

#小程序配置
fw.miniprogramconfig [{"id":"uat","name":"UAT","checked":true,"host":"https://mstest.yidaohome.com","visible":true},{"id":"uat2","name":"UAT版2","host":"https://mstest2.yidaohome.com","visible":false},{"id":"training","name":"仿真","host":"https://musitest.yidaohome.com/","visible":true},{"id":"training2","name":"培训","host":"https://mspx.yidaohome.com","visible":true}]
true
#站点默认版本号
ms.site.version 2025021217962

#ms.site.domain https://mstest.yidaohome.com/

#企业微信WebAPI服务地址
ms.ewc.webapi.host https://mstest.yidaohome.com:5006/

#运维系统WebAPI服务地址
ms.ops.webapi.host https://mstest.yidaohome.com:5005/

fw.dashboardpage sys_dashboard
fw.dashboardtitle 仪表盘
fw.productname 慕思
fw.serviceprovider 

#微服务参数配置：网关，消息中继模式（central或p2p）
ms.gateway http://localhost:5003/
#微服务网关访问验证码
ms.gateway.authcode ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

#用于微信前端调用后端接口的临时令牌
ms.wx.server.ydj https://m.yidaohome.com
ms.wx.token.ydj ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

#用于微信公众平台的配置
ms.wx.serverappid.ydj wxffc52eaf22135e99
ms.wx.serverappsecret.ydj dcc2f047491256ad17c22591e98a56ee
ms.wx.encodingaeskey.ydj Va35REeR0bwiEaWIbub67iX2SRfjpHeCcWfHZsKFw68
ms.wx.wxtoken.ydj yidaohome

#极光推送配置
ms.jg.serverid.ydj 49432de3b2b52b0466377ba3
ms.jg.serversecret.ydj f2cee8968be3fea54accf06e

#当前站点租用方式：single（单租户），multi（多租户）
#此参数主要影响本站点的登录机制及数据库连接行为
ms.site.tenanttype multi

#定义消息系统登录成功后显示的欢迎信息
im.signalr.welcome {0}，你好，欢迎登录智慧新零售系统！

#业务产品标识，此参数通常由未来的自动部署系统自动生成
ms.product.id 230300441701912576
ms.product.number ydj.store
ms.product.name 智慧新零售系统
ms.product.alias ydj.store

#当前站点允许使用的企业信息:默认站点
#ms.company.icon logo-ydj.png
#ms.company.title 智慧新零售系统

#慕思K3定制辅助属性编码
ms.musi.k3.customauxpropnumber ProcReq
ms.musi.k3.enablecustomauxprop true

#启动计划任务
ms.site.enabletask false

#序列号生成器id
fw.Sequence.WorkId 1

#service stack 框架所依赖的配置项
servicestack:license ydj-e1JlZjp5ZGosTmFtZTrmt7HlnLPmmJPliLDlrrbnp5HmioDmnInpmZDlhazlj7gsVHlwZTpFbnRlcnByaXNlLEhhc2g6SjBMcHFNS01wR0ZRbWhJMk0zQXVvS1FFQVdjb1R4K1NYZXlTSkFDVkpoQVhGT3o5bC81K2hySWhFVkJSRXlKa1ZPSmdDbStXRXRTMlUxbWVDeW9DN1VQUWVGSkZKWmkwdlp5RHFoSG1seWpiMklVV3lPaUlvTzh3OHR0T2RZdUplbTJtMll6V0MyQ2lDeUI4dnRTRDdIS0J1dk9BZ0Jhd3YvR2N2akxOVFcwPSxFeHBpcnk6OTk5OS0xMi0zMVQyMzo1OTo1OS45OTk5OTk5KzA4OjAwfQ==

jwt.PrivateKeyXml <RSAKeyValue><Modulus>0QoG0XKaqEpqH8qZgiLSfQn0uJSr+LVmALWwZJvyWU0Vvuiwe3kuo8pxvHluqiGTRUclJGjc00t5k0FIX07vCHB0B7x7jWdWRrDDekIGTKAIyKldS084ZJS4Sw0Hdt0x99SyPbETVEGPZNflv3QSplhZozV/A9Q0HO6G3m6hcMk=</Modulus><Exponent>AQAB</Exponent><P>+kgcGku2DQkZzt3Ws+Tmg2Ur9gb1p2NkoBj63XhyNilAWonLb9GYLbWooXDx5JthjoyYLtrJy+bZCEtD0g2iqw==</P><Q>1dCwy7XN7EojYr9qlFrQDwusZK9CdfHtibmK0/UETQYYIXc3ljT1oL02FQ6IY/hIcwb3B7vwAa+EaULDTcnaWw==</Q><DP>YgFgt89T75DTwVTPIUyOE3/HkiSxhyV9ns+JVLY4iwrmP03cEuJRI/0Rln5CypEpx4c4hIh6ItU3wW6vlQ5v1w==</DP><DQ>Ucjdl04XUkaZenAVaGHEK70fV/PhzBOZ9JBQFfsmhf/KwyJC1OW0/qLhSPSiHvsdW0JSw1aT21EzqL+szNhk+Q==</DQ><InverseQ>wQfOO9KPEXkoZ1Lm/EXUUtAmqE+6z1EmcOVdEy0w/zAuadW72v8GHBGfsoo99S1dagfqqhq8Rjfy0wm6gmoWpA==</InverseQ><D>tK1K6Vt6C32OLn5Jom6AqiyhxaxS27vGN1TS6pLTrJXw9rpeV8qNImrHVZVLrmcoyFdO5iJww3xXz4w8hoxOEtclIzzf7CiXHW0110ckeuuy6LTCudeDoZadlLCw8h7aZ2ocfU3808YI4HIb2CtZLbe+qufHVLxVOn5FAqwJ59U=</D></RSAKeyValue>
jwt.PublicKeyXml <RSAKeyValue><Modulus>0QoG0XKaqEpqH8qZgiLSfQn0uJSr+LVmALWwZJvyWU0Vvuiwe3kuo8pxvHluqiGTRUclJGjc00t5k0FIX07vCHB0B7x7jWdWRrDDekIGTKAIyKldS084ZJS4Sw0Hdt0x99SyPbETVEGPZNflv3QSplhZozV/A9Q0HO6G3m6hcMk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>
jwt.PublicKeyXml.1 <RSAKeyValue><Modulus>ylbL6NirdRNDggCVPPVxFW1ypF3gbYDzJtayM76e4sz5YHDCpSeaDdu4Mb8YaXLEzbM72n1nnfss5g4xd6xr94zUAjLJr9Nlu0Bn0ktqjBlMNEoLig9TVT1fHTsq2/Wxvavnuw73o/ibU+cxJOxiU7dMrij3m9A5z85xn9YnVvE=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>
jwt.HashAlgorithm RS256
#令牌过期时间为50年
jwt.ExpireTokensInDays 18250
#本机jwt令牌听众标识
jwt.Audience abcd@1234
#本机jwt令牌发行者信息
jwt.issuer fw.abcd



#从库
fw.db.slave.enable 0
fw.db.slave.dbHost localhost
fw.db.slave.dbUser ms_ydj_test
fw.db.slave.dbPwd ms&25#ydj%10!test@2024
fw.db.slave.dbName ydj_uat_store_20220514

#慕思消息队列redis
ms.mq.redis.host tQ9%gA8/uR0<@localhost:31686

#3维家配置
#ms.svj.appid 22305672987
#ms.svj.appkey 1464a659b96f43c0a189bf72c7f8392f
#ms.svj.ssohost https://sso-test.3weijia.com
#ms.svj.graphhost https://graph-test.3weijia.com
#ms.svj.openhost https://open-test.3weijia.com

#ms.swj.appid 22305672987
#ms.swj.appkey 1464a659b96f43c0a189bf72c7f8392f
#ms.swj.ssohost https://sso.3vjia.com
#ms.swj.graphhost https://open.3vjia.com
#ms.swj.openhost https://open.3vjia.com

#正式环境
ms.swj.appid 22305672987
ms.swj.appkey 1464a659b96f43c0a189bf72c7f8392f
ms.swj.ssohost https://sso.3vjia.com
ms.swj.graphhost https://graph.3vjia.com
ms.swj.openhost https://open.3vjia.com
ms.swj.redirecturi https://admin.3vjia.com/3DLoading
ms.swj.detailuri https://aimes.3vjia.com/crm/customOrderDetail
ms.swj.serviceredirecturi https://admin.3vjia.com/3DLoading
ms.swj.detailuri https://aimes.3vjia.com/crm/customOrderDetail
ms.swj.opengatwayhost https://open-gateway.3vjia.com





#预发环境
#ms.swj.appid 22305672987
#ms.swj.appkey 1464a659b96f43c0a189bf72c7f8392f
#ms.swj.ssohost https://pre-sso.3vjia.com
#ms.swj.ssohost https://sso.3vjia.com
#ms.swj.graphhost https://graph.3vjia.com
#ms.swj.graphhost https://pre-graph.3vjia.com
#ms.swj.openhost https://pre-open.3vjia.com
#ms.swj.openhost https://open.3vjia.com
#ms.swj.redirecturi https://pre-3d.3vjia.com/V5/basic
#ms.swj.redirecturi https://admin.3vjia.com/3DLoading


#华为云oss服务配置:
fw.huaweioss.ak MNZGFJGBRIVZUIDDVUPI
fw.huaweioss.sk ksZ4GzA4VZOdvi62wSwyyy0APoJNotvmKY9hLmXU
fw.huaweioss.endpoint obs.cn-south-1.myhuaweicloud.com
fw.huaweioss.bucketname derucci-public


#总部企业标识
fw.topcompanyid 821347239912935425

#全量测试企业
#ms.testcompanyids 829653207454318616

#OMS服务配置
ms.oms.redirecturi https://wx.derucci.com:8093/

#天地图appKey
fw.tianditu.appKey e221e793b05f3a2086fbd8479e28633e

#健康检查
fw.metafeature.viewer ydjcheck

#集成禅道配置
#旧禅道配置
#ms.zt.datasource dmp.jienor.com
#ms.zt.userid root
#ms.zt.password Jienor0803++
#ms.zt.database zentao
#ms.zt.port 13306
#ms.zt.project 2024慕思运维项目

#新禅道配置（测试环境）
ms.zt.datasource ***************
ms.zt.userid root
ms.zt.password Jienor0803++
ms.zt.database zentao_test
ms.zt.port 3306
ms.zt.project 2024慕思运维项目

#新禅道配置
#ms.zt.datasource ***************
#ms.zt.userid root
#ms.zt.password Jienor0803++
#ms.zt.database zentao_up
#ms.zt.port 3306
#ms.zt.project 2024慕思运维项目

#慕思BI服务
ms.bi.linkUrl https://rp.yidaohome.com/
ms.bi.publicKey MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiHTB7C4EsEhXexBmhBL5t/dZJ9wFjk11m5/PdCdBvzt0HCTWqop9wtpLSN848SdCFQh28IIkVZVGEeAuKiYG4s0Dcp0+IM6AheBrsLKnInqKBaxz0tq5abrHW+N6AbOHxXc2134K2tLuUuRoV5m7WVBw153Rh5BU4bebNTL/gs53tn2/4zjH4AQyF83lQLG3sfHJFMBJyzcKofym1JQsLeB0os49+EfkyBOqIOuilvhoNXFrfA3xPwMcdQMWPUMXMlrrHQBLmcKA0P6iubtokiA3Ue2yD2hSVd/tS74vGXT8dd2bcW5uspDY5oQG55aNXXyDzAlaDL+n75zDFjqYoQIDAQAB

#列表查询是否返回统计信息
ms.listquerydata.cangetdesc true


#商品授权缓存
ms.prdisolate.tmp.cachemin 1
ms.prdisolate.tmp.prddatacacheminutes 2
#商品授权是否要清除缓存
ms.prdisolate.tmp.canClearCache true

#慕思认证平台
ms.auth.linkUrl https://chandao.derucci.com:6443/web/admin-dealer-account/#/

#服务请求连接超时时长，按秒记
fw:http.connectiontimeout 300

#登录session过期时间，按分钟计，默认为1天
fw:auth.sessionexpiry 30

#sql执行超时时长，按秒记
fw:database.dbcommandtimeout 300
