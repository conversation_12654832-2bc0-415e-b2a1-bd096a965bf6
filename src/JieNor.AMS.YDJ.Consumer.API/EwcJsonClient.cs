using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.AMS.YDJ.Consumer.API.Config;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.AMS.YDJ.Consumer.API.DTO;

namespace JieNor.AMS.YDJ.Consumer.API
{
    /// <summary>
    /// 企业微信WebAPI服务访问接口
    /// </summary>
    public static class EwcJsonClient
    {
        /// <summary>
        /// 向企业微信API发送GET请求，如对方要求传递签名参数，则用此方法
        /// </summary>
        /// <typeparam name="T">响应对象中Data属性的泛型类型</typeparam>
        /// <param name="request">请求对象</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应对象</returns>
        public static EwcBaseResponse<T> Get<T>(EwcBaseDTO request, Dictionary<string, string> headers = null)
             where T : class, new()
        {
            return Invoke<T>(request, Enu_HttpMethod.Get, headers);
        }

        /// <summary>
        /// 向企业微信API发送POST请求，如对方要求传递签名参数，则用此方法
        /// </summary>
        /// <typeparam name="T">响应对象中Data属性的泛型类型</typeparam>
        /// <param name="request">请求对象</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应对象</returns>
        public static EwcBaseResponse<T> Post<T>(EwcBaseDTO request, Dictionary<string, string> headers = null)
             where T : class, new()
        {
            return Invoke<T>(request, Enu_HttpMethod.Post, headers);
        }

        /// <summary>
        /// 向企业微信API发送HTTP请求，如对方要求传递签名参数，则用此方法
        /// </summary>
        /// <typeparam name="T">响应对象中Data属性的泛型类型</typeparam>
        /// <param name="request">请求对象</param>
        /// <param name="method">HTTP请求方式</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应对象</returns>
        public static EwcBaseResponse<T> Invoke<T>(EwcBaseDTO request, Enu_HttpMethod method = Enu_HttpMethod.Post, Dictionary<string, string> headers = null)
             where T : class, new()
        {
            if (request.Code.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException($"{nameof(request.Code)} 参数值不能为空！");
            }

            //客户密钥
            var secret = request.Code.GetCustomerSecretKey();
            if (secret.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException($"根据 {nameof(request.Code)} 参数值 {request.Code} 获取不到客户密钥，请检查 customer.json 配置文件！");
            }

            //添加签名参数
            request.Timestamp = BeiJingTime.Now.TimestampFromBeiJingTime().ToString();
            request.Nonce = Guid.NewGuid().ToString("N");
            request.Signature = CheckSignature.GetSignature(request.Timestamp.ToString(), request.Nonce, secret);

            return Invoke<T, EwcBaseDTO>(request, method, headers);
        }

        /// <summary>
        /// 向企业微信API发送GET请求
        /// </summary>
        /// <typeparam name="T">响应对象中Data属性的泛型类型</typeparam>
        /// <typeparam name="TRequest">请求对象的类型</typeparam>
        /// <param name="request">请求对象</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应对象</returns>
        public static EwcBaseResponse<T> Get<T, TRequest>(TRequest request, Dictionary<string, string> headers = null)
             where T : class, new()
             where TRequest : class
        {
            return Invoke<T, TRequest>(request, Enu_HttpMethod.Get, headers);
        }

        /// <summary>
        /// 向企业微信API发送POST请求
        /// </summary>
        /// <typeparam name="T">响应对象中Data属性的泛型类型</typeparam>
        /// <typeparam name="TRequest">请求对象的类型</typeparam>
        /// <param name="request">请求对象</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应对象</returns>
        public static EwcBaseResponse<T> Post<T, TRequest>(TRequest request, Dictionary<string, string> headers = null)
             where T : class, new()
             where TRequest : class
        {
            return Invoke<T, TRequest>(request, Enu_HttpMethod.Post, headers);
        }

        /// <summary>
        /// 向企业微信API发送HTTP请求
        /// </summary>
        /// <typeparam name="T">响应对象中Data属性的泛型类型</typeparam>
        /// <typeparam name="TRequest">请求对象的类型</typeparam>
        /// <param name="request">请求对象</param>
        /// <param name="method">HTTP请求方式</param>
        /// <param name="headers">请求头</param>
        /// <returns>响应对象</returns>
        public static EwcBaseResponse<T> Invoke<T, TRequest>(TRequest request, Enu_HttpMethod method = Enu_HttpMethod.Post, Dictionary<string, string> headers = null)
            where T : class, new()
            where TRequest : class
        {
            var targetServer = GetEwcService(headers);
            return JsonClient.InvokeThird<TRequest, EwcBaseResponse<T>>(targetServer, request, method);
        }

        /// <summary>
        /// 获取企业微信WebAPI服务地址，比如：http://ewc.yidaohome.com/
        /// </summary>
        /// <remarks>
        /// 该目标服务地址由 host.config 文件中的 ms.ewc.webapi.host 配置节点提供。
        /// </remarks>
        /// <param name="headers"></param>
        /// <returns></returns>
        public static TargetServer GetEwcService(Dictionary<string, string> headers = null)
        {
            return JsonClient.GetTargetServer("ms.ewc.webapi.host", headers);
        }
    }
}