using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.Model.MP;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.AMS.YDJ.Core.DataEntity.Weixin;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.MP
{
    /// <summary>
    /// 微信小程序：生成小程序码
    /// </summary>
    public class WeixinGenerateWxCodeController : BaseNotAuthController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(WeixinGenerateWxaCodeDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<WeixinGenerateWxCodeModel>();

            if (dto.Data.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 data 不能为空！";
                resp.Success = false;
                return resp;
            }

            var weixinService = this.Container.GetService<IWeixinService>();

            var wxaCode = weixinService.GenerateConsumerWxaCode(this.Context, dto.Data, dto.Page);

            if (wxaCode == null)
            {
                resp.Data = new WeixinGenerateWxCodeModel();
                resp.Message = "生成小程序码失败！";
                resp.Success = false;
            }
            else
            {
                resp.Data = new WeixinGenerateWxCodeModel()
                {
                    Scene = wxaCode.Scene,
                    Page = wxaCode.Page,
                    Data= wxaCode.Data,
                    Image = ImageFieldUtil.ParseImage(wxaCode.ImageId, "", true)
                };
                resp.Message = "生成小程序码成功！";
                resp.Success = true;
            }

            return resp;
        }
    }
}