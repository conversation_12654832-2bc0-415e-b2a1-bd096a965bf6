using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.BD.Product
{
    public class ProductListByCategoryController : BaseNotAuthController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductListByCategoryDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<ProductsCategoryModel>>
            {
                Data = new BaseListPageData<ProductsCategoryModel>()
            };

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, "824389883303956505"),
                new SqlParam("@topfmainorgid", System.Data.DbType.String,this.Context.TopCompanyId)
            };

            //品类过滤条件
            if (!dto.CategoryId.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParam.Add(new SqlParam("@categoryid", System.Data.DbType.String, dto.CategoryId));
            }
            //获取分页数据
            var list = this.GetList(dto, sqlParam);

            //设置响应数据包
            resp.Data.List = list;
            resp.Message = "取数成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 获取分页数据
        /// </summary>
        private List<ProductsCategoryModel> GetList(
            ProductListByCategoryDTO dto,
            List<SqlParam> sqlParam)
        {
            var list = new List<ProductsCategoryModel>();

            //用于小程序功能演示，暂时只取每个商品类别的10条商品记录
            var sqlText = $@"/*dialect*/
                            WITH RankedResults AS (
                                SELECT
                                    pc.fid AS categoryid,
                                    pc.fname AS categoryName,
                                    sc.fid AS subcategoryid,
                                    sc.fname AS subCategoryName,
                                    m.fid,
                                    m.fname,
                                    m.fimage,
                                    m.fguideprice,
                                    m.fcreatedate,
                                    m.fdefattrinfo,
                                    m.fsalprice,
                                    ROW_NUMBER() OVER (PARTITION BY sc.fid ORDER BY m.fimage DESC) AS RowNum
                                FROM
                                    ser_ydj_category pc
                                    JOIN ser_ydj_category sc ON pc.fid = sc.fparentid
                                    JOIN T_BD_MATERIAL m ON m.fcategoryid = sc.fid
                            		where pc.fid=@categoryid and (m.fmainorgid=@fmainorgid or m.fmainorgid=@topfmainorgid)
                            )
                            SELECT
                                categoryid,
                                categoryName,
                                subcategoryid,
                                subCategoryName,
                                fid,
                                fname,
                                fimage,
                                fguideprice,
                                fcreatedate,
                                fdefattrinfo,
                                fsalprice
                            FROM
                                RankedResults
                            WHERE
                                RowNum <= 10;
                            ";

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    list.Add(new ProductsCategoryModel
                    {
                        Id = reader.GetValueToString("fid"),
                        Name = reader.GetValueToString("fname"),
                        AuxPropValId = reader.GetValueToString("fdefattrinfo").Trim(),
                        ImageUrl = reader.GetValueToString("fimage").GetSignedFileUrl(true),
                        SalPrice = reader.GetValueToDecimal("fsalprice"),
                        GuidePrice = reader.GetValueToDecimal("fguideprice"),
                        TopCategoryId = reader.GetValueToString("categoryid"),
                        TopCategoryName = reader.GetValueToString("categoryName"),
                        SubCategoryId = reader.GetValueToString("subcategoryid"),
                        SubCategoryName = reader.GetValueToString("subCategoryName"),
                    });
                }
            }
            //this.LoadProductImageList(list.Select(l => l as ProductListModel).ToList());

            var categoryGroup = list.GroupBy(p => new { p.TopCategoryId, p.TopCategoryName, p.SubCategoryId, p.SubCategoryName });
            var result = new List<ProductsCategoryModel>();
            foreach (var group in categoryGroup)
            {
                result.Add(new ProductsCategoryModel
                {
                    TopCategoryId = group.Key.TopCategoryId,
                    TopCategoryName = group.Key.TopCategoryName,
                    SubCategoryId = group.Key.SubCategoryId,
                    SubCategoryName = group.Key.SubCategoryName,
                    ProductLists = group.Select(g => g as ProductListModel).ToList()
                });
            }

            return result;
        }

        /// <summary>
        /// 加载商品图片列表，后续可按需优化取数逻辑
        /// </summary>
        private void LoadProductImageList(List<ProductListModel> list)
        {
            foreach (var item in list)
            {
                item.ImageList = ProductUtil.GetImages(this.Context, item.Id, item.AuxPropValId);
            }
        }

    }
}
