using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO
{
    /// <summary>
    /// 企业微信：接收消息接口请求对象
    /// 接收来自于企业微信WebAPI发起的请求
    /// </summary>
    [Api("企业微信消息接收接口")]
    [Route("/mpapi/ewc/message/send")]
    public class EwcReceiveMsgDTO : EwcBaseDTO
    {
        /// <summary>
        /// 企业微信用户标识
        /// </summary>
        public string UserId { get; set; }

        //企业微信企业标识
        public string CorpId { get; set; }
    }
}