using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO
{
    /// <summary>
    /// 消费者小程序：获取小程序码
    /// </summary>
    [Api("获取小程序码")]
    [Route("/consumerapi/wx/getWxaCode")]
    public class WeixinGetWxaCodeDTO : BaseNotAuthDTO
    {
        /// <summary>
        /// 
        /// </summary>
        public string Scene { get; set; }
    }
}