using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO
{
    /// <summary>
    /// 登录接口
    /// </summary>
    [Api("登录接口")]
    [Route("/consumerapi/login")]
    public class LoginDTO : BaseAuthDTO
    {
        /// <summary>
        /// 微信用户登录凭证
        /// </summary>
        public string Code { get; set; }
    }
}