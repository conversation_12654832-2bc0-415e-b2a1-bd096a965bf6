using ServiceStack;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.Consumer.API.DTO
{
    /// <summary>
    /// 商品属性取数接口（销售价、可用库存、图片 等等）
    /// </summary>
    [Api("商品属性取数接口")]
    [Route("/consumerapi/product/profile")]
    public class ProductProfileDTO : BaseNotAuthDTO
    {
        /// <summary>
        /// 辅助属性值
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; }
        public string Id { get; set; }
    }
}