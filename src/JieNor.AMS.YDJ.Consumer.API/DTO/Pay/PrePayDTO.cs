using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO.Pay
{

    /// <summary>
    /// 小程序支付接口
    /// </summary>
    [Api("小程序支付接口")]
    [Route("/mpapi/common/prepay")]
    [Authenticate]
    public class PrePayDTO:BaseDTO
    {
        /// <summary>
        /// 业务订单号
        /// </summary>
        public string bizNo { get; set; }
    }
}
