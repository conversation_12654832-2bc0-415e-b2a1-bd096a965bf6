using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Consumer.API.Utils
{
    /// <summary>
    /// 商品帮助类：定义商品相关的通用逻辑
    /// </summary>
    public partial class ProductUtil
    {
        /// <summary>
        /// 将后端麦浩的辅助属性值打包成小程序端需要的数据结构
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="dynObj">辅助属性字段所在的数据包，比如：意向单明细行数据包</param>
        /// <param name="fieldKey">辅助属性字段标识</param>
        /// <returns>打包后的数据结构，比如：[{ auxPropId: "714842810959925253", valueId: "黑色", valueName: "黑色"}]</returns>
        public static List<Dictionary<string, string>> PackAuxPropFieldValue(HtmlForm formMeta, DynamicObject dynObj, string fieldKey = "fattrinfo")
        {
            var auxPropVals = new List<Dictionary<string, string>>();

            if (formMeta == null || dynObj == null || fieldKey.IsNullOrEmptyOrWhiteSpace()) return auxPropVals;

            var auxPropField = formMeta.GetField(fieldKey) as HtmlAuxPropertyField;
            if (auxPropField == null)
            {
                throw new BusinessException($"表单 {formMeta.Caption} 不存在 {fieldKey} 辅助属性值字段，请检查！");
            }

            var auxPropObj = auxPropField.RefDynamicProperty.GetValue<DynamicObject>(dynObj);
            var valueSetNumber = Convert.ToString(auxPropObj?["fnumber"]);
            if (valueSetNumber.IsNullOrEmptyOrWhiteSpace()) return auxPropVals;

            auxPropVals = PackAuxPropFieldValue(valueSetNumber);

            return auxPropVals;
        }

        /// <summary>
        /// 将后端麦浩的辅助属性值打包成小程序端需要的数据结构
        /// </summary>
        /// <param name="valueSetNumber">比如：714842810959925253:黑色,714842863862681606:真皮,714842901401702406:单人位</param>
        /// <returns>打包后的数据结构，比如：[{ auxPropId: "714842810959925253", valueId: "黑色", valueName: "黑色"}]</returns>
        public static List<Dictionary<string, string>> PackAuxPropFieldValue(string valueSetNumber)
        {
            var auxPropVals = new List<Dictionary<string, string>>();

            var numbers = valueSetNumber?.Trim()?.Split(',');
            if (numbers != null && numbers.Length > 0)
            {
                foreach (var item in numbers)
                {
                    var items = item.Split(':');
                    if (items.Length == 2)
                    {
                        auxPropVals.Add(new Dictionary<string, string>
                        {
                            { "auxPropId", items[0] },
                            { "valueId", items[1] },
                            { "valueName", items[1] },
                        });
                    }
                }
            }

            return auxPropVals;
        }

        /// <summary>
        /// 辅助属性组合值名称格式化
        /// </summary>
        /// <param name="formMeta">表单模型</param>
        /// <param name="dynObj">辅助属性字段所在的数据包，比如：意向单明细行数据包</param>
        /// <param name="fieldKey">辅助属性字段标识</param>
        /// <returns>格式化后的名称，比如：黑色/真皮/单人位</returns>
        public static string AuxPropValueSetFormat(HtmlForm formMeta, DynamicObject dynObj, string fieldKey = "fattrinfo")
        {
            var spce = "";

            if (formMeta == null || dynObj == null || fieldKey.IsNullOrEmptyOrWhiteSpace()) return spce;

            var auxPropField = formMeta.GetField(fieldKey) as HtmlAuxPropertyField;
            if (auxPropField == null)
            {
                throw new BusinessException($"表单 {formMeta.Caption} 不存在 {fieldKey} 辅助属性值字段，请检查！");
            }

            var auxPropObj = auxPropField.RefDynamicProperty.GetValue<DynamicObject>(dynObj);
            var valueSetName = Convert.ToString(auxPropObj?["fname"]);
            if (valueSetName.IsNullOrEmptyOrWhiteSpace()) return spce;

            spce = AuxPropValueSetFormat(valueSetName);

            return spce;
        }

        /// <summary>
        /// 辅助属性组合值名称格式化
        /// </summary>
        /// <param name="valueSetName">辅助属性组合值名称，比如：颜色:黑色,材质:真皮,规格:单人位</param>
        /// <returns>格式化后的名称，比如：黑色/真皮/单人位</returns>
        public static string AuxPropValueSetFormat(string valueSetName)
        {
            var spce = new StringBuilder();
            var setNames = valueSetName?.Trim()?.Split(',');
            if (setNames != null && setNames.Length > 0)
            {
                foreach (var item in setNames)
                {
                    var items = item.Split(':');
                    if (items.Length == 2)
                    {
                        spce.Append(items[1]).Append("/");
                    }
                }
            }
            return spce.ToString().TrimEnd('/');
        }

        /// <summary>
        /// 获取商品辅助属性列表
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="materialId">商品ID</param>
        /// <returns>商品辅助属性列表</returns>
        public static JArray GetAuxPropInfo(UserContext userCtx, string materialId)
        {
            return GetAuxPropInfo(userCtx, materialId, string.Empty);
        }

        /// <summary>
        /// 获取商品辅助属性列表
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="materialId">商品ID</param>
        /// <param name="auxPropVals">辅助属性值列表，用于处理默认的辅助属性值</param>
        /// <returns>商品辅助属性列表</returns>
        public static JArray GetAuxPropInfo(UserContext userCtx, string materialId, List<Dictionary<string, string>> auxPropVals = null)
        {
            if (materialId.IsNullOrEmptyOrWhiteSpace()) return null;

            //辅助属性组合值键值对
            Dictionary<string, string> auxPropKv = null;
            if (auxPropVals != null && auxPropVals.Any())
            {
                auxPropKv = new Dictionary<string, string>();
                foreach (var item in auxPropVals)
                {
                    var auxPropId = Convert.ToString(item["auxPropId"]);
                    var valueId = Convert.ToString(item["valueId"]);
                    auxPropKv[auxPropId] = valueId;
                }
            }

            return GetAuxPropInfo(userCtx, materialId, auxPropKv);
        }

        /// <summary>
        /// 获取商品辅助属性列表
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="materialId">商品ID</param>
        /// <param name="auxPropValId">辅助属性组合值ID，用于处理默认的辅助属性值</param>
        /// <returns>商品辅助属性列表</returns>
        public static JArray GetAuxPropInfo(UserContext userCtx, string materialId, string auxPropValId = "")
        {
            if (materialId.IsNullOrEmptyOrWhiteSpace()) return null;

            //辅助属性组合值键值对
            Dictionary<string, string> auxPropKv = null;
            if (!auxPropValId.IsNullOrEmptyOrWhiteSpace())
            {
                var metaService = userCtx.Container.GetService<IMetaModelService>();
                var dm = userCtx.Container.GetService<IDataManager>();

                var valSetForm = metaService.LoadFormModel(userCtx, "bd_auxpropvalueset");
                dm.InitDbContext(userCtx, valSetForm.GetDynamicObjectType(userCtx));
                var setObj = dm.Select(auxPropValId) as DynamicObject;
                var setEntrys = setObj?["fentity"] as DynamicObjectCollection;
                if (setEntrys != null && setEntrys.Any())
                {
                    auxPropKv = new Dictionary<string, string>();
                    foreach (var item in setEntrys)
                    {
                        var auxPropId = Convert.ToString(item["fauxpropid"]);
                        var valueId = Convert.ToString(item["fvalueid"]);
                        auxPropKv[auxPropId] = valueId;
                    }
                }
            }

            return GetAuxPropInfo(userCtx, materialId, auxPropKv);
        }

        /// <summary>
        /// 获取商品辅助属性列表
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="materialId">商品ID</param>
        /// <param name="auxPropValId">辅助属性组合值ID</param>
        /// <param name="auxPropKv">辅助属性组合值键值对，用于处理默认的辅助属性值</param>
        /// <returns>商品辅助属性列表</returns>
        public static JArray GetAuxPropInfo(UserContext userCtx, string materialId, Dictionary<string, string> auxPropKv = null)
        {
            if (materialId.IsNullOrEmptyOrWhiteSpace()) return null;

            var metaService = userCtx.Container.GetService<IMetaModelService>();
            var dm = userCtx.Container.GetService<IDataManager>();

            //辅助属性值映射
            var valMapForm = metaService.LoadFormModel(userCtx, "bd_auxpropvaluemap");
            dm.InitDbContext(userCtx, valMapForm.GetDynamicObjectType(userCtx));
            var sqlWhere = "fmainorgid=@fmainorgid and fmaterialid=@fmaterialid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fmaterialid", System.Data.DbType.String, materialId)
            };
            var reader = userCtx.GetPkIdDataReader(valMapForm, sqlWhere, sqlParam);
            var dynObjs = dm.SelectBy(reader).OfType<DynamicObject>();
            if (dynObjs == null || !dynObjs.Any()) return null;

            //辅助属性基础资料
            var auxPropIds = dynObjs.Select(o => o["fauxpropid"]).Distinct();
            var auxPropForm = metaService.LoadFormModel(userCtx, "sel_prop");
            dm.InitDbContext(userCtx, auxPropForm.GetDynamicObjectType(userCtx));
            var auxPropObjs = dm.Select(auxPropIds).OfType<DynamicObject>();
            if (auxPropObjs == null || !auxPropObjs.Any()) return null;

            //辅助属性列表
            var auxPropInfo = new JArray();

            foreach (var dynObj in dynObjs)
            {
                var entrys = dynObj["fentity"] as DynamicObjectCollection;
                if (entrys == null || !entrys.Any()) continue;

                var auxPropId = Convert.ToString(dynObj["fauxpropid"]);

                //按照指定的辅助属性组合值来设置默认值
                var valueId = "";
                auxPropKv?.TryGetValue(auxPropId, out valueId);
                if (!valueId.IsNullOrEmptyOrWhiteSpace())
                {
                    foreach (var entry in entrys)
                    {
                        if (Convert.ToString(entry["fvalueid"]).EqualsIgnoreCase(valueId))
                        {
                            entry["fisdefval"] = true;
                        }
                        else
                        {
                            entry["fisdefval"] = false;
                        }
                    }
                }

                //辅助属性值集
                var valueList = new JArray();
                foreach (var entry in entrys)
                {
                    var isDisable = Convert.ToBoolean(entry["fisdisable"]);
                    if (!isDisable)
                    {
                        var valueItem = new JObject();
                        valueItem["valueId"] = Convert.ToString(entry["fvalueid"]);
                        valueItem["valueName"] = Convert.ToString(entry["fvaluename"]);
                        //valueItem["valueNumber"] = Convert.ToString(entry["fvaluenumber"]);
                        valueItem["isDefVal"] = Convert.ToBoolean(entry["fisdefval"]);
                        valueList.Add(valueItem);
                    }
                }

                //辅助属性
                var auxPropObj = auxPropObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(auxPropId));
                var auxProp = new JObject();
                auxProp["auxPropId"] = auxPropId;
                auxProp["auxPropName"] = Convert.ToString(auxPropObj["fname"]);
                //auxProp["auxPropNumber"] = Convert.ToString(auxPropObj["fnumber"]);
                auxProp["valueList"] = valueList;
                auxPropInfo.Add(auxProp);
            }

            return auxPropInfo;
        }

        /// <summary>
        /// 获取商品可用量
        /// 可用量 = 库存量 + 在途量 - 预留量
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品可用量</returns>
        public static decimal GetUsableQty(
            UserContext userCtx,
            string productId,
            List<Dictionary<string, string>> auxPropVals = null,
            string customDesc = "")
        {
            var realStockQty = GetLocalRealStockQty(userCtx, productId, auxPropVals, customDesc);

            var intransitQty = GetIntransitQty(userCtx, productId, auxPropVals, customDesc);

            var reserveQty = GetReserveQty(userCtx, productId, auxPropVals, customDesc);

            //可用量 = 库存量 + 在途量 - 预留量
            var usableQty = realStockQty + intransitQty - reserveQty;

            return usableQty;
        }

        /// <summary>
        /// 获取商品可用量
        /// 可用量 = 库存量 + 在途量 - 预留量
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropValId">商品辅助属性值集ID</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品可用量</returns>
        public static decimal GetUsableQty(
            UserContext userCtx,
            string productId,
            string auxPropValId = "",
            string customDesc = "")
        {
            var realStockQty = GetLocalRealStockQty(userCtx, productId, auxPropValId, customDesc);

            var intransitQty = GetIntransitQty(userCtx, productId, auxPropValId, customDesc);

            var reserveQty = GetReserveQty(userCtx, productId, auxPropValId, customDesc);

            //可用量 = 库存量 + 在途量 - 预留量
            var usableQty = realStockQty + intransitQty - reserveQty;

            return usableQty;
        }

        /// <summary>
        /// 获取商品本地销售价
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <param name="salPrice">商品销售价</param>
        /// <returns>商品本地销售价</returns>
        public static decimal GetLocalSalPrice(
            UserContext userCtx,
            string productId,
            List<Dictionary<string, string>> auxPropVals = null,
            decimal salPrice = 0M)
        {
            var auxPropWhere = GetAuxPropWhere(auxPropVals);
            return GetLocalSalPrice(userCtx, productId, auxPropWhere, salPrice, null);
        }

        /// <summary>
        /// 获取商品本地销售价
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropValId">商品辅助属性值集ID</param>
        /// <param name="salPrice">商品销售价</param>
        /// <returns>商品本地销售价</returns>
        public static decimal GetLocalSalPrice(
            UserContext userCtx,
            string productId,
            string auxPropValId = "",
            decimal salPrice = 0M)
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@auxPropValId", System.Data.DbType.String, auxPropValId),
            };
            var auxPropWhere = $" and te.fattrinfo=@auxPropValId";
            return GetLocalSalPrice(userCtx, productId, auxPropWhere, salPrice, sqlParam);
        }

        /// <summary>
        /// 获取商品本地销售价
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropWhere">辅助属性值过滤条件</param>
        /// <param name="salPrice">商品销售价</param>
        /// <returns>商品本地销售价</returns>
        private static decimal GetLocalSalPrice(
            UserContext userCtx,
            string productId,
            string auxPropWhere,
            decimal salPrice = 0M,
            List<SqlParam> paramList = null)
        {
            if (productId.IsNullOrEmptyOrWhiteSpace()) return salPrice;

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fproductid", System.Data.DbType.String, productId)
            };

            var sqlWhere = new StringBuilder();
            sqlWhere.AppendFormat(@"
            t.fmainorgid=@fmainorgid and te.fproductid=@fproductid and te.fconfirmstatus='2'
            and '{0}'>=te.fstartdate and '{0}'<=te.fexpiredate {1}", DateTime.Now.ToString("yyyy-MM-dd"), auxPropWhere);

            if (paramList != null)
            {
                sqlParam.AddRange(paramList);
            }

            var sqlText = $@"
            select max(te.fsalprice) from t_ydj_price t with(nolock) 
            inner join t_ydj_priceentry te with(nolock) on te.fid=t.fid 
            where t.fforbidstatus='0' and {sqlWhere}";

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    var obj = reader[0];
                    if (obj != DBNull.Value)
                    {
                        salPrice = Convert.ToDecimal(obj);
                    }
                }
            }

            return salPrice;
        }

        /// <summary>
        /// 获取商品本地即时库存数
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品本地即时库存数</returns>
        public static decimal GetLocalRealStockQty(
            UserContext userCtx,
            string productId,
            List<Dictionary<string, string>> auxPropVals = null,
            string customDesc = "")
        {
            var auxPropWhere = GetAuxPropWhere(auxPropVals);
            return GetLocalRealStockQty(userCtx, productId, auxPropWhere, customDesc, null);
        }

        /// <summary>
        /// 获取商品本地即时库存数
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropValId">商品辅助属性值集ID</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品本地即时库存数</returns>
        public static decimal GetLocalRealStockQty(
            UserContext userCtx,
            string productId,
            string auxPropValId = "",
            string customDesc = "")
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@auxPropValId", System.Data.DbType.String, auxPropValId),
            };
            var auxPropWhere = $" and te.fattrinfo=@auxPropValId";
            return GetLocalRealStockQty(userCtx, productId, auxPropWhere, customDesc, sqlParam);
        }

        /// <summary>
        /// 获取商品本地即时库存数
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropWhere">辅助属性值过滤条件</param>
        /// <param name="customDesc">定制说明</param>
        /// <param name="paramList">参数列表</param>
        /// <returns>商品本地即时库存数</returns>
        private static decimal GetLocalRealStockQty(
            UserContext userCtx,
            string productId,
            string auxPropWhere,
            string customDesc = "",
            List<SqlParam> paramList = null)
        {
            var stockQty = 0M;

            if (productId.IsNullOrEmptyOrWhiteSpace()) return stockQty;

            customDesc = (customDesc ?? "").Trim();

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fmaterialid", System.Data.DbType.String, productId)
            };

            if (paramList != null)
            {
                sqlParam.AddRange(paramList);
            }

            var sqlWhere = new StringBuilder();
            sqlWhere.AppendFormat($@"
            te.fmainorgid=@fmainorgid and te.fmaterialid=@fmaterialid and te.fmtono=''{auxPropWhere}");

            var sqlText = $@"select sum(fqty) from t_stk_inventorylist te with(nolock) where {sqlWhere}";

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    var obj = reader[0];
                    if (obj != DBNull.Value)
                    {
                        stockQty = Convert.ToDecimal(obj);
                    }
                }
            }

            return stockQty;
        }

        /// <summary>
        /// 获取商品在途量
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品在途量</returns>
        public static decimal GetIntransitQty(
            UserContext userCtx,
            string productId,
            List<Dictionary<string, string>> auxPropVals = null,
            string customDesc = "")
        {
            var auxPropWhere = GetAuxPropWhere(auxPropVals);
            return GetIntransitQty(userCtx, productId, auxPropWhere, customDesc, null);
        }

        /// <summary>
        /// 获取商品在途量
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropValId">商品辅助属性值集ID</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品在途量</returns>
        public static decimal GetIntransitQty(
            UserContext userCtx,
            string productId,
            string auxPropValId = "",
            string customDesc = "")
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@auxPropValId", System.Data.DbType.String, auxPropValId),
            };
            var auxPropWhere = $" and te.fattrinfo=@auxPropValId";
            return GetIntransitQty(userCtx, productId, auxPropWhere, customDesc, sqlParam);
        }

        /// <summary>
        /// 获取商品在途量
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropWhere">辅助属性值过滤条件</param>
        /// <param name="customDesc">定制说明</param>
        /// <param name="paramList">参数列表</param>
        /// <returns>商品在途量</returns>
        private static decimal GetIntransitQty(
            UserContext userCtx,
            string productId,
            string auxPropWhere,
            string customDesc = "",
            List<SqlParam> paramList = null)
        {
            var intransitQty = 0M;

            if (productId.IsNullOrEmptyOrWhiteSpace()) return intransitQty;

            customDesc = (customDesc ?? "").Trim();

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fmaterialid", System.Data.DbType.String, productId)
            };

            if (paramList != null)
            {
                sqlParam.AddRange(paramList);
            }

            var sqlWhere = new StringBuilder();
            sqlWhere.AppendFormat($@"
            r.fmainorgid=@fmainorgid and r.fcancelstatus='0' and te.fmaterialid=@fmaterialid and te.fmtono=''{auxPropWhere}");

            //在途量 = 
            //未审核未作废的其它入库单数量 + 
            //未审核未作废的销售退货单数量 + 
            //未审核未作废的库存调拨单数量 + 
            //已审核未作废未关闭的采购订单（数量 - 入库数量 - 退款数量）+ 退换数量
            var sqlText = $@"
            select 
            (
		        select isnull(sum(fqty),0) from t_stk_otherstockin r with(nolock) 
		        inner join t_stk_otherstockinentry te with(nolock) on te.fid=r.fid 
		        where {sqlWhere} and r.fstatus<>'E'
	        )+(
		        select isnull(sum(fqty),0) from t_stk_sostockreturn r with(nolock) 
		        inner join t_stk_sostockreturnentry te with(nolock) on te.fid=r.fid 
                where {sqlWhere} and r.fstatus<>'E'
	        )+(
		        select isnull(sum(fqty),0) from t_stk_invtransfer r with(nolock) 
		        inner join t_stk_invtransferentry te with(nolock) on te.fid=r.fid 
                where {sqlWhere} and r.fstatus<>'E'
	        )+(
		        select isnull(sum((fqty-finstockqty-frefundqty)+freturnqty),0) from t_ydj_purchaseorder r with(nolock) 
		        inner join t_ydj_poorderentry te with(nolock) on te.fid=r.fid 
                where {sqlWhere} and r.fstatus='E' and (te.fclosestatus='0' or te.fclosestatus='')
	        )";

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    var obj = reader[0];
                    if (obj != DBNull.Value)
                    {
                        intransitQty = Convert.ToDecimal(obj);
                    }
                }
            }

            return intransitQty;
        }

        /// <summary>
        /// 获取商品预留量
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品预留量</returns>
        public static decimal GetReserveQty(
            UserContext userCtx,
            string productId,
            List<Dictionary<string, string>> auxPropVals = null,
            string customDesc = "")
        {
            var auxPropWhere = GetAuxPropWhere(auxPropVals);
            return GetReserveQty(userCtx, productId, auxPropWhere, customDesc, null);
        }

        /// <summary>
        /// 获取商品预留量
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropValId">商品辅助属性值集ID</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品预留量</returns>
        public static decimal GetReserveQty(
            UserContext userCtx,
            string productId,
            string auxPropValId = "",
            string customDesc = "")
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@auxPropValId", System.Data.DbType.String, auxPropValId),
            };
            var auxPropWhere = $" and te.fattrinfo=@auxPropValId";
            return GetReserveQty(userCtx, productId, auxPropWhere, customDesc, sqlParam);
        }

        /// <summary>
        /// 获取商品预留量
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropWhere">辅助属性值过滤条件</param>
        /// <param name="customDesc">定制说明</param>
        /// <param name="paramList">参数列表</param>
        /// <returns>商品预留量</returns>
        private static decimal GetReserveQty(
            UserContext userCtx,
            string productId,
            string auxPropWhere,
            string customDesc = "",
            List<SqlParam> paramList = null)
        {
            var reserveQty = 0M;

            if (productId.IsNullOrEmptyOrWhiteSpace()) return reserveQty;

            customDesc = (customDesc ?? "").Trim();

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fmaterialid", System.Data.DbType.String, productId)
            };

            if (paramList != null)
            {
                sqlParam.AddRange(paramList);
            }

            var sqlWhere = new StringBuilder();
            sqlWhere.AppendFormat($@"
            t.fmainorgid=@fmainorgid and t.fstatus='E' and t.fcancelstatus='0' 
            and te.fmaterialid=@fmaterialid and te.fmtono=''{auxPropWhere}");

            var sqlText = $@"
            select fentryid,fparentid,fqty,fdirection from t_stk_reservebill t with(nolock) 
            inner join t_stk_reservebillentry te with(nolock) on te.fid=t.fid and te.fqty >0  
            where {sqlWhere}";

            var dbService = userCtx.Container.GetService<IDBService>();
            var allResEntrys = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);
            if (allResEntrys == null || allResEntrys.Count < 1) return reserveQty;

            //筛选出有效的预留单主明细行
            var parents = allResEntrys.Where(o => o["fparentid"].IsNullOrEmptyOrWhiteSpace());
            foreach (var parent in parents)
            {
                //统计当前主明细行的实际预留量 = 主明细行预留数量 + 子明细行预留数量汇总
                var childQtySum = allResEntrys.Sum(o =>
                {
                    var qty = 0M;
                    if (Convert.ToString(o["fparentid"]).EqualsIgnoreCase(Convert.ToString(parent["fentryid"])))
                    {
                        qty = Convert.ToDecimal(o["fqty"]);
                        if (Convert.ToString(o["fdirection"]).EqualsIgnoreCase("1"))
                        {
                            qty = qty * -1;
                        }
                    }
                    return qty;
                });
                reserveQty += childQtySum + Convert.ToDecimal(parent["fqty"]);
            }

            return reserveQty;
        }

        /// <summary>
        /// 获取辅助属性值过滤条件
        /// </summary>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <returns>过滤条件</returns>
        private static string GetAuxPropWhere(List<Dictionary<string, string>> auxPropVals = null)
        {
            var sqlWhere = new StringBuilder();
            if (auxPropVals != null && auxPropVals.Any())
            {
                for (int i = 0; i < auxPropVals.Count; i++)
                {
                    var attrInfo = auxPropVals[i];
                    sqlWhere.AppendFormat(@" and exists (select top 1 1 from t_bd_auxpropvalueentry apve with(nolock) 
                    inner join t_sel_prop ap with(nolock) on ap.fid = apve.fauxpropid 
                    where ap.fid='{0}' and apve.fvalueid='{1}' and apve.fid=te.fattrinfo)",
                    Convert.ToString(attrInfo["auxPropId"]), Convert.ToString(attrInfo["valueId"]));
                }
            }
            else
            {
                //没有辅助属性值时，根据空匹配，目的是为了精确匹配
                sqlWhere.Append(" and te.fattrinfo=''");
            }
            return sqlWhere.ToString();
        }
    }
}