using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Config
{
    /// <summary>
    /// 配置文件扩展类
    /// </summary>
    public static class ConfigExtentions
    {
        /// <summary>
        /// 客户配置文件
        /// </summary>
        private static CustomerConfig customerConfig = new CustomerConfig();

        /// <summary>
        /// 构造函数
        /// </summary>
        static ConfigExtentions()
        {
            LoadCustomerConfig();
        }

        /// <summary>
        /// 加载客户配置文件
        /// </summary>
        public static void LoadCustomerConfig()
        {
            try
            {
                var configFilePath = Path.Combine(PathUtils.GetStartupPath(), "customer.json");
                customerConfig = configFilePath.ReadObjectFromFile<CustomerConfig>();
                if (customerConfig == null) customerConfig = new CustomerConfig();
            }
            catch { }
        }

        /// <summary>
        /// 获取客户密钥
        /// </summary>
        /// <param name="idOrNumber">客户标识或编码</param>
        /// <returns>客户密钥</returns>
        public static string GetCustomerSecretKey(this string idOrNumber)
        {
            var customer = idOrNumber.GetCustomer();
            return customer.SecretKey;
        }

        /// <summary>
        /// 获取客户信息
        /// </summary>
        /// <param name="idOrNumber">客户标识或编码</param>
        /// <returns>客户信息</returns>
        public static CustomerInfo GetCustomer(this string idOrNumber)
        {
            CustomerInfo customer = null;
            if (!idOrNumber.IsNullOrEmptyOrWhiteSpace())
            {
                customer = customerConfig?.Customers?.FirstOrDefault(o =>
                {
                    return o.Id.EqualsIgnoreCase(idOrNumber) || o.Number.EqualsIgnoreCase(idOrNumber);
                });
            }
            if (customer == null)
            {
                customer = new CustomerInfo();
            }
            return customer;
        }

        /// <summary>
        /// 获取客户信息
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <returns>客户信息</returns>
        public static CustomerInfo GetCustomer(this UserContext userCtx)
        {
            CustomerInfo customer = customerConfig?.Customers?.FirstOrDefault(o => o.CompanyId.EqualsIgnoreCase(userCtx.Company));
            if (customer == null)
            {
                customer = new CustomerInfo();
            }
            return customer;
        }

        /// <summary>
        /// 获取默认客户信息
        /// </summary>
        /// <returns>客户信息</returns>
        public static CustomerInfo GetDefaultCustomer()
        {
            CustomerInfo customer = customerConfig?.Customers?.FirstOrDefault();
            if (customer == null)
            {
                customer = new CustomerInfo();
            }
            return customer;
        }
    }
}