using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Model.Pay
{
    /// <summary>
    /// 金蝶支付接口返回请求业务数据模型
    /// </summary>
    public class KingdeeQrCodePayModel
    {
        /// <summary>
        /// 业务响应码（KP1100：请求成功
        /// </summary>
        public string bizCode { get; set; }
        /// <summary>
        /// 业务响应描述
        /// </summary>
        public string bizMsg { get; set; }
        /// <summary>
        /// 二维码字符串(需要手动转换成二维码)
        /// </summary>
        public string codeUrl { get; set; }
        /// <summary>
        /// 二维码图片地址
        /// </summary>
        public string codeImgUrl { get; set; }
    }
}
