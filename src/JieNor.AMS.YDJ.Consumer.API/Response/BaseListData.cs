using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API
{
    /// <summary>
    /// 列表数据模型基类
    /// </summary>
    /// <typeparam name="T">列表数据泛型类型</typeparam>
    public class BaseListData<T> where T : class, new()
    {
        /// <summary>
        /// 列表数据：数据结构由实际的接口确定。
        /// </summary>
        public List<T> List { get; set; }
    }
}