using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.DTO.IncomeDisburse;
using JieNor.AMS.YDJ.MS.API.DTO.Order;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MS.API.Controller.Order
{
    /// <summary>
    /// 慕思：焕新订单合同接收通知
    /// </summary>
    public class OrderRenewalNotifyController : BaseController<OrderRenewalNotifyDTO>
    {
        public string FormId
        {
            get { return "ydj_order"; }
        }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "fbillno";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(OrderRenewalNotifyDTO dto)
        {
            var resp = new BaseResponse<object>();

            if (!Valid(dto, resp)) return resp;

            var agentCtx = this.Context.CreateAgentDBContext(dto.AgentId);
            try
            {
                var result = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", null, "MSRenewalNotify",
                    new Dictionary<string, object> { { "data", dto } });

                resp = result.ToResponseModel<object>();
                if (result.IsSuccess)
                {
                    var ordeObj = GetOrderData(dto, agentCtx);

                    if (ordeObj != null)
                    {
                        //焕新订单标记-直营
                        var frenewalflag = Convert.ToBoolean(ordeObj["frenewalflag"]);
                        if (frenewalflag && agentCtx.IsDirectSale)
                        {
                            // 允许收款的焕新订单（焕新类型.允许发起收款=勾选），通过慕思会员收款后，自动更新【结算进度=已收款】自动触发【直营锁单】；反之，不允许收款的焕新订单（焕新类型.允许发起收款≠勾选），则手动点击【直营锁单】
                            var reNewType = Convert.ToString(ordeObj["frenewtype"]);
                            if (!reNewType.IsNullOrEmptyOrWhiteSpace())
                            {
                                var reNewTypeObj = agentCtx.LoadBizBillHeadDataById("ydj_renewtype", reNewType, "fisincome");
                                if (reNewTypeObj != null)
                                {
                                    var isIncome = Convert.ToBoolean(Convert.ToInt32(reNewTypeObj["fisincome"]));
                                    if (!isIncome)
                                    {
                                        return resp;
                                    }
                                }
                            }
                            //提交总部
                            var submitHeadquart = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order",
                                new List<DynamicObject> { ordeObj }, "SubmitHeadquart",
                                new Dictionary<string, object> { { "MSRenewalNotify", false } });

                            ProcessOperationResult(submitHeadquart, resp, dto.fbillno);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                resp.Code = 400;
                resp.Message = $"操作失败！,{ex.Message}{ex.StackTrace}";
                resp.Success = false;
            }

            return resp;
        }

        /// <summary>
        /// 处理操作结果
        /// </summary>
        /// <param name="result"></param>
        /// <param name="resp"></param>
        /// <param name="billNo"></param>
        private void ProcessOperationResult(IOperationResult result, BaseResponse<object> resp,
            string billNo)
        {
            if (result.IsSuccess)
            {
                resp.Code = 200;
                resp.Success = true;
                resp.Message = "操作成功！";

                foreach (var message in result.ComplexMessage.SuccessMessages)
                {
                    resp.Messages.Add(message);
                }

                this.Request.SetBillNo(MSKey.SuccessNumber, billNo);
            }
            else
            {
                resp.Code = 400;
                resp.Success = false;
                resp.Message = string.Join(",", result.ComplexMessage.ErrorMessages);

                this.Request.SetBillNo(MSKey.FailNumber, billNo);
            }
        }

        /// <summary>
        /// 获取销售合同
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <param name="agentCtx"></param>
        /// <returns></returns>
        private DynamicObject GetOrderData(OrderRenewalNotifyDTO dto, UserContext agentCtx)
        {
            var order = agentCtx.LoadBizDataById("ydj_order", new List<string> { dto.Id })
                ?.FirstOrDefault();

            return order;
        }

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Valid(OrderRenewalNotifyDTO dto, BaseResponse<object> resp)
        {
            if (dto.fbillno.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数fbillno不能为空！";
                resp.Success = false;

                return false;
            }

            if (dto.fagentno.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数fagentno不能为空！";
                resp.Success = false;

                return false;
            }

            var bizAgentId = this.Context.GetBizAgentId(dto.fagentno);
            if (bizAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = $"经销商【{dto.fagentno}】不存在！";
                resp.Success = false;

                return false;
            }

            dto.AgentId = bizAgentId;

            return true;
        }

        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected override void SetNumbers()
        {
            this.Request.SetBillNo(MSKey.BillNo, (this.Request.Dto as OrderRenewalNotifyDTO)?.fbillno);
        }

        protected override Dictionary<string, string> CreateDistributedLocks(OrderRenewalNotifyDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.fbillno}", $"销售合同 {dto.fbillno} 正在锁定中，请稍后再操作！" }
            };
        }
    }
}