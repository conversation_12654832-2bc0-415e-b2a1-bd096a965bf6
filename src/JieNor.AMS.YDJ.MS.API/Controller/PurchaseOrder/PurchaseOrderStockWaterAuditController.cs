using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.MS.API.Controller.PurchaseOrder
{
    public class PurchaseOrderStockWaterAuditController : BaseController<PurchaseOrderStockWaterAuditDTO>
    {
        public string FormId
        {
            get { return "ydj_purchaseorder"; }
        }
        protected HtmlForm HtmlForm { get; set; }
        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "billNo";

        protected override string BizObjectFormId => this.FormId;
        public override object Execute(PurchaseOrderStockWaterAuditDTO dto)
        {
            var resp = new BaseResponse<object>();
            resp.Code = 200;
            resp.Success = true;
            resp.Message = "审批成功！";
            if (!Valid(dto, resp)) return resp;
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);
            var _logservice = this.Container.GetService<ILogService>();
            var agentCtx = this.Context.CreateAgentDBContext(dto.BizAgentId);
           
            var purchaseOrder = agentCtx.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new string[] { dto.BillNo }, true)
                ?.FirstOrDefault();
            if (purchaseOrder == null)
            {
                resp.Code = 400;
                resp.Success = false;
                resp.Message = $"采购订单{dto.BillNo}不存在，请检查！";
                return resp;
            }
            purchaseOrder["fstockwaterspecialstatus"] = dto.Status;
            agentCtx.SaveBizData(FormId, purchaseOrder);
            _logservice.WriteLog(agentCtx, new LogEntry
            {
                BillIds = purchaseOrder["id"] as string,
                BillNos = purchaseOrder["fbillno"] as string,
                BillFormId = FormId,
                OpName = "库存水位管控审批",
                OpCode = "stockwateraudit",
                Content = $"执行了【库存水位管控审批】操作",
                DebugData = $"执行了【库存水位管控审批】操作",
                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                Level = Enu_LogLevel.Info.ToString(),
                LogType = Enu_LogType.RecordType_03,
            });
            return resp;

        }
        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Valid(PurchaseOrderStockWaterAuditDTO dto, BaseResponse<object> resp)
        {
            if (dto.BillNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数billNo不能为空！";
                resp.Success = false;

                return false;
            }

            dto.BizAgentId = this.Context.GetBizAgentId(dto.AgentNo);
            if (dto.BizAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = $"经销商【{dto.AgentNo}】不存在！";
                resp.Success = false;

                return false;
            }
            return true;
        }

        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected override void SetNumbers()
        {
            this.Request.SetBillNo(MSKey.BillNo, (this.Request.Dto as PurchaseOrderStockWaterAuditDTO)?.BillNo);
        }
    }
}
