using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.MS.API.DTO.IncomeDisburse;
using JieNor.AMS.YDJ.MS.API.DTO.Order;
using JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.AMS.YDJ.SWJ.API;
using JieNor.AMS.YDJ.SWJ.API.APIs;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.DTO.Supplier;

namespace JieNor.AMS.YDJ.MS.API.Controller.Supplier
{
    /// <summary>
    /// 创建供应商控制器
    /// </summary>
    public class CreateSupplierController : BaseController<CreateSupplierDto>
    {
        public string FormId
        {
            get { return "ydj_supplier"; }
        }

        protected HtmlForm HtmlForm { get; set; }

        protected ILogService loger { get; set; }

        UserContext agentCtx { get; set; }

        /// <summary>
        /// 执行创建供应商操作
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>执行结果</returns>
        public override object Execute(CreateSupplierDto dto)
        {
            var resp = new BaseResponse<object>();

            if (!Valid(dto, resp)) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";

            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            this.agentCtx = this.Context.CreateAgentDBContext(dto.BizAgentId);

            ResetSysAdminUserContext(this.agentCtx);

            var supplier = this.agentCtx
                .LoadBizDataByNo(this.HtmlForm.Id, "fthridpartycode", new string[] { dto.SaletoCode })
                ?.FirstOrDefault();
            if (supplier != null)
            {
                resp.Code = 400;
                resp.Message = $"直营编码{dto.SaletoCode}已存在 请检查！";
                resp.Success = false;

                return resp;
            }

            var supplierObj = GetSupplierDisburseObject(agentCtx, dto);

            if (resp.Success)
            {
                var result = this.HttpGateway.InvokeBillOperation(this.agentCtx, this.HtmlForm.Id,
                    new List<DynamicObject> { supplierObj },
                    "save", new Dictionary<string, object>());

                ProcessOperationResult(result, resp);
            }
            else
            {
                resp.Code = 400;
                resp.Success = false;
            }

            return resp;
        }

        /// <summary>
        /// 处理操作结果
        /// </summary>
        /// <param name="result"></param>
        /// <param name="resp"></param>
        /// <param name="dto"></param>
        private void ProcessOperationResult(IOperationResult result, BaseResponse<object> resp)
        {
            if (result.IsSuccess)
            {
                resp.Code = 200;
                resp.Success = true;
                resp.Message = string.Join(",", result.ComplexMessage.SuccessMessages);
                resp.Messages = result.ComplexMessage.SuccessMessages;

                //this.Request.SetBillNo(MSKey.SuccessNumber, dto.BillNo);
            }
            else
            {
                resp.Code = 400;
                resp.Success = false;
                resp.Message = string.Join(",", result.ComplexMessage.ErrorMessages);
                resp.Messages = result.ComplexMessage.ErrorMessages;

                //this.Request.SetBillNo(MSKey.FailNumber, dto.BillNo);
            }
        }

        /// <summary>
        /// 验证请求参数
        /// </summary>
        /// <param name="dto">请求参数</param>
        /// <param name="resp">请求参数</param>
        private bool Valid(CreateSupplierDto dto, BaseResponse<object> resp)
        {
            if (dto.SaletoCode.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数 【SaletoCode-直营编码】不能为空！";
                resp.Success = false;

                return false;
            }

            if (dto.ShopCode.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数 【ShopCode-商场编码】不能为空！";
                resp.Success = false;

                return false;
            }

            if (dto.ShopName.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数 【ShopName-商场名称】不能为空！";
                resp.Success = false;

                return false;
            }

            dto.BizAgentId = this.Context.GetBizAgentId(dto.SaletoCode);
            if (dto.BizAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = $"经销商【{dto.SaletoCode}】不存在！";
                resp.Success = false;

                return false;
            }

            return true;
        }


        /// <summary>
        /// 获取供应商数据包
        /// </summary>
        /// <param name="incomeDisburse"></param>
        /// <returns></returns>
        private DynamicObject GetSupplierDisburseObject(UserContext sysctx, CreateSupplierDto supplier)
        {
            DynamicObject billHead = new DynamicObject(this.HtmlForm.GetDynamicObjectType(sysctx));
            billHead["fthridpartycode"] = supplier.ShopCode; //第三方编码
            billHead["faddress"] = supplier.Address; //详细地址
            billHead["fname"] = supplier.ShopName; //名称
            billHead["ftype"] = "suppliertype_01"; //供应商
            billHead["foperationmode"] = "2"; //独立经营
            billHead["fiscashagent"] = true; //卖场

            return billHead;
        }

        /// <summary>
        /// 重置为系统管理员
        /// </summary>
        private void ResetSysAdminUserContext(UserContext context)
        {
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = context.Product;
            session.Company = context.Company;
            session.BizOrgId = context.Company;
            session.TopCompanyId = context.TopCompanyId;
            session.ParentCompanyId = context.ParentCompanyId;
            session.Companys = context.Companys.ToList();
            session.Id = context.Id;

            context.SetUserSession(session);
        }
    }
}