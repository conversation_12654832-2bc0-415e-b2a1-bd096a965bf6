using JieNor.AMS.YDJ.MS.API.DTO.User;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.MS.API.Controller.User
{
    /// <summary>
    /// 用户：同步接口
    /// </summary>
    public class ChangePwdController : BaseAuthController<ChangePwdDto>
    {
        protected HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "sec_user"; }
        }

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "account";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(ChangePwdDto dto)
        {

            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);
            var userService = this.Container.GetService<IUserRegisterService>();

            var resp = new BaseResponse<MuSiData>();

            var sql = $@"select fid,fnumber,fname,fmainorgid,fismodifypwd from t_sec_user with(nolock) where fnumber in ({dto.Data.Select(x => x.account).JoinEx(",", true)})";
            var userList = this.DBService.ExecuteDynamicObject(this.Context, sql);

            if (!Valid(dto, resp, userList?.ToList())) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";
            resp.Data.Flag = MuSiFlag.SUCCESS.ToString();

            //1.密码接口字段变更：取消【确认密码】、【组织id】、【组织编码】字段；增加【类型】字段，【类型】字段用于区分密码变化的类型：0：密码修改   1：密码重置（忘记密码短信重置） 2：管理员重置；
            // 2.认证平台：
            // 2.1用户首次调用认证平台修改密码，需输入旧密码但平台不做校验，二次登录修改密码开始强校验旧密码正确才可以修改密码；
            // 2.2重置密码不做旧密码校验，直接输入新密码，新密码需做两次一致确认校验;
            // 2.3接口【类型】字段，区分杰诺方调用修改密码 / 重置密码的界面，0：用户修改密码界面，1：短信重置密码界面   2：重置按钮操作，通过接口返回金蝶系统默认密码
            // 2.4 接口交互必须包含【类型】字段值，重置密码接口 旧密码为空值
            // 3.经销商基础运营平台
            // 3.1经销商基础运营平台未调用认证平台功能进行修改密码的用户，保持原来的密码不变，经销商基础运营平台需考虑区分用户首次调用修改密码 区分逻辑，不校验旧密码；二次修改密码后，强校验旧密码；
            // 3.2经销商基础运营平台修改密码场景，调用密码修改界面，接口【类型】字段值为0；忘记密码重置场景调用短信重置页面，接口【类型】字段值为1；管理员重置密码场景按钮触发接口，接口【类型】字段值为2；重置密码操作，旧密码字段为空值;
            foreach (var data in dto.Data)
            {
                try
                {
                    var newpwd = MusiAuthHelper.Decrypt(data.newpwd);
                    var oldpwd = MusiAuthHelper.Decrypt(data.oldpwd);

                    var user = userList?.FirstOrDefault(x => Convert.ToString(x["fnumber"]) == data.account);
                    var agentCtx = this.Context.CreaeteaAgentDBContextWithUser(Convert.ToString(user["fmainorgid"]), Convert.ToString(user["fid"]));

                    IOperationResult result = new OperationResult();
                    if (data.type == "0" && Convert.ToString(user["fismodifypwd"]) == "1")
                    {
                        var response = this.HttpGateway.Invoke(agentCtx, TargetSEP.AuthService, new CommonFormDTO()
                        {
                            FormId = "auth_user",
                            OperationNo = "modifypwd",
                            SimpleData = new Dictionary<string, string>
                                            {
                                                    { "userName", agentCtx.UserName },
                                                    { "newpwd", newpwd },
                                                    { "newrepwd", newpwd },
                                                    { "oldpwd", oldpwd }
                                            },
                        }) as DynamicDTOResponse;
                        result = response?.OperationResult;
                    }
                    else
                    {
                        var isSysReset = false;
                        if (data.type == "2")
                        {
                            newpwd = "888888";
                            isSysReset = true;
                        };

                        var option = OperateOption.Create();
                        option.SetVariableValue("isSysReset", isSysReset);
                        result = userService.ResetUserPassword(agentCtx, new List<string> { data.account }, newpwd, option);
                        if (result.SimpleMessage.EqualsIgnoreCase("密码修改成功")) result.IsSuccess = true;
                        if (!result.ComplexMessage.ErrorMessages.Any() && result.SimpleMessage.IsNullOrEmptyOrWhiteSpace())
                        {
                            result.IsSuccess = true;
                        }
                    }

                    if (result.IsSuccess)
                    {
                        resp.Data.SucceedNumbers.Add(data.account);
                        resp.Data.SucceedNumbers_Log.Add(data.account);
                    }
                    else
                    {
                        resp.Success = false;
                        resp.Data.FailedNumbers.Add(data.account);
                        resp.Data.FailedNumbers_Log.Add(data.account);
                        resp.Data.ErrorMsgs.AddRange(result.ToString()?.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
                    }
                }
                catch (Exception e)
                {
                    this.LogService.Error(e);
                    resp.Success = false;
                    resp.Data.ErrorMsgs.Add(e.Message);
                }
            }

            if (resp.Data.SucceedNumbers.Any())
            {
                var updateUsers = userList.Where(x => Convert.ToString(x["fismodifypwd"]) == "0" && resp.Data.SucceedNumbers.Contains(Convert.ToString(x["fnumber"])))
                    .Select(x => Convert.ToString(x["fnumber"]));
                if (updateUsers != null && updateUsers.Any())
                {  //标记用户同步修改过密码的标识
                    var updatesql = $@"update t_sec_user set fismodifypwd='1' where fnumber in ({updateUsers.JoinEx(",", true)})";
                    this.DBService.ExecuteDynamicObject(this.Context, updatesql);
                }
            }

            if (!resp.Success)
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", resp.Data.ErrorMsgs);
                resp.Data.Flag = resp.Data.SucceedNumbers.Any()
                    ? MuSiFlag.PARTSUCCESS.ToString()
                    : MuSiFlag.FAIL.ToString();
            }

            this.Request.Items.Add("fsuccessnumber", string.Join(",",resp.Data.SucceedNumbers));
            this.Request.Items.Add("ffailnumber", string.Join(",", resp.Data.FailedNumbers));
            return resp;
        }


        private bool Valid(ChangePwdDto dto, BaseResponse<MuSiData> resp, List<DynamicObject> users)
        {
            if (dto.Data == null || dto.Data.Count == 0)
            {
                resp.Code = 400;
                resp.Message = "参数data不能为空！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);
                return false;
            }
            List<string> errorMsgs = new List<string>();

            foreach (var data in dto.Data)
            {
                if (data.account.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数account不能为空！");
                    resp.Data.FailedNumbers.Add(data.account);
                }
                var user = users?.FirstOrDefault(x => Convert.ToString(x["fnumber"]) == data.account);
                if (user == null)
                {
                    errorMsgs.Add(data.account + "修改用户不存在！");
                    resp.Data.FailedNumbers.Add(data.account);
                }

                //to do 获取认证中心的验证码，匹配传过来的验证码是否一致。
                bool CheckResult = this.MusiAuthService.ValidateSigeCode(this.Context, data.account, data.signcode);
                if (!CheckResult)
                {
                    errorMsgs.Add($"账号：{data.account}验证码未通过，不允许修改密码！");
                    resp.Data.FailedNumbers.Add(data.account);
                }

                switch (data.type)
                {
                    case "0":
                        if (data.oldpwd.IsNullOrEmptyOrWhiteSpace())
                        {
                            errorMsgs.Add("旧密码不能为空！");
                            resp.Data.FailedNumbers.Add(data.account);
                        }
                        break;
                    case "1":
                        break;
                    case "2":
                        break;
                    default:
                        errorMsgs.Add(data.account + "参数type不正确！");
                        resp.Data.FailedNumbers.Add(data.account);
                        break;
                }

                if (data.newpwd.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add(data.account + "新密码不能为空！");
                    resp.Data.FailedNumbers.Add(data.account);
                }
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;

                resp.Data.ErrorMsgs = errorMsgs;
                resp.Data.Flag = MuSiFlag.FAIL.ToString();
                this.Request.Items.Add("ffailnumber", string.Join(",", resp.Data.FailedNumbers.Distinct()));

                return false;
            }

            return true;
        }
    }
}
