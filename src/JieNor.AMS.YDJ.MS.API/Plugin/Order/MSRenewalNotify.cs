using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.MS.API.DTO.Order;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MS.API.Plugin.Order
{
    /// <summary>
    /// 销售合同：焕新订单合同接收通知
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("MSRenewalNotify")]
    public class MSRenewalNotify : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (!this.Option.TryGetVariableValue<OrderRenewalNotifyDTO>("data", out var dto) || dto == null)
            {
                throw new BusinessException("数据包出错，请检查！");
            }

            var order = this.Context.LoadBizDataById(this.HtmlForm.Id, dto.Id);
            if (order == null)
            {
                throw new BusinessException($"销售合同{dto.fbillno}不存在，请检查！");
            }

            switch (dto.ftype)
            {
                case 0:
                    ReceiptHandle(dto, order);
                    break;
                case 1:
                    RefundHandle(dto, order);
                    break;
                default:
                    throw new BusinessException($"未实现ftype={dto.ftype}，请检查！");
            }

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "操作成功！";
        }

        /// <summary>
        /// 付款处理
        /// </summary>
        /// <param name="dto"></param>
        private void ReceiptHandle(OrderRenewalNotifyDTO dto, DynamicObject order)
        {
            /*
             * 2、当回传类型=付款：
             * 1）当【结算进度】=收款中，才允许接收处理。
             * 2）更新【结算进度】=已收款。
             * 3）更新单据头.【补贴总金额】后，计算明细.【补贴金额】。（计算逻辑：依据每一行商品成交金额在所有明细成交金额汇总占比，进行按比例计算。计算结果需精确到个位数，若存在多行，则将除最后一行的前几行计算完毕后，将剩余金额全部归集在最后一行）
             * 当【单据类型】=v6定制柜合同，需剔除【定制订单进度】=单据作废 的商品行后，再进行每一行【补贴金额】计算。（反之，非v6定制柜合同依然按现有整单商品进行计算处理）
             * 4）基于合同生成收款单，系统自动提交审核。（字段映射逻辑请看下方单据转换：销售合同->收款单）
             */

            var fsettlprogress = Convert.ToString(order["fsettlprogress"]);
            // 收款中
            if (!fsettlprogress.EqualsIgnoreCase(Enu_RenewalSettleProgress.收款中))
            {
                return;
            }

            var fbilltype = Convert.ToString(order["fbilltype"]);
            var frenewalrectag = Convert.ToBoolean(order["frenewalrectag"]);
            var billTypeObj = this.Context.LoadBizBillHeadDataById("bd_billtype", fbilltype);
            bool isV6 = billTypeObj != null && Convert.ToString(billTypeObj["fname"]).EqualsIgnoreCase("v6定制柜合同");

            order["fsettlprogress"] = Enu_RenewalSettleProgress.已收款; //已收款
            order["fsubsidyamount"] = dto.fsubsidyamount_h;
            if (isV6 && frenewalrectag)
            {
                order["frecsumamount_gb"] = dto.fsubsidyamount_h;
            }
            order["fmembershiptranid"] = dto.fmembershiptranid;
            order["fmembershippaydate"] = Convert.ToDateTime(dto.fpaydate);

            var fentry = order["fentry"] as DynamicObjectCollection;
            foreach (var item in fentry)
            {
                if (isV6 && frenewalrectag)
                {
                    item["fsubrecdealamount_gb"] = item["fdealamount"];
                    item["fsubrecsumamount_gb"] = dto.fsubsidyamount_h;
                }
            }
            var calEntrys = fentry.Where(s =>
            {
                //发起收款时才校验，这里不做处理
                //if (isV6)
                //{
                //    var fomsprogress = Convert.ToString(s["fomsprogress"]);
                //    if (!fomsprogress.EqualsIgnoreCase(Enu_OMSProgress.流程完成))
                //    {
                //        return false;
                //    }
                //}

                return true;
            });

            var beAmount = 0M;
            var sumDealAmount = calEntrys.Sum(s => Convert.ToDecimal(s["fdealamount"]));

            foreach (var entry in calEntrys)
            {
                var fdealamount = Convert.ToDecimal(entry["fdealamount"]);

                var fsubsidyamount = Convert.ToDecimal(Math.Floor(dto.fsubsidyamount_h * (fdealamount / sumDealAmount)));

                entry["fsubsidyamount"] = fsubsidyamount;

                beAmount += fsubsidyamount;
            }

            if (dto.fsubsidyamount_h > beAmount)
            {
                var lastEntry = calEntrys.Last();
                lastEntry["fsubsidyamount"] =
                    Convert.ToDecimal(lastEntry["fsubsidyamount"]) + (dto.fsubsidyamount_h - beAmount);
            }

            var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new List<DynamicObject> { order }, "draft",
                 new Dictionary<string, object>());
            result.ThrowIfHasError();

            //收款逻辑迁移到 一键采购插件[RenewalPur]中处理，因为兰考焕新手动一键采购也需要 自动生成收款单
            //// 基于合同生成收款单，系统自动提交审核。（字段映射逻辑请看下方单据转换：销售合同->收款单）
            //var incomeDisburseMeta = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            //var payOrder = incomeDisburseMeta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;

            //payOrder["frenewalflag"] = true;
            //payOrder["fdate"] = Convert.ToDateTime(dto.fpaydate);
            //payOrder["fway"] = "payway_14"; //焕新合同收款
            //payOrder["fpurpose"] = "bizpurpose_02";  //订单付款
            //payOrder["fdirection"] = "direction_02";    //减
            //payOrder["fbizdirection"] = "bizdirection_01";  //收入
            //payOrder["fsourceformid"] = "ydj_order";
            //payOrder["fsourcenumber"] = order["fbillno"];
            //payOrder["fsourceid"] = order["id"];
            //payOrder["fstaffid"] = order["fstaffid"];
            //payOrder["fdeptid"] = order["fdeptid"];
            //payOrder["fcustomerid"] = order["fcustomerid"];
            //payOrder["fmembershiptranid"] = dto.fmembershiptranid;
            //payOrder["famount"] = order["fdealamount"];
            //payOrder["paymentdesc"] = "790610086597890051";   //合同款

            //result = this.Gateway.InvokeBillOperation(this.Context, incomeDisburseMeta.Id, new[] { payOrder }, "save",
            //    new Dictionary<string, object> { { "IgnoreValidateDataEntities", true }, { "NotAutoSubmit", true } });
            //result.ThrowIfHasError();

            //result = this.Gateway.InvokeBillOperation(this.Context, incomeDisburseMeta.Id, new[] { payOrder }, "submit", new Dictionary<string, object> { { "IgnoreValidateDataEntities", true } });
            //result.ThrowIfHasError();

            //result = this.Gateway.InvokeBillOperation(this.Context, incomeDisburseMeta.Id, new[] { payOrder }, "audit", new Dictionary<string, object> { { "IgnoreValidateDataEntities", true } });
            //result.ThrowIfHasError();

            //var frenewalrectag = Convert.ToBoolean(order?["frenewalrectag"] ?? false);
            //2、当【基本信息.焕新预收标记】=是：①取消自动调用一键采购的逻辑。当【基本信息.焕新预收标记】=否：保持现有逻辑。
            if (!frenewalrectag && !Convert.ToBoolean(order["fpiecesendtag"]) && !this.Context.IsDirectSale)
            {
                result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { order }, "RenewalPur",
                        new Dictionary<string, object> { { "IgnoreValidateDataEntities", false }, { "MSRenewalNotify", true } });
                result.ThrowIfHasError();
            }
        }

        private void RefundHandle(OrderRenewalNotifyDTO dto, DynamicObject order)
        {
            /*
             * 1、当回传类型=退款，暂不接收处理。
             */
        }
    }
}
