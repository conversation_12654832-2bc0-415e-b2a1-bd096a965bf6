using JieNor.AMS.YDJ.MS.API.Filter;
using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.ProductAuth
{
    /// <summary>
    /// 总部下发商品授权清单接口
    /// </summary>
    [Api("总部下发商品授权清单接口")]
    [Route("/msapi/productauth/push")]
    [Authenticate]
    public class ProductAuthDTO : BaseDTO
    {
        /// <summary>
        /// 售达方编码
        /// </summary>
        public string AgentNo { get; set; }

        public List<ProductAuthInfo> Data { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }

    public class ProductAuthInfo
    {
        /// <summary>
        /// 商品编码
        /// </summary>
        public string ProductNumber { get; set; }
        /// <summary>
        /// 不允许采购
        /// </summary>
        public bool NoPurchase { get; set; }

        /// <summary>
        /// 商品更新标记
        /// </summary>
        public string ProductMarking { get; set; }


    }
}
