using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.User
{
    /// <summary>
    /// 用户：启用/禁用接口
    /// </summary>
    [Api("启用/禁用接口")]
    [Route("/msapi/userforbid/sync")]
    [Authenticate]
    [OperationLogFilter("sec_user")]
    public class UserForbidDto : BaseDTO
    {
        public List<UserForbid> Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }

    public class UserForbid
    {
        /// <summary>
        /// 外部Id
        /// </summary>
        public string id { get; set; }
        public string name { get; set; }
        public string status { get; set; }
        public string operatorid { get; set; }
        public string agentid { get; set; }
        public string agentno { get; set; }
        public string secagentid { get; set; }
        public string secagentno { get; set; }
    }
}
