using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.SelType
{
    /// <summary>
    /// 型号：总部下发接口
    /// </summary>
    [Api("总部下发接口")]
    [Route("/msapi/sel_type/push")]
    [Authenticate]
    //[OperationLogFilter("sel_type")]
    public class SelTypePushDTO : BaseDTO
    {
        public JArray Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }
}