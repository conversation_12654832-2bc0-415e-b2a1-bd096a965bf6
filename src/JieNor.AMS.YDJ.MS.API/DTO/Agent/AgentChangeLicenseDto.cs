using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.User
{
    /// <summary>
    /// 用户：同步接口
    /// </summary>
    [Api("认证同步接口更换营业执照")]
    [Route("/msapi/agent/changelicense")]
    [Authenticate]
    [OperationLogFilter("bas_agent")]
    public class AgentChangeLicenseDto : BaseDTO
    {
        public AgentData Data { get; set; }

        public class AgentData 
        { 
            //新经销商外部id
            public string NewAgentId { get; set; }
            public string NewAgentNumber { get; set; } 
            public string NewAgentName { get; set; }

            //旧经销商外部id
            public string OldAgentId { get; set; }
            public string OldAgentNumber { get; set; }
            public string OldAgentName { get; set; }
        }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }

     
}
