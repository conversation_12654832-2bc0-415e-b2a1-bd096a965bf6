using System.Collections.Generic;
using JieNor.AMS.YDJ.MS.API.Filter;
using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.PromotCombine
{
    /// <summary>
    /// 活动促销下发接口
    /// </summary>
    [Api("活动促销下发接口")]
    [Route("/msapi/promotion/sendpromotion")]
    [Authenticate]
    [OperationLogFilter("bas_musipromotion")]
    public class PromotCombineDTO : BaseDTO
    {
        public List<PromotCombineOrder> DATA { get; set; }
    }

    public class PromotCombineOrder
    {
        public string type { get; set; }
        public string code { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public string beginDate { get; set; }
        public string endDate { get; set; }
        public string priority { get; set; }
        public List<PromotCombineItem> item { get; set; }
        public List<PromotCombineRange> rangeList { get; set; }
    }

    public class PromotCombineItem
    {
        public string code { get; set; }
        public string comCode { get; set; }
        public string desc { get; set; }
        public string groupNum { get; set; }
        public string groupDesc { get; set; }
        public string qty { get; set; }
        public string prodCode { get; set; }
        public string prodName { get; set; }
        public string combinerate { get; set; }
        public string combinepriority { get; set; }
        public string beginDate { get; set; }
        public string endDate { get; set; }
        public string remark { get; set; }
        public string size { get; set; }
        public string color { get; set; }
    }

    public class PromotCombineRange
    {
        public string comCode { get; set; }
        /// <summary>
        /// 送达方
        /// </summary>
        public string shipToCode { get; set; }
        public string resultBrandNum { get; set; }
        public string hannel { get; set; }
        /// <summary>
        /// 中台，SAP可能不会传，不传就不管，相当于不删除
        /// </summary>
        public string deleteFlag { get; set; }
        public string resulCode { get; set; }
    }
}
