using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.SWJ.API.Response.SWJ
{
    /// <summary>
    /// access_token响应类
    /// </summary>
    public class AccessTokenResponse
    {
        /// <summary>
        /// 获取的access_token,请求成功时才会返回此字段
        /// </summary>
        public string access_token { get; set; }

        /// <summary>
        /// access_token剩余可用时间，单位:秒,请求成功时才会返回此字段
        /// </summary>
        public int expires_in { get; set; }

        /// <summary>
        /// 错误类型,请求失败时才会返回此字段
        /// </summary>
        public string error { get; set; }

        /// <summary>
        /// 错误描述,请求失败时才会返回此字段
        /// </summary>
        public string error_description { get; set; }
    }
}
