using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.SWJ.API.Response;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.SWJ.API
{
    /// <summary>
    /// 用户上下文扩展类
    /// </summary>
    public static class UserContextExtentions
    {
        /// <summary>
        /// 转换基础资料（从编码转换为id）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="baseDataFormIds">转换的基础资料表单</param>
        /// <param name="dataEntitys">数据实体</param>
        public static void ConvertBaseData(this UserContext userCtx, HtmlForm htmlForm, IEnumerable<string> baseDataFormIds,
            DynamicObject[] dataEntitys)
        {
            if (baseDataFormIds == null || baseDataFormIds.Count() == 0) return;
            if (dataEntitys == null || dataEntitys.Length == 0) return;

            //拉平数据包
            var dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(userCtx, dataEntitys, htmlForm);

            List<string> errorMsgs = new List<string>();
            HashSet<string> hit = new HashSet<string>(); // 已判断
            var allFileds = htmlForm.GetFieldList();

            foreach (var bdFormId in baseDataFormIds)
            {
                //找出所有的募思基础资料编码
                var dicFormFieldValues = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);

                var numbers = new List<string>();

                var fields = new List<HtmlBaseDataField>();
                foreach (var field in allFileds)
                {
                    if (!(field is HtmlBaseDataField)) continue;

                    var baseDataField = (HtmlBaseDataField)field;
                    if (!baseDataField.RefFormId.EqualsIgnoreCase(bdFormId)) continue;

                    fields.Add(baseDataField);
                }

                foreach (var field in fields)
                {
                    dicFormFieldValues.TryGetValue(bdFormId, out numbers);
                    if (numbers == null) numbers = new List<string>();
                    dicFormFieldValues[bdFormId] = numbers;

                    var setDataEntitys = dataEntitySet.FindByEntityKey(field.EntityKey);
                    foreach (var setDataEntity in setDataEntitys)
                    {
                        var number = Convert.ToString(setDataEntity.DataEntity[field.PropertyName]);
                        if (number.IsNullOrEmptyOrWhiteSpace()) continue;
                        numbers.Add(number);
                    }
                }

                if (numbers.Count == 0) continue;

                var bdForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, bdFormId);
                var numberField = bdForm.GetNumberField();

                var dynObjs = userCtx.LoadBizDataByNo(bdFormId, numberField.FieldName, numbers);
                var map = new Dictionary<string, string>();
                foreach (var dynObj in dynObjs)
                {
                    map[Convert.ToString(dynObj[numberField.PropertyName])] = Convert.ToString(dynObj["id"]);
                }

                foreach (var field in fields)
                {
                    var setDataEntitys = dataEntitySet.FindByEntityKey(field.EntityKey);
                    foreach (var setDataEntity in setDataEntitys)
                    {
                        var number = Convert.ToString(setDataEntity.DataEntity[field.PropertyName]);
                        if (number.IsNullOrEmptyOrWhiteSpace()) continue;

                        if (map.TryGetValue(number, out var id))
                        {
                            setDataEntity.DataEntity[field.PropertyName] = id;
                        }
                        else
                        {
                            string key = $"{bdFormId}:{number}";
                            if (!hit.Contains(key))
                            {
                                errorMsgs.Add($"【{bdForm.Caption}】编码【{number}】不存在！");
                                hit.Add(key);
                            }
                        }
                    }
                }
            }

            if (errorMsgs.Any())
            {
                throw new BusinessException(string.Join("\r\n", errorMsgs));
            }
        }

        /// <summary>
        /// 转换基础资料
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="bizForm">业务表单</param>
        /// <param name="bizData">业务对象</param> 
        /// <param name="errorMsgs"></param>
        /// <returns></returns>
        public static bool TryConvertBaseData(this UserContext userCtx, HtmlForm bizForm, List<DynamicObject> bizData, out List<string> errorMsgs)
        {
            return TryConvertBaseData(userCtx, bizForm, bizData, null, out errorMsgs);
        }

        /// <summary>
        /// 转换基础资料
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="bizForm">业务表单</param>
        /// <param name="bizData">业务对象</param> 
        /// <param name="errorMsgs"></param>
        /// <returns></returns>
        public static bool TryConvertBaseData(this UserContext userCtx, HtmlForm bizForm, List<DynamicObject> bizData, IEnumerable<string> baseDataFieldIds, out List<string> errorMsgs)
        {
            List<HtmlBaseDataField> baseDataFields = bizForm.GetFieldList()
                .Where(s => s.ElementType == HtmlElementType.HtmlField_BaseDataField).Select(s => (HtmlBaseDataField)s)
                .ToList();

            if (!baseDataFieldIds.IsNullOrEmpty())
            {
                baseDataFields = baseDataFields.Where(s => baseDataFieldIds.Contains(s.Id)).ToList();
            }

            errorMsgs = new List<string>();

            var dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(userCtx, bizData, bizForm);

            foreach (var field in baseDataFields)
            {
                var refForm = field.RefHtmlForm(userCtx);
                var numberField = refForm.GetNumberField();

                var dataEntities = dataEntitySet.FindByEntityKey(field.EntityKey).Select(s => s.DataEntity);

                var baseDataObjs = GetBaseDataList(userCtx, field, dataEntities);

                foreach (var dataEntity in dataEntities)
                {
                    var number = Convert.ToString(dataEntity[field.PropertyName]);
                    if (number.IsNullOrEmptyOrWhiteSpace()) continue;

                    var baseDataObj = baseDataObjs.FirstOrDefault(s => Convert.ToString(s[numberField.PropertyName]).EqualsIgnoreCase(number));
                    if (baseDataObj == null)
                    {
                        errorMsgs.Add($"{field.Caption}【{number}】不存在！");
                        continue;
                    }

                    field.DynamicProperty.SetValue(dataEntity, baseDataObj["id"]);
                    field.RefDynamicProperty.SetValue(dataEntity, baseDataObj);
                }
            }

            return !errorMsgs.Any();
        }

        /// <summary>
        /// 获取基础资料
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="field"></param>
        /// <param name="dataEntities"></param> 
        /// <returns></returns>
        public static List<DynamicObject> GetBaseDataList(this UserContext userCtx, HtmlBaseDataField field, IEnumerable<DynamicObject> dataEntities)
        {
            // 如果是多选基础资料
            if (field is HtmlMulBaseDataField)
            {
                throw new NotImplementedException("未实现多选基础资料转换");
            }

            var refForm = field.RefHtmlForm(userCtx);

            return userCtx.LoadBizDataByNo(field.RefFormId, refForm.GetNumberField().FieldName,
                dataEntities.Select(s => Convert.ToString(s[field.PropertyName]))
                    .Where(s => !s.IsNullOrEmptyOrWhiteSpace()));
        }

        /// <summary>
        /// 获取经销商(优先获取总部的经销商，其次取第一个匹配的经销商)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agentNo"></param>
        /// <returns></returns>
        public static DynamicObject GetAgent(this UserContext userCtx, string agentNo)
        {
            var agentMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "bas_agent");

            string sql = $"select {agentMeta.BillPKFldName} from {agentMeta.BillHeadTableName} with(nolock) where {agentMeta.NumberFldKey}='{agentNo}'";

            var reader = userCtx.ExecuteReader(sql, new List<SqlParam>());

            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, agentMeta.GetDynamicObjectType(userCtx));

            var agents = dm.SelectBy(reader).OfType<DynamicObject>().ToList();

            if (agents == null || !agents.Any()) return null;

            // 优先获取总部的经销商，其次取第一个匹配的经销商
            var agent = agents.FirstOrDefault(s => Convert.ToString(s["fmainorgid"]).EqualsIgnoreCase(userCtx.Company));
            if (agent == null)
            {
                agent = agents.FirstOrDefault();
            }

            return agent;
        }
    }
}
