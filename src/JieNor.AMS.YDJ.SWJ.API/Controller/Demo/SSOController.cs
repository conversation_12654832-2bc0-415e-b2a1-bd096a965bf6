using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using JieNor.AMS.YDJ.SWJ.API.APIs;
using JieNor.AMS.YDJ.SWJ.API.DTO.Demo;
using JieNor.AMS.YDJ.SWJ.API.Response;
using JieNor.Framework;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.SWJ.API.Controller.Demo
{
    /// <summary>
    /// 示例接口
    /// </summary>
    public class SSOController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(SSODTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<object>();

            var loger = this.Context.Container.GetService<ILogService>();
            SWJService sWJService = new SWJService();
            string type = dto?.type;
            if (type == "new")
            {
                Dictionary<string, string> keyValuePairs = dto?.custdata?.FromJson<Dictionary<string, string>>();
                resp.Data = sWJService.GetSSOUrl(this.Context, keyValuePairs);
                loger.WriteLog(this.Context, new LogEntry()
                {
                    BillIds = dto.id,
                    BillNos = dto.billno,
                    BillFormId = "ydj_order",
                    OpName = "3D设计",
                    OpCode = "threeddesign",
                    Content = "执行了【3D设计】操作！URL：" + System.Web.HttpUtility.UrlDecode(Convert.ToString(resp.Data)),
                    DebugData = "执行了【3D设计】操作！URL：" + System.Web.HttpUtility.UrlDecode(Convert.ToString(resp.Data)),
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                });
            }
            else if (type == "service")
            {
                Dictionary<string, string> keyValuePairs = dto?.custdata?.FromJson<Dictionary<string, string>>();
                resp.Data = sWJService.GetServiceSSOUrl(this.Context, keyValuePairs);
                loger.WriteLog(this.Context, new LogEntry()
                {
                    BillIds = dto.id,
                    BillNos = dto.billno,
                    BillFormId = "ydj_order",
                    OpName = "3D售后设计",
                    OpCode = "aftservicedesign",
                    Content = "执行了【3D售后设计】操作！URL：" + System.Web.HttpUtility.UrlDecode(Convert.ToString(resp.Data)),
                    DebugData = "执行了【3D售后设计】操作！URL：" + System.Web.HttpUtility.UrlDecode(Convert.ToString(resp.Data)),
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                });
            }
            resp.Success = true;
            resp.Code = 10000;

            return resp;
        }
    }
}