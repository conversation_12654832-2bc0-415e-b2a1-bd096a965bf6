using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 销售合同服务接口定义
    /// </summary>
    public interface IOrderService
    {
        /// <summary>
        /// 更新单据头.【补贴总金额】后，计算明细.【补贴金额】。（计算逻辑：依据每一行商品成交金额在所有明细成交金额汇总占比，进行按比例计算。计算结果需精确到个位数，若存在多行，则将除最后一行的前几行计算完毕后，将剩余金额全部归集在最后一行）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orders">销售意向单实体对象</param>
        /// <returns></returns>
        void CalculateEntrySubsidyamount(UserContext userCtx, IEnumerable<DynamicObject> orders);
        
        /// <summary>
        /// 计算【未收款】
        /// 注：需要自行调用保存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orders">销售意向单实体对象</param>
        /// <returns></returns>
        void CalculateUnreceived(UserContext userCtx, IEnumerable<DynamicObject> orders);

        /// <summary>
        /// 计算【结算状态】
        /// 注：需要自行调用保存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orders">销售合同实体对象</param>
        /// <returns></returns>
        void CalculateReceiptStatus(UserContext userCtx, IEnumerable<DynamicObject> orders);

        /// <summary>
        /// 获取佣金余额
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orderNo">合同编号</param>
        /// <returns></returns>
        decimal GetBrokerageBalance(UserContext userCtx, string orderNo);

        /// <summary>
        /// 获取佣金余额
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="order">合同</param>
        /// <returns></returns>
        decimal GetBrokerageBalance(UserContext userCtx, DynamicObject order);

        /// <summary>
        /// 获取【待结算金额】
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <returns></returns>
        decimal GetUnsettleAmount(UserContext userCtx, DynamicObject order);

        /// <summary>
        /// 计算结算信息（模拟前端计算）
        /// 注：需要自行调用保存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="order">销售合同</param>
        /// <returns></returns>
        void CalculateSettlement(UserContext userCtx, DynamicObject order, HtmlForm htmlFrom);

        /// <summary>
        /// 重算明细成交单价
        /// 注：解决成交单价*销售数量!=成交金额的问题，谨慎调用
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="order">销售合同</param>
        /// <returns></returns>
        void CalculateDealPrice(UserContext userCtx, DynamicObject order, HtmlForm htmlFrom);

        /// <summary>
        /// 检查【无统一零售价商品禁止下单】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productid"></param>
        /// <returns>true代表允许下单，false代表禁止下单</returns>
        bool CheckNoOrders(UserContext userCtx, string productid, decimal fsalprice);

        /// <summary>
        /// 检查【无统一零售价商品禁止下单】参数param传入key值为：fdataorigin、fissuit、fprice、fisgiveaway，funstdtypet的字典
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="param">传入key值为：fdataorigin、fissuit、fprice、fisgiveaway、funstdtypet的字典</param>
        /// <returns>true代表允许下单，false代表禁止下单</returns>
        bool CheckNoOrders(UserContext userCtx, Dictionary<string, object> param);

        /// <summary>
        /// 检查【无统一零售价商品禁止下单】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        bool CheckNoOrders(UserContext userCtx, NoOrderParmas param);

        /// <summary>
        ///  检查【无统一零售价商品禁止下单】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="param"></param>
        /// <param name="dic">传入key值为：Company、TopCompany的字典</param>
        /// <returns></returns>
        bool CheckNoOrders(UserContext userCtx, NoOrderParmas param, Dictionary<string, bool> dic);

        /// <summary>
        /// 过滤停购商品明细
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="entrys">商品明细</param>
        /// <param name="snapDatas">变更前商品明细快照信息</param>
        /// <returns></returns>
        List<DynamicObject> FilterEndPurchase(UserContext userCtx, List<DynamicObject> entrys, List<DynamicObject> snapDatas = null);

        /// <summary>
        /// 检查非标审批状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order"></param>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        bool CheckUnstdStatus(UserContext userCtx, DynamicObject order, out string errorMsg);

        /// <summary>
        /// 校验非标商品零售价是否可下单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order"></param>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        bool CheckNoOrders(UserContext userCtx, DynamicObject order, out string errorMsg);

        /// <summary>
        /// 返回对应单据类型id 如果有自建 优先取自建的
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="billtypename"></param>
        /// <returns></returns>
        string GetBillTypeData(UserContext userCtx, HtmlForm htmlForm, string billtypename);

        /// <summary>
        /// 合同变更中或已提交状态
        /// </summary>
        /// <returns></returns>
        bool ChangeOrSubmitStatus(UserContext userCtx, DynamicObject dyn);

        /// <summary>
        ///查部门是否关联门店，并查出客户是否绑定会员 ID，条件满足找到会员ID 为空返回true 前端提示， 否则返回false 不提示
        /// </summary>
        /// <returns></returns>
        bool GetAgentmember(UserContext userCtx, string jsonp);

        /// <summary>
        /// 获取明细行匹配的送达方
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order">销售合同</param>
        /// <param name="entrys">商品明细行</param>
        /// <returns></returns>
        Dictionary<string, List<DynamicObject>> GetEntryMatchDelivers(UserContext userCtx, DynamicObject order,
            List<DynamicObject> entrys);

        string GetParentCity(UserContext ctx, string cityid);

        bool SetGbAddressData(UserContext userCtx, DynamicObject order);

        /// <summary>
        /// 存在下游库存调拨单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orderEntrys"></param>
        /// <returns></returns>
        List<DynamicObject> ExistsInventoryTransferBill(UserContext userCtx, IEnumerable<DynamicObject> orderEntrys);

        ///// <summary>
        ///// 验证是否存在下游库存调拨单
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="orders"></param>
        ///// <param name="entryIds"></param>
        ///// <returns></returns>
        //IOperationResult CheckInventoryTransfer(UserContext userCtx, IEnumerable<DynamicObject> orders, IEnumerable<string> entryIds);

        /// <summary>
        /// 获取销售合同明细行关联的销售出库数（作废状态=否）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orderEntryIds"></param>
        /// <returns></returns>
        Dictionary<string, Tuple<decimal, decimal>> GetOutQty(UserContext userCtx, IEnumerable<string> orderEntryIds);

        /// <summary>
        /// 计算【收款比例】
        /// 注：需要自行调用保存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orders">销售合同实体对象</param>
        /// <returns></returns>
        void CalculatePaymentRatios(UserContext userCtx, IEnumerable<DynamicObject> orders);

        /// <summary>
        /// 重置折率
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order"></param>
        void ResetDis(UserContext userCtx, DynamicObject order);

        /// <summary>
        /// 审核时根据 辅助属性给【初始辅助属性】赋值
        /// </summary>
        /// <param name="dataEntities"></param>
        void writeAttrinfoFirst(UserContext userCtx, HtmlForm htmlform, IEnumerable<DynamicObject> dataEntities, int IsAudit);

        /// <summary>
        /// 填充非标信息（从数据库重新获取）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orders"></param>
        void FillUnstdInfo(UserContext userCtx, IEnumerable<DynamicObject> orders);

        /// <summary>
        /// 校验是否编辑了指定字段
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns>true:代表编辑了其他字段</returns>
        bool CheckEditSpecifiedField(UserContext userCtx, DynamicObject[] dataEntities);

        #region 直营赠品，不需要
        /*/// <summary>
        /// 计算直营分摊0.1金额
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities">销售合同数据包</param>
        void CalculateDirectGiftProductApportionAmount(UserContext userCtx,DynamicObject [] dataEntities);

        /// <summary>
        /// 复原计算直营分摊0.1金额
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        void RestoreCalculateDirectGiftProductApportionAmount(UserContext userCtx,DynamicObject [] dataEntities);*/
        #endregion

        /// <summary>
        /// 计算表头的佣金比例以及行上的佣金与佣金比例(直营)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orderDy"></param>
        void CalculateCommissionAmountAndRate(UserContext userCtx,DynamicObject orderDy);

    }

    /// <summary>
    /// 参数对象
    /// </summary>
    public class NoOrderParmas
    {
        /// <summary>
        /// 企业id
        /// </summary>
        public string fmainorgid { get; set; }
        /// <summary>
        /// 是否非标
        /// </summary>
        public bool funstdtype { get; set; }
        /// <summary>
        /// 是否套件
        /// </summary>
        public bool fissuit { get; set; }
        /// <summary>
        /// 销售价(零售价)
        /// </summary>
        public decimal fprice { get; set; }
        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool fisgiveaway { get; set; }
    }
}