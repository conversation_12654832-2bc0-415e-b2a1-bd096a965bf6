using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 3维家服务接口定义
    /// </summary>
    public interface ISWJService
    {
        /// <summary>
        /// 获取单点登录URL
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="extraParams">扩展参数</param>
        /// <returns></returns>
        string GetSSOUrl(UserContext userCtx, Dictionary<string, string> extraParams);
    }
}
