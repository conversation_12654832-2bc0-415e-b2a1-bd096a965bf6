using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface.Finance
{
    /// <summary>
    /// 财务基础服务
    /// 作者：zpf
    /// 日期：2022-07-27
    /// </summary>
    public interface IFinanceBaseService
    {
        /// <summary>
        /// 获取最近关账日期
        /// 作者：zpf
        /// 日期：2022-07-27
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dtRefDate"></param>
        /// <returns>返回参考日期前的最近一次关账日期</returns>
        DateTime? GetLatestFinanceCloseDate(UserContext userCtx, DateTime? dtRefDate = null);

        /// <summary>
        /// 财务关账服务
        /// 作者：zpf
        /// 日期：2022-07-27
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="dtCloseDate">关账日期</param>
        /// <param name="option"操作项></param>
        /// <returns></returns>
        IOperationResult Closing(UserContext userCtx, DateTime dtCloseDate, OperateOption option);

        /// <summary>
        /// 财务反关账服务
        /// 作者：zpf
        /// 日期：2022-07-27
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="option">操作项</param>
        /// <returns></returns> 
        IOperationResult AntiClosing(UserContext userCtx, OperateOption option);
    }
}
