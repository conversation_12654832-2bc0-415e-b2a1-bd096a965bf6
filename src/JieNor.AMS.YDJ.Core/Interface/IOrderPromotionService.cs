using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 合同促销活动服务接口
    /// </summary>
    public interface IOrderPromotionService
    {
        /// <summary>
        /// 使用组合套餐
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order"></param>
        /// <param name="comboPromotions"></param>
        void UseComboPromotion(UserContext userCtx, DynamicObject order, List<DynamicObject> comboPromotions);

        /// <summary>
        /// 使用商品促销
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order"></param>
        /// <param name="productPromotion"></param>
        /// <param name="gifts">赠品
        /// [{
        ///     "fmaterialid",
        ///     "fattrinfo",
        ///     "funitid",
        ///     "fqty"
        /// }]
        /// </param>
        void UseProductPromotion(UserContext userCtx, DynamicObject order, DynamicObject productPromotion, JArray gifts);

        /// <summary>
        /// 获取匹配的商品促销
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order"></param>
        /// <param name="newDialogObj"></param>
        /// <returns></returns>
        DynamicObject GetSelectProductPromotionDialog(UserContext userCtx, DynamicObject order,
            DynamicObject newDialogObj, bool throwError = false);
    }
}
