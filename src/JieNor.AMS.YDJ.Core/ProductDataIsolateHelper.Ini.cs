using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using JieNor.Framework.Interface.Log;
using ServiceStack;
using System.Diagnostics;
using System.Runtime.Serialization;
using System.Text;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework.Interface.Cache;
using System.Data;

namespace JieNor.AMS.YDJ.Core
{


    /// <summary>
    /// 【商品】数据权限隔离辅助类：初始化
    /// </summary>  
    public partial  class ProductDataIsolateHelper  
    {

        static bool _updateSchema = false;

         
        static DateTime _nextUpdate = DateTime.Now.AddYears(1);


        /// <summary>
        /// 商品授权相关数据变更后，等待多长时间（分钟）更新授权缓存数据
        /// （这里不定义为static变量，以便修改配置文件的配置时，立即生效）
        /// </summary>
        public static  int ClearCacheWaitMinutes
        {
            get
            {
                return "".GetAppConfig<int>(YDJHostConfigKeys.MS.ProductDataIsolate_Tmp_PrdDataCacheMinutes, 60);
            }
        }

        public static void Init(UserContext userCtx)
        {
            CacheDatas(userCtx);
               
            var dueTime = new TimeSpan(0, 0, 0);//延迟时间，用于指定延迟多少秒开始执行定时任务
            var period = new TimeSpan(0, 0, 10); //定时周期，用于指定每隔多长时间执行一次，10秒执行一次 
            doWork = new System.Threading.Timer(DelayClearCache, null, dueTime, period);

            _nextUpdate = DateTime.Now.AddMinutes(ClearCacheWaitMinutes);
        }




        private static void UpdateSchema(UserContext ctx)
        {
            if (_updateSchema)
            {
                return;
            }

            Task task = new Task(() =>
            {
                _updateSchema = true;

                var dm = ctx.Container.GetService<IDataManager>();
                dm.Option = ctx.Container.GetService<OperateOption>();
                dm.Option.SetVariableValue("forceRecreate", true);
                dm.Option.SetVariableValue("AutoUpdateScheme", true);

                var svc = ctx.Container.GetService<IMetaModelService>();
                var meta = svc.LoadFormModel(ctx, "bas_organization");
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));

                meta = svc.LoadFormModel(ctx, "sec_user");
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));

                meta = svc.LoadFormModel(ctx, "bas_agent");
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));

                meta = svc.LoadFormModel(ctx, "ydj_orgresultbrand");
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));

                meta = svc.LoadFormModel(ctx, "ydj_product");
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));

                meta = svc.LoadFormModel(ctx, "ydj_series");
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));

                meta = svc.LoadFormModel(ctx, "ydj_supplier");
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));

                meta = svc.LoadFormModel(ctx, "bas_deliver");
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));

                WriteDebugLog("UpdateSchema");
            });
            ThreadWorker.QuequeTask(task, result => { });
        }




    }

}