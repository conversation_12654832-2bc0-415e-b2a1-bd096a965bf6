using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    /// <summary>
    /// 基于db的唯一编码规则
    /// </summary>
    public class UniqueCodeRuleBaseDb
    {
        /// <summary>
        /// 前缀
        /// </summary>
        public string Prefix { get; set; }
        /// <summary>
        /// 字段标识
        /// </summary>
        public string FieldKey { get; set; }
        /// <summary>
        /// 字段值格式
        /// </summary>
        public string FieldValueFormat { get; set; }

        /// <summary>
        /// 流水号长度
        /// </summary>
        public int SeqLength { get; set; }

        /// <summary>
        /// 规则索引
        /// </summary>
        public Dictionary<string, UniqueCodeRuleIndex> CodeRuleIndex { get; set; } = new Dictionary<string, UniqueCodeRuleIndex>(StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 唯一编码规则索引
    /// </summary>
    public class UniqueCodeRuleIndex
    {
        /// <summary>
        /// 最后编号
        /// </summary>
        public long LastSeq { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }
    }
}
