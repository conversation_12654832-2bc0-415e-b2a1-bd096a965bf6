using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    /// <summary>
    /// 库存更新明细行对象
    /// </summary>
    public class StockUpdateObject : IDisposable
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tableId"></param>
        public StockUpdateObject(UserContext userCtx, string tableId)
        {
            this.BuildInnerStockEntryDt(userCtx, tableId);            
        }

        public DynamicObjectType TempInvDataType { get; private set; }
        /// <summary>
        /// 库存更新配置
        /// </summary>
        public StockUpdateSetting Setting { get; set; }

        /// <summary>
        /// 库存单据上需要更新库存的明细分录行（需要转换成与即时库存结构一致的数据包）
        /// </summary>
        public DynamicObjectCollection InvBillObjs { get; private set; }

        /// <summary>
        /// 库存单据标识
        /// </summary>
        public HtmlForm HtmlForm { get; set; }

        public void Dispose()
        {
            this.HtmlForm = null;
            this.InvBillObjs?.Clear();
            this.InvBillObjs = null;
            this.Setting = null;
            this.TempInvDataType = null;
        }

        private void BuildInnerStockEntryDt(UserContext userCtx, string seqId)
        {
            this.TempInvDataType = HtmlParser.CloneFormModel(userCtx, "stk_inventorylist", entity =>
            {
                switch (entity.Id.ToLower())
                {
                    case "fbillhead":
                        return $"tmpi_{seqId}";
                    case "fentity":
                        return $"tmpie_{seqId}";
                    default:
                        return $"tmpiec_{seqId}";
                }
            });

            if (!this.TempInvDataType.Properties.ContainsKey("fbillinterid"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fbillinterid", typeof(string), "", false, new SimplePropertyAttribute()
                {
                    Alias = "fbillinterid",
                });
            }

            if (!this.TempInvDataType.Properties.ContainsKey("fbillentryid"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fbillentryid", typeof(string), "", false, new SimplePropertyAttribute()
                {
                    Alias = "fbillentryid",
                });
            }
            //表单名称
            if (!this.TempInvDataType.Properties.ContainsKey("fbillformid"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fbillformid", typeof(string), "", false, new SimplePropertyAttribute()
                {
                    Alias = "fbillformid",
                });
            }
            //单据编号
            if (!this.TempInvDataType.Properties.ContainsKey("fbillno"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fbillno", typeof(string), "", false, new SimplePropertyAttribute()
                {
                    Alias = "fbillno",
                });
            }
            //分录行号
            if (!this.TempInvDataType.Properties.ContainsKey("fbillentryseq"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fbillentryseq", typeof(int), 0, false, new SimplePropertyAttribute()
                {
                    Alias = "fbillentryseq",
                });
            }
            //物料名称
            if (!this.TempInvDataType.Properties.ContainsKey("fmaterialname"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fmaterialname", typeof(string), "", false, new SimplePropertyAttribute()
                {
                    Alias = "fmaterialname",
                });
            }

            if (!this.TempInvDataType.Properties.ContainsKey("finventoryid"))
            {
                this.TempInvDataType.RegisterSimpleProperty("finventoryid", typeof(string), "", false, new SimplePropertyAttribute()
                {
                    Alias = "finventoryid",
                });
            }

            if (!this.TempInvDataType.Properties.ContainsKey("frealinventoryid"))
            {
                this.TempInvDataType.RegisterSimpleProperty("frealinventoryid", typeof(string), "", false, new SimplePropertyAttribute()
                {
                    Alias = "frealinventoryid",
                });
            }

            //库存更新服务标识
            if (!this.TempInvDataType.Properties.ContainsKey("fupdserviceid"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fupdserviceid", typeof(string), "", false, new SimplePropertyAttribute()
                {
                    Alias = "fupdserviceid",
                });
            }

            //更新标志
            if (!this.TempInvDataType.Properties.ContainsKey("fupdateflag"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fupdateflag", typeof(int), 0, false, new SimplePropertyAttribute()
                {
                    Alias = "fupdateflag",
                });
            }

            if (!this.TempInvDataType.Properties.ContainsKey("fissnapshot"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fissnapshot", typeof(bool), false, false, new SimplePropertyAttribute()
                {
                    Alias = "fissnapshot",
                });
            }

            if (!this.TempInvDataType.Properties.ContainsKey("fsrcqty"))
            {
                this.TempInvDataType.RegisterSimpleProperty("fsrcqty", typeof(decimal), false, false, new SimplePropertyAttribute()
                {
                    Alias = "fsrcqty",
                });
            }

            this.InvBillObjs = new DynamicObjectCollection(this.TempInvDataType);
        }

    }

}
