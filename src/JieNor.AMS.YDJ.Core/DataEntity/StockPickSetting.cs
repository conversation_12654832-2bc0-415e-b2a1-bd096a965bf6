using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    /// <summary>
    /// 库存拣货设置参数
    /// </summary>
    [Serializable]
    public class StockPickSetting
    {
        /// <summary>
        /// 库存维度字段配置
        /// </summary>
        public Dictionary<string, string> InvFlexFieldSetting { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 库存拣货时主单据体
        /// </summary>
        public string ActiveEntityKey { get; set; }

        /// <summary>
        /// 库存拣货服务的前置条件
        /// </summary>
        public string PreCondition { get; set; }

        /// <summary>
        /// 实发数量字段标识
        /// </summary>
        public string QtyFieldKey { get; set; }

        /// <summary>
        /// 应发数量字段标识
        /// </summary>
        public string PlanQtyFieldKey { get; set; }

        /// <summary>
        /// 计价单位数量字段标识
        /// </summary>
        public string StockQtyFieldKey { get; set; }

        /// <summary>
        /// 单价字段标识
        /// </summary>
        public string PriceFieldKey { get; set; }

        /// <summary>
        /// 金额字段标识
        /// </summary>
        public string AmountFieldKey { get; set; }
    }
}