<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>JieNor.AMS.YDJ.Stock.AppService</RootNamespace>
    <AssemblyName>JieNor.AMS.YDJ.Stock.AppService</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\lib\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>CS1591;CS1573;CS1587</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="JieNor.AMS.YDJ.Core">
      <HintPath>..\..\lib\Debug\JieNor.AMS.YDJ.Core.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.AMS.YDJ.DataTransferObject, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.AMS.YDJ.DataTransferObject.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.BizExpression, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL" />
    <Reference Include="JieNor.Framework.DataEntity, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.DataEntity.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.DataTransferObject, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.DataTransferObject.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.IM, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL" />
    <Reference Include="JieNor.Framework.Interface, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.Interface.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.MetaCore, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.MetaCore.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.SuperOrm, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.SuperOrm.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BarcodeMgrService.cs" />
    <Compile Include="Bcm\AbstractPushInOutStockForBCMServicePlugIn.cs" />
    <Compile Include="Bcm\AbstractUpdateBarcodeStatusServicePlugIn.cs" />
    <Compile Include="Bcm\DeliveryNotice\Audit.cs" />
    <Compile Include="Bcm\DeliveryNotice\Push2InventoryTransfer.cs" />
    <Compile Include="Bcm\DeliveryNotice\Push2OtherOutStock.cs" />
    <Compile Include="Bcm\DeliveryNotice\Push2ReturnGood.cs" />
    <Compile Include="Bcm\DeliveryNotice\Push2SoStockOut.cs" />
    <Compile Include="Bcm\DeliveryNotice\Unaudit.cs" />
    <Compile Include="Bcm\DeliveryScanTask\CommitOutStock.cs" />
    <Compile Include="Bcm\InOutStockMenuFilter.cs" />
    <Compile Include="Bcm\InStock\Audit.cs" />
    <Compile Include="Bcm\InStock\Unaudit.cs" />
    <Compile Include="Bcm\OutStock\Audit.cs" />
    <Compile Include="Bcm\OutStock\Unaudit.cs" />
    <Compile Include="Bcm\ReceiptNotice\Push2OtherInstock.cs" />
    <Compile Include="Bcm\ReceiptNotice\Push2PoStockIn.cs" />
    <Compile Include="Bcm\ReceiptNotice\Push2ReturnGood.cs" />
    <Compile Include="Bcm\ScanResult\Delete.cs" />
    <Compile Include="Bcm\ScanResult\Save.cs" />
    <Compile Include="Common\OrderCommon.cs" />
    <Compile Include="Enums\TransferOrderStatus.cs" />
    <Compile Include="Inv\InventoryBalance\Correct.cs" />
    <Compile Include="Inv\InventoryService.ReserveStock.cs" />
    <Compile Include="Inv\InventoryService.FIFOStock.cs" />
    <Compile Include="Inv\InvCompleteInit\GetMiniStockBillDate.cs" />
    <Compile Include="Inv\InventoryTransfer\AbstractQueryDyn.cs" />
    <Compile Include="Inv\InventoryTransfer\AllotOut.cs" />
    <Compile Include="Inv\InventoryTransfer\Audit.cs" />
    <Compile Include="Inv\InventoryTransfer\CanceIinTransfer.cs" />
    <Compile Include="Inv\InventoryTransfer\CheckZYNeedSync.cs" />
    <Compile Include="Inv\InventoryTransfer\CreateIntransfer.cs" />
    <Compile Include="Inv\InventoryTransfer\ZYCheckValidation.cs" />
    <Compile Include="Inv\InventoryTransfer\Cancel.cs" />
    <Compile Include="Inv\InventoryTransfer\CancelAllotOut.cs" />
    <Compile Include="Inv\InventoryTransfer\CheckInAgentBill.cs" />
    <Compile Include="Inv\InventoryTransfer\CreateInvTransfer.cs" />
    <Compile Include="Inv\InventoryTransfer\Delete.cs" />
    <Compile Include="Inv\InventoryTransfer\CreateStockInTask.cs" />
    <Compile Include="Inv\InventoryTransfer\CreateStockOutTask.cs" />
    <Compile Include="Inv\InventoryTransfer\FuzzyQuery.cs" />
    <Compile Include="Inv\InventoryTransfer\GetInvTransferType.cs" />
    <Compile Include="Inv\InventoryTransfer\GetStoreHouseLockSet.cs" />
    <Compile Include="Inv\InventoryTransfer\LinkFormSearch.cs" />
    <Compile Include="Inv\InventoryTransfer\Pull.cs" />
    <Compile Include="Inv\InventoryTransfer\QueryInventory.cs" />
    <Compile Include="Inv\InventoryTransfer\UnAudit.cs" />
    <Compile Include="Inv\InventoryTransfer\UnCancel.cs" />
    <Compile Include="Inv\InventoryTransfer\UnSubmit.cs" />
    <Compile Include="Inv\InventoryTransfer\UpdateReserveBill.cs" />
    <Compile Include="Inv\InventoryUtil.cs" />
    <Compile Include="OpService\BizInventoryUsableCheck.cs" />
    <Compile Include="OpService\BizReserveDeleteService.cs" />
    <Compile Include="OpService\BizInventoryCheck.cs" />
    <Compile Include="OpService\ReserveBorrow.cs" />
    <Compile Include="OpValidation\InventoryTransfer\SaveValidation.cs" />
    <Compile Include="OpValidation\InventoryTransfer\OrderQtyValidation.cs" />
    <Compile Include="OpValidation\SoStockReturn\UnAuditValidation.cs" />
    <Compile Include="Plugin\Auth\StockSynthesizeDataRule.cs" />
    <Compile Include="Reserve\ReserveBill\DefaultFilterSchema.cs" />
    <Compile Include="Reserve\ReserveBill\CheckFiledVisble.cs" />
    <Compile Include="Reserve\ReserveBill\LinkFormSearch.cs" />
    <Compile Include="Reserve\ReserveBill\Modify.cs" />
    <Compile Include="Reserve\ReserveBill\ShowReserveByInvFlex.cs" />
    <Compile Include="Reserve\ReserveBill\Archiving.cs" />
    <Compile Include="Reserve\ReserveBorrowDialog\AbstractQueryDyn.cs" />
    <Compile Include="Reserve\ReserveBorrowDialog\FuzzyQuery.cs" />
    <Compile Include="Reserve\ReserveBorrowOrder\Modify.cs" />
    <Compile Include="Reserve\ReserveBorrowDialog\QuerySelector.cs" />
    <Compile Include="Reserve\ReserveBorrowDialog\New.cs" />
    <Compile Include="Reserve\ReserveBorrowOrder\QueryData.cs" />
    <Compile Include="Reserve\ReserveReleaseDialog\New.cs" />
    <Compile Include="Reserve\ReserveService.POStockInReserve.cs" />
    <Compile Include="Reserve\ReserveService.Borrow.cs" />
    <Compile Include="Reserve\UpdateInvCheckSchema.cs" />
    <Compile Include="Reserve\ReserveBill\Delete.cs" />
    <Compile Include="Reserve\ReserveService.CheckInv.cs" />
    <Compile Include="Reserve\ReserveService.cs" />
    <Compile Include="Reserve\ReserveService.BizCheckInv.cs" />
    <Compile Include="Reserve\ReserveService.Delete.cs" />
    <Compile Include="Reserve\ReserveService.ReleaseExpirer.cs" />
    <Compile Include="Reserve\ReserveService.TransferBill.cs" />
    <Compile Include="Reserve\ReserveService.Transfer.cs" />
    <Compile Include="Reserve\ReserveService.Release.cs" />
    <Compile Include="Reserve\ReserveUtil.cs" />
    <Compile Include="Inv\InitStockBill\New.cs" />
    <Compile Include="Inv\InitStockBill\Unaudit.cs" />
    <Compile Include="Inv\InvCloseAccount\CloseAccount.cs" />
    <Compile Include="Inv\InvCloseAccount\New.cs" />
    <Compile Include="Inv\InvCloseAccount\Refresh.cs" />
    <Compile Include="Inv\InvCloseAccount\UncloseAccount.cs" />
    <Compile Include="Inv\InvCompleteInit\InitInv.cs" />
    <Compile Include="Inv\InvCompleteInit\New.cs" />
    <Compile Include="Inv\InvCompleteInit\Refresh.cs" />
    <Compile Include="Inv\InvCompleteInit\ReinitInv.cs" />
    <Compile Include="Inv\InventoryBalance\QueryData.cs" />
    <Compile Include="Inv\InventoryList\Correct.cs" />
    <Compile Include="Inv\InventoryList\Push2InventoryTransferConvertPlugIn.cs" />
    <Compile Include="Inv\InventoryList\Push2InventoryTransferReqConvertPlugIn.cs" />
    <Compile Include="Inv\InventoryList\PushInvTransfer.cs" />
    <Compile Include="Inv\InventoryTransfer\Save.cs" />
    <Compile Include="Reserve\ReserveBill\Copy.cs" />
    <Compile Include="Reserve\ReserveBill\ManualRelease.cs" />
    <Compile Include="Reserve\ReserveBill\ReleaseExpirer.cs" />
    <Compile Include="Reserve\ReserveBill\Save.cs" />
    <Compile Include="Reserve\ReserveDialog\LoadStockQty.cs" />
    <Compile Include="Reserve\ReserveDialog\New.cs" />
    <Compile Include="OpService\BizReserveReleaseService.cs" />
    <Compile Include="OpService\BizReserveUpdateService.cs" />
    <Compile Include="OpService\BizStockPickService.cs" />
    <Compile Include="OpService\BizStockUpdateService.cs" />
    <Compile Include="OpService\BizStockUpdateSetting.cs" />
    <Compile Include="OpService\QueryInventory.cs" />
    <Compile Include="OpService\ReserveInventory.cs" />
    <Compile Include="OpValidation\StockBillSaveValidation.cs" />
    <Compile Include="Bcm\DeliveryNotice\CreateBarcodeLink.cs" />
    <Compile Include="Bcm\DeliveryNotice\DeleteBarcodeLink.cs" />
    <Compile Include="OpValidation\StockLocationMustValidation.cs" />
    <Compile Include="Op\QueryBarcode.cs" />
    <Compile Include="Op\SaveScanResult.cs" />
    <Compile Include="Inv\InventoryService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Reserve\ReserveService.AddOrUpdate.cs" />
    <Compile Include="Rpt\AbstractInventoryReportPlugIn.cs" />
    <Compile Include="Rpt\CustomerDelivery\QueryListReportData.cs" />
    <Compile Include="Rpt\PriceSynthesize\UpdatePriceData.cs" />
    <Compile Include="Rpt\ProductMonthlySales\UpdatMonthlySaleData.cs" />
    <Compile Include="Rpt\StockInDetail\QueryListReport.cs" />
    <Compile Include="Rpt\StockInDetail\QueryListReportData.cs" />
    <Compile Include="Rpt\StockOverReserve\QueryListReportData.cs" />
    <Compile Include="Rpt\StockDetail\QueryListReport.cs" />
    <Compile Include="Rpt\StockDetail\QueryListReportData.cs" />
    <Compile Include="Rpt\StockSummary\QueryListReportData.cs" />
    <Compile Include="Reserve\ReserveBill\ShowReserve.cs" />
    <Compile Include="Rpt\StockSynthesize\GetIndbQty.cs" />
    <Compile Include="Rpt\StockSynthesize\GetOutingQty.cs" />
    <Compile Include="Rpt\StockSynthesize\UpdateExtendData.cs" />
    <Compile Include="Rpt\StockSynthesize\QueryData.cs" />
    <Compile Include="Rpt\StockSynthesize\UpdateVolume.cs" />
    <Compile Include="Rpt\StockWaterLevel\UpdateStockData.cs" />
    <Compile Include="Sal\PoStockIn\Save.cs" />
    <Compile Include="Sal\SoStockOut\Save.cs" />
    <Compile Include="Sal\SoStockReturn\Audit.cs" />
    <Compile Include="Sal\SoStockReturn\CreateRecTask.cs" />
    <Compile Include="Sal\SoStockReturn\Delete.cs" />
    <Compile Include="Sal\SoStockReturn\Pack.cs" />
    <Compile Include="Sal\SoStockReturn\Save.cs" />
    <Compile Include="Sal\SoStockReturn\stk_inventoryverify2bcm_packorderinit.cs" />
    <Compile Include="Sal\SoStockReturn\stk_inventoryverify2bcm_packorder.cs" />
    <Compile Include="Sal\SoStockReturn\stk_sostockreturn2bcm_packorder.cs" />
    <Compile Include="Sal\SoStockReturn\UnAudit.cs" />
    <Compile Include="SchedulerTask\AutoCalculateProRelevantTime.cs" />
    <Compile Include="SchedulerTask\AutoUpdateSettleTime.cs" />
    <Compile Include="SchedulerTask\CorrectTask.cs" />
    <Compile Include="SchedulerTask\LoginDeadlineWarnTask.cs" />
    <Compile Include="SchedulerTask\ReserveReleaseTask.cs" />
    <Compile Include="CostCalulateService.cs" />
    <Compile Include="SoStockReturnService.cs" />
    <Compile Include="StockBaseService.cs" />
    <Compile Include="StockPickService.cs" />
    <Compile Include="StockUpdateService.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="OpServicePlugIn\" />
    <Folder Include="Pur\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="工程结构说明.txt" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\JieNor.AMS.YDJ.MP.API\JieNor.AMS.YDJ.MP.API.csproj">
      <Project>{18E7268B-D122-4398-A796-270764724832}</Project>
      <Name>JieNor.AMS.YDJ.MP.API</Name>
    </ProjectReference>
    <ProjectReference Include="..\JieNor.AMS.YDJ.Store.AppService\JieNor.AMS.YDJ.Store.AppService.csproj">
      <Project>{3640F504-8B8C-4836-A6D1-6C10861DFFE6}</Project>
      <Name>JieNor.AMS.YDJ.Store.AppService</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <!-- <PropertyGroup>
    <PostBuildEvent>xcopy /e /r /y "$(TargetDir)$(TargetName).dll" "$(SolutionDir)JieNor.AMS.YDJ.Store.Web\bin\"
xcopy /e /r /y "$(TargetDir)$(TargetName).pdb" "$(SolutionDir)JieNor.AMS.YDJ.Store.Web\bin\"</PostBuildEvent>
  </PropertyGroup> -->
</Project>