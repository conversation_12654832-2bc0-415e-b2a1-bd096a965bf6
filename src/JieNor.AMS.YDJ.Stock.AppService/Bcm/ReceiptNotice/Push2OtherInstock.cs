using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Bcm.ReceiptNotice
{
    //[InjectService]
    [FormId("stk_otherstockinreq")]
    [OperationNo("push2otherinstock")]
    public class Push2OtherInstock : AbstractPushInOutStockForBCMServicePlugIn
    {
        protected override string GetConvertRuleId()
        {
            throw new NotImplementedException();
        }
    }
}
