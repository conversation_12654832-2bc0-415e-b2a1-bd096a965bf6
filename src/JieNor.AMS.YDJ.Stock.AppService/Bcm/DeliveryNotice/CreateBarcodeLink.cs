using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity.Barcode;
using JieNor.AMS.YDJ.Core.Interface.BarcodeMgr;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Op
{
    /// <summary>
    /// 备码处理逻辑
    /// </summary>
    [InjectService]
    [FormId("(sal_deliverynotice|pur_returnnotice|stk_otherstockoutreq|stk_inventorytransferreq)")]
    [OperationNo("createbarcodelink")]
    public class CreateBarcodeLink : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 取消事务处理
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            e.OpCtlParam.DisableTransaction = true;            
        }
        /// <summary>
        /// 执行操作服务逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null
                || e.DataEntitys.Length==0)
            {
                throw new BusinessException("请选择有效单据后再操作！");
            }
            //根据当前传回的条码信息进行反向解析得到按商品维度配套的数据
            
            var dctOperationParam = this.OperationContext.HtmlOperation?.Parameter?.FromJson<Dictionary<string, string>>() 
                ?? new Dictionary<string, string>()
                {
                    {"qtyFieldKey","fqty" },
                    {"stockQtyFieldKey","fstockqty" },
                    {"demandQtyFieldKey","fplanqty" },
                    {"priceFieldKey","fprice" },
                    {"amountFieldKey","famount" },
                    {"activeEntityKey","fentity" },
                };

            var activeEntityKey = dctOperationParam.GetValue("activeEntityKey", "fentity");
            var activeEntity = this.OperationContext.HtmlForm.GetEntryEntity(activeEntityKey);
            if (activeEntity == null)
            {
                throw new BusinessException("请为本操作指定商品明细单据体标识！");
            }

            var lstPackageItems = this.GetQueryOrSimpleParam<string>("scanData")?
                .FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();

            if(lstPackageItems==null
                || lstPackageItems.Any() == false)
            {
                throw new BusinessException("请至少选择一个条码后再进行此操作！");
            }

            MatchBarcodeOption matchOption = new MatchBarcodeOption()
            {
                ActiveEntityKey = activeEntity.Id,
                AdditionalBarCode = lstPackageItems,
                DemandQtyFieldKey = dctOperationParam.GetValue("demandQtyFieldKey", "fplanqty"),
                PriceFieldKey = dctOperationParam.GetValue("priceFieldKey", "fprice"),
                AmountFieldKey = dctOperationParam.GetValue("amountFieldKey", "famount"),
                QtyFieldKey = dctOperationParam.GetValue("qtyFieldKey", "fqty"),
                StockQtyFieldKey = dctOperationParam.GetValue("stockQtyFieldKey", "fstockqty"),
                BackDateCheckStockId = this.HtmlForm.Id.EqualsIgnoreCase("stk_inventorytransferreq"),
            };

            var qtyField = this.OperationContext.HtmlForm.GetField(matchOption.QtyFieldKey);
            var barcodeService = this.Container.GetService<IBarcodeMgrService>();
            var consumedDetailResult = barcodeService.MatchInvBillRowQty(this.Context, this.OperationContext.HtmlForm, e.DataEntitys, matchOption);
                        
            var allBillEntryObjs = consumedDetailResult.Item2
                    .SelectMany(o => o.Value)
                    .Select(o => o.Key)
                    .Distinct()
                    .ToArray();
            //返回条码解析结果
            this.OperationContext.Result.SrvData = new
            {
                packedData = consumedDetailResult.Item1.Select(o => new
                {
                    barCode = o.Value.Id,
                    resolveStatus = ((int)o.Value.ResolvedCode).ToString(),
                    failMsg = o.Value.ErrorMessage
                })
                .ToArray(),
                packData = allBillEntryObjs
                    .Select(o => new
                    {
                        id=(o.DataEntity.Parent as DynamicObject)["id"],
                        entryId=o["id"],
                        billFormId=this.OperationContext.HtmlForm.Id,
                        qty=o[qtyField.PropertyName]
                    })
                    .ToArray(),
            };

            //模拟试算不用返回刷新操作
            var mockCalc = this.GetQueryOrSimpleParam<bool>("mockCalc",false);
            if (!mockCalc)
            {
                //保存条码匹配明细信息
                var autoInstockObjs = this.CommitConsumeResult(this.Context, this.OperationContext.HtmlForm, consumedDetailResult.Item2, matchOption);

                this.OperationContext.AddRefreshPageAction();

                this.Result.IsSuccess = true;

                if (autoInstockObjs.Any())
                {
                    this.AutoOutStock(this.Context, this.HtmlForm, autoInstockObjs);
                }
            }
            else
            { 
            }
        }

        /// <summary>
        /// 提交表单的条码消耗明细信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="allConsumeBarcodeViews"></param>
        /// <param name="matchOption"></param>
        /// <remarks>
        /// 提交结果数据的处理逻辑：
        /// 1、找出与每单关联的所有扫描记录
        /// 2、结合当前匹配的结果与上述扫描记录分析得到，哪些已存在的扫描记录要删除，哪些新的条码要生成新的扫描记录？
        /// 3、要删除的扫描记录，需要将关联条码的状态进行更新回退
        /// 4、新增加的扫描记录，需要将关联条码的状态进行更新
        /// </remarks>
        private IEnumerable<DynamicObject> CommitConsumeResult(UserContext userCtx,
            HtmlForm htmlForm,
            Dictionary<DynamicObject, Dictionary<ExtendedDataEntity,List<Tuple<ResolvedBarcodeView, Dictionary<string,object>,decimal>>>> allConsumeBarcodeViews,
            MatchBarcodeOption matchOption)
        {
            using (var tran = userCtx.CreateTransaction())
            {
                List<DynamicObject> lstAutoProcObjs = new List<DynamicObject>();

                //是否来自于用户点击确认，若是来自于用户确认，则需要将条码状态修改在已核验
                var userConfirm = this.GetQueryOrSimpleParam<bool>("userConfirm", false);

                DynamicProperty dpAutoContinue = null;
                switch (htmlForm.Id.ToLower())
                {
                    case "sal_deliverynotice":
                        dpAutoContinue = htmlForm.GetField("fautobcoutstock")?.DynamicProperty;
                        break;
                    case "pur_returnnotice":
                        dpAutoContinue = htmlForm.GetField("fautobcoutstock")?.DynamicProperty;
                        break;
                    case "stk_otherstockoutreq":
                        dpAutoContinue = htmlForm.GetField("fautobcoutstock")?.DynamicProperty;
                        break;
                    case "stk_inventorytransferreq":
                        dpAutoContinue = htmlForm.GetField("fautobctransfer")?.DynamicProperty;
                        break;
                }
                var tempTableName = "";
                var barcodeResultMeta = this.MetaModelService.LoadFormModel(userCtx, "bcm_scanresult");
                var dm = this.GetDataManager();
                dm.InitDbContext(userCtx, barcodeResultMeta.GetDynamicObjectType(userCtx));
                var strPkIdSql = $@"
select fid
from {barcodeResultMeta.BillHeadTableName} u1
";
                var allBillDataObjs = allConsumeBarcodeViews.Select(o => o.Key).Distinct().ToList();
                var allBillPkIds = allBillDataObjs.Select(o => o.GetPrimaryKeyValue<string>());
                if (allBillPkIds.IsGreaterThan(20))
                {
                    tempTableName = this.DBService.CreateTempTableWithDataList(userCtx, allBillPkIds,false);
                    strPkIdSql += $@"
inner join {tempTableName} temp on temp.fid=u1.fsourceinterid
where u1.fsourceformid=@formId 
";
                }
                else
                {
                    strPkIdSql += $"where u1.fsourceformid=@formId and u1.fsourceinterid in ({allBillPkIds.JoinEx(",", true)})";
                }
                var pkIdReader = this.DBService.ExecuteReader(userCtx, strPkIdSql, new SqlParam("formId", DbType.String, htmlForm.Id));
                var allBarcodeScanResultObjs = dm.SelectBy(pkIdReader)
                    .OfType<DynamicObject>();

                if (tempTableName.IsNullOrEmptyOrWhiteSpace ()==false )
                {
                    DBService.DeleteTempTableByName(userCtx, tempTableName, true);
                }

                var barcodeMgrService = this.Container.GetService<IBarcodeMgrService>();

                #region 比较扫描记录与备码信息获得删除结果
                Dictionary<DynamicObject, List<DynamicObject>> dctDeleteBarcodeScanResultObjs = new Dictionary<DynamicObject, List<DynamicObject>>();

                //删除多余的关联扫描记录
                List<object> lstNeedDeleteObjs = new List<object>();
                foreach (var barcodeObj in allBarcodeScanResultObjs)
                {
                    var barcode = barcodeObj["fbarcode"] as string;
                    var sourceInterId = barcodeObj["fsourceinterid"] as string;

                    var linkBillObj = allBillDataObjs.FirstOrDefault(o => o.GetPrimaryKeyValue<string>().EqualsIgnoreCase(sourceInterId));
                    
                    Dictionary<ExtendedDataEntity, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>> dctBillConsumeInfo;
                    if (!allConsumeBarcodeViews.TryGetValue(linkBillObj, out dctBillConsumeInfo))
                    {
                        dctBillConsumeInfo = new Dictionary<ExtendedDataEntity, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>>();
                    }

                    var existConsumeBarcodeView = dctBillConsumeInfo.FirstOrDefault(o => o.Value.Any(k => k.Item1.Id.EqualsIgnoreCase(barcode)));
                    if (existConsumeBarcodeView.Value.IsNullOrEmpty())
                    {
                        //记录删除的条码，后面将其状态恢复成【可用】
                        if (linkBillObj != null)
                        {
                            List<DynamicObject> lstBarcodeIds;
                            if (!dctDeleteBarcodeScanResultObjs.TryGetValue(linkBillObj, out lstBarcodeIds))
                            {
                                lstBarcodeIds = new List<DynamicObject>();
                                dctDeleteBarcodeScanResultObjs[linkBillObj] = lstBarcodeIds;
                            }
                            lstBarcodeIds.Add(barcodeObj);
                        }

                        //记录需要删除的扫描记录
                        lstNeedDeleteObjs.Add(barcodeObj["id"]);
                    }
                }
                //找出删除扫描记录，进行条码状态更新
                foreach (var kvpItem in dctDeleteBarcodeScanResultObjs)
                {
                    var traceBillInfo = this.CreateTraceBillInfo(kvpItem.Key, true);
                    barcodeMgrService.UpdateBarcodeStatus(userCtx, traceBillInfo, kvpItem.Value.Distinct(), Enu_BarcodeStatus.Instock);
                }

                if (lstNeedDeleteObjs.Any())
                {
                    dm.Delete(lstNeedDeleteObjs);
                }
                #endregion

                #region 根据备码明细生成新的扫描记录
                //添加缺失的扫描记录
                var billNoField = htmlForm.GetNumberField();
                List<DynamicObject> lstNeedAddObjs = new List<DynamicObject>();

                Dictionary<DynamicObject, List<DynamicObject>> dctAddBarcodeScanResultObjs = new Dictionary<DynamicObject, List<DynamicObject>>();

                //获取条码分组信息得到被哪些业务分录消耗了
                var allEntryObjConsumeInfo = allConsumeBarcodeViews.SelectMany(o => o.Value.SelectMany(k => k.Value.Select(v => Tuple.Create(v.Item1, v.Item2, k.Key, o.Key))))
                    .GroupBy(g => g.Item1)
                    .ToArray();
                var dctDuplicateData = new Dictionary<string, DynamicObject>();
                foreach (var consumedBarcode in allEntryObjConsumeInfo)
                {
                    var linkBillObj = consumedBarcode.FirstOrDefault()?.Item4;
                    if (linkBillObj == null) continue;

                    var existBarcodeObj = allBarcodeScanResultObjs.FirstOrDefault(o => Convert.ToString(o["fbarcode"]).EqualsIgnoreCase(consumedBarcode.Key.Id));
                    if (existBarcodeObj == null)
                    {
                        if (dctDuplicateData.ContainsKey(consumedBarcode.Key.Id))
                        {
                            existBarcodeObj = dctDuplicateData[consumedBarcode.Key.Id];
                        }
                        else
                        {
                            existBarcodeObj = dm.DataEntityType.CreateInstance() as DynamicObject;
                            existBarcodeObj["fbarcode"] = consumedBarcode.Key.Id;
                            existBarcodeObj["fdate"] = DateTime.Now;
                            existBarcodeObj["fbizstatus"] = "1";
                            switch (htmlForm.Id.ToLower())
                            {
                                case "stk_inventorytransferreq":
                                    existBarcodeObj["fscansceneid"] = "3";

                                    //将调拨单分录上库存维度信息备份写入扫描记录中
                                    break;
                                default:
                                    existBarcodeObj["fscansceneid"] = "2";
                                    break;
                            }
                            
                            existBarcodeObj["foperatorid"] = userCtx.UserId;
                            existBarcodeObj["fopdatetime"] = DateTime.Now;

                            existBarcodeObj["fsourceformid"] = htmlForm.Id;
                            existBarcodeObj["fsourcebillno"] = billNoField.DynamicProperty.GetValue(linkBillObj);
                            existBarcodeObj["fsourceinterid"] = linkBillObj["id"];

                            dctDuplicateData[consumedBarcode.Key.Id] = existBarcodeObj;
                        }
                    }

                    //记录当前单据与条码关联时的对应旧的库存维度与新的库存维度
                    this.RecordInvFlexFieldValue(userCtx, htmlForm, consumedBarcode, existBarcodeObj, matchOption);

                    lstNeedAddObjs.Add(existBarcodeObj);

                    existBarcodeObj["fdeviceid"] = this.GetQueryOrSimpleParam<string>("X-deviceid", "");

                    var autoInstock = userConfirm || dpAutoContinue?.GetValue<bool>(linkBillObj) == true;
                    if (autoInstock)
                    {
                        existBarcodeObj["fbizstatus"] = "2";
                        existBarcodeObj["fcheckuserid"] = userCtx.UserId;
                        existBarcodeObj["fcheckdatetime"] = DateTime.Now;
                        lstAutoProcObjs.Add(linkBillObj);
                    }

                    //记录新加或更新的条码，后面将其状态更新成【已备货】
                    List<DynamicObject> lstBarcodeIds;
                    if (!dctAddBarcodeScanResultObjs.TryGetValue(linkBillObj, out lstBarcodeIds))
                    {
                        lstBarcodeIds = new List<DynamicObject>();
                        dctAddBarcodeScanResultObjs[linkBillObj] = lstBarcodeIds;
                    }
                    lstBarcodeIds.Add(existBarcodeObj);
                }
                if (lstNeedAddObjs.Any())
                {
                    var scanResultObjs = lstNeedAddObjs.Distinct().ToArray();
                    var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
                    prepareService.PrepareDataEntity(userCtx, barcodeResultMeta, scanResultObjs, this.OperationContext.Option);
                    dm.InitDbContext(userCtx, barcodeResultMeta.GetDynamicObjectType(userCtx));
                    dm.Save(scanResultObjs);
                }

                //找出新增的扫描记录，分别进行条码状态更新
                foreach (var kvpItem in dctAddBarcodeScanResultObjs)
                {
                    var traceBillInfo = this.CreateTraceBillInfo(kvpItem.Key, false);
                    barcodeMgrService.UpdateBarcodeStatus(userCtx, traceBillInfo, kvpItem.Value.Distinct(), Enu_BarcodeStatus.Inused);
                } 
                #endregion

                //保存当前库存单据信息
                var allDataObjs = allConsumeBarcodeViews.Select(o => o.Key).Distinct();
                var unitConvertService = userCtx.Container.GetService<IUnitConvertService>();
                unitConvertService.ConvertByBasQty(userCtx, htmlForm, allDataObjs, this.OperationContext.Option);
                dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
                dm.Save(allDataObjs);

                tran.Complete();

                return lstAutoProcObjs.Distinct().ToArray();
            }
        }

        /// <summary>
        /// 将关联的条码上的库存维度信息记录至扫描记录上
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="consumeBarcodeInfo"></param>
        /// <param name="existBarcodeObj"></param>
        /// <param name="matchOption"></param>
        private void RecordInvFlexFieldValue(UserContext userCtx, 
            HtmlForm htmlForm, 
            IGrouping<ResolvedBarcodeView,Tuple<ResolvedBarcodeView,Dictionary<string,object>,ExtendedDataEntity,DynamicObject>> consumeBarcodeInfo, 
            DynamicObject existBarcodeObj,
            MatchBarcodeOption matchOption)
        {
            var logger = userCtx.Container.GetService<ILogServiceEx>();

            //将新的库存维度信息更新后设置为新的条码快照，方便后续确认时进行条码状态更新
            var resolveBarcodeView = consumeBarcodeInfo.Key.ToJson().FromJson<ResolvedBarcodeView>();

            //设置条码原状态信息为扫描记录快照
            existBarcodeObj["fbarcodesnap"] = resolveBarcodeView.ToJson();            

            var numberField = htmlForm.GetNumberField();
            //设置仓库，仓位以及商品明细分录相关联的库存维度信息
            foreach (var consumeItem in consumeBarcodeInfo)
            {
                string destStockId = "", destStockLocId = "", destStockStatusId = "";

                switch (htmlForm.Id.ToLower())
                {
                    case "stk_inventorytransferreq":
                        destStockId = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "fstorehouseidto", matchOption);
                        destStockLocId = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "fstorelocationidto", matchOption);
                        destStockStatusId = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "fstockstatusto", matchOption);
                        break;
                    default:
                        destStockId = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "fstorehouseid", matchOption);
                        destStockLocId = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "fstorelocationid", matchOption);
                        destStockStatusId = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "fstockstatus", matchOption);
                        break;
                }
                if (!destStockId.IsEmptyPrimaryKey())
                {
                    resolveBarcodeView.StockId = destStockId;

                    if(!resolveBarcodeView.StockId.IsEmptyPrimaryKey() && !resolveBarcodeView.StockId.EqualsIgnoreCase(destStockId))
                    {
                        logger.Error($"备码失败，同一个条码不可以向不同的仓库位置迁移：条码({resolveBarcodeView.BarCode}),{htmlForm.Caption}单号：{numberField?.DynamicProperty?.GetValue(consumeItem.Item4)}");
                    }
                }

                if (!destStockLocId.IsEmptyPrimaryKey())
                {
                    resolveBarcodeView.StockLocId = destStockLocId;
                }
                var mtrlPkId = consumeItem.Item2.GetValue("id", "").ToString();
                var existMtrlItem = resolveBarcodeView.ResolvedData.FirstOrDefault(o => o.GetValue("id", "").ToString().EqualsIgnoreCase(mtrlPkId));
                if (existMtrlItem != null)
                {
                    existMtrlItem["fstockstatus"] = destStockStatusId;
                    existMtrlItem["flotno"] = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "flotno", matchOption);
                    existMtrlItem["fmtono"] = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "fmtono", matchOption);
                    existMtrlItem["fownertype"] = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "fownertype", matchOption);
                    existMtrlItem["fownerid"] = this.GetFlexFieldValue<string>(userCtx, htmlForm, consumeItem.Item3, "fownerid", matchOption);
                }
            }

            existBarcodeObj["fbarcodenextsnap"] = resolveBarcodeView.ToJson();
        }

        private T GetFlexFieldValue<T>(UserContext userCtx, HtmlForm htmlForm, ExtendedDataEntity dataEntity, string flexFieldKey, MatchBarcodeOption matchOption)
        {
            var mapFlexFieldKey = flexFieldKey;
            if(!matchOption.InvFlexFieldSetting.TryGetValue(flexFieldKey,out mapFlexFieldKey))
            {
                mapFlexFieldKey = flexFieldKey;
            }
            var flexField = htmlForm.GetField(mapFlexFieldKey);
            if (flexField == null) return default(T);
            return (T)Convert.ChangeType(dataEntity[flexField.PropertyName],typeof(T));
        }

        private UpdateBarcodeTraceBill CreateTraceBillInfo(DynamicObject dataEntity, bool rollback)
        {
            var bizDateField = this.OperationContext.HtmlForm.GetField(this.OperationContext.HtmlForm.BizDateFldKey);
            var billNoField = this.OperationContext.HtmlForm.GetNumberField();
            var bizDate = bizDateField.DynamicProperty.GetValue<DateTime?>(dataEntity);

            return new UpdateBarcodeTraceBill(rollback, true)
            {
                BillDate = bizDate.HasValue ? bizDate.Value : DateTime.MinValue,
                BillNo = billNoField.DynamicProperty.GetValue<string>(dataEntity),
                BillId = dataEntity.GetPrimaryKeyValue<string>(),
                FormId = this.OperationContext.HtmlForm.Id,
                OperationName = "备货"
            };
        }

        /// <summary>
        /// 处理自动入库行为
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntities"></param>
        private void AutoOutStock(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntities)
        {            
            var lstSelRows = dataEntities.Select(o => new SelectedRow()
            {
                PkValue = o["id"] as string
            })
            .ToArray();

            //先自动审核当前数据
            var auditResult = this.Gateway.InvokeListOperation(userCtx, htmlForm.Id, lstSelRows, "audit", new Dictionary<string, object>());
            //有错误信息才抛出
            if (auditResult?.IsSuccess == false)
            {
                this.Result.MergeResult(auditResult);
            }
            
            var dctOption = new Dictionary<string, object>();
            var billFormId = htmlForm.Id.ToLower();
            var strOperationNo = "";
            switch (billFormId)
            {
                case "sal_deliverynotice":
                    strOperationNo = "push2sostockout";
                    break;
                case "pur_returnnotice":
                    strOperationNo = "push2returngood";
                    break;
                case "stk_otherstockoutreq":
                    strOperationNo = "push2otheroutstock";
                    break;
                case "stk_inventorytransferreq":
                    strOperationNo = "push2inventorytransfer";
                    break;
            }
            if (strOperationNo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"不识别的业务对象标识：{billFormId}");
            }
            var listDto = new CommonListDTO()
                .SetPageId(this.OperationContext.PageId)
                .SetFormId(billFormId)
                .SetOperationNo(strOperationNo)
                .SetSelectedRows(lstSelRows)
                .SetOption(dctOption)
                .Runbackground();

            var resp = this.Gateway.InvokeLocal<DynamicDTOResponse>(this.Context, listDto);
            if (resp != null)
            {
                this.Result.MergeResult(resp.OperationResult);
            }

        }
    }
}
