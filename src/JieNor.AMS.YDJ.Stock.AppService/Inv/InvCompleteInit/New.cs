using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InvCompleteInit
{
    [InjectService]
    [FormId("stk_invcompleteinit")]
    [OperationNo("new")]
    public class New : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //初始化相关业务对象的存储模型
            var initStockBillMeta = this.MetaModelService.LoadFormModel(this.Context, "stk_initstockbill");
            var inventoryBalanceMeta = this.MetaModelService.LoadFormModel(this.Context, "stk_inventorybalance");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, initStockBillMeta.GetDynamicObjectType(this.Context));
            dm.InitDbContext(this.Context, inventoryBalanceMeta.GetDynamicObjectType(this.Context));

            var systemProfileService = this.Container.GetService<ISystemProfile>();
            var dynamicObj = systemProfileService.GetSystemParameter(this.Context, "stk_invcompleteinit");
            if (dynamicObj != null)
            {
                e.DataEntitys = new DynamicObject[] { dynamicObj };
            }
            if (e.DataEntitys?.Any() == true && e.DataEntitys.First()["finitdate"].IsNullOrEmpty())
            {
                var dtMinimumDate = this.Container.GetService<IStockBaseService>()?.GetMinimumStockBillDate(this.Context, true);
                e.DataEntitys.First()["finitdate"] = dtMinimumDate;
            }
        }
    }
}
