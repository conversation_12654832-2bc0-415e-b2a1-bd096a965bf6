using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryList
{
    /// <summary>
    /// 销售出库单->销售退货的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransferreq")]
    [OperationNo("stk_inventorylist2stk_inventorytransferreq")]
    public class Push2InventoryTransferReqConvertPlugIn : AbstractConvertServicePlugIn
    {
        /// <summary>
        /// 退货单字段计算逻辑处理
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities?.Any() != true)
            {
                throw new BusinessException("调拨失败：未能成功生成库存调拨单！");
            }

            var transferType = "";
            this.Option.TryGetVariableValue("transferType", out transferType);
            if (transferType.IsNullOrEmptyOrWhiteSpace())
            {
                transferType = "invtransfer_biztype_01";
            }

            var targetReturnBillObjs = e.TargetDataEntities;

            foreach (var returnBillObj in targetReturnBillObjs)
            {
                returnBillObj["ftransfertype"] = transferType;
            }
        }
    }
}
