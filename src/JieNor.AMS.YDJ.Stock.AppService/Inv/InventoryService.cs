using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.AMS.YDJ.DataTransferObject.Rpt;
using JieNor.AMS.YDJ.Stock.AppService.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv
{
    /// <summary>
    /// 库存服务
    /// </summary>
    [InjectService]
    public partial class InventoryService : IInventoryService
    {
        /// <summary>
        /// 获取库存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productInfos">商品信息</param>
        /// <param name="showDetail">是否展示库存明细</param>
        /// <returns></returns>
        public List<InventoryModel> GetInventory(UserContext userCtx, JArray productInfos, bool showDetail)
        {
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            string fgetinventorymode = profileService.GetSystemParameter(userCtx, "stk_stockparam", "fgetinventorymode", "1");

            switch (fgetinventorymode)
            {
                case "1":
                    return GetLocalInventory(userCtx, productInfos, showDetail);
                case "2":
                    return GetRemoteInventory(userCtx, productInfos, showDetail);

                default:
                    throw new BusinessException($"未实现商品库存获取模式={fgetinventorymode}！");
            }
        }

        /// <summary>
        /// 获取本地库存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productInfos">商品信息</param>
        /// <param name="showDetail">是否展示库存明细</param>
        /// <returns></returns>
        public List<InventoryModel> GetLocalInventory(UserContext userCtx, JArray productInfos, bool showDetail)
        {
            var ls = new List<InventoryModel>();
            if (productInfos == null || productInfos.Count == 0) return ls;

            var filterValueMap = new List<Dictionary<string, string>>();

            foreach (var productInfo in productInfos)
            {
                var item = new Dictionary<string, string>();

                string productId = productInfo.GetJsonValue("productId", "");
                item["fmaterialid"] = productId;

                string attrInfoId = productInfo["attrInfo"].GetJsonValue("id", "");
                string attrInfoId_e = string.Empty;
                if (attrInfoId.IsNullOrEmptyOrWhiteSpace())
                {
                    var auxPropVals = productInfo["attrInfo"]["entities"].ToObject<List<Dictionary<string, string>>>();

                    var propValueIds = new List<string>();
                    auxPropVals.ForEach(e =>
                    {
                        propValueIds.Add((string)e["valueId"]);
                    });
                    var propValueDatas = userCtx.LoadBizBillHeadDataById("sel_propvalue", propValueIds, "fname,fid,fnumber,fnosuitcreate")
                    ?.OfType<DynamicObject>()
                    ?.ToList();  

                    var propList = new List<PropEntity>();
                    foreach (var auxPropVal in auxPropVals)
                    {
                        propList.Add(new PropEntity
                        {
                            PropId = auxPropVal["auxPropId"],
                            ValueId = auxPropVal["valueId"]
                        });
                    }  

                    foreach (var prop in propList)
                    {
                        var propValueData = propValueDatas?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(prop.ValueId)); 
                        if (propValueData != null)
                        {
                            prop.ValueName = Convert.ToString(propValueData["fname"]);
                            prop.ValueNumber = Convert.ToString(propValueData["fnumber"]);
                            prop.IsNosuitCreate = Convert.ToString(propValueData["fnosuitcreate"]) == "1";
                        }
                    } 

                    var Attrobj = userCtx.Container.GetService<IPropSelectionService>().GetAuxPropId(userCtx, productId, propList);
                    if (!Attrobj.IsNullOrEmptyOrWhiteSpace()) 
                    {
                        attrInfoId = Convert.ToString(Attrobj["fattrinfo"]);
                        attrInfoId_e = Convert.ToString(Attrobj["fattrinfo_e"]);
                    } 
                    productInfo["attrInfo"]["id"] = attrInfoId;
                    productInfo["attrInfo_e"] = attrInfoId_e;
                }

                item["fattrinfo"] = attrInfoId;

                item["fcustomdesc"] = productInfo.GetJsonValue<string>("customDesc") ?? string.Empty;

                filterValueMap.Add(item);
            }

            var stockDatas = GetStockData(userCtx, filterValueMap.ToJson());

            foreach (var productInfo in productInfos)
            {
                string clientId = productInfo.GetJsonValue("clientId", "");
                string productId = productInfo.GetJsonValue("productId", "");
                string attrInfoId = productInfo["attrInfo"].GetJsonValue("id", "").Trim();
                string attrInfoId_e = Convert.ToString(productInfo["attrInfo_e"] ?? "");

                var objs = stockDatas.Where(s => s.fmaterialid.EqualsIgnoreCase(productId) && s.fattrinfo_e.EqualsIgnoreCase(attrInfoId_e));

                decimal qty = objs.Sum(s => s.fqty); // 库存量
                decimal reserveQty = objs.Sum(s => s.freserveqty);    // 预留量
                decimal intransitQty = objs.Sum(s => s.fintransitqty);    // 在途量
                decimal usableQty = objs.Sum(s => s.fusableqty); // 可用库存

                // 汇总
                var product = new InventoryModel
                {
                    ClientId = clientId,
                    ProductId = productId,
                    Qty = qty,
                    ReserveQty = reserveQty,
                    IntransitQty = intransitQty,
                    UsableQty = usableQty,
                    IsClearStock = false
                };

                if (showDetail)
                {
                    var list = new List<InventoryQtyDetailModel>();

                    foreach (var obj in objs)
                    {
                        decimal qty_sub = obj.fqty; // 库存量
                        decimal reserveQty_sub = obj.freserveqty;    // 预留量
                        decimal intransitQty_sub = obj.fintransitqty;    // 在途量

                        ////可用量 = 库存量 + 在途量 - 预留量
                        //decimal usableQty_sub = qty_sub + reserveQty_sub - intransitQty_sub;    // 可用库存

                        decimal usableQty_sub = obj.fusableqty;

                        var item = new InventoryQtyDetailModel
                        {
                            Qty = qty_sub,
                            //ReserveQty = reserveQty_sub,
                            //IntransitQty = intransitQty_sub,
                            UsableQty = usableQty_sub,

                            StoreHouseId = obj.fstorehouseid,
                            StoreHouseName = obj.fstorehousename,
                            StoreLocationId = obj.fstorelocationid,
                            StoreLocationName = obj.fstorelocationname,

                            StockStatusId = obj.fstockstatus,
                            StockStatusName = obj.fstockstatusname
                        };

                        list.Add(item);
                    }

                    product.List = list;
                }

                ls.Add(product);
            }

            return ls;
        }

        /// <summary>
        /// 获取远程库存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productInfos">商品信息</param>
        /// <param name="showDetail">是否展示库存明细</param>
        /// <returns></returns>
        public List<InventoryModel> GetRemoteInventory(UserContext userCtx, JArray productInfos, bool showDetail)
        {
            var ls = new List<InventoryModel>();
            if (productInfos == null || productInfos.Count == 0) return ls;

            // 根据商品获取对应的协同方
            var productIds = productInfos.Select(s => s.GetJsonValue("productId", ""))
                .Where(s => s.IsNullOrEmptyOrWhiteSpace() == false).Distinct();
            if (productIds.Count() == 0) return ls;
            string sql = $"select fpublishcid,fpublishcid_pid,fid from T_BD_MATERIAL where fid in ({string.Join(",", productIds.Select(s => $"'{s}'"))}) and fpublishcid<>'{userCtx.Company}'";

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sql);

            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "", "");
            var auxProps = userCtx.LoadBizDataByFilter("sel_prop", aclFilter);

            foreach (var group in dynObjs.GroupBy(s => $"{s["fpublishcid"]}{s["fpublishcid_pid"]}"))
            {
                string publishCId = Convert.ToString(group.First()["fpublishcid"]);
                string publishPId = Convert.ToString(group.First()["fpublishcid_pid"]);
                TargetSEP target = new TargetSEP(publishCId, publishPId);

                List<string> pids = group.Select(s => Convert.ToString(s["fid"])).ToList();
                var pinfos = productInfos.Where(s => pids.Contains(s.GetJsonValue("productId", ""))).ToList();

                if (pinfos.Count == 0) continue;

                var attrInfoIds = pinfos.Select(s => s["attrInfo"]?.GetJsonValue("id", "")).Where(s => !s.IsNullOrEmptyOrWhiteSpace());
                var attrInfos = new List<DynamicObject>();  // 辅助属性组合值
                if (attrInfoIds.Any())
                {
                    attrInfos = userCtx.LoadBizDataById("bd_auxpropvalueset", attrInfoIds);
                }
                var products = userCtx.LoadBizDataById("ydj_product", pids);

                // 处理辅助属性
                foreach (var item in pinfos)
                {
                    string attrInfoId = item["attrInfo"]?.GetJsonValue("id", "");
                    if (attrInfoId.IsNullOrEmptyOrWhiteSpace())
                    {
                        JArray entities = item["attrInfo"]?.GetJsonValue<JArray>("entities") ?? new JArray();

                        foreach (var entity in entities)
                        {
                            string auxPropId = entity.GetJsonValue("auxPropId", "");
                            entity["auxPropName"] = Convert.ToString(auxProps.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(auxPropId))?["fname"]).Trim();
                        }

                        item["attrInfos"] = entities;
                        //item["attrInfo"].Remove();
                    }
                    else
                    {
                        // 找出辅助属性组合id，转换成辅助属性组合集
                        var attrInfo =
                            attrInfos.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(attrInfoId));
                        JArray entities = new JArray();
                        if (attrInfo != null)
                        {
                            var attrInfoEntrys = attrInfo["FEntity"] as DynamicObjectCollection;
                            foreach (var entry in attrInfoEntrys)
                            {
                                var jObj = new JObject();

                                string auxPropId = Convert.ToString(entry["fauxpropid"]);
                                string auxPropName = Convert.ToString(auxProps.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(auxPropId))?["fname"]).Trim();

                                jObj["auxPropId"] = auxPropId;
                                jObj["auxPropName"] = auxPropName;
                                jObj["valueId"] = Convert.ToString(entry["fvalueid"]);
                                jObj["valueName"] = Convert.ToString(entry["fvaluename"]);

                                entities.Add(jObj);
                            }
                        }

                        item["attrInfos"] = entities;
                        //item["attrInfo"].Remove();
                    }

                    // 添加商品编码
                    string productId = item.GetJsonValue("productId", "");
                    item["productNumber"] =
                        Convert.ToString(
                            products.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(productId))?[
                                "fnumber"]);
                }

                //数据发送时采用异步消息模式发送，消息中指定回调类型
                var result = gateway.Invoke(
                    userCtx,
                    target,
                    new CommonBillDTO()
                    {
                        FormId = "stk_inventorylist",
                        OperationNo = "getinventory_v2",        // 暂时只有博领使用
                        //BillData = "",
                        ExecInAsync = false,
                        AsyncMode = (int)Enu_AsyncMode.Background,
                        SimpleData = new Dictionary<string, string>
                        {
                            { "productInfos", JsonConvert.SerializeObject(pinfos) },
                            { "showDetail", showDetail.ToString() }
                        }
                    }) as CommonBillDTOResponse;
                result?.OperationResult?.ThrowIfHasError(true, $"协同获取库存失败，对方系统未返回任何响应！");

                var srvData = result?.OperationResult?.SrvData;
                var stockDatas = srvData is string ? JArray.Parse(srvData.ToString()) : JArray.FromObject(srvData);
                if (stockDatas == null || stockDatas.Count < 1) continue;

                foreach (var productInfo in pinfos)
                {
                    string clientId = productInfo.GetJsonValue("clientId", "");
                    string productId = productInfo.GetJsonValue("productId", "");

                    InventoryModel product = null;
                    var stockData = stockDatas.FirstOrDefault(s => s.GetJsonValue("clientId", "").EqualsIgnoreCase(clientId));
                    if (stockData != null)
                    {
                        decimal qty = stockData.GetJsonValue("qty", 0M); // 库存量
                        decimal reserveQty = decimal.MinValue;    // 预留量
                        decimal intransitQty = decimal.MinValue;    // 在途量
                        decimal usableQty = stockData.GetJsonValue("usableQty", 0M); // 可用库存

                        // 汇总
                        product = new InventoryModel
                        {
                            ClientId = clientId,
                            ProductId = productId,

                            Qty = qty,
                            ReserveQty = reserveQty,
                            IntransitQty = intransitQty,
                            UsableQty = usableQty,

                            IsClearStock = stockData.GetJsonValue("isClearStock", false)  // 清库存
                        };

                        if (showDetail)
                        {
                            var list = new List<InventoryQtyDetailModel>();

                            foreach (var obj in (JArray)stockData["list"])
                            {
                                decimal qty_sub = obj.GetJsonValue("qty", 0M); // 库存量
                                //decimal reserveQty_sub = 0;    // 预留量
                                //decimal intransitQty_sub = 0;    // 在途量

                                decimal usableQty_sub = decimal.MinValue;//obj.GetJsonValue("usableQty", 0M);    // 可用库存

                                var item = new InventoryQtyDetailModel
                                {
                                    Qty = qty_sub,
                                    //ReserveQty = reserveQty_sub,
                                    //IntransitQty = intransitQty_sub,
                                    UsableQty = usableQty_sub,

                                    //{ "storeHouseId", Convert.ToString(obj["storageid"]) },
                                    StoreHouseName = obj.GetJsonValue("storeHouseName", ""),
                                    //{ "storeLocationId", Convert.ToString(obj["flocid"]) },
                                    StoreLocationName = obj.GetJsonValue("storeLocationName", ""),
                                    //{ "stockStatusId", Convert.ToString(obj["stockstatusid"]) },
                                    StockStatusName = obj.GetJsonValue("stockStatusName", "")
                                };

                                list.Add(item);
                            }

                            product.List = list;
                        }
                    }
                    else
                    {
                        product = new InventoryModel
                        {
                            ClientId = clientId,
                            ProductId = productId
                        };
                    }
                    ls.Add(product);
                }
            }

            return ls;
        }

        /// <summary>
        /// 校验库存（仅用于销售意向/销售合同）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productEntrys">商品明细</param>
        /// <param name="errorMsg">错误消息</param>
        /// <returns></returns>
        public bool CheckInventory(UserContext userCtx, DynamicObjectCollection productEntrys, string formId, out string errorMsg)
        {
            errorMsg = "";

            if (productEntrys == null || productEntrys.Count == 0) return true;

            var profileService = userCtx.Container.GetService<ISystemProfile>();
            string fgetinventorymode = profileService.GetSystemParameter(userCtx, "stk_stockparam", "fgetinventorymode", "1");

            // 非【取总部商品库存】，直接返回true
            if (!fgetinventorymode.EqualsIgnoreCase("2")) return true;

            JArray productInfos = new JArray();

            // 映射clientId与相同商品（指商品编码+辅助属性相同），用于后续的数量判断
            Dictionary<string, string> mapProductIdClientId = new Dictionary<string, string>();

            string productFiled = formId.EqualsIgnoreCase("ydj_saleintention") ? "fmaterialid" : "fproductid";

            foreach (var productEntry in productEntrys)
            {
                string productId = Convert.ToString(productEntry[productFiled]);
                string attrInfoId = Convert.ToString(productEntry["fattrinfo"]);

                string key = $"{productId}:{attrInfoId}";

                if (!mapProductIdClientId.ContainsKey(key))
                {
                    string clientId = Guid.NewGuid().ToString("N");
                    string customDesc = Convert.ToString(productEntry["fcustomdes_e"]);
                    var attrInfoObj = productEntry["fattrinfo_ref"] as DynamicObject;

                    List<Dictionary<string, string>> auxPropVals = new List<Dictionary<string, string>>();
                    if (attrInfoObj != null)
                    {
                        var attrInfoEntrys = attrInfoObj["FEntity"] as DynamicObjectCollection;
                        foreach (var attrInfoEntry in attrInfoEntrys)
                        {
                            var auxPropVal = new Dictionary<string, string>();
                            auxPropVal["auxPropId"] = Convert.ToString(attrInfoEntry["fauxpropid"]);
                            auxPropVal["valueId"] = Convert.ToString(attrInfoEntry["fvalueid"]);
                            auxPropVal["valueName"] = Convert.ToString(attrInfoEntry["fvaluename"]);

                            auxPropVals.Add(auxPropVal);
                        }
                    }

                    productInfos.Add(BuildData(clientId, productId, string.Empty, auxPropVals, customDesc));

                    mapProductIdClientId[key] = clientId;
                }
            }

            var inventoryList = GetRemoteInventory(userCtx, productInfos, false);

            var errorMsgs = new List<string>();

            foreach (var group in productEntrys.GroupBy(s => $"{s[productFiled]}:{s["fattrinfo"]}"))
            {
                string key = group.Key;

                string clientId = mapProductIdClientId[key];

                var inventory =
                    inventoryList.FirstOrDefault(s => s.ClientId.EqualsIgnoreCase(clientId));
                if (inventory != null)
                {
                    bool isClearStock = inventory.IsClearStock;
                    if (isClearStock)
                    {
                        // 如果 “K3即时库存” - “K3未出库数” – “麦浩《销售意向.商品明细.销售数量》或《销售合同.商品明细.销售数量》小于 0 时, 即可销库存为负库存, 并提示：商品编码为XXXXX的可销库存不足，不允许保存，请重新检查下单数量！

                        decimal usableQty = inventory.UsableQty;
                        decimal sumQty = group.Sum(s => Convert.ToDecimal(s["fbizqty"]));
                        //string productNumber = inventory.GetJsonValue<string>("productNumber");
                        string productId = inventory.ProductId;
                        var product = userCtx.LoadBizDataById("ydj_product", productId);

                        if (usableQty < sumQty)
                        {
                            errorMsgs.Add($"商品编码为{product?["fnumber"]}的可销库存不足，不允许保存，请重新检查下单数量！");
                        }
                    }
                }
            }

            if (errorMsgs.Count > 0)
            {
                errorMsg = string.Join("<br/>", errorMsgs);
            }

            return errorMsg.IsNullOrEmptyOrWhiteSpace();
        }

        /// <summary>
        /// 构建数据包
        /// </summary>
        /// <param name="clientId">自定义id，用于数据区分</param>
        /// <param name="productId">商品id</param>
        /// <param name="auxPropValId">辅助属性id（与辅助属性值组合二选一）</param>
        /// <param name="auxPropVals">
        /// 辅助属性值组合（与辅助属性id二选一）
        /// 格式：
        /// [{
        ///     "auxPropId": "740943490174816262",
        ///     "valueId": "咖啡色",
        ///     "valueName": "咖啡色"
        /// },{
        ///     "auxPropId": "740943591442092038",
        ///     "valueId": "00154249/测试单人位/100*100",
        ///     "valueName": "00154249/测试单人位/100*100"
        /// },{
        ///     "auxPropId": "740943454586146822",
        ///     "valueId": "A类布",
        ///     "valueName": "A类布"
        /// }]
        /// </param>
        /// <param name="customDesc">定制说明（暂时不用）</param>
        /// <returns></returns>
        public JObject BuildData(object clientId, object productId, object auxPropValId, object auxPropVals, object customDesc)
        {
            return JObject.FromObject(new
            {
                clientId = clientId,
                productId = productId,
                customDesc = customDesc,
                attrInfo = new
                {
                    id = auxPropValId,
                    entities = auxPropVals
                }
            });
        }

        /// <summary>
        /// 分页查询库存明细
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="invQueryPara">查询参数</param> 
        /// <returns></returns>
        public BaseListPageData<InventoryDetailModel> PageInventoryDetail(UserContext userCtx, StockSynthesizeQueryParaInfo invQueryPara)
        {
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            string fgetinventorymode = profileService.GetSystemParameter(userCtx, "stk_stockparam", "fgetinventorymode", "1");

            switch (fgetinventorymode)
            {
                case "1":
                    return PageLocalInventoryDetail(userCtx, invQueryPara);
                case "2":
                    return PageRemoteInventoryDetail(userCtx, invQueryPara);

                default:
                    throw new BusinessException($"未实现商品库存获取模式={fgetinventorymode}！");
            }
        }

        /// <summary>
        /// 特殊字符
        /// </summary>
        private const string SpecialChar = "@yidaohome.com@";

        private BaseListPageData<InventoryDetailModel> PageRemoteInventoryDetail(UserContext userCtx, StockSynthesizeQueryParaInfo invQueryPara)
        {
            List<string> chainDataIds = GetChainDataIds(userCtx, invQueryPara);

            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();

            TargetSEP target = userCtx.Container.GetService<ISynergyService>().GetSyncTargetSEP(userCtx);

            var simpleData = new Dictionary<string, string>
            {
                { "pageIndex",invQueryPara.PageIndex.ToString() },
                { "pageSize", invQueryPara.PageSize.ToString() },
                { "sortord", invQueryPara.Sortord },
                { "sortby",invQueryPara.Sortby   },
                { "keyword",invQueryPara.Keyword    },
                { "chainDataIds", string.Join(",",chainDataIds) },
                { "materialNumber", "" },
                { "auxPropVals", "" }
            };
            simpleData.Add("billTypeName", invQueryPara.BillTypeName);
            simpleData.Add("billTypeNo", invQueryPara.BillTypeNo);
            simpleData.Add("srcFormId", invQueryPara.SrcFormId);

            if (!invQueryPara.ProductId.IsNullOrEmptyOrWhiteSpace())
            {
                var product = userCtx.LoadBizDataById("ydj_product", invQueryPara.ProductId);
                simpleData["materialNumber"] = Convert.ToString(product?["fnumber"]);
            }

            if (invQueryPara.AuxPropVals != null)
            {
                simpleData["auxPropVals"] = string.Join(",", invQueryPara.AuxPropVals.Select(s => $"{s["auxPropName"]}:{s["valueId"]}"));
            }

            //数据发送时采用异步消息模式发送，消息中指定回调类型
            var result = gateway.Invoke(
                userCtx,
                target,
                new CommonBillDTO()
                {
                    FormId = "stk_inventorylist",
                    OperationNo = "getinventorydetail",
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = simpleData
                }) as CommonBillDTOResponse;
            result?.OperationResult?.ThrowIfHasError(true, $"协同获取库存明细失败，对方系统未返回任何响应！");

            var srvData = result?.OperationResult?.SrvData?.ToString() ?? "{}";

            var srvDataObj = JObject.Parse(srvData);

            BaseListPageData<InventoryDetailModel> page = new BaseListPageData<InventoryDetailModel>();

            page.PageSize = invQueryPara.PageSize;
            page.TotalRecord = srvDataObj.GetJsonValue("totalCount", 0);
            page.List = new List<InventoryDetailModel>();

            JArray data = srvDataObj["data"] as JArray;
            if (data != null && data.Count > 0)
            {
                foreach (var item in data)
                {
                    InventoryDetailModel model = new InventoryDetailModel();

                    model.ChainDataId = item.GetJsonValue("fchaindataid", "");
                    model.AuxPropValue = item.GetJsonValue("fauxpropvalue", "");
                    model.IsClearStock = item.GetJsonValue("fisclearstock", false);
                    model.Qty = Math.Round(item.GetJsonValue("fqty", 0M), 2).ToString();
                    model.StoreHouseName = item.GetJsonValue("fstockname", "");
                    model.StockStatusName = item.GetJsonValue("fstockstatusname", "");
                    model.Orderdate = invQueryPara.Orderdate;

                    page.List.Add(model);
                }
            }

            if (page.List.Any())
            {
                SetProductInfoByChainDataId(userCtx, page.List);
                SetAttrInfo(userCtx, page.List);
                //SetSalPrice(userCtx, page.List);
                SetImage(userCtx, page.List);
            }

            // 处理取总部库存时的字段值
            foreach (var model in page.List)
            {
                model.Attribute = SpecialChar;
                model.CustomDesc = SpecialChar;
                model.StoreLocationName = SpecialChar;
                model.ReserveQty = SpecialChar;
                model.IntransitQty = SpecialChar;
                model.UsableQty = SpecialChar;
                model.LotNo = SpecialChar;
                model.MtoNo = SpecialChar;
                model.OwnerName = SpecialChar;
                model.OwnerTypeName = SpecialChar;
                model.Specifica = SpecialChar;
            }

            return page;
        }

        private BaseListPageData<InventoryDetailModel> PageLocalInventoryDetail(UserContext userCtx, StockSynthesizeQueryParaInfo invQueryPara)
        {
            string selectDataType = invQueryPara.SelectDataType;
            var obj = new { selectDataType };
            //组合查询条件参数
            var simpleData = new Dictionary<string, object>();
            simpleData.Add("pageIndex", invQueryPara.PageIndex.ToString());
            simpleData.Add("pageSize", invQueryPara.PageSize.ToString());
            simpleData.Add("isFilterFmtono", invQueryPara.IsFilterFmtono);
            simpleData.Add("srcPara", JsonConvert.SerializeObject(obj));
            if (!invQueryPara.StorehouseId.IsNullOrEmptyOrWhiteSpace())
            {
                simpleData.Add("fstorehouseid", invQueryPara.StorehouseId);
            }
            if (invQueryPara.BrandIds != null && invQueryPara.BrandIds.Any())
            {
                simpleData.Add("brandIds", string.Join(",", invQueryPara.BrandIds));
            }

            if (!invQueryPara.CategoryId.IsNullOrEmptyOrWhiteSpace())
            {
                simpleData.Add("categoryId", invQueryPara.CategoryId);
            }

            if (!invQueryPara.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                simpleData.Add("keyword", invQueryPara.Keyword);
            }

            if (!invQueryPara.Sortby.IsNullOrEmptyOrWhiteSpace())
            {
                simpleData.Add("sortby", invQueryPara.Sortby);
            }

            if (!invQueryPara.Sortord.IsNullOrEmptyOrWhiteSpace())
            {
                simpleData.Add("sortord", invQueryPara.Sortord);
            }

            if (invQueryPara.StyleIds != null && invQueryPara.StyleIds.Any())
            {
                simpleData.Add("styleIds", string.Join(",", invQueryPara.StyleIds));
            }

            if (invQueryPara.SpaceIds != null && invQueryPara.SpaceIds.Any())
            {
                simpleData.Add("spaceIds", string.Join(",", invQueryPara.SpaceIds));
            }
            if (!invQueryPara.SeriesId.IsNullOrEmptyOrWhiteSpace())
            {
                simpleData.Add("seriesId", invQueryPara.SeriesId);
            }
            if (!invQueryPara.ProductId.IsNullOrEmptyOrWhiteSpace())
            {
                simpleData.Add("productId", invQueryPara.ProductId);
            }
            if (!invQueryPara.StockStatusId.IsNullOrEmptyOrWhiteSpace())
            {
                simpleData.Add("stockStatusId", invQueryPara.StockStatusId);
            }
            if (!invQueryPara.ProductId.IsNullOrEmptyOrWhiteSpace() && invQueryPara.AuxPropVals != null)
            {
                if (invQueryPara.AuxPropVals.Any())
                {
                    // 获取辅助属性id
                    List<string> list = new List<string>();
                    foreach (var auxPropVal in invQueryPara.AuxPropVals)
                    {
                        list.Add($"{auxPropVal["auxPropId"]}:{auxPropVal["valueId"]}");
                    }

                    var auxPropValueSets = PermutationsUtil.PermuteUnique(list.ToArray());

                    var attrInfo = Convert.ToString(userCtx.LoadBizDataByFilter("bd_auxpropvalueset", $" fnumber in ({string.Join(",", auxPropValueSets.Select(s => $"'{string.Join(",", s)}'"))}) and fmaterialid='{ invQueryPara.ProductId }' ").FirstOrDefault()?["Id"]);

                    simpleData.Add("attrInfo", attrInfo);
                }
                else
                {
                    simpleData.Add("attrInfo", " ");
                }
            }
            else
            {
                simpleData.Add("attrInfo", "-1");
            }

            simpleData.Add("billTypeName", invQueryPara.BillTypeName);
            simpleData.Add("billTypeNo", invQueryPara.BillTypeNo);
            simpleData.Add("srcFormId", invQueryPara.SrcFormId);

            //simpleData.Add("isFilterFmtono", dto.IsFilterFmtono.ToString());

            //向麦浩系统发请求
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();

            var result = gateway.InvokeBillOperation(userCtx, "rpt_stocksynthesize", null, "querystocksynthesize",
                simpleData);
            result.ThrowIfHasError(true, $"获取本地库存失败！");

            JArray dataList = null;
            var srvDataJson = result?.SrvData?.ToJson();
            var totalRecord = 0;
            if (!srvDataJson.IsNullOrEmptyOrWhiteSpace())
            {
                //srvDataJson = Regex.Replace(srvDataJson, @"(\\[^bfrnt\\/'\""])", "\\$1");//处理带"\"特殊字符
                var srvData = JObject.Parse(srvDataJson);
                dataList = srvData?["data"] as JArray;
                totalRecord = Convert.ToInt32(srvData?["dataDesc"]?["rows"]);
            }

            BaseListPageData<InventoryDetailModel> page = new BaseListPageData<InventoryDetailModel>();
            page.List = new List<InventoryDetailModel>();
            page.TotalRecord = totalRecord;
            page.PageSize = invQueryPara.PageSize;

            //var list = new List<StockSynthesizeListModel>();
            if (dataList != null && dataList.Any())
            {
                foreach (var item in dataList)
                {
                    var model = new InventoryDetailModel();
                    model.ProductId = Convert.ToString(item["fmaterialid"]);
                    model.ProductNumber = Convert.ToString(item["fmtrlnumber"]);
                    model.ProductName = Convert.ToString(item["fmaterialid_fname"]);
                    //model.Attribute = Convert.ToString(item["fattribute"]);
                    model.AuxPropValId = Convert.ToString(item["fattrinfo_e"]);
                    model.AuxPropValue = Convert.ToString(item["fattrinfo_e_fname"]);
                    //model.BrandId.Name = Convert.ToString(item["fbrandid"]);
                    //model.Category = Convert.ToString(item["fmtrlcategoryid"]);
                    model.CustomDesc = Convert.ToString(item["fcustomdesc"]);
                    //model.Id = Convert.ToString(item["id"]);
                    model.LotNo = Convert.ToString(item["flotno"]);
                    model.MtoNo = Convert.ToString(item["fmtono"]);
                    model.Specifica = Convert.ToString(item["fmtrlmodel"]);
                    //model.Name = Convert.ToString(item["fmaterialid_fname"]);
                    model.SalPrice = Convert.ToDecimal(item["fsalprice"]);
                    //model.Series.Name = Convert.ToString(item["fseriesid"]);
                    //model.ShopCarqty = 0;
                    model.CategoryName = Convert.ToString(item["fmtrlcategoryid"]);

                    model.IntransitQty= decimal.TryParse((item["fstockintransitqty"]?.ToString() ?? "").Trim(), out decimal val)? Math.Round(val, 2).ToString(): "0.00";
                    model.Qty = decimal.TryParse((item["fqty"]?.ToString() ?? "").Trim(), out decimal fqty) ? Math.Round(fqty, 2).ToString() : "0.00";
                    model.ReserveQty = decimal.TryParse((item["freserveqty"]?.ToString() ?? "").Trim(), out decimal freserveqty) ? Math.Round(freserveqty, 2).ToString() : "0.00";
                    model.UsableQty = decimal.TryParse((item["fusableqty"]?.ToString() ?? "").Trim(), out decimal fusableqty) ? Math.Round(fusableqty, 2).ToString() : "0.00";
                    //model.IntransitQty = Convert.ToDecimal(item["fstockintransitqty"]);
                    //model.Qty = Convert.ToDecimal(item["fstockqty"]);
                    //model.ReserveQty = Convert.ToDecimal(item["fstockreserveqty"]);
                    //model.UsableQty = Convert.ToDecimal(item["fstockusableqty"]);

                    model.StockStatusId = Convert.ToString(item["fstockstatus"]);
                    model.StockStatusName = Convert.ToString(item["fstockstatus_fname"]);
                    model.StoreHouseId = Convert.ToString(item["fstorehouseid"]);
                    model.StoreHouseName = Convert.ToString(item["fstorehouseid_fname"]);
                    model.StoreLocationId = Convert.ToString(item["fstorelocationid"]);
                    model.StoreLocationName = Convert.ToString(item["fstorelocationid_fname"]);

                    model.UnitId = Convert.ToString(item["funitid"]);
                    model.UnitName = Convert.ToString(item["funitid_fname"]);
                    model.StockUnitId = Convert.ToString(item["fstockunitid"]);
                    model.StockUnitName = Convert.ToString(item["fstockunitid_fname"]);

                    model.OwnerTypeId = Convert.ToString(item["fownertype"]);
                    model.OwnerTypeName = Convert.ToString(item["fownertype_txt"]);
                    model.OwnerId = Convert.ToString(item["fownerid"]);
                    model.OwnerName = Convert.ToString(item["fownerid_fname"]);
                    model.Orderdate = invQueryPara.Orderdate;
                    page.List.Add(model);
                }
            }

            if (page.List.Any())
            {
                var productIds = page.List.Select(s => s.ProductId).Distinct().ToList();

                string sql = $@"
select  
    m.fid, m.fname, m.fnumber, m.fspecifica, m.fattribute, m.fbrandid, b.fname as fbrandname, m.fseriesid, s.fname as fseriesname, m.fcategoryid, c.fname as fcategoryname,m.fsuiteflag,m.funstdtype,m.fmainorgid
from t_bd_material m
left join t_ydj_brand b on m.fbrandid=b.fid
left join t_ydj_series s on m.fseriesid=s.fid
left join ser_ydj_category c on m.fcategoryid=c.fid
where m.fid in ({string.Join(",", productIds.Select(s => $"'{s}'"))})";

                var dbService = userCtx.Container.GetService<IDBService>();
                var dynObjs = dbService.ExecuteDynamicObject(userCtx, sql);

                foreach (var item in page.List)
                {
                    var dynObj = dynObjs.FirstOrDefault(s =>
                        Convert.ToString(s["fid"]).EqualsIgnoreCase(item.ProductId));
                    if (dynObj != null)
                    {
                        item.BrandId = Convert.ToString(dynObj["fbrandid"]).Trim();
                        item.BrandName = Convert.ToString(dynObj["fbrandname"]).Trim();
                        item.SeriesId = Convert.ToString(dynObj["fseriesid"]).Trim();
                        item.SeriesName = Convert.ToString(dynObj["fseriesname"]).Trim();
                        item.CategoryId = Convert.ToString(dynObj["fcategoryid"]).Trim();
                        item.CategoryName = Convert.ToString(dynObj["fcategoryname"]).Trim();
                        item.Attribute = Convert.ToString(dynObj["fattribute"]).Trim();
                        item.Specifica = Convert.ToString(dynObj["fspecifica"]).Trim();
                        item.IsNonStandard = Convert.ToInt32(dynObj["funstdtype"]) > 0;
                        item.IsSuite = Convert.ToInt32(dynObj["fsuiteflag"]) > 0;
                        item.Isgiveaway = false;
                        item.Dataorigin = Convert.ToString(dynObj["fmainorgid"]).EqualsIgnoreCase(userCtx.TopCompanyId) ? "总部" : "";
                        item.Mainorgid = Convert.ToString(dynObj["fmainorgid"]);
                    }
                }
            }

            SetAttrInfo(userCtx, page.List, true);
            //SetSalPrice(userCtx, page.List);
            SetImage(userCtx, page.List);
            SetIsSample(userCtx, page.List);

            return page;
        }

        /// <summary>
        /// 获取商品云链Id
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="keyword"></param>
        /// <param name="brandIds"></param>
        /// <param name="seriesId"></param>
        /// <param name="categoryId"></param>
        /// <param name="styleIds"></param>
        /// <param name="spaceIds"></param>
        /// <returns></returns>
        private List<string> GetChainDataIds(UserContext userCtx, StockSynthesizeQueryParaInfo invQueryPara)
        {
            string sql = $@"select ffromchaindataid from t_bd_material 
where fmainorgid='{userCtx.Company}' 
and ffromchaindataid<>''";

            List<SqlParam> sqlParams = new List<SqlParam>();

            // 关键字：商品名称+商品编码+规格型号+品牌+系列+辅助属性
            var keyword = invQueryPara.Keyword?.Trim();
            if (!keyword.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParams.Add(new SqlParam("@keyword", DbType.String, $"%{keyword}%"));
                sql += $@" 
and
(
    (fname like @keyword)
    or (fnumber like @keyword)
    or (fspecifica like @keyword)
    or (fbrandid in (select fid from t_ydj_brand where fname like @keyword))
    or (fseriesid in (select fid from t_ydj_series where fname like @keyword))
)";
            }
            // 品牌
            if (invQueryPara.BrandIds != null && invQueryPara.BrandIds.Any())
            {
                sql += $" and fbrandid in ({string.Join(",", invQueryPara.BrandIds.Select(s => $"'{s}'"))}) ";
            }
            // 系列
            if (!invQueryPara.SeriesId.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParams.Add(new SqlParam("@fseriesid", DbType.String, invQueryPara.SeriesId));
                sql += $" and fseriesid=@fseriesid ";
            }
            // 品类
            if (!invQueryPara.CategoryId.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParams.Add(new SqlParam("@fcategoryid", DbType.String, invQueryPara.CategoryId));
                sql += $" and fcategoryid=@fcategoryid ";
            }
            // 风格
            if (invQueryPara.StyleIds != null && invQueryPara.StyleIds.Any())
            {
                sql += $" and fstyle in ({string.Join(",", invQueryPara.StyleIds.Select(s => $"'{s}'"))}) ";
            }
            // 空间
            if (invQueryPara.SpaceIds != null && invQueryPara.SpaceIds.Any())
            {
                sql += $" and fspace in ({string.Join(",", invQueryPara.SpaceIds.Select(s => $"'{s}'"))}) ";
            }

            List<string> chainDataIds = new List<string>();
            using (var reader = userCtx.Container.GetService<IDBService>().ExecuteReader(userCtx, sql, sqlParams))
            {
                while (reader.Read())
                {
                    string chainDataId = reader.GetValueToString("ffromchaindataid", "");
                    if (!chainDataId.IsNullOrEmptyOrWhiteSpace()) chainDataIds.Add(chainDataId);
                }
            }

            return chainDataIds;
        }

        private void SetProductInfoByChainDataId<T>(UserContext userCtx, List<T> models) where T : InventorySummaryModel
        {
            if (models == null || models.Count == 0) return;

            var chainDataIds = models.Where(s => !s.ChainDataId.IsNullOrEmptyOrWhiteSpace()).Select(s => s.ChainDataId).ToList();

            string sql = $@"
select  
    m.ffromchaindataid, m.fid, m.fname, m.fnumber, m.fspecifica, m.fattribute, m.fbrandid, b.fname as fbrandname, m.fseriesid, s.fname as fseriesname
    , m.fcategoryid, c.fname as fcategoryname, m.funitid, unit.fname as funitname, m.fstockunitid, stockunit.fname as fstockunitname, fimage, fimage_txt
from t_bd_material m
left join t_ydj_brand b on m.fbrandid=b.fid
left join t_ydj_series s on m.fseriesid=s.fid
left join ser_ydj_category c on m.fcategoryid=c.fid
left join t_ydj_unit unit on m.funitid=unit.fid
left join t_ydj_unit stockunit on m.fstockunitid=stockunit.fid
where m.fmainorgid='{userCtx.Company}' and m.ffromchaindataid in ({string.Join(",", chainDataIds.Select(s => $"'{s}'"))})";

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sql);

            foreach (var model in models)
            {
                var dynObj = dynObjs.FirstOrDefault(s =>
                    Convert.ToString(s["ffromchaindataid"]).EqualsIgnoreCase(model.ChainDataId));
                if (dynObj != null)
                {
                    model.ProductId = Convert.ToString(dynObj["fid"]);
                    model.ProductName = Convert.ToString(dynObj["fname"]);
                    model.ProductNumber = Convert.ToString(dynObj["fnumber"]);

                    if (model is InventoryDetailModel)
                    {
                        var detailModel = model as InventoryDetailModel;

                        detailModel.BrandId = Convert.ToString(dynObj["fbrandid"]).Trim();
                        detailModel.BrandName = Convert.ToString(dynObj["fbrandname"]).Trim();
                        detailModel.SeriesId = Convert.ToString(dynObj["fseriesid"]).Trim();
                        detailModel.SeriesName = Convert.ToString(dynObj["fseriesname"]).Trim();
                        detailModel.CategoryId = Convert.ToString(dynObj["fcategoryid"]).Trim();
                        detailModel.CategoryName = Convert.ToString(dynObj["fcategoryname"]).Trim();
                        detailModel.Attribute = Convert.ToString(dynObj["fattribute"]).Trim();
                        detailModel.Specifica = Convert.ToString(dynObj["fspecifica"]).Trim();
                        detailModel.UnitId = Convert.ToString(dynObj["funitid"]).Trim();
                        detailModel.UnitName = Convert.ToString(dynObj["funitname"]).Trim();
                        detailModel.StockUnitId = Convert.ToString(dynObj["fstockunitid"]).Trim();
                        detailModel.StockUnitName = Convert.ToString(dynObj["fstockunitname"]).Trim();
                    }
                }
            }
        }

        /// <summary>
        /// 设置商品图片
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="models">数据</param>
        private void SetImage<T>(UserContext userCtx, List<T> models) where T : InventorySummaryModel
        {
            if (models == null || models.Count == 0) return;

            IProductImageService productImageService = userCtx.Container.GetService<IProductImageService>();

            Dictionary<string, T> dict = new Dictionary<string, T>();

            JArray productInfos = new JArray();
            foreach (var model in models)
            {
                string clientId = Guid.NewGuid().ToString("N");
                var productInfo = JToken.FromObject(new
                {
                    clientId,
                    productId = model.ProductId,
                    attrInfo = new
                    {
                        id = string.Empty,
                        entities = model.AuxPropVals
                    }
                });

                dict[clientId] = model;
                productInfos.Add(productInfo);
            }

            var productImages = productImageService.GetImages(userCtx, productInfos);
            foreach (var productImage in productImages)
            {
                string clientId = productImage.GetJsonValue("clientId", "");
                if (dict.ContainsKey(clientId))
                {
                    dict[clientId].ImageId = productImage.GetJsonValue("image", "");
                    dict[clientId].ImageTxt = productImage.GetJsonValue("imageTxt", "");
                }
            }
        }

        /// <summary>
        /// 设置辅助属性
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="models">数据</param>
        /// <param name="byAuxPropValIdLoad">是否根据辅助属性组合值ID加载辅助属性信息</param>
        private void SetAttrInfo<T>(UserContext userCtx, List<T> models, bool byAuxPropValIdLoad = false) where T : InventorySummaryModel
        {
            if (models == null || models.Count == 0) return;

            //根据辅助属性组合值ID加载辅助属性信息
            if (byAuxPropValIdLoad)
            {
                var auxPropValIds = models
                    ?.Select(o => o.AuxPropValId)
                    ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                    ?.Distinct()
                    ?.ToList();
                if (auxPropValIds == null || !auxPropValIds.Any()) return;

                //批量加载辅助属性信息
                var auxPropInfos = this.LoadAuxPropInfo(userCtx, auxPropValIds);
                if (auxPropInfos == null || !auxPropInfos.Any()) return;

                foreach (var model in models)
                {
                    if (model.AuxPropValId.IsNullOrEmptyOrWhiteSpace()) continue;

                    var _auxPropInfos = auxPropInfos
                        .Where(o => Convert.ToString(o["fid"]).EqualsIgnoreCase(model.AuxPropValId))
                        .ToList();
                    if (!_auxPropInfos.Any()) continue;

                    foreach (var item in _auxPropInfos)
                    {
                        //优先取属性值基础中的信息，取不到再取辅助属性明细中的信息
                        var valueId = Convert.ToString(item["fpvid"]);
                        if (valueId.IsNullOrEmptyOrWhiteSpace())
                        {
                            valueId = Convert.ToString(item["fvalueid"]);
                        }
                        var valueName = Convert.ToString(item["fpvname"]);
                        if (valueName.IsNullOrEmptyOrWhiteSpace())
                        {
                            valueName = Convert.ToString(item["fvaluename"]);
                        }
                        //去重，重复的不必返回。 
                        if (model.AuxPropVals.Any(o => Convert.ToString(o?["auxPropId"]??"").EqualsIgnoreCase(Convert.ToString(item["fauxpropid"])))) continue;

                        JObject jObj = new JObject();
                        jObj["auxPropId"] = Convert.ToString(item["fauxpropid"]);
                        jObj["auxPropName"] = Convert.ToString(item["fauxpropname"]);
                        jObj["valueId"] = valueId;
                        jObj["valueName"] = valueName;
                        model.AuxPropVals.Add(jObj);
                    }
                }

                return;
            }

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "", "");
            var auxProps = userCtx.LoadBizDataByFilter("sel_prop", aclFilter);

            foreach (var model in models)
            {
                model.AuxPropVals = new JArray();
                if (model.AuxPropValue.IsNullOrEmptyOrWhiteSpace()) continue;

                var auxPropValues = model.AuxPropValue.SplitKey(",");
                foreach (var value in auxPropValues)
                {
                    var kv = value.SplitKey(":");

                    string auxPropName = kv[0];
                    var auxProp = auxProps.FirstOrDefault(s =>
                        Convert.ToString(s["fname"]).EqualsIgnoreCase(auxPropName));
                    if (auxProp == null)
                    {
                        throw new BusinessException($"不存在辅助属性【{auxPropName}】");
                    }

                    JObject jObj = new JObject();
                    jObj["auxPropId"] = Convert.ToString(auxProp["Id"]);
                    jObj["auxPropName"] = Convert.ToString(auxProp["fname"]);
                    jObj["valueId"] = kv[1];
                    jObj["valueName"] = kv[1];

                    model.AuxPropVals.Add(jObj);
                }
            }
        }

        /// <summary>
        /// 加载辅助属性信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="auxPropValIds">辅助属性组合值ID集合</param>
        /// <returns>辅助属性信息</returns>
        private DynamicObjectCollection LoadAuxPropInfo(UserContext userCtx, List<string> auxPropValIds)
        {
            if (auxPropValIds == null || !auxPropValIds.Any()) return null;

            var sqlText = $@"
            select distinct apext.fid,apve.fauxpropid,p.fname fauxpropname,apve.fvalueid,apve.fvaluename,pv.fid fpvid,pv.fname fpvname,apve.fdisplayseq  from t_bd_auxpropvalueentry apve 
            inner join t_sel_prop p on p.fid=apve.fauxpropid 
            inner join t_bd_auxpropvalue as ap on ap.fid =apve.fid 
            inner join t_bd_auxpropvalue_ext as apext on apext.fname_e = ap.fname_e
            inner join t_sel_propvalue pv on pv.fid=apve.fvalueid 
            where apext.fid in('{string.Join("','", auxPropValIds)}') order by apve.fdisplayseq ";

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText);

            return dynObjs;
        }

        /// <summary>
        /// 设置销售价
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="models">数据</param>
        private void SetSalPrice<T>(UserContext userCtx, List<T> models) where T : InventorySummaryModel
        {
            if (models == null || models.Count == 0) return;

            IPriceService priceService = userCtx.Container.GetService<IPriceService>();

            Dictionary<string, T> dict = new Dictionary<string, T>();

            JArray productInfos = new JArray();
            foreach (var model in models)
            {
                string clientId = Guid.NewGuid().ToString("N");
                var productInfo = JToken.FromObject(new
                {
                    clientId,
                    productId = model.ProductId,
                    bizDate = model.Orderdate.IsNullOrEmpty() ? DateTime.Now : model.Orderdate,
                    length = 0,
                    width = 0,
                    thick = 0,
                    attrInfo = new
                    {
                        id = string.Empty,
                        entities = model.AuxPropVals
                    }
                });

                dict[clientId] = model;
                productInfos.Add(productInfo);
            }

            var prices = priceService.GetPrice(userCtx, 1, productInfos);
            foreach (var price in prices)
            {
                string clientId = price.GetJsonValue("clientId", "");
                bool success = price.GetJsonValue("success", false);
                if (success && dict.ContainsKey(clientId))
                {
                    dict[clientId].SalPrice = price.GetJsonValue("salPrice", 0M);
                }
            }
        }

        /// <summary>
        /// 设置是否样品
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="userCtx"></param>
        /// <param name="models"></param>
        private void SetIsSample<T>(UserContext userCtx, List<T> models) where T : InventorySummaryModel
        {
            if (models == null || models.Count == 0) return;

            var stockStatusIds = models.Where(s => !s.StockStatusId.IsNullOrEmptyOrWhiteSpace()).Select(s => s.StockStatusId).ToList();
            if (stockStatusIds.Count == 0) return;

            var stockStatuses = userCtx.LoadBizDataById("ydj_stockstatus", stockStatusIds);
            foreach (var model in models)
            {
                var stockStatus =
                    stockStatuses.FirstOrDefault(s => Convert.ToString(s["Id"]).EqualsIgnoreCase(model.StockStatusId));
                if (stockStatus != null)
                {
                    model.IsSample = Convert.ToBoolean(stockStatus["fissample"]);
                }
            }
        }

        /// <summary>
        /// 分页查询库存汇总
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        // <param name="invQueryPara">查询参数</param> 
        /// <returns></returns>
        public BaseListPageData<InventorySummaryModel> PageInventorySummary(UserContext userCtx, StockSynthesizeQueryParaInfo invQueryPara)
        {
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            string fgetinventorymode = profileService.GetSystemParameter(userCtx, "stk_stockparam", "fgetinventorymode", "1");

            switch (fgetinventorymode)
            {
                case "1":
                    return PageLocalInventorySummary(userCtx, invQueryPara);
                case "2":
                    return PageRemoteInventorySummary(userCtx, invQueryPara);

                default:
                    throw new BusinessException($"未实现商品库存获取模式={fgetinventorymode}！");
            }
        }

        private BaseListPageData<InventorySummaryModel> PageRemoteInventorySummary(UserContext userCtx, StockSynthesizeQueryParaInfo invQueryPara)
        {
            List<string> chainDataIds = GetChainDataIds(userCtx, invQueryPara);

            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();

            TargetSEP target = userCtx.Container.GetService<ISynergyService>().GetSyncTargetSEP(userCtx);

            //数据发送时采用异步消息模式发送，消息中指定回调类型
            var result = gateway.Invoke(
                userCtx,
                target,
                new CommonBillDTO()
                {
                    FormId = "stk_inventorylist",
                    OperationNo = "getinventorysumary",
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string>
                    {
                        { "pageIndex",invQueryPara.PageIndex  .ToString() },
                        { "pageSize",invQueryPara.PageSize   .ToString() },
                        { "sortord",invQueryPara.Sortord   },
                        { "sortby",invQueryPara.Sortby   },
                        { "keyword",invQueryPara.Keyword    },
                        { "chainDataIds", string.Join(",",chainDataIds) }
                    }
                }) as CommonBillDTOResponse;
            result?.OperationResult?.ThrowIfHasError(true, $"协同获取库存汇总失败，对方系统未返回任何响应！");

            var srvData = result?.OperationResult?.SrvData?.ToString() ?? "{}";

            var srvDataObj = JObject.Parse(srvData);

            BaseListPageData<InventorySummaryModel> page = new BaseListPageData<InventorySummaryModel>();

            page.PageSize = invQueryPara.PageSize;
            page.TotalRecord = srvDataObj.GetJsonValue("totalCount", 0);
            page.List = new List<InventorySummaryModel>();

            JArray data = srvDataObj["data"] as JArray;
            if (data != null && data.Count > 0)
            {
                foreach (var item in data)
                {
                    InventorySummaryModel model = new InventorySummaryModel();

                    model.ChainDataId = item.GetJsonValue("fchaindataid", "");
                    model.AuxPropValue = item.GetJsonValue("fauxpropvalue", "");
                    model.IsClearStock = item.GetJsonValue("fisclearstock", false);
                    model.Qty = Math.Round(item.GetJsonValue("fqty", 0M), 2).ToString();
                    model.UsableQty = Math.Round(item.GetJsonValue("fusableqty", 0M), 2).ToString();
                    model.StockStatusName = item.GetJsonValue("fstockstatusname", "");

                    model.ReserveQty = SpecialChar;
                    model.IntransitQty = SpecialChar;
                    model.Orderdate = invQueryPara.Orderdate;

                    page.List.Add(model);
                }
            }

            if (page.List.Any())
            {
                SetProductInfoByChainDataId(userCtx, page.List);
                SetAttrInfo(userCtx, page.List);
                //SetSalPrice(userCtx, page.List);
                SetImage(userCtx, page.List);
            }

            return page;
        }

        private BaseListPageData<InventorySummaryModel> PageLocalInventorySummary(UserContext userCtx, StockSynthesizeQueryParaInfo invQueryPara)
        {
            // 排序方式：升序为 asc，降序为 desc，默认为 desc 
            var sortOrder = "asc";
            if (invQueryPara.Sortord.EqualsIgnoreCase("desc")) sortOrder = "desc";
            else sortOrder = "asc";

            // 排序依据：按什么规则排序，比如按价格排序（0综合 1库存量排序 2可用量） 
            string orderBy = string.Empty;
            switch (invQueryPara.Sortby?.ToLower())
            {
                // 综合
                case "0":
                    orderBy = $" fqty desc ";
                    break;
                // 库存量排序
                case "1":
                    orderBy = $" fqty {sortOrder} ";
                    break;
                // 可用量排序
                case "2":
                    orderBy = $" fusableqty {sortOrder} ";
                    break;
                // 综合排序
                default:
                    orderBy = " fqty desc ";
                    break;
            }

            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@fmainorgid", DbType.String, userCtx.Company));

            string productWhere2 = string.Empty;

            // 关键字：商品名称+商品编码+规格型号+品牌+系列+辅助属性
            var keyword = invQueryPara.Keyword?.Trim();
            if (!keyword.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParams.Add(new SqlParam("@keyword", DbType.String, $"%{keyword}%"));
                productWhere2 += $@" 
and
(
    (fname like @keyword)
    or (fnumber like @keyword)
    or (fspecifica like @keyword)
    or (fbrandid in (select fid from t_ydj_brand where fname like @keyword))
    or (fseriesid in (select fid from t_ydj_series where fname like @keyword)) 
    or exists 
            (
                select top 1 1 from t_bd_auxpropvalue apv  with(nolock)  
                inner join t_bd_auxpropvalueentry apve with(nolock) on apv.fid=apve.fid
                inner join t_sel_prop ap with(nolock) on ap.fid = apve.fauxpropid
                where apve.fvalueid like @keyword and apv.fmaterialid=m.fid
            )
)";
            }
            // 品牌
            if (invQueryPara.BrandIds != null && invQueryPara.BrandIds.Any())
            {
                productWhere2 += $" and fbrandid in ({string.Join(",", invQueryPara.BrandIds.Select(s => $"'{s}'"))}) ";
            }
            // 系列
            if (!invQueryPara.SeriesId.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParams.Add(new SqlParam("@fseriesid", DbType.String, invQueryPara.SeriesId));
                productWhere2 += $" and fseriesid=@fseriesid ";
            }
            // 品类
            if (!invQueryPara.CategoryId.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParams.Add(new SqlParam("@fcategoryid", DbType.String, invQueryPara.CategoryId));
                productWhere2 += $" and fcategoryid=@fcategoryid ";
            }
            // 风格
            if (invQueryPara.StyleIds != null && invQueryPara.StyleIds.Any())
            {
                productWhere2 += $" and fstyle in ({string.Join(",", invQueryPara.StyleIds.Select(s => $"'{s}'"))}) ";
            }
            // 空间
            if (invQueryPara.SpaceIds != null && invQueryPara.SpaceIds.Any())
            {
                productWhere2 += $" and fspace in ({string.Join(",", invQueryPara.SpaceIds.Select(s => $"'{s}'"))}) ";
            }

            string productWhere = $@" and inv.fmaterialid in (select fid from t_bd_material m where m.fmainorgid=@fmainorgid {productWhere2})";

            //string auxPropWhere = string.Empty;
            //// 关键字：辅助属性
            //if (!keyword.IsNullOrEmptyOrWhiteSpace())
            //{
            //    auxPropWhere = $@" 
            //and exists 
            //(
            //    select top 1 1 from t_bd_auxpropvalueentry apve with(nolock) 
            //    inner join t_bd_auxproperty ap with(nolock) on ap.fid = apve.fauxpropid
            //    where apve.fvalueid like @keyword and apve.fid = inv.fattrinfo
            //)";
            //}

            //在途量的查询条件（需要按照库存维度字段值关联查询）
            var intransitWhere = $@"
            r.fmainorgid=@fmainorgid and r.fcancelstatus='0' and te.fmaterialid=inv.fmaterialid and te.fattrinfo_e=inv.fattrinfo_e and te.fmtono=''";

            //在途量 = 
            //未审核未作废的其它入库单数量 + 
            //未审核未作废的销售退货单数量 + 
            //未审核未作废的库存调拨单数量 + 
            //已审核未作废未关闭的采购订单（数量 - 入库数量 - 退款数量）+ 退换数量
            var intransitText = $@"
		    select 
		    (
			    select isnull(sum(fqty),0) from t_stk_otherstockin r with(nolock) 
			    inner join t_stk_otherstockinentry te with(nolock) on te.fid=r.fid 
			    where {intransitWhere} and r.fstatus<>'E' 
			    and te.fstockstatus=inv.fstockstatus
		    )+(
			    select isnull(sum(fqty),0) from t_stk_sostockreturn r with(nolock) 
			    inner join t_stk_sostockreturnentry te with(nolock) on te.fid=r.fid 
			    where {intransitWhere} and r.fstatus<>'E' 
			    and te.fstockstatus=inv.fstockstatus
		    )+(
			    select isnull(sum(fqty),0) from t_stk_invtransfer r with(nolock) 
			    inner join t_stk_invtransferentry te with(nolock) on te.fid=r.fid 
			    where {intransitWhere} and r.fstatus<>'E' 
			    and te.fstockstatus=inv.fstockstatus
		    )+(
			    select isnull(sum((fqty-finstockqty-frefundqty)+freturnqty),0) from t_ydj_purchaseorder r with(nolock) 
			    inner join t_ydj_poorderentry te with(nolock) on te.fid=r.fid 
			    where {intransitWhere} and r.fstatus='E' and (te.fclosestatus='0' or te.fclosestatus='') 
		    )";

            //预留量的查询语句（商品、辅助属性、库存状态）
            var reserveText = $@"
            select sum(case te.fdirection when '0' then te.fqty when '1' then te.fqty*-1 else 0 end) from t_stk_reservebill t with(nolock) 
            inner join t_stk_reservebillentry te with(nolock) on te.fid=t.fid and te.freservestatus='0' 
            where t.fmainorgid=@fmainorgid and t.fstatus='E' and t.fcancelstatus='0' 
            and te.fmaterialid=inv.fmaterialid and te.fattrinfo_e=inv.fattrinfo_e and te.fstockstatus=inv.fstockstatus and te.fmtono=''";

            //库存量的查询条件
            var sqlWhere = $@"
            inv.fqty>0 and inv.fmainorgid=@fmainorgid and inv.fmtono='' {productWhere}";

            //按即时库存维度字段（商品、辅助属性、库存状态）分别查询库存量、在途量、预留量、可用量
            string table = $@"
            (
                select t.fmaterialid as fproductid, m.fname as fproductname, m.fnumber as fproductnumber, aux.fnumber as fauxpropvalue, t.fstockstatus, ss.fname fstockstatusname, t.fqty, t.fintransitqty, t.freserveqty, (t.fqty+t.fintransitqty-t.freserveqty) as fusableqty, t.fsalprice from 
                (
                    select inv.fmaterialid, inv.fattrinfo, inv.fstockstatus, sum(inv.fqty) fqty,
                    ISNULL(({intransitText}), 0) fintransitqty,
                    ISNULL(({reserveText}), 0) freserveqty,
                    isnull((
                        select top 1 pt.fsalprice from t_ydj_price p with(nolock) 
                        left join t_ydj_priceentry pt with(nolock) on p.fid=pt.fid 
                        where p.fmainorgid=@fmainorgid and p.flimit='' and p.fforbidstatus='0'
                        and pt.fproductid=inv.fmaterialid 
                        and pt.fattrinfo_e=inv.fattrinfo_e 
                        and pt.fconfirmstatus='2'  --[销售价目.报价信息.状态]=已确认
                        and datediff(d,pt.fstartdate,getdate())>-1 and datediff(d,getdate(),pt.fexpiredate)>-1 
                        order by pt.fstartdate desc   --[销售价目.生效日期]=最新
                    ),0) as fsalprice 
                    from t_stk_inventorylist inv with(nolock) 
                    where {sqlWhere} 
	                group by inv.fmaterialid, inv.fattrinfo, inv.fstockstatus
                ) t 
                inner join t_bd_material m with(nolock) on m.fid=t.fmaterialid
                left join T_BD_AUXPROPVALUE aux with(nolock) on aux.fid=t.fattrinfo
                left join t_ydj_stockstatus ss with(nolock) on ss.fid=t.fstockstatus
            ) t";

            var dataSql = $@"select top {invQueryPara.PageSize} * from (select row_number() over(order by {orderBy}) rownum,* from {table}) t where rownum > {invQueryPara.PageSize * (invQueryPara.PageIndex - 1)} ";

            var countSql = $@"select COUNT(1) as totalRecord from {table}";

            var page = new BaseListPageData<InventorySummaryModel>();
            page.PageSize = invQueryPara.PageSize;
            page.List = new List<InventorySummaryModel>();

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, countSql, sqlParams))
            {
                if (reader.Read())
                {
                    page.TotalRecord = reader.GetValueToInt("totalRecord");
                }
            }

            var dynObjs = dbService.ExecuteDynamicObject(userCtx, dataSql, sqlParams);
            if (dynObjs == null || !dynObjs.Any()) return page;

            foreach (var item in dynObjs)
            {
                var model = new InventorySummaryModel
                {
                    ProductId = Convert.ToString(item["fproductid"]),
                    ProductName = Convert.ToString(item["fproductname"]),
                    ProductNumber = Convert.ToString(item["fproductnumber"]),
                    Qty = Math.Round(Convert.ToDecimal(item["fqty"]), 2).ToString(),
                    IntransitQty = Math.Round(Convert.ToDecimal(item["fintransitqty"]), 2).ToString(),
                    ReserveQty = Math.Round(Convert.ToDecimal(item["freserveqty"]), 2).ToString(),
                    UsableQty = Math.Round(Convert.ToDecimal(item["fusableqty"]), 2).ToString(),
                    SalPrice = Convert.ToDecimal(item["fsalprice"]),
                    AuxPropValue = Convert.ToString(item["fauxpropvalue"]),
                    StockStatusId = Convert.ToString(item["fstockstatus"]),
                    StockStatusName = Convert.ToString(item["fstockstatusname"]),
                    Orderdate = invQueryPara.Orderdate
                };

                JArray auxPropInfo = new JArray();
                if (!model.AuxPropValue.IsNullOrEmptyOrWhiteSpace())
                {
                    // 转换辅助属性
                    var auxPropValues = model.AuxPropValue.SplitKey(",");
                    foreach (var value in auxPropValues)
                    {
                        var kv = value.SplitKey(":");

                        string auxPropId = kv[0];
                        string valueId = kv[1];

                        JObject jObj = new JObject();
                        jObj["auxPropId"] = auxPropId;
                        jObj["valueId"] = valueId;
                        jObj["valueName"] = valueId;

                        auxPropInfo.Add(jObj);
                    }
                }
                model.AuxPropVals = auxPropInfo;

                page.List.Add(model);
            }

            //SetAttrInfo(userCtx, page.List);
            //SetSalPrice(userCtx, page.List);
            SetImage(userCtx, page.List);
            SetIsSample(userCtx, page.List);

            return page;
        }

        /// <summary>
        /// 检查库存锁定
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formMeta">库存单据</param>
        /// <param name="dataEntities">库存数据</param>
        public void CheckStockLock(UserContext userCtx, HtmlForm formMeta, DynamicObject[] dataEntities)
        {
            // 盘点单不验证
            if (formMeta.Id.EqualsIgnoreCase("stk_inventoryverify"))
            {
                return;
            }

            var dt = formMeta.GetDynamicObjectType(userCtx);
            userCtx.Container.GetService<LoadReferenceObjectManager>()
                .Load(userCtx, dt, dataEntities, false, new Dictionary<string, string> { { "fmaterialid", "fmaterialid" } });

            if (!CheckInventoryBase(userCtx, formMeta, dataEntities))
            {
                throw new BusinessException("库存盘点冻结中，禁止出入库！");
            }

            //if (!CheckInventoryVerify(userCtx, formMeta, dataEntities))
            //{
            //    throw new BusinessException("库存盘点冻结中，禁止出入库！");
            //}
        }

        /// <summary>
        /// 验证盘点单
        /// </summary>
        /// <returns></returns>
        private bool CheckInventoryVerify(UserContext userCtx, HtmlForm formMeta, DynamicObject[] dataEntities)
        {
            string strSql = @"select t1.fbillno,t2.fmaterialid,t2.fstorehouseid,t2.fstorelocationid 
            from t_stk_invverify t1
            inner join t_stk_invverifyentry t2 on t1.fid=t2.fid
            where t1.fmainorgid=@fmainorgid and t1.fstatus!='E' and t1.fcancelstatus='0'";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company)
            };
            var dbService = userCtx.Container.GetService<IDBService>();
            var data = dbService.ExecuteDynamicObject(userCtx, strSql, sqlParam);
            if (data.IsNullOrEmpty())
            {
                return true;
            }

            var entryFlds = formMeta.GetEntryFieldList("fentity");
            var storeHouseFlds =
                entryFlds.Where(s => (s as HtmlBaseDataField)?.RefFormId.EqualsIgnoreCase("ydj_storehouse") ?? false);
            var storeLocationFlds =
                entryFlds.Where(s => s is HtmlBaseDataEntryField);

            #region 验证盘点单商品仓库仓位 
            foreach (var val in data)
            {
                string fmaterialid = Convert.ToString(val["fmaterialid"]);
                string fstorehouseid = Convert.ToString(val["fstorehouseid"]);
                string fstorelocationid = Convert.ToString(val["fstorelocationid"]).Trim();

                if (formMeta.Id.EqualsIgnoreCase("stk_initstockbill"))
                {
                    //初始库存单特殊处理
                    var result = dataEntities.Any(t =>
                                  Convert.ToString(t["fmaterialid"]).EqualsIgnoreCase(fmaterialid)
                                  && Convert.ToString(t["fstorehouseid"]).EqualsIgnoreCase(fstorehouseid)
                                  && Convert.ToString(t["fstorelocationid"]).Trim().EqualsIgnoreCase(fstorelocationid)
                               );
                    if (result)
                    {
                        return false;
                    }
                }
                else
                {
                    foreach (var item in dataEntities)
                    {
                        var detailedRow = item["fentity"] as DynamicObjectCollection;

                        foreach (var storeHouseFld in storeHouseFlds)
                        {
                            var storeLocationFld = storeLocationFlds.FirstOrDefault(s =>
                                ((HtmlBaseDataEntryField)s).ControlFieldKey.EqualsIgnoreCase(storeHouseFld.Id));
                            if (storeLocationFld == null)
                            {
                                continue;
                            }

                            //验证匹配成功的盘点单
                            var result = detailedRow.Any(x =>
                                 Convert.ToString(x["fmaterialid"]).EqualsIgnoreCase(fmaterialid)
                                 && Convert.ToString(x[storeHouseFld.PropertyName]).EqualsIgnoreCase(fstorehouseid)
                                 && Convert.ToString(x[storeLocationFld.PropertyName]).Trim().EqualsIgnoreCase(fstorelocationid)
                             );
                            if (result)
                            {
                                return false;
                            }
                        }
                    }
                }
            }

            #endregion

            return true;
        }

        /// <summary>
        /// 验证盘点方案
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formMeta">库存单据</param>
        /// <param name="dataEntities">库存数据</param>
        /// <returns></returns>
        private bool CheckInventoryBase(UserContext userCtx, HtmlForm formMeta, DynamicObject[] dataEntities)
        {
            if (dataEntities.IsNullOrEmpty())
            {
                return true;
            }

            string strSql = @"select fcondition,fname from t_stk_inventorybase
            where fmainorgid=@fmainorgid and fstatus='D' and fforbidstatus='0' and freezeout=1
            union all 
            select a.fcondition,a.fname from t_stk_inventorybase as a with(nolock)
            inner join t_stk_invverify as b with(nolock) on a.fid=b.finventbase 
            where a.fmainorgid=@fmainorgid and a.fstatus='E' and a.fforbidstatus='0' and a.freezeout=1 and b.fstatus!='E' and b.fcancelstatus='0'";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company)
            };
            var dbService = userCtx.Container.GetService<IDBService>();
            var inventoryBases = dbService.ExecuteDynamicObject(userCtx, strSql, sqlParam);
            if (inventoryBases.IsNullOrEmpty())
            {
                return true;
            }

            var entryFlds = formMeta.GetEntryFieldList("fentity");
            var storeHouseFlds =
                entryFlds.Where(s => (s as HtmlBaseDataField)?.RefFormId.EqualsIgnoreCase("ydj_storehouse") ?? false);
            var storeLocationFlds =
                entryFlds.Where(s => s is HtmlBaseDataEntryField);
            if (formMeta.Id.EqualsIgnoreCase("stk_initstockbill"))
            {
                //初始库存单特殊处理
                storeHouseFlds = new List<HtmlField>() { formMeta.FieldList["fstorehouseid"] };
                storeLocationFlds = new List<HtmlField>() { formMeta.FieldList["fstorelocationid"] };
            }

            var listBuilder = userCtx.Container.GetService<IListSqlBuilder>();
            foreach (var inventoryBase in inventoryBases)
            {
                foreach (var storeHouseFld in storeHouseFlds)
                {
                    var storeLocationFld = storeLocationFlds.FirstOrDefault(s =>
                        ((HtmlBaseDataEntryField)s).ControlFieldKey.EqualsIgnoreCase(storeHouseFld.Id));
                    if (storeLocationFld == null)
                    {
                        continue;
                    }

                    if (ExistsInScopeEntry(userCtx, formMeta, dataEntities, inventoryBase, listBuilder, storeHouseFld.Id, storeLocationFld.Id))
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// 存在符合范围的明细行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formMeta"></param>
        /// <param name="dataEntities"></param>
        /// <param name="inventoryBase"></param>
        /// <param name="listBuilder"></param>
        /// <returns></returns>
        private static bool ExistsInScopeEntry(UserContext userCtx, HtmlForm formMeta, DynamicObject[] dataEntities,
            DynamicObject inventoryBase, IListSqlBuilder listBuilder, string storehouseFldKey = "fstorehouseid", string storelocationFldKey = "fstorelocationid")
        {
            SqlBuilderParameter sqlparam = new SqlBuilderParameter(userCtx, formId: formMeta.Id);
            var billNos = dataEntities.Select(s => Convert.ToString(s[formMeta.NumberFldKey])).Distinct();
            sqlparam.AppendFilterString($" fbillno in ({billNos.JoinEx(",", true)})");

            var condition = Convert.ToString(inventoryBase["fcondition"]);
            var filterList = condition.FromJson<List<FilterRowObject>>();
            if (filterList.IsNullOrEmpty())
            {
                return false;
            }

            // 默认第一项为并且，否则语义会有问题
            filterList.First().Logic = "And";

            foreach (var filterRowObject in filterList)
            {
                //if (filterRowObject.ReplaceId == null)
                //{
                //    return false;
                //}

                //32063 
                //filterRowObject.ReplaceId = filterRowObject.ReplaceId.ToLower();
                filterRowObject.ReplaceId = filterRowObject.ReplaceId == null ? "" : filterRowObject.ReplaceId.ToLower();
                filterRowObject.Id = filterRowObject.Id.ToLower();

                if (filterRowObject.Id.Contains("fstorehouseid") || filterRowObject.ReplaceId.Contains("fstorehouseid"))
                {
                    filterRowObject.Id = filterRowObject.Id.Replace("fstorehouseid", storehouseFldKey);
                    filterRowObject.ReplaceId = filterRowObject.ReplaceId.Replace("fstorehouseid", storehouseFldKey);
                }

                if (filterRowObject.Id.Contains("fstorelocationid") || filterRowObject.ReplaceId.Contains("fstorelocationid"))
                {
                    filterRowObject.Id = filterRowObject.Id.Replace("fstorelocationid", storelocationFldKey);
                    filterRowObject.ReplaceId = filterRowObject.ReplaceId.Replace("fstorelocationid", storelocationFldKey);
                }
            }

            sqlparam.SetFilter(filterList);

            try
            {
                var listDesc = listBuilder.GetListDesc(userCtx, sqlparam);
                if (listDesc.Rows > 0 && filterList.Count > 0)
                {
                    return true;
                }
            }
            catch (Exception ee)
            {
                throw new BusinessException("盘点范围不正确，请重新调整输入！");
            }

            return false;
        }

        /// <summary>
        /// 清空库存综合查询的扩展数据缓存标记
        /// </summary>
        /// <param name="userCtx"></param>
        public void ClearExtendDataCache(UserContext userCtx)
        {
            string formId = "rpt_stocksynthesize";

            string cacheKey = $"biz:{formId}:UpdateExtendData:{userCtx.Company}";

            var cacheClient = userCtx.Container.GetService<IRedisCache>();
            (cacheClient as IPreInitCache)?.Init(userCtx);

            bool result = cacheClient.Remove(cacheKey);
        }

        /// <summary>
        /// 是否启用仓位
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="storeHouseIds"></param>
        /// <returns></returns>
        private Dictionary<string, bool> IsEnableLocation(UserContext userCtx, IEnumerable<string> storeHouseIds)
        {
            var dic = new Dictionary<string, bool>();

            if (storeHouseIds == null || !storeHouseIds.Any())
            {
                return dic;
            }

            foreach (var id in storeHouseIds)
            {
                dic[id] = false;
            }


            string sql = $@"
select distinct sh.fid from T_YDJ_STOREHOUSE sh with(nolock)
inner join t_ydj_storehouselocation sl with(nolock) on sh.fid=sl.fid
where sh.fid in ({storeHouseIds.JoinEx(",", true)})
";

            var dynObjs = userCtx.ExecuteDynamicObject(sql, new List<SqlParam>());

            foreach (var dynObj in dynObjs)
            {
                var id = Convert.ToString(dynObj["fid"]);

                dic[id] = true;
            }

            return dic;
        }
    }

    /// <summary>
    /// 全排列工具类
    /// </summary>
    public class PermutationsUtil
    {
        public static IList<IList<T>> PermuteUnique<T>(T[] data)
        {
            Array.Sort(data);
            IList<IList<T>> lstAllRes = new List<IList<T>>();
            int len = data.Length;
            if (len == 0) return lstAllRes;
            Stack<T> path = new Stack<T>();
            bool[] visited = new bool[len];
            DFS(data, len, 0, visited, path, lstAllRes);
            return lstAllRes;
        }

        private static void DFS<T>(T[] data, int len, int depth, bool[] visited, Stack<T> path, IList<IList<T>> lstAllRes)
        {
            if (len == depth)
            {
                lstAllRes.Add(new List<T>(path));
                return;
            }

            for (int i = 0; i < len; i++)
            {
                //剪枝
                if (i > 0 && data[i].Equals(data[i - 1]) && visited[i - 1] == false)
                {
                    continue;
                }
                if (visited[i] == false)
                {
                    path.Push(data[i]);
                    visited[i] = true;

                    DFS(data, len, depth + 1, visited, path, lstAllRes);
                    path.Pop();
                    visited[i] = false;
                }
            }
        }
    }



}
