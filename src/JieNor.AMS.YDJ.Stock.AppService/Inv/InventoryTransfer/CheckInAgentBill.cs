using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryTransfer
{
    /// <summary>
    /// 库存调拨单：校验调入方单据是否存在，单据状态等
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("checkinagentbill")]
    public class CheckInAgentBill : AbstractOperationServicePlugIn
    {

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype", "fmaterialid", "fstorehouseid" });
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(new ZYCheckValidation());
        }
    }


    /// <summary>
    /// 库存调拨单：校验调入方单据单据状态
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("checkinagentbillstatus")]
    public class CheckInAgentBillStatus : AbstractOperationServicePlugIn
    {

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var finagentid = this.GetQueryOrSimpleParam<string>("finagentid");
            var fintransferbillno = this.GetQueryOrSimpleParam<string>("fintransferbillno");
            if (string.IsNullOrWhiteSpace(finagentid) || string.IsNullOrWhiteSpace(fintransferbillno))
                throw new BusinessException($"调入经销商或调入方单据编号为空，请检查数据!");

            if (!string.IsNullOrWhiteSpace(fintransferbillno))
            {
                var tagAgentCtx = this.Context.CreateAgentDBContext(finagentid);
                var tagBill = tagAgentCtx.LoadBizDataByNo("stk_inventorytransfer", "fbillno", new List<string>() { fintransferbillno });
                if (tagBill.Count > 0)
                {
                    var status = Convert.ToString(tagBill.FirstOrDefault()?["fstatus"]);
                    if (status.Equals("C") || status.Equals("B"))
                    {
                        this.Result.IsSuccess = true;
                        this.Result.SrvData = $"调入方调拨单号：{fintransferbillno}已创建。若取消调入单，该单据将作废，是否确认取消？";
                        return;
                    }
                    else if (status.Equals("D"))
                        throw new BusinessException($"调入方调拨单号：{fintransferbillno} 已提交。如需取消调入单，请先联系调入方撤销该单据的提交状态，再发起取消操作。");
                    else if (status.Equals("E"))
                        throw new BusinessException($"调入方调拨单号：{fintransferbillno} 已审核。如需取消调入单，请先联系调入方撤销该单据的提交状态，再发起取消操作。");
                }
                else
                {
                    throw new BusinessException($"未查询到调入方调拨单号：{fintransferbillno}，请检查数据!");
                }
            }
            else
            {
                throw new BusinessException($"调入方调拨单号为空，请检查数据!");
            }

        }
    }
}
