using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveDialog;
using JieNor.Framework.Enums;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve
{
    /// <summary>
    /// 预留删除
    /// </summary>
    public partial class ReserveService
    {




        /// <summary>
        /// 预留删除----如果业务单据删除，则删除对应预留单，同时如果上游业务的预留单有转移，要重新转回去。
        /// 比如销售出库单，对应上游业务为销售合同，下推销售出库单保存时，会生成销售出库单的预留，同时把上游的销售合同的预留转出，
        /// 那么销售出库单删除时，要把对应预留单删除，同时把预留转回到上游的销售合同
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billForm"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult DeleteReserve(UserContext userCtx, HtmlForm billForm, IEnumerable<DynamicObject> billDatas, OperateOption option)
        {
            Initialize(userCtx);

            IOperationResult result = new OperationResult();
            result.IsSuccess = false;

            if (billForm.Id.EqualsIgnoreCase("stk_inventorytransfer"))
            {
                result = DeletetransferBillReserve(userCtx, billForm, billDatas, option);

                //库存调拨单也有预留单
                //return result;
            }

            //获取单据对应的预留单
            var reserveBillDatas = new List<DynamicObject>();
            if (billForm.Id.EqualsIgnoreCase("stk_reservebill"))
            {
                reserveBillDatas = billDatas.ToList();
            }
            else
            {
                reserveBillDatas = ReserveUtil.GetReserveBillData(userCtx, billForm, billDatas);
            }
            if (reserveBillDatas == null || reserveBillDatas.Count == 0)
            {
                result.IsSuccess = true;
                return result;
            }

            var sourceBillMeta = GetSourceFormMeta(userCtx, billDatas);
            if (sourceBillMeta == null)
            {
                DeleteReserveBill(userCtx, reserveBillDatas);

                result.IsSuccess = true;
                result.ComplexMessage.SuccessMessages.Add("成功删除预留");
                return result;
            }
            //获取上游单据信息
            var preBillDatas = GetSourceBillDatas(userCtx, billForm, sourceBillMeta, billDatas);
            if (preBillDatas == null || preBillDatas.Count == 0)
            {
                DeleteReserveBill(userCtx, reserveBillDatas);

                result.IsSuccess = true;
                result.ComplexMessage.SuccessMessages.Add("成功删除预留");
                return result;
            }

            var preIds = reserveBillDatas.Select(f => Convert.ToString(f["fprereservepkid"])).ToList();
            var preReserveBillDatas = userCtx.LoadBizDataById("stk_reservebill", preIds);

            var beUpdate = new List<DynamicObject>();
            foreach (var reserveBillData in reserveBillDatas)
            {
                //对应的上游预留单
                var preId = reserveBillData["fprereservepkid"]?.ToString();
                var preReserveBillData = preReserveBillDatas.FirstOrDefault(f => preId.EqualsIgnoreCase(f["Id"]?.ToString()));
                if (preReserveBillData == null)
                {
                    continue;
                }

                //找到对应的上游单据  
                var fsourcenumber = preReserveBillData["fsourcenumber"]?.ToString();
                var preBillData = preBillDatas.FirstOrDefault(f => fsourcenumber.EqualsIgnoreCase(f["fbillno"].ToString()));
                if (preBillData == null)
                {
                    continue;
                }

                //如果上游单据是销售合同且已经关闭，不需要更新预留
                if (sourceBillMeta.Id.EqualsIgnoreCase("ydj_order") && Convert.ToString(preBillData["fclosestate"]) == "1")
                {
                    continue;
                }

                //上游单据已经作废，不需要再更新预留
                if (Convert.ToBoolean(preBillData["fcancelstatus"]))
                {
                    continue;
                }

                DeleteReserve(userCtx, billForm, reserveBillData, preReserveBillData);

                if (!beUpdate.Any(f => Convert.ToString(f["Id"]) == Convert.ToString(preReserveBillData["Id"])))
                {
                    beUpdate.Add(preReserveBillData);
                }
            }

            if (beUpdate.Any())
            {
                //根据基本单位数量自动反算关联业务单位数量字段（如库存单位，业务单位对应的数量）
                var unitService = userCtx.Container.GetService<IUnitConvertService>();
                unitService.ConvertByBasQty(userCtx, ReserveBillFormMeta, beUpdate, option);

                var para = new Dictionary<string, object>();
                para.Add("IgnoreCheckPermssion", true);
                para.Add("TopOrperationNo", "ExcelImport");
                var invokeResult = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(
                        userCtx,
                        "stk_reservebill",
                        beUpdate,
                        "draft",
                        para);
                result.MergeResult(invokeResult);

                ReserveUtil.UpdateOrderReserveQty(userCtx, beUpdate);
            }

            //最后删除对应的预留单
            DeleteReserveBill(userCtx, reserveBillDatas);

            result.IsSuccess = true;
            result.ComplexMessage.SuccessMessages.Add("成功删除预留");

            return result;
        }




        /// <summary>
        /// 预留删除---更新上游预留单的预留信息：上游预留单加上预留转移记录
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="reserveBill">下游单据对应的预留单（当前被删除的预留单）</param>
        /// <param name="preReserveBill">上游单据对应的预留单</param>
        private void DeleteReserve(UserContext userCtx, HtmlForm billForm, DynamicObject reserveBill, DynamicObject preReserveBill)
        {
            var srcEnRows = reserveBill["fentity"] as DynamicObjectCollection;
            var targetEnRows = preReserveBill["fentity"] as DynamicObjectCollection;
            if (srcEnRows == null || srcEnRows.Count == 0 || targetEnRows == null || targetEnRows.Count == 0)
            {
                return;
            }

            var reservePKID = reserveBill["Id"].ToString();
            var reserveSrcBillNo = reserveBill["fsourcenumber"].ToString();
            DeletePreReserveBillTransferRow(reservePKID, reserveSrcBillNo, targetEnRows);

            //重新汇总需求明细行的预留数量
            foreach (var targetEnRow in targetEnRows)
            {
                var targetDetailRows = targetEnRow["fdetail"] as DynamicObjectCollection;
                CalculateReserveQty(targetEnRow, targetDetailRows);
            }

            //同时记录上游预留单的信息，以便下游预留单删除时，把预留转移再还回来
            reserveBill["fprereservepkid"] = "";
        }




        /// <summary>
        /// 删除调拨单时，把上游业务对应的预留单的转移信息删除
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        private IOperationResult DeletetransferBillReserve(UserContext userCtx, HtmlForm billForm, IEnumerable<DynamicObject> billDatas, OperateOption option)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = false;

            var sourceBillMeta = GetSourceFormMeta(userCtx, billDatas);

            //获取上游单据信息
            var preBillDatas = GetSourceBillDatas(userCtx, billForm, sourceBillMeta, billDatas);
            if (preBillDatas == null || preBillDatas.Count == 0)
            {
                result.IsSuccess = true;
                return result;
            }

            //获取上游单据对应的预留单
            var preBillReserveBills = ReserveUtil.GetReserveBillData(userCtx, sourceBillMeta, preBillDatas);
            if (preBillReserveBills == null || preBillReserveBills.Count == 0)
            {
                result.IsSuccess = true;
                return result;
            }

            var beUpdate = new List<DynamicObject>();
            foreach (var preBillReserveBill in preBillReserveBills)
            {
                //找到对应的上游单据  
                var fsourcenumber = preBillReserveBill["fsourcenumber"]?.ToString();
                var preBillData = preBillDatas.FirstOrDefault(f => fsourcenumber.EqualsIgnoreCase(f["fbillno"].ToString()));
                if (preBillData == null)
                {
                    continue;
                }

                //如果上游单据是销售合同且已经关闭，不需要更新预留
                if (sourceBillMeta.Id.EqualsIgnoreCase("ydj_order") && Convert.ToString(preBillData["fclosestate"]) == "1")
                {
                    continue;
                }

                //上游单据已经作废，不需要再更新预留
                if (Convert.ToBoolean(preBillData["fcancelstatus"]))
                {
                    continue;
                }

                //对应的调拨单
                var tranferBill = billDatas.FirstOrDefault(f => fsourcenumber == f["fsourcenumber"]?.ToString());
                if (tranferBill == null)
                {
                    continue;
                }

                DeleteReserve(userCtx, sourceBillMeta, preBillReserveBill, preBillData, tranferBill);

                if (!beUpdate.Any(f => Convert.ToString(f["Id"]) == Convert.ToString(preBillReserveBill["Id"])))
                {
                    beUpdate.Add(preBillReserveBill);
                }
            }

            if (beUpdate.Count > 0)
            {
                //根据基本单位数量自动反算关联业务单位数量字段（如库存单位，业务单位对应的数量）
                var unitService = userCtx.Container.GetService<IUnitConvertService>();
                unitService.ConvertByBasQty(userCtx, ReserveBillFormMeta, beUpdate, option);
                var para = new Dictionary<string, object>();
                para.Add("IgnoreCheckPermssion", true);
                para.Add("TopOrperationNo", "ExcelImport");
                var invokeResult = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(
                    userCtx,
                    "stk_reservebill",
                    beUpdate,
                    "draft",
                    para);

                result.MergeResult(invokeResult);
            }

            result.IsSuccess = true;
            return result;
        }


        /// <summary>
        /// 删除上游预留单的关于调拨单的转移信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="reserveBill"></param>
        /// <param name="tranferBill"></param>
        private void DeleteReserve(UserContext userCtx, HtmlForm sourceBillMeta, DynamicObject preReserveBill, DynamicObject preBillData, DynamicObject tranferBill)
        {
            var targetEnRows = preReserveBill["fentity"] as DynamicObjectCollection;
            if (targetEnRows == null || targetEnRows.Count == 0)
            {
                return;
            }

            var reservePKID = tranferBill["Id"].ToString();
            foreach (var targetEnRow in targetEnRows)
            {
                var targetDetails = targetEnRow["fdetail"] as DynamicObjectCollection;
                var beDelDetail = targetDetails?.Where(f => reservePKID.EqualsIgnoreCase(f["fnextreservepkid"].ToString()))?.ToList();
                if (beDelDetail == null)
                {
                    continue;
                }

                foreach (var item in beDelDetail)
                {
                    targetDetails.Remove(item);
                }
            }

            //重新汇总需求明细行的预留数量
            foreach (var targetEnRow in targetEnRows)
            {
                var targetDetailRows = targetEnRow["fdetail"] as DynamicObjectCollection;
                CalculateReserveQty(targetEnRow, targetDetailRows);

                UpdateDemandBillStockInfo(userCtx, sourceBillMeta, preBillData, targetEnRow, targetDetailRows);
            }

            UpdateSourceOrder(userCtx, sourceBillMeta, new List<DynamicObject> { preBillData });
        }




        /// <summary>
        /// 删除预留单
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="beDelete"></param>
        private void DeleteReserveBill(UserContext ctx, List<DynamicObject> beDelete)
        {
            ////被删除的预留单，更新对应销售合同的预留量
            //var sql = new List<string>();
            //foreach (var item in beDelete)
            //{
            //    var srcForm = item["fsourcetype"]?.ToString();
            //    if (srcForm.IsNullOrEmptyOrWhiteSpace() || !srcForm.EqualsIgnoreCase("ydj_order"))
            //    {
            //        continue;
            //    }

            //    var srcBillId = item["fsourcepkid"]?.ToString();
            //    sql.Add(@"/*dialect*/update t set fbizreserveqty=0 ,freserveqty=0
            //            from t_ydj_orderentry as t 
            //            left join (
            //            select a.fid,a.fentryid,b.fqty,b.fbizqty
            //            from t_ydj_orderentry a
            //            inner join t_stk_reservebillentry b on a.fentryid=b.fsourceentryid  and a.fid=b.fsourceinterid
            //            where b.fid='{0}'
            //            ) x on t.fid=x.fid and t.fentryid =x.fentryid  
            //            where t.fid='{1}' ".Fmt(item["Id"], srcBillId));
            //}
            //if (sql.Count > 0)
            //{
            //    var dbSvc = ctx.Container.GetService<IDBServiceEx>();
            //    dbSvc.ExecuteBatch(ctx, sql);
            //}

            ReserveBillFormMeta = this.MetaModelService.LoadFormModel(ctx, "stk_reservebill");
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, ReserveBillFormMeta.GetDynamicObjectType(ctx));
            dm.Delete(beDelete.Select(f => f["Id"]));

            ReserveUtil.UpdateOrderReserveQty(ctx, beDelete);
        }


    }






}