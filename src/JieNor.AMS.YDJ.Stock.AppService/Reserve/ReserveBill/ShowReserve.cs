using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveBill
{
    /// <summary>
    /// 预留单：根据即时库存Id所在的库存维度匹配预留单，并且打开预留单列表页面，列表中显示匹配到的预留单
    /// 如果匹配到多个则打开列表页面，如果只匹配到一个则打开编辑页面
    /// </summary>
    [InjectService]
    [FormId("stk_reservebill")]
    [OperationNo("showreserve")]
    public class ShowReserve : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var stockId = this.GetQueryOrSimpleParam<string>("stockId");
            if (stockId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"参数 stockId 为空，请检查！");
            }

            //先对表结构进行构建，以免后面执行sql语句报某个表不存在的错误
            var inventoryForm = this.MetaModelService.LoadFormModel(this.Context, "stk_inventorylist");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, inventoryForm.GetDynamicObjectType(this.Context));

            var sqlText = $@"
select distinct r.fid
from t_stk_reservebill r  with(nolock) 
inner join t_stk_reservebillentry re with(nolock) on re.fid=r.fid and re.fqty >0
inner join (
    select fentryid, fstorehouseid, fstockstatus, SUM(case when fdirection_d=0 then fqty_d else fqty_d * -1 end) fqty_d 
    from t_stk_reservebilldetail rd with(nolock)
    group by fentryid, fstorehouseid, fstockstatus
    having SUM(case when fdirection_d=0 then fqty_d else fqty_d * -1 end)>0
) rd on re.fentryid=rd.fentryid 
inner join t_stk_inventorylist il with(nolock)
    on re.fmaterialid=il.fmaterialid 
    and re.fattrinfo_e=il.fattrinfo_e
    and re.fcustomdesc=il.fcustomdesc 
    and re.funitid=il.funitid 
    and rd.fstorehouseid=il.fstorehouseid
    and rd.fstockstatus=il.fstockstatus 
    and (re.fmtono=il.fmtono or re.fmtono='' or il.fmtono='')  
where r.fmainorgid=@fmainorgid and il.fmainorgid=@fmainorgid and il.fid=@stockid 
    and rd.fqty_d>0 and r.fstatus='E' and r.fcancelstatus='0'  ";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@stockid", System.Data.DbType.String, stockId)
            };

            var reserveIds = new List<string>();
            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    var reserveId = reader.GetString("fid");
                    if (!reserveId.IsNullOrEmptyOrWhiteSpace())
                    {
                        reserveIds.Add(reserveId);
                    }
                }
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = reserveIds;
        }
    }
}