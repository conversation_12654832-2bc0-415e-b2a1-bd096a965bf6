using System;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveBorrowDialog
{
    /// <summary>
    /// 动态列基础资料字段（模糊查询、弹窗查询）操作抽象基类
    /// </summary>
    public abstract class AbstractQueryDyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    this.PrepareQueryBuilderParameter(e);
                    break;
                case "onAfterParseFilterString":
                    this.OnAfterParseFilterString(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="e"></param>
        protected void PrepareQueryBuilderParameter(OnCustomServiceEventArgs e)
        {
            var para = e.EventData as SqlBuilderParameter;
            if (para == null) return;

            var filterString = GetReserveBorrowOrderFilter();

            para.FilterString = para.FilterString.JoinFilterString(filterString);
        }

        protected virtual void OnAfterParseFilterString(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, string>;
            var fieldKey = eventData?.Item1?.ToLowerInvariant(); //基础资料字段标识
            var fieldFilter = eventData?.Item2; //基础资料字段过滤条件

            switch (fieldKey)
            {
                case "fborroworderid":
                    {
                        e.Result = GetReserveBorrowOrderFilter();
                        e.Cancel = true;
                    }
                    break;
            }
        }

        private string GetReserveBorrowOrderFilter()
        {
            /*
             * 只获取符合以下条件的销售合同明细行：【数据状态】=已审核 且 【关闭状态】=正常 或 部分关闭 且 【行关闭状态】=正常 或 部分关闭 且 {（销售合同明细行的【销售数量】- 销售合同明细行的【预留量】- 销售合同明细行关联的销售出库明细行的【实发数量】累加）的差额 > 0 } 且 同一需求商品维度（商品编码+定制说明+物流跟踪号+规格型号+辅助属性+销售单位）
             */

            string fmaterialid = this.GetQueryOrSimpleParam<string>("fmaterialid");
            string fcustomdesc = this.GetQueryOrSimpleParam<string>("fcustomdesc");
            string fmtono = this.GetQueryOrSimpleParam<string>("fmtono");
            string fattrinfo = this.GetQueryOrSimpleParam<string>("fattrinfo");
            string fattrinfo_e = this.GetQueryOrSimpleParam<string>("fattrinfo_e");
            string funitid = this.GetQueryOrSimpleParam<string>("funitid");
            string fentryid = this.GetQueryOrSimpleParam<string>("fentryid");

            var filterString =
                $@" fmainorgid='{this.Context.Company}'
and fstatus='E' and fclosestatus in ('{(int)CloseStatus.Default}','{(int)CloseStatus.Part}')
and fentryid<>'{fentryid}'
and  fclosestatus_e in ('{(int)CloseStatus.Default}','{(int)CloseStatus.Part}')
and (fqty-freserveqty-foutqty)>0
and fproductid='{fmaterialid}' and fcustomdes_e='{fcustomdesc}' and fmtono='{fmtono}' and fattrinfo_e='{fattrinfo_e}' and funitid='{funitid}' ";

            return filterString;
        }
    }
}
